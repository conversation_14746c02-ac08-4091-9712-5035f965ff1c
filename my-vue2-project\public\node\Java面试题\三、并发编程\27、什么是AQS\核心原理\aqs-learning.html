<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AQS 核心思想 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f7fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2d3748;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0%;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .game-score {
            text-align: center;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 600;
            margin: 20px 0;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9rem;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-style: italic;
            margin: 10px 0;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">AQS 核心思想</h1>
            <p class="subtitle">通过动画和交互学习 AbstractQueuedSynchronizer</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是 AQS？</h2>
            <div class="explanation">
                <p><span class="highlight">AQS（AbstractQueuedSynchronizer）</span> 是 Java 并发包中的核心基础框架，它为实现依赖于先进先出（FIFO）等待队列的阻塞锁和相关同步器提供了一个框架。</p>
                <br>
                <p>简单来说，AQS 就像一个<span class="highlight">智能的排队管理系统</span>，帮助多个线程有序地获取和释放资源。</p>
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="300"></canvas>
            </div>
            <p class="interactive-hint">点击画布开始动画演示！</p>
        </div>

        <div class="section">
            <h2 class="section-title">🔢 状态变量（State）</h2>
            <div class="explanation">
                <p><span class="highlight">状态变量（state）</span> 是 AQS 的核心，它是一个 32 位的整数，用来表示同步状态：</p>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li><strong>ReentrantLock：</strong>state 表示锁的重入次数</li>
                    <li><strong>Semaphore：</strong>state 表示剩余许可数</li>
                    <li><strong>CountDownLatch：</strong>state 表示计数器的值</li>
                </ul>
            </div>
            <div class="canvas-container">
                <canvas id="stateCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="demonstrateReentrantLock()">演示 ReentrantLock</button>
                <button class="btn" onclick="demonstrateSemaphore()">演示 Semaphore</button>
                <button class="btn" onclick="demonstrateCountDown()">演示 CountDownLatch</button>
            </div>
            <div class="game-score" id="stateScore">操作次数: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">🚶‍♂️ CLH 队列</h2>
            <div class="explanation">
                <p><span class="highlight">CLH 队列</span> 是一个 FIFO（先进先出）的双向队列，当多个线程竞争同一个锁时：</p>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li>未获取到锁的线程会被封装成 <strong>Node 节点</strong> 加入队列</li>
                    <li>队列中的线程会按照 <strong>FIFO 顺序</strong> 等待获取锁</li>
                    <li>每个节点都有 <strong>前驱</strong> 和 <strong>后继</strong> 指针</li>
                </ul>
            </div>
            <div class="canvas-container">
                <canvas id="queueCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="addThread()">添加线程</button>
                <button class="btn" onclick="releaseThread()">释放锁</button>
                <button class="btn" onclick="resetQueue()">重置队列</button>
            </div>
            <div class="game-score" id="queueScore">队列长度: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 综合演示游戏</h2>
            <div class="explanation">
                <p>现在让我们通过一个完整的游戏来理解 AQS 的工作原理！</p>
                <p>在这个游戏中，你将扮演一个<span class="highlight">资源管理员</span>，管理多个线程对共享资源的访问。</p>
            </div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="600"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startGame()">开始游戏</button>
                <button class="btn" onclick="pauseGame()">暂停/继续</button>
                <button class="btn" onclick="resetGame()">重新开始</button>
            </div>
            <div class="game-score" id="gameScore">得分: 0 | 等待线程: 0 | 活跃线程: 0</div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 全局变量
        let currentStep = 0;
        let animationId;
        let gameRunning = false;
        let operationCount = 0;
        let gameScore = 0;
        let queueLength = 0;

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / 4) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 介绍动画
        function initIntroCanvas() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            let animationFrame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制 AQS 标题
                ctx.font = 'bold 36px Arial';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('AQS', canvas.width / 2, 60);
                
                // 绘制状态变量
                ctx.font = '20px Arial';
                ctx.fillStyle = '#2d3748';
                ctx.fillText('状态变量 (State)', 200, 120);
                
                // 动态状态框
                const stateValue = Math.floor(Math.sin(animationFrame * 0.1) * 3) + 3;
                ctx.fillStyle = '#fed6e3';
                ctx.fillRect(150, 140, 100, 40);
                ctx.fillStyle = '#2d3748';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(stateValue.toString(), 200, 165);
                
                // 绘制队列
                ctx.font = '20px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('CLH 队列', 450, 120);
                
                // 动态队列节点
                for (let i = 0; i < 4; i++) {
                    const x = 400 + i * 80;
                    const y = 140;
                    const offset = Math.sin(animationFrame * 0.1 + i * 0.5) * 5;
                    
                    ctx.fillStyle = i === 0 ? '#667eea' : '#a8edea';
                    ctx.fillRect(x, y + offset, 60, 40);
                    ctx.fillStyle = '#2d3748';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`T${i + 1}`, x + 30, y + 25 + offset);
                    
                    // 箭头
                    if (i < 3) {
                        ctx.strokeStyle = '#667eea';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(x + 65, y + 20 + offset);
                        ctx.lineTo(x + 75, y + 20 + offset);
                        ctx.moveTo(x + 70, y + 15 + offset);
                        ctx.lineTo(x + 75, y + 20 + offset);
                        ctx.lineTo(x + 70, y + 25 + offset);
                        ctx.stroke();
                    }
                }
                
                // 连接线
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(250, 160);
                ctx.lineTo(380, 160);
                ctx.stroke();
                ctx.setLineDash([]);
                
                animationFrame++;
                requestAnimationFrame(animate);
            }
            
            canvas.addEventListener('click', () => {
                currentStep = Math.max(currentStep, 1);
                updateProgress();
            });
            
            animate();
        }

        // 状态变量演示
        function initStateCanvas() {
            const canvas = document.getElementById('stateCanvas');
            const ctx = canvas.getContext('2d');
            let currentDemo = 'none';
            let animationFrame = 0;

            function drawStateDemo() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                if (currentDemo === 'reentrant') {
                    drawReentrantLockDemo(ctx, animationFrame);
                } else if (currentDemo === 'semaphore') {
                    drawSemaphoreDemo(ctx, animationFrame);
                } else if (currentDemo === 'countdown') {
                    drawCountDownDemo(ctx, animationFrame);
                } else {
                    drawDefaultState(ctx);
                }
                
                animationFrame++;
                requestAnimationFrame(drawStateDemo);
            }

            window.demonstrateReentrantLock = () => {
                currentDemo = 'reentrant';
                operationCount++;
                updateStateScore();
                currentStep = Math.max(currentStep, 2);
                updateProgress();
            };

            window.demonstrateSemaphore = () => {
                currentDemo = 'semaphore';
                operationCount++;
                updateStateScore();
                currentStep = Math.max(currentStep, 2);
                updateProgress();
            };

            window.demonstrateCountDown = () => {
                currentDemo = 'countdown';
                operationCount++;
                updateStateScore();
                currentStep = Math.max(currentStep, 2);
                updateProgress();
            };

            drawStateDemo();
        }

        function drawDefaultState(ctx) {
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('选择一个同步器来查看状态变量的作用', canvas.width / 2, canvas.height / 2);
        }

        function drawReentrantLockDemo(ctx, frame) {
            ctx.font = 'bold 28px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'center';
            ctx.fillText('ReentrantLock - 重入锁', 400, 50);
            
            // 状态值
            const stateValue = Math.floor(frame / 60) % 4;
            ctx.font = 'bold 48px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText(`State: ${stateValue}`, 400, 120);
            
            ctx.font = '18px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.fillText('重入次数', 400, 150);
            
            // 绘制锁的获取过程
            for (let i = 0; i < 4; i++) {
                const y = 200 + i * 40;
                const isActive = i <= stateValue;
                
                ctx.fillStyle = isActive ? '#fed6e3' : '#f7fafc';
                ctx.fillRect(300, y, 200, 30);
                ctx.fillStyle = '#2d3748';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`第 ${i + 1} 次获取锁`, 310, y + 20);
            }
        }

        function drawSemaphoreDemo(ctx, frame) {
            ctx.font = 'bold 28px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'center';
            ctx.fillText('Semaphore - 信号量', 400, 50);
            
            const maxPermits = 5;
            const currentPermits = (Math.floor(frame / 60) % (maxPermits + 1));
            
            ctx.font = 'bold 48px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText(`State: ${currentPermits}`, 400, 120);
            
            ctx.font = '18px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.fillText('剩余许可数', 400, 150);
            
            // 绘制许可证
            for (let i = 0; i < maxPermits; i++) {
                const x = 250 + i * 60;
                const y = 200;
                const isAvailable = i < currentPermits;
                
                ctx.fillStyle = isAvailable ? '#a8edea' : '#e2e8f0';
                ctx.fillRect(x, y, 50, 50);
                ctx.fillStyle = '#2d3748';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(isAvailable ? '✓' : '✗', x + 25, y + 30);
            }
        }

        function drawCountDownDemo(ctx, frame) {
            ctx.font = 'bold 28px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'center';
            ctx.fillText('CountDownLatch - 倒计时门闩', 400, 50);
            
            const maxCount = 5;
            const currentCount = maxCount - (Math.floor(frame / 60) % (maxCount + 1));
            
            ctx.font = 'bold 48px Arial';
            ctx.fillStyle = currentCount === 0 ? '#10b981' : '#667eea';
            ctx.fillText(`State: ${currentCount}`, 400, 120);
            
            ctx.font = '18px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.fillText(currentCount === 0 ? '门闩已开启！' : '等待计数归零', 400, 150);
            
            // 绘制倒计时进度
            for (let i = 0; i < maxCount; i++) {
                const x = 250 + i * 60;
                const y = 200;
                const isCompleted = i < (maxCount - currentCount);
                
                ctx.fillStyle = isCompleted ? '#10b981' : '#e2e8f0';
                ctx.fillRect(x, y, 50, 50);
                ctx.fillStyle = '#2d3748';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(isCompleted ? '✓' : (i + 1).toString(), x + 25, y + 30);
            }
        }

        function updateStateScore() {
            document.getElementById('stateScore').textContent = `操作次数: ${operationCount}`;
        }

        // 队列演示
        function initQueueCanvas() {
            const canvas = document.getElementById('queueCanvas');
            const ctx = canvas.getContext('2d');
            let queue = [];
            let animationFrame = 0;

            function drawQueue() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.font = 'bold 28px Arial';
                ctx.fillStyle = '#2d3748';
                ctx.textAlign = 'center';
                ctx.fillText('CLH 队列演示', 400, 40);
                
                // 绘制头节点
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 100, 80, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('HEAD', 90, 135);
                
                // 绘制队列节点
                for (let i = 0; i < queue.length; i++) {
                    const x = 180 + i * 100;
                    const y = 100;
                    const node = queue[i];
                    const bounce = Math.sin(animationFrame * 0.1 + i * 0.3) * 3;
                    
                    // 节点
                    ctx.fillStyle = node.waiting ? '#fed6e3' : '#a8edea';
                    ctx.fillRect(x, y + bounce, 80, 60);
                    
                    // 节点文本
                    ctx.fillStyle = '#2d3748';
                    ctx.font = 'bold 14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.name, x + 40, y + 25 + bounce);
                    ctx.fillText(node.waiting ? 'WAIT' : 'READY', x + 40, y + 45 + bounce);
                    
                    // 箭头
                    if (i === 0) {
                        drawArrow(ctx, 130, 130, x - 10, 130);
                    }
                    if (i < queue.length - 1) {
                        drawArrow(ctx, x + 80, 130 + bounce, x + 90, 130 + bounce);
                    }
                }
                
                // 绘制尾节点
                if (queue.length > 0) {
                    const lastX = 180 + (queue.length - 1) * 100;
                    ctx.fillStyle = '#764ba2';
                    ctx.fillRect(lastX + 110, 100, 80, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('TAIL', lastX + 150, 135);
                    
                    drawArrow(ctx, lastX + 80, 130, lastX + 100, 130);
                }
                
                // 绘制说明
                ctx.font = '16px Arial';
                ctx.fillStyle = '#2d3748';
                ctx.textAlign = 'left';
                ctx.fillText('• 蓝色节点：正在等待锁', 50, 220);
                ctx.fillText('• 粉色节点：等待状态', 50, 250);
                ctx.fillText('• 队列按 FIFO 顺序处理', 50, 280);
                
                animationFrame++;
                requestAnimationFrame(drawQueue);
            }

            window.addThread = () => {
                const threadId = queue.length + 1;
                queue.push({
                    name: `T${threadId}`,
                    waiting: true
                });
                queueLength = queue.length;
                updateQueueScore();
                currentStep = Math.max(currentStep, 3);
                updateProgress();
            };

            window.releaseThread = () => {
                if (queue.length > 0) {
                    queue.shift();
                    if (queue.length > 0) {
                        queue[0].waiting = false;
                    }
                }
                queueLength = queue.length;
                updateQueueScore();
            };

            window.resetQueue = () => {
                queue = [];
                queueLength = 0;
                updateQueueScore();
            };

            drawQueue();
        }

        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function updateQueueScore() {
            document.getElementById('queueScore').textContent = `队列长度: ${queueLength}`;
        }

        // 综合游戏
        function initGameCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            let gameState = {
                threads: [],
                resources: 3,
                score: 0,
                time: 0,
                paused: false
            };

            function gameLoop() {
                if (!gameRunning || gameState.paused) {
                    requestAnimationFrame(gameLoop);
                    return;
                }
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 更新游戏逻辑
                updateGameLogic(gameState);
                
                // 绘制游戏界面
                drawGameInterface(ctx, gameState);
                
                requestAnimationFrame(gameLoop);
            }

            window.startGame = () => {
                gameRunning = true;
                gameState = {
                    threads: [],
                    resources: 3,
                    score: 0,
                    time: 0,
                    paused: false
                };
                currentStep = Math.max(currentStep, 4);
                updateProgress();
                gameLoop();
            };

            window.pauseGame = () => {
                if (gameRunning) {
                    gameState.paused = !gameState.paused;
                }
            };

            window.resetGame = () => {
                gameRunning = false;
                gameState = {
                    threads: [],
                    resources: 3,
                    score: 0,
                    time: 0,
                    paused: false
                };
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawGameInterface(ctx, gameState);
            };

            // 初始绘制
            drawGameInterface(ctx, gameState);
        }

        function updateGameLogic(gameState) {
            gameState.time++;
            
            // 随机添加新线程
            if (Math.random() < 0.02) {
                gameState.threads.push({
                    id: Date.now(),
                    x: Math.random() * 700 + 50,
                    y: Math.random() * 400 + 100,
                    state: 'waiting',
                    waitTime: 0
                });
            }
            
            // 更新线程状态
            gameState.threads.forEach(thread => {
                if (thread.state === 'waiting') {
                    thread.waitTime++;
                    if (thread.waitTime > 120) {
                        gameState.score -= 10; // 等待太久扣分
                        thread.waitTime = 0;
                    }
                }
            });
            
            // 自动处理一些线程
            if (gameState.time % 60 === 0 && gameState.threads.length > 0) {
                const waitingThreads = gameState.threads.filter(t => t.state === 'waiting');
                if (waitingThreads.length > 0 && gameState.resources > 0) {
                    waitingThreads[0].state = 'running';
                    gameState.resources--;
                    gameState.score += 10;
                }
            }
            
            // 释放资源
            if (gameState.time % 90 === 0) {
                const runningThreads = gameState.threads.filter(t => t.state === 'running');
                if (runningThreads.length > 0) {
                    gameState.threads = gameState.threads.filter(t => t !== runningThreads[0]);
                    gameState.resources++;
                    gameState.score += 20;
                }
            }
        }

        function drawGameInterface(ctx, gameState) {
            // 背景
            ctx.fillStyle = '#f7fafc';
            ctx.fillRect(0, 0, 800, 600);
            
            // 标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'center';
            ctx.fillText('AQS 资源管理游戏', 400, 30);
            
            // 资源状态
            ctx.font = '18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`可用资源: ${gameState.resources}`, 50, 70);
            ctx.fillText(`得分: ${gameState.score}`, 200, 70);
            ctx.fillText(`时间: ${Math.floor(gameState.time / 60)}s`, 350, 70);
            
            // 绘制资源
            for (let i = 0; i < 3; i++) {
                const x = 500 + i * 60;
                const y = 50;
                ctx.fillStyle = i < gameState.resources ? '#10b981' : '#e2e8f0';
                ctx.fillRect(x, y, 40, 40);
                ctx.fillStyle = '#2d3748';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('R', x + 20, y + 25);
            }
            
            // 绘制线程
            gameState.threads.forEach(thread => {
                ctx.fillStyle = thread.state === 'waiting' ? '#fed6e3' : '#a8edea';
                ctx.fillRect(thread.x, thread.y, 40, 40);
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(thread.state === 'waiting' ? 'W' : 'R', thread.x + 20, thread.y + 25);
            });
            
            // 绘制队列可视化
            const waitingThreads = gameState.threads.filter(t => t.state === 'waiting');
            ctx.font = '16px Arial';
            ctx.fillStyle = '#2d3748';
            ctx.textAlign = 'left';
            ctx.fillText('等待队列:', 50, 520);
            
            waitingThreads.slice(0, 10).forEach((thread, index) => {
                const x = 50 + index * 50;
                const y = 530;
                ctx.fillStyle = '#fed6e3';
                ctx.fillRect(x, y, 40, 40);
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText((index + 1).toString(), x + 20, y + 25);
            });
            
            // 游戏说明
            if (!gameRunning || gameState.paused) {
                ctx.fillStyle = 'rgba(0,0,0,0.7)';
                ctx.fillRect(0, 0, 800, 600);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(gameRunning ? '游戏暂停' : '点击开始游戏', 400, 300);
                ctx.font = '16px Arial';
                ctx.fillText('管理线程队列，合理分配资源获得高分！', 400, 330);
            }
            
            // 更新分数显示
            const waitingCount = gameState.threads.filter(t => t.state === 'waiting').length;
            const runningCount = gameState.threads.filter(t => t.state === 'running').length;
            document.getElementById('gameScore').textContent = 
                `得分: ${gameState.score} | 等待线程: ${waitingCount} | 活跃线程: ${runningCount}`;
        }

        // 初始化所有画布
        document.addEventListener('DOMContentLoaded', () => {
            initIntroCanvas();
            initStateCanvas();
            initQueueCanvas();
            initGameCanvas();
            updateProgress();
        });
    </script>
</body>
</html>
