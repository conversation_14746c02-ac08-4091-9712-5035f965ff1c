<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库视图 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .game-area {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 20px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title floating">数据库视图</h1>
            <p class="subtitle">探索虚拟表的奥秘 - 交互式学习体验</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是视图？</h2>
            <div class="explanation">
                <p><span class="highlight">视图</span>是数据库中的一个重要概念。想象一下，如果数据库中的表是真实的房子，那么视图就像是房子的<span class="highlight">镜像</span>或者<span class="highlight">投影</span>。</p>
                <p>视图的列可以来自不同的表，是表的抽象和在逻辑意义上建立的新关系。</p>
            </div>
            <div class="canvas-container">
                <canvas id="viewConceptCanvas" width="800" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="animateViewConcept()">🎬 播放动画</button>
                <button class="interactive-btn" onclick="resetViewConcept()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">实表 vs 虚表</h2>
            <div class="explanation">
                <p><span class="highlight">视图是由基本表(实表)产生的表(虚表)</span>。这就像是用积木搭建房子：</p>
                <ul style="margin-left: 30px; margin-top: 15px;">
                    <li>🏗️ <strong>实表</strong>：真实存储数据的表，就像实际的积木块</li>
                    <li>🪞 <strong>虚表(视图)</strong>：不存储数据，只是显示数据的方式，就像积木的影子</li>
                </ul>
            </div>
            <div class="canvas-container">
                <canvas id="realVsVirtualCanvas" width="800" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="animateRealVsVirtual()">🎭 实表变虚表</button>
                <button class="interactive-btn" onclick="showDataFlow()">📊 数据流动</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">视图的特性</h2>
            <div class="explanation">
                <p>让我们通过互动游戏来理解视图的重要特性：</p>
            </div>
            <div class="canvas-container">
                <canvas id="propertiesCanvas" width="800" height="500"></canvas>
            </div>
            <div class="game-area">
                <div class="score">特性探索得分: <span id="propertyScore">0</span></div>
                <button class="interactive-btn" onclick="startPropertyGame()">🎮 开始特性探索</button>
                <button class="interactive-btn" onclick="showProperty(1)">📝 建立删除不影响基本表</button>
                <button class="interactive-btn" onclick="showProperty(2)">🔄 更新直接影响基本表</button>
                <button class="interactive-btn" onclick="showProperty(3)">🚫 多表视图限制</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">视图操作大挑战</h2>
            <div class="explanation">
                <p><span class="highlight">视图的操作包括创建视图，查看视图，删除视图和修改视图</span>。让我们通过拖拽游戏来学习！</p>
            </div>
            <div class="canvas-container">
                <canvas id="operationsCanvas" width="800" height="500"></canvas>
            </div>
            <div class="game-area">
                <div class="score">操作挑战得分: <span id="operationScore">0</span></div>
                <button class="interactive-btn" onclick="startOperationGame()">🎯 开始操作挑战</button>
                <button class="interactive-btn" onclick="resetOperationGame()">🔄 重新开始</button>
                <div style="margin-top: 20px; font-size: 14px; color: #666;">
                    拖拽操作到正确的位置完成视图生命周期！
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">知识总结</h2>
            <div class="explanation">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 20px 0;">
                    <h3 style="margin-bottom: 20px; text-align: center;">🎓 你已经掌握了视图的核心知识！</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                        <div>
                            <h4>✨ 视图本质</h4>
                            <p>• 虚拟表，不存储数据</p>
                            <p>• 来自一个或多个基本表</p>
                            <p>• 逻辑上的新关系</p>
                        </div>
                        <div>
                            <h4>🔧 视图操作</h4>
                            <p>• 创建、查看、删除、修改</p>
                            <p>• 建立删除不影响基本表</p>
                            <p>• 更新操作直接影响基本表</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="300"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn pulse" onclick="celebrateCompletion()">🎉 完成学习庆祝</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentStep = 0;

        // 视图概念动画
        function animateViewConcept() {
            const canvas = document.getElementById('viewConceptCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制基本表
            drawTable(ctx, 100, 150, '学生表', ['ID', '姓名', '年龄'], [
                ['1', '张三', '20'],
                ['2', '李四', '21']
            ], '#e3f2fd');
            
            drawTable(ctx, 300, 150, '成绩表', ['学生ID', '科目', '分数'], [
                ['1', '数学', '95'],
                ['2', '数学', '88']
            ], '#f3e5f5');
            
            // 动画效果：箭头和视图生成
            setTimeout(() => {
                drawArrow(ctx, 200, 200, 500, 200, '#667eea');
                drawArrow(ctx, 400, 200, 500, 200, '#764ba2');
                
                setTimeout(() => {
                    drawTable(ctx, 500, 150, '学生成绩视图', ['姓名', '科目', '分数'], [
                        ['张三', '数学', '95'],
                        ['李四', '数学', '88']
                    ], '#e8f5e8');
                    
                    // 添加闪烁效果
                    ctx.shadowColor = '#4caf50';
                    ctx.shadowBlur = 20;
                    ctx.strokeStyle = '#4caf50';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(495, 145, 210, 120);
                }, 1000);
            }, 500);
        }

        // 实表vs虚表动画
        function animateRealVsVirtual() {
            const canvas = document.getElementById('realVsVirtualCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制实表（3D效果）
            draw3DTable(ctx, 150, 100, '实表 (真实数据)', true);
            
            setTimeout(() => {
                // 绘制虚表（透明效果）
                draw3DTable(ctx, 450, 100, '虚表 (视图)', false);
                
                // 绘制连接线
                drawDashedLine(ctx, 350, 160, 450, 160);
                
                // 添加文字说明
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('数据映射', 380, 140);
            }, 1000);
        }

        // 绘制表格
        function drawTable(ctx, x, y, title, headers, data, bgColor) {
            const cellWidth = 70;
            const cellHeight = 30;
            const headerHeight = 35;
            
            // 绘制标题
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + (cellWidth * headers.length) / 2, y - 10);
            
            // 绘制表头
            ctx.fillStyle = bgColor;
            ctx.fillRect(x, y, cellWidth * headers.length, headerHeight);
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, cellWidth * headers.length, headerHeight);
            
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            headers.forEach((header, i) => {
                ctx.fillText(header, x + i * cellWidth + cellWidth/2, y + headerHeight/2 + 5);
            });
            
            // 绘制数据行
            ctx.fillStyle = 'white';
            data.forEach((row, rowIndex) => {
                const rowY = y + headerHeight + rowIndex * cellHeight;
                ctx.fillRect(x, rowY, cellWidth * headers.length, cellHeight);
                ctx.strokeRect(x, rowY, cellWidth * headers.length, cellHeight);
                
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#555';
                row.forEach((cell, cellIndex) => {
                    ctx.fillText(cell, x + cellIndex * cellWidth + cellWidth/2, rowY + cellHeight/2 + 4);
                });
            });
        }

        // 绘制3D表格
        function draw3DTable(ctx, x, y, title, isReal) {
            const width = 200;
            const height = 120;
            const depth = 20;
            
            // 绘制标题
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + width/2, y - 20);
            
            if (isReal) {
                // 实表 - 实心
                ctx.fillStyle = '#4caf50';
                ctx.fillRect(x, y, width, height);
                ctx.fillStyle = '#45a049';
                ctx.fillRect(x + depth, y - depth, width, height);
                
                // 连接线
                ctx.strokeStyle = '#45a049';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, y);
                ctx.lineTo(x + depth, y - depth);
                ctx.moveTo(x + width, y);
                ctx.lineTo(x + width + depth, y - depth);
                ctx.moveTo(x, y + height);
                ctx.lineTo(x + depth, y + height - depth);
                ctx.moveTo(x + width, y + height);
                ctx.lineTo(x + width + depth, y + height - depth);
                ctx.stroke();
                
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('存储真实数据', x + width/2, y + height/2);
            } else {
                // 虚表 - 透明
                ctx.globalAlpha = 0.6;
                ctx.fillStyle = '#2196f3';
                ctx.fillRect(x, y, width, height);
                ctx.fillStyle = '#1976d2';
                ctx.fillRect(x + depth, y - depth, width, height);
                
                ctx.strokeStyle = '#1976d2';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.strokeRect(x, y, width, height);
                ctx.strokeRect(x + depth, y - depth, width, height);
                
                ctx.globalAlpha = 1;
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('只显示数据', x + width/2, y + height/2);
                ctx.setLineDash([]);
            }
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        // 绘制虚线
        function drawDashedLine(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 2;
            ctx.setLineDash([10, 5]);
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // 重置视图概念
        function resetViewConcept() {
            const canvas = document.getElementById('viewConceptCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 显示数据流动
        function showDataFlow() {
            const canvas = document.getElementById('realVsVirtualCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布并重绘基础内容
            animateRealVsVirtual();
            
            setTimeout(() => {
                // 添加流动的粒子效果
                let particles = [];
                for (let i = 0; i < 10; i++) {
                    particles.push({
                        x: 350,
                        y: 160 + (Math.random() - 0.5) * 20,
                        vx: 2,
                        vy: (Math.random() - 0.5) * 2,
                        life: 50
                    });
                }
                
                function animateParticles() {
                    particles.forEach((particle, index) => {
                        if (particle.life <= 0) {
                            particles.splice(index, 1);
                            return;
                        }
                        
                        ctx.globalAlpha = particle.life / 50;
                        ctx.fillStyle = '#667eea';
                        ctx.beginPath();
                        ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                        ctx.fill();
                        
                        particle.x += particle.vx;
                        particle.y += particle.vy;
                        particle.life--;
                        ctx.globalAlpha = 1;
                    });
                    
                    if (particles.length > 0) {
                        requestAnimationFrame(animateParticles);
                    }
                }
                
                animateParticles();
            }, 1500);
        }

        // 视图特性游戏
        let propertyScore = 0;
        let currentProperty = 0;

        function startPropertyGame() {
            propertyScore = 0;
            document.getElementById('propertyScore').textContent = propertyScore;
            showProperty(1);
        }

        function showProperty(propertyNum) {
            const canvas = document.getElementById('propertiesCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            currentProperty = propertyNum;

            switch(propertyNum) {
                case 1:
                    animatePropertyOne(ctx);
                    break;
                case 2:
                    animatePropertyTwo(ctx);
                    break;
                case 3:
                    animatePropertyThree(ctx);
                    break;
            }

            propertyScore += 10;
            document.getElementById('propertyScore').textContent = propertyScore;
        }

        function animatePropertyOne(ctx) {
            // 特性1：建立删除不影响基本表
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('特性1：视图的建立和删除不影响基本表', 400, 50);

            // 绘制基本表
            drawStableTable(ctx, 150, 150, '基本表', '#4caf50');

            // 绘制视图创建过程
            setTimeout(() => {
                drawViewCreation(ctx, 450, 150);
            }, 500);

            setTimeout(() => {
                drawViewDeletion(ctx, 450, 150);
            }, 2000);

            setTimeout(() => {
                // 基本表依然存在
                ctx.strokeStyle = '#4caf50';
                ctx.lineWidth = 5;
                ctx.strokeRect(145, 145, 160, 120);
                ctx.fillStyle = '#4caf50';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('基本表完好无损！', 225, 300);
            }, 3500);
        }

        function animatePropertyTwo(ctx) {
            // 特性2：更新直接影响基本表
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('特性2：对视图内容的更新直接影响基本表', 400, 50);

            // 绘制基本表和视图
            drawLinkedTables(ctx, 150, 150, 450, 150);

            setTimeout(() => {
                // 在视图中更新数据
                updateViewData(ctx, 450, 150);
            }, 1000);

            setTimeout(() => {
                // 显示基本表也被更新
                updateBaseTableData(ctx, 150, 150);
                drawSyncArrow(ctx, 350, 200, 250, 200);
            }, 2000);
        }

        function animatePropertyThree(ctx) {
            // 特性3：多表视图限制
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('特性3：多个基本表的视图不允许添加和删除数据', 400, 50);

            // 绘制多个基本表
            drawTable(ctx, 100, 150, '表1', ['ID', '姓名'], [['1', '张三']], '#e3f2fd');
            drawTable(ctx, 300, 150, '表2', ['ID', '分数'], [['1', '95']], '#f3e5f5');

            // 绘制多表视图
            setTimeout(() => {
                drawArrow(ctx, 200, 200, 450, 200, '#667eea');
                drawArrow(ctx, 400, 200, 450, 200, '#764ba2');

                setTimeout(() => {
                    drawTable(ctx, 450, 150, '多表视图', ['姓名', '分数'], [['张三', '95']], '#fff3e0');

                    // 显示限制标志
                    setTimeout(() => {
                        ctx.strokeStyle = '#f44336';
                        ctx.lineWidth = 4;
                        ctx.beginPath();
                        ctx.moveTo(440, 140);
                        ctx.lineTo(580, 280);
                        ctx.moveTo(580, 140);
                        ctx.lineTo(440, 280);
                        ctx.stroke();

                        ctx.fillStyle = '#f44336';
                        ctx.font = '16px Microsoft YaHei';
                        ctx.fillText('禁止添加/删除', 510, 320);
                    }, 1000);
                }, 1000);
            }, 500);
        }

        // 操作游戏相关函数
        let operationScore = 0;
        let draggedOperation = null;
        let operations = [
            {name: '创建视图', x: 100, y: 400, color: '#4caf50', target: {x: 200, y: 150}},
            {name: '查看视图', x: 250, y: 400, color: '#2196f3', target: {x: 350, y: 150}},
            {name: '修改视图', x: 400, y: 400, color: '#ff9800', target: {x: 500, y: 150}},
            {name: '删除视图', x: 550, y: 400, color: '#f44336', target: {x: 650, y: 150}}
        ];

        function startOperationGame() {
            const canvas = document.getElementById('operationsCanvas');
            const ctx = canvas.getContext('2d');

            setupOperationCanvas(canvas);
            drawOperationGame(ctx);
        }

        function setupOperationCanvas(canvas) {
            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);
        }

        function drawOperationGame(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制标题
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('拖拽操作到正确位置完成视图生命周期', 400, 30);

            // 绘制生命周期阶段
            const stages = ['创建', '查看', '修改', '删除'];
            stages.forEach((stage, i) => {
                const x = 200 + i * 150;
                const y = 150;

                ctx.fillStyle = '#f5f5f5';
                ctx.fillRect(x - 50, y - 30, 100, 60);
                ctx.strokeStyle = '#ddd';
                ctx.strokeRect(x - 50, y - 30, 100, 60);

                ctx.fillStyle = '#666';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(stage, x, y + 5);
            });

            // 绘制操作按钮
            operations.forEach(op => {
                drawOperationButton(ctx, op);
            });
        }

        function drawOperationButton(ctx, operation) {
            ctx.fillStyle = operation.color;
            ctx.fillRect(operation.x - 40, operation.y - 20, 80, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(operation.name, operation.x, operation.y + 5);
        }

        // 鼠标事件处理
        function handleMouseDown(e) {
            const rect = e.target.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            operations.forEach(op => {
                if (x >= op.x - 40 && x <= op.x + 40 && y >= op.y - 20 && y <= op.y + 20) {
                    draggedOperation = op;
                }
            });
        }

        function handleMouseMove(e) {
            if (draggedOperation) {
                const rect = e.target.getBoundingClientRect();
                draggedOperation.x = e.clientX - rect.left;
                draggedOperation.y = e.clientY - rect.top;

                const ctx = e.target.getContext('2d');
                drawOperationGame(ctx);
            }
        }

        function handleMouseUp(e) {
            if (draggedOperation) {
                const target = draggedOperation.target;
                const distance = Math.sqrt(
                    Math.pow(draggedOperation.x - target.x, 2) +
                    Math.pow(draggedOperation.y - target.y, 2)
                );

                if (distance < 50) {
                    operationScore += 25;
                    document.getElementById('operationScore').textContent = operationScore;

                    // 成功放置动画
                    const ctx = e.target.getContext('2d');
                    ctx.fillStyle = '#4caf50';
                    ctx.beginPath();
                    ctx.arc(target.x, target.y, 30, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓', target.x, target.y + 7);
                }

                draggedOperation = null;
            }
        }

        function resetOperationGame() {
            operationScore = 0;
            document.getElementById('operationScore').textContent = operationScore;

            // 重置操作位置
            operations = [
                {name: '创建视图', x: 100, y: 400, color: '#4caf50', target: {x: 200, y: 150}},
                {name: '查看视图', x: 250, y: 400, color: '#2196f3', target: {x: 350, y: 150}},
                {name: '修改视图', x: 400, y: 400, color: '#ff9800', target: {x: 500, y: 150}},
                {name: '删除视图', x: 550, y: 400, color: '#f44336', target: {x: 650, y: 150}}
            ];

            const canvas = document.getElementById('operationsCanvas');
            const ctx = canvas.getContext('2d');
            drawOperationGame(ctx);
        }

        // 辅助绘制函数
        function drawStableTable(ctx, x, y, title, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 160, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + 80, y + 65);
        }

        function drawViewCreation(ctx, x, y) {
            // 创建视图动画
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#2196f3';
            ctx.fillRect(x, y, 160, 120);
            ctx.globalAlpha = 1;

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('视图创建', x + 80, y + 65);
        }

        function drawViewDeletion(ctx, x, y) {
            // 删除视图动画
            ctx.clearRect(x, y, 160, 120);
            ctx.strokeStyle = '#f44336';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(x, y, 160, 120);
            ctx.setLineDash([]);

            ctx.fillStyle = '#f44336';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('视图已删除', x + 80, y + 65);
        }

        function drawLinkedTables(ctx, x1, y1, x2, y2) {
            // 绘制基本表
            drawStableTable(ctx, x1, y1, '基本表', '#4caf50');

            // 绘制视图
            ctx.fillStyle = '#2196f3';
            ctx.fillRect(x2, y2, 160, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('视图', x2 + 80, y2 + 65);

            // 绘制连接线
            drawArrow(ctx, x1 + 160, y1 + 60, x2, y2 + 60, '#666');
        }

        function updateViewData(ctx, x, y) {
            // 在视图上显示更新
            ctx.fillStyle = '#ff9800';
            ctx.fillRect(x + 10, y + 10, 140, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('数据更新', x + 80, y + 65);
        }

        function updateBaseTableData(ctx, x, y) {
            // 在基本表上显示更新
            ctx.fillStyle = '#ff9800';
            ctx.fillRect(x + 10, y + 10, 140, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('同步更新', x + 80, y + 65);
        }

        function drawSyncArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#ff9800';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 双向箭头
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.fillStyle = '#ff9800';
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }

        // 庆祝完成函数
        function celebrateCompletion() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制庆祝动画
            let fireworks = [];
            for (let i = 0; i < 20; i++) {
                fireworks.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    life: 60
                });
            }

            function animateFireworks() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制恭喜文字
                ctx.font = 'bold 36px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 恭喜完成学习！ 🎉', canvas.width/2, canvas.height/2);

                fireworks.forEach((fw, index) => {
                    if (fw.life <= 0) {
                        fireworks.splice(index, 1);
                        return;
                    }

                    ctx.globalAlpha = fw.life / 60;
                    ctx.fillStyle = fw.color;
                    ctx.beginPath();
                    ctx.arc(fw.x, fw.y, 5, 0, Math.PI * 2);
                    ctx.fill();

                    fw.x += fw.vx;
                    fw.y += fw.vy;
                    fw.life--;
                    ctx.globalAlpha = 1;
                });

                if (fireworks.length > 0) {
                    requestAnimationFrame(animateFireworks);
                }
            }

            animateFireworks();
        }

        // 初始化
        window.onload = function() {
            // 自动播放第一个动画
            setTimeout(animateViewConcept, 1000);

            // 初始化总结画布
            setTimeout(() => {
                const canvas = document.getElementById('summaryCanvas');
                const ctx = canvas.getContext('2d');

                // 绘制学习路径图
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('🎓 数据库视图学习完成路径', canvas.width/2, 50);

                // 绘制学习步骤
                const steps = [
                    {text: '理解视图概念', x: 150, y: 150, color: '#4caf50'},
                    {text: '区分实表虚表', x: 350, y: 150, color: '#2196f3'},
                    {text: '掌握视图特性', x: 550, y: 150, color: '#ff9800'},
                    {text: '学会视图操作', x: 650, y: 150, color: '#f44336'}
                ];

                steps.forEach((step, i) => {
                    setTimeout(() => {
                        ctx.fillStyle = step.color;
                        ctx.beginPath();
                        ctx.arc(step.x, step.y, 30, 0, Math.PI * 2);
                        ctx.fill();

                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Microsoft YaHei';
                        ctx.fillText((i + 1).toString(), step.x, step.y + 5);

                        ctx.fillStyle = '#333';
                        ctx.font = '12px Microsoft YaHei';
                        ctx.fillText(step.text, step.x, step.y + 50);

                        if (i < steps.length - 1) {
                            drawArrow(ctx, step.x + 30, step.y, steps[i + 1].x - 30, steps[i + 1].y, '#ddd');
                        }
                    }, i * 500);
                });
            }, 3000);
        };
    </script>
</body>
</html>
