<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 核心特性趣味学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f7f9;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 40px;
        }
        p, li {
            font-size: 1.1em;
            color: #555;
        }
        .concept-box {
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #3498db;
            background-color: #ecf5fb;
            border-radius: 5px;
        }
        canvas {
            background-color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
            display: block;
            width: 100%;
            box-sizing: border-box;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
            margin-bottom: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .output-box {
            margin-top: 15px;
            padding: 15px;
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 5px;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            min-height: 30px;
        }
        .flex-container {
            display: flex;
            gap: 20px;
        }
        .flex-child {
            flex: 1;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Java 核心特性趣味学习</h1>

        <!-- 10. 健壮性 -->
        <section id="robustness">
            <h2>10. 健壮性 (Robustness)</h2>
            <div class="concept-box">
                <p><strong>核心思想：</strong>程序得"皮实"，不容易崩溃。Java 强调对可能发生的错误进行预先检查和处理。</p>
                <p><strong>主要工具：</strong>通过 <code>try-catch-finally</code> 机制来捕获和管理"异常"（也就是程序运行时发生的错误），确保即使某部分代码出错，整个程序也能优雅地处理，而不是直接崩溃。</p>
            </div>
            <p><strong>交互演示：</strong>下面是模拟除法运算的 Java 代码。请点击按钮，观察程序在不同情况下的执行流程。</p>
            <canvas id="tryCatchCanvas" width="900" height="250"></canvas>
            <div class="controls">
                <button id="runTryCatchFail">模拟失败场景 (10 / 0)</button>
                <button id="runTryCatchSuccess">模拟成功场景 (10 / 2)</button>
            </div>
            <strong>控制台输出:</strong>
            <div id="tryCatchOutput" class="output-box"></div>
        </section>

        <!-- 11. 静态类型语言 -->
        <section id="static-typing">
            <h2>11. 静态类型语言 (Statically Typed Language)</h2>
            <div class="concept-box">
                <p><strong>核心思想：</strong>在代码运行之前，就要明确每个"盒子"（变量）里要装什么类型的东西（比如数字、文字）。</p>
                <ul>
                    <li><strong>编译时确定变量类型：</strong>一旦你告诉 Java 一个变量是用来存数字的，它就不能再存文字。这个规则在代码运行前（编译时）就会被严格检查。</li>
                    <li><strong>优点：</strong>提供了更好的代码可靠性和 IDE（编程软件）支持。IDE 能提前发现很多潜在错误，并给你智能提示。</li>
                    <li><strong>对比：</strong>与 Python 这样的"动态类型语言"形成对比，后者允许你在程序运行时改变变量的类型。</li>
                </ul>
            </div>
            <p><strong>交互演示：</strong>点击按钮，观看 Java 和 Python 在处理变量类型时的不同表现。</p>
            <canvas id="staticTypingCanvas" width="900" height="300"></canvas>
            <div class="controls">
                 <button id="runStaticTypingDemo">开始演示</button>
            </div>
        </section>

        <!-- 12. 开源与社区支持 -->
        <section id="open-source">
            <h2>12. 开源与社区支持 (Open Source & Community)</h2>
            <div class="concept-box">
                 <ul>
                    <li><strong>采用开源许可 (GPL/LGPL)：</strong>意味着任何人都可以免费使用、修改和分发 Java，这极大地促进了它的普及。</li>
                    <li><strong>庞大的开发者社区和丰富的学习资源：</strong>遇到问题时，你几乎总能找到解决方案或获得帮助。有无数的教程、框架和工具可供使用。</li>
                    <li><strong>持续更新和演进：</strong>Java 社区非常活跃，会定期发布新版本（如 Java 17/21 等 LTS 长期支持版本），不断引入新功能，保持语言的现代性。</li>
                </ul>
                <p><strong>总结：</strong>这些特点共同使得 Java 成为企业级应用开发、Android 应用开发、大数据处理等领域的首选语言之一。</p>
            </div>
        </section>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {

    // --- 健壮性: Try-Catch 动画 ---
    const tryCatchCanvas = document.getElementById('tryCatchCanvas');
    const tcFgCtx = tryCatchCanvas.getContext('2d');
    const tryCatchOutput = document.getElementById('tryCatchOutput');
    const failBtn = document.getElementById('runTryCatchFail');
    const successBtn = document.getElementById('runTryCatchSuccess');

    const tcCode = [
        "1  try {",
        "2      int result = 10 / ?;",
        "3  } catch (ArithmeticException e) {",
        "4      System.out.println(\"除数不能为零\");",
        "5  }"
    ];
    let tcCurrentLine = -1;
    let tcAnimationId;

    function drawTryCatch(denominator) {
        tcFgCtx.clearRect(0, 0, tryCatchCanvas.width, tryCatchCanvas.height);
        tcFgCtx.font = "18px 'Courier New', Courier, monospace";
        tcFgCtx.fillStyle = '#333';
        
        const codeToDraw = [...tcCode];
        codeToDraw[1] = `2      int result = 10 / ${denominator};`;

        codeToDraw.forEach((line, index) => {
            tcFgCtx.fillText(line, 20, 40 + index * 30);
        });

        if (tcCurrentLine !== -1) {
            tcFgCtx.fillStyle = 'rgba(52, 152, 219, 0.7)';
            tcFgCtx.beginPath();
            tcFgCtx.moveTo(0, 20 + tcCurrentLine * 30);
            tcFgCtx.lineTo(15, 35 + tcCurrentLine * 30);
            tcFgCtx.lineTo(0, 50 + tcCurrentLine * 30);
            tcFgCtx.closePath();
            tcFgCtx.fill();
        }
    }
    
    function animateTryCatch(scenario) {
        cancelAnimationFrame(tcAnimationId);
        tcCurrentLine = -1;
        tryCatchOutput.textContent = '';
        failBtn.disabled = true;
        successBtn.disabled = true;

        const denominator = scenario === 'fail' ? 0 : 2;
        const steps = scenario === 'fail' 
            ? [0, 1, 'error', 2, 3, 4, 'end'] 
            : [0, 1, 2, 'skip', 4, 'end'];

        let stepIndex = 0;

        function step() {
            const currentStep = steps[stepIndex];

            if (currentStep === 'end') {
                tcCurrentLine = -1;
                drawTryCatch(denominator);
                failBtn.disabled = false;
                successBtn.disabled = false;
                return;
            }
            if (currentStep === 'skip') {
                // just move to next step
            } else if (currentStep === 'error') {
                drawTryCatch(denominator);
                // Draw explosion/error icon
                tcFgCtx.font = "40px Arial";
                tcFgCtx.fillStyle = "red";
                tcFgCtx.fillText("💥", 400, 75);
                tcFgCtx.font = "20px Arial";
                tcFgCtx.fillText("错误! 发生算术异常!", 450, 80);
            } else {
                 tcCurrentLine = currentStep;
                 drawTryCatch(denominator);
            }

            // Handle output
            if (currentStep === 3) {
                setTimeout(() => { 
                    tryCatchOutput.textContent = '除数不能为零';
                }, 500);
            }
            if (scenario === 'success' && currentStep === 1) {
                setTimeout(() => {
                     tryCatchOutput.textContent = '计算成功, result = 5';
                }, 500);
            }
            
            stepIndex++;
            tcAnimationId = setTimeout(step, 1200);
        }
        step();
    }
    
    failBtn.addEventListener('click', () => animateTryCatch('fail'));
    successBtn.addEventListener('click', () => animateTryCatch('success'));
    drawTryCatch('?');


    // --- 静态类型语言动画 ---
    const staticTypingCanvas = document.getElementById('staticTypingCanvas');
    const stCtx = staticTypingCanvas.getContext('2d');
    const staticBtn = document.getElementById('runStaticTypingDemo');
    let stAnimationId;

    const javaCode = [
        '// Java (静态类型)',
        'int number = 100;',
        'number = "hello";'
    ];
    const pythonCode = [
        '# Python (动态类型)',
        'variable = 100',
        'variable = "hello"'
    ];

    function drawStaticTypingBase() {
        stCtx.clearRect(0, 0, staticTypingCanvas.width, staticTypingCanvas.height);
        stCtx.font = "bold 20px sans-serif";
        stCtx.fillStyle = "#2c3e50";
        stCtx.fillText("Java (静态类型)", 50, 40);
        stCtx.fillText("Python (动态类型)", 500, 40);

        stCtx.font = "18px 'Courier New', Courier, monospace";
        stCtx.fillStyle = "#333";
        javaCode.forEach((line, i) => stCtx.fillText(line.replace('// Java (静态类型)',''), 50, 80 + i * 30));
        pythonCode.forEach((line, i) => stCtx.fillText(line.replace('# Python (动态类型)',''), 500, 80 + i * 30));
    }

    function animateStaticTyping() {
        cancelAnimationFrame(stAnimationId);
        staticBtn.disabled = true;
        drawStaticTypingBase();
        
        let phase = 0;
        const totalPhases = 7;

        function step() {
            drawStaticTypingBase();
            stCtx.font = "bold 18px sans-serif";

            // Phase 0: Compiler checking Java
            if (phase >= 0) {
                 stCtx.fillStyle = "orange";
                 stCtx.fillText("⚙️ 编译器开始检查...", 50, 200);
            }
            // Phase 1: Java Line 1 OK
            if (phase >= 1) {
                stCtx.fillStyle = "green";
                stCtx.fillText("✅ int number = 100; (类型正确)", 50, 230);
            }
            // Phase 2: Java Line 2 ERROR
            if (phase >= 2) {
                stCtx.fillStyle = "red";
                stCtx.fillText('❌ number = "hello";', 50, 140);
                stCtx.fillText("🛑 错误! 不能把文字赋值给数字变量!", 50, 260);
                stCtx.fillText("程序无法运行!", 50, 290);
            }
            // Phase 3: Interpreter checking Python
            if (phase >= 3) {
                 stCtx.fillStyle = "orange";
                 stCtx.fillText("▶️ 解释器开始运行...", 500, 200);
            }
            // Phase 4: Python Line 1 OK
            if (phase >= 4) {
                 stCtx.fillStyle = "green";
                 stCtx.fillText('✅ variable = 100 (现在是数字)', 500, 230);
            }
            // Phase 5: Python Line 2 OK
            if (phase >= 5) {
                 stCtx.fillStyle = "green";
                 stCtx.fillText('✅ variable = "hello" (现在是文字)', 500, 260);
            }
            // Phase 6: Python OK
            if (phase >= 6) {
                 stCtx.fillStyle = "#3498db";
                 stCtx.fillText("程序成功运行!", 500, 290);
            }
            
            phase++;
            if (phase <= totalPhases) {
                 stAnimationId = setTimeout(step, 1500);
            } else {
                 staticBtn.disabled = false;
            }
        }
        step();
    }

    staticBtn.addEventListener('click', animateStaticTyping);
    drawStaticTypingBase();
});
</script>

</body>
</html> 