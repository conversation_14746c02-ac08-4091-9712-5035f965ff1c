/**
 * 应用程序监控服务
 * 用于监控当前活动的应用程序和网站
 */

class ApplicationMonitorService {
  constructor() {
    this.isMonitoring = false;
    this.currentApp = null;
    this.currentWebsite = null;
    this.monitorInterval = null;
    this.callbacks = {
      onAppChange: [],
      onWebsiteChange: []
    };
  }

  /**
   * 开始监控应用程序
   */
  startMonitoring() {
    if (this.isMonitoring) {
      console.warn('应用监控已经在运行中');
      return;
    }

    this.isMonitoring = true;
    console.log('开始应用程序监控');

    // 检测是否在Electron环境中
    if (this.isElectronEnvironment()) {
      this.startElectronMonitoring();
    } else {
      this.startWebMonitoring();
    }

    // 定期检查应用状态
    this.monitorInterval = setInterval(() => {
      this.checkCurrentApplication();
    }, 2000); // 每2秒检查一次
  }

  /**
   * 停止监控应用程序
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    console.log('停止应用程序监控');

    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    if (this.isElectronEnvironment()) {
      this.stopElectronMonitoring();
    }
  }

  /**
   * 检测是否在Electron环境中
   */
  isElectronEnvironment() {
    return typeof window !== 'undefined' && window.require;
  }

  /**
   * 开始Electron环境下的监控
   */
  startElectronMonitoring() {
    try {
      const { ipcRenderer } = window.require('electron');

      // 请求开始应用监控
      ipcRenderer.send('start-app-monitoring');

      // 监听应用变化事件
      ipcRenderer.on('app-changed', (event, appInfo) => {
        console.log('收到应用变化事件:', appInfo);
        this.handleAppChange(appInfo);
      });

      // 监听网站变化事件
      ipcRenderer.on('website-changed', (event, websiteInfo) => {
        console.log('收到网站变化事件:', websiteInfo);
        this.handleWebsiteChange(websiteInfo);
      });

      // 监听当前应用信息回复
      ipcRenderer.on('current-app-info', (event, appInfo) => {
        console.log('收到当前应用信息:', appInfo);
        this.handleAppChange(appInfo);
      });

      // 监听当前网站信息回复
      ipcRenderer.on('current-website-info', (event, websiteInfo) => {
        console.log('收到当前网站信息:', websiteInfo);
        this.handleWebsiteChange(websiteInfo);
      });

      console.log('Electron应用监控已启动');
    } catch (error) {
      console.error('启动Electron应用监控失败:', error);
      // 降级到Web监控
      this.startWebMonitoring();
    }
  }

  /**
   * 停止Electron环境下的监控
   */
  stopElectronMonitoring() {
    try {
      const { ipcRenderer } = window.require('electron');

      // 请求停止应用监控
      ipcRenderer.send('stop-app-monitoring');

      // 移除事件监听器
      ipcRenderer.removeAllListeners('app-changed');
      ipcRenderer.removeAllListeners('website-changed');
      ipcRenderer.removeAllListeners('current-app-info');
      ipcRenderer.removeAllListeners('current-website-info');

      console.log('Electron应用监控已停止');
    } catch (error) {
      console.error('停止Electron应用监控失败:', error);
    }
  }

  /**
   * 开始Web环境下的监控（模拟数据）
   */
  startWebMonitoring() {
    console.log('Web环境下使用模拟应用监控数据');
    
    // 监控浏览器标签页变化
    this.monitorBrowserTabs();
    
    // 监控页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // 监控窗口焦点变化
    window.addEventListener('focus', this.handleWindowFocus.bind(this));
    window.addEventListener('blur', this.handleWindowBlur.bind(this));
  }

  /**
   * 监控浏览器标签页
   */
  monitorBrowserTabs() {
    // 获取当前页面信息
    const currentWebsite = {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      timestamp: new Date().toISOString()
    };
    
    this.handleWebsiteChange(currentWebsite);
    
    // 监控URL变化（SPA路由变化）
    let lastUrl = window.location.href;
    const checkUrlChange = () => {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrl) {
        lastUrl = currentUrl;
        const websiteInfo = {
          url: currentUrl,
          title: document.title,
          domain: window.location.hostname,
          timestamp: new Date().toISOString()
        };
        this.handleWebsiteChange(websiteInfo);
      }
    };
    
    // 监听路由变化
    window.addEventListener('popstate', checkUrlChange);
    
    // 定期检查URL变化（处理编程式导航）
    setInterval(checkUrlChange, 1000);
  }

  /**
   * 检查当前应用程序状态
   */
  checkCurrentApplication() {
    if (this.isElectronEnvironment()) {
      try {
        const { ipcRenderer } = window.require('electron');
        // 请求当前应用和网站信息
        ipcRenderer.send('get-current-app');
        ipcRenderer.send('get-current-website');
      } catch (error) {
        console.warn('获取当前应用信息失败:', error);
      }
    } else {
      // Web环境下模拟应用检测
      this.simulateApplicationDetection();
    }
  }

  /**
   * 模拟应用程序检测（Web环境）
   */
  simulateApplicationDetection() {
    const currentApp = {
      name: this.detectBrowserName(),
      title: document.title,
      pid: 'web-process',
      path: window.location.href,
      timestamp: new Date().toISOString()
    };

    // 只有当应用信息发生变化时才触发回调
    if (!this.currentApp || this.currentApp.name !== currentApp.name || this.currentApp.title !== currentApp.title) {
      this.handleAppChange(currentApp);
    }
  }

  /**
   * 检测浏览器名称
   */
  detectBrowserName() {
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('Edg')) {
      return 'Edge';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      return 'Safari';
    } else {
      return '未知浏览器';
    }
  }

  /**
   * 处理应用程序变化
   */
  handleAppChange(appInfo) {
    this.currentApp = appInfo;
    console.log('应用程序变化:', appInfo);
    
    // 触发所有注册的回调
    this.callbacks.onAppChange.forEach(callback => {
      try {
        callback(appInfo);
      } catch (error) {
        console.error('应用变化回调执行失败:', error);
      }
    });
  }

  /**
   * 处理网站变化
   */
  handleWebsiteChange(websiteInfo) {
    this.currentWebsite = websiteInfo;
    console.log('网站变化:', websiteInfo);
    
    // 触发所有注册的回调
    this.callbacks.onWebsiteChange.forEach(callback => {
      try {
        callback(websiteInfo);
      } catch (error) {
        console.error('网站变化回调执行失败:', error);
      }
    });
  }

  /**
   * 处理页面可见性变化
   */
  handleVisibilityChange() {
    if (document.hidden) {
      console.log('页面变为不可见');
    } else {
      console.log('页面变为可见');
      // 页面重新可见时，重新检测当前状态
      this.checkCurrentApplication();
    }
  }

  /**
   * 处理窗口获得焦点
   */
  handleWindowFocus() {
    console.log('窗口获得焦点');
    this.checkCurrentApplication();
  }

  /**
   * 处理窗口失去焦点
   */
  handleWindowBlur() {
    console.log('窗口失去焦点');
  }

  /**
   * 注册应用变化回调
   */
  onAppChange(callback) {
    if (typeof callback === 'function') {
      this.callbacks.onAppChange.push(callback);
    }
  }

  /**
   * 注册网站变化回调
   */
  onWebsiteChange(callback) {
    if (typeof callback === 'function') {
      this.callbacks.onWebsiteChange.push(callback);
    }
  }

  /**
   * 移除回调
   */
  removeCallback(type, callback) {
    if (this.callbacks[type]) {
      const index = this.callbacks[type].indexOf(callback);
      if (index > -1) {
        this.callbacks[type].splice(index, 1);
      }
    }
  }

  /**
   * 获取当前应用信息
   */
  getCurrentApp() {
    return this.currentApp;
  }

  /**
   * 获取当前网站信息
   */
  getCurrentWebsite() {
    return this.currentWebsite;
  }
}

// 创建单例实例
const applicationMonitor = new ApplicationMonitorService();

export default applicationMonitor;
