<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COM对象重用 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 0.8s ease-out;
        }

        .concept-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .concept-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #animationCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>COM对象重用机制</h1>
            <p>通过动画和交互学习包含(Containment)和聚集(Aggregation)的区别</p>
        </div>

        <div class="learning-section">
            <h2 class="concept-title">什么是COM对象重用？</h2>
            
            <div class="explanation">
                <h3>🎯 学习目标</h3>
                <p>COM（Component Object Model）不支持实现继承，但支持两种对象重用形式：</p>
                <p><strong>包含（Containment）</strong>：外部对象转发请求给内部对象</p>
                <p><strong>聚集（Aggregation）</strong>：直接传递内部对象的接口引用给客户</p>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="showContainment()">演示包含模式</button>
                <button class="btn btn-secondary" onclick="showAggregation()">演示聚集模式</button>
                <button class="btn btn-primary" onclick="resetAnimation()">重置动画</button>
            </div>

            <div class="explanation" id="currentExplanation">
                <h3>点击按钮开始学习！</h3>
                <p>选择上面的按钮来查看不同的对象重用模式演示。</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px;">🎮 互动测试</h2>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="quiz-question" id="quizQuestion">
                COM支持两种形式的外部对象的（ ）重用形式下，一个外部对象拥有指向一个内部对象的唯一引用，外部对象只是把请求转发给内部对象：在（ ）重用形式下，直接把内部对象的接口引用传给外部对象的客户，而不再转发请求。
            </div>

            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 聚集、包含</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 包含、聚集</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 链接、多态</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 多态、链接</div>
            </div>

            <div id="quizResult" style="text-align: center; margin-top: 20px; font-size: 1.2rem;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationState = 'idle';
        let animationFrame = 0;

        // 绘制基础元素
        function drawClient(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#4CAF50';
                ctx.shadowBlur = 20;
            }
            ctx.fillStyle = highlight ? '#4CAF50' : '#2196F3';
            ctx.fillRect(x, y, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('客户端', x + 40, y + 35);
            ctx.restore();
        }

        function drawOuterObject(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#FF9800';
                ctx.shadowBlur = 20;
            }
            ctx.fillStyle = highlight ? '#FF9800' : '#9C27B0';
            ctx.fillRect(x, y, 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('外部对象', x + 50, y + 45);
            ctx.restore();
        }

        function drawInnerObject(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#E91E63';
                ctx.shadowBlur = 20;
            }
            ctx.fillStyle = highlight ? '#E91E63' : '#F44336';
            ctx.fillRect(x, y, 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('内部对象', x + 50, y + 45);
            ctx.restore();
        }

        function drawArrow(fromX, fromY, toX, toY, color = '#333', animated = false) {
            ctx.save();
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            if (animated) {
                ctx.setLineDash([10, 5]);
                ctx.lineDashOffset = -animationFrame * 2;
            }
            
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            ctx.restore();
        }

        function drawLabel(x, y, text, color = '#333') {
            ctx.save();
            ctx.fillStyle = color;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
            ctx.restore();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function showContainment() {
            animationState = 'containment';
            animationFrame = 0;
            document.getElementById('currentExplanation').innerHTML = `
                <h3>🔄 包含模式 (Containment)</h3>
                <p><strong>特点：</strong>外部对象拥有指向内部对象的唯一引用</p>
                <p><strong>工作方式：</strong>客户端请求 → 外部对象 → 转发给内部对象</p>
                <p><strong>优点：</strong>完全透明，客户端不知道内部实现</p>
                <p><strong>缺点：</strong>多层转发可能影响性能</p>
            `;
            animateContainment();
        }

        function showAggregation() {
            animationState = 'aggregation';
            animationFrame = 0;
            document.getElementById('currentExplanation').innerHTML = `
                <h3>🔗 聚集模式 (Aggregation)</h3>
                <p><strong>特点：</strong>直接传递内部对象的接口引用</p>
                <p><strong>工作方式：</strong>客户端直接访问内部对象接口</p>
                <p><strong>优点：</strong>性能更好，无需转发</p>
                <p><strong>缺点：</strong>需要保持透明性，客户端不应感知聚集</p>
            `;
            animateAggregation();
        }

        function animateContainment() {
            if (animationState !== 'containment') return;
            
            clearCanvas();
            
            // 绘制标题
            drawLabel(400, 30, '包含模式 (Containment)', '#9C27B0');
            
            // 绘制对象
            const clientHighlight = animationFrame % 120 < 30;
            const outerHighlight = animationFrame % 120 >= 30 && animationFrame % 120 < 60;
            const innerHighlight = animationFrame % 120 >= 60 && animationFrame % 120 < 90;
            
            drawClient(50, 200, clientHighlight);
            drawOuterObject(300, 180, outerHighlight);
            drawInnerObject(600, 180, innerHighlight);
            
            // 绘制箭头
            if (animationFrame % 120 >= 15) {
                drawArrow(130, 230, 300, 220, '#4CAF50', true);
                drawLabel(215, 215, '请求', '#4CAF50');
            }
            
            if (animationFrame % 120 >= 45) {
                drawArrow(400, 220, 600, 220, '#FF9800', true);
                drawLabel(500, 215, '转发', '#FF9800');
            }
            
            if (animationFrame % 120 >= 75) {
                drawArrow(600, 240, 400, 240, '#E91E63', true);
                drawLabel(500, 255, '响应', '#E91E63');
            }
            
            if (animationFrame % 120 >= 90) {
                drawArrow(300, 240, 130, 240, '#2196F3', true);
                drawLabel(215, 255, '返回', '#2196F3');
            }
            
            animationFrame++;
            requestAnimationFrame(animateContainment);
        }

        function animateAggregation() {
            if (animationState !== 'aggregation') return;
            
            clearCanvas();
            
            // 绘制标题
            drawLabel(400, 30, '聚集模式 (Aggregation)', '#E91E63');
            
            // 绘制对象
            const clientHighlight = animationFrame % 100 < 25;
            const innerHighlight = animationFrame % 100 >= 25 && animationFrame % 100 < 75;
            
            drawClient(50, 200, clientHighlight);
            drawOuterObject(300, 120);
            drawInnerObject(600, 200, innerHighlight);
            
            // 绘制直接连接
            if (animationFrame % 100 >= 15) {
                drawArrow(130, 230, 600, 230, '#4CAF50', true);
                drawLabel(365, 215, '直接访问接口', '#4CAF50');
            }
            
            if (animationFrame % 100 >= 50) {
                drawArrow(600, 250, 130, 250, '#E91E63', true);
                drawLabel(365, 265, '直接响应', '#E91E63');
            }
            
            // 绘制聚集关系
            drawArrow(350, 160, 650, 200, '#9C27B0', false);
            drawLabel(500, 175, '聚集关系', '#9C27B0');
            
            animationFrame++;
            requestAnimationFrame(animateAggregation);
        }

        function resetAnimation() {
            animationState = 'idle';
            clearCanvas();
            drawLabel(400, 250, '点击按钮开始演示', '#666');
            document.getElementById('currentExplanation').innerHTML = `
                <h3>点击按钮开始学习！</h3>
                <p>选择上面的按钮来查看不同的对象重用模式演示。</p>
            `;
        }

        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 包含、聚集')) {
                    option.classList.add('correct');
                }
            });
            
            const resultDiv = document.getElementById('quizResult');
            const progressFill = document.getElementById('progressFill');
            
            if (isCorrect) {
                resultDiv.innerHTML = '🎉 恭喜答对了！包含模式转发请求，聚集模式直接传递接口引用。';
                resultDiv.style.color = '#4CAF50';
                progressFill.style.width = '100%';
            } else {
                resultDiv.innerHTML = '❌ 答案错误。正确答案是B：包含模式转发请求，聚集模式直接传递接口引用。';
                resultDiv.style.color = '#f44336';
                progressFill.style.width = '30%';
            }
            
            setTimeout(() => {
                options.forEach(option => {
                    option.style.pointerEvents = 'auto';
                    option.classList.remove('correct', 'wrong');
                });
                resultDiv.innerHTML = '';
                progressFill.style.width = '0%';
            }, 5000);
        }

        // 初始化
        resetAnimation();
    </script>
</body>
</html>
