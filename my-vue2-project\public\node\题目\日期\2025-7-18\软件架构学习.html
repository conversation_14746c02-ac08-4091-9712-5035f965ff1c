<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件系统架构 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-5px);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 15px;
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件系统架构</h1>
            <p class="subtitle">通过动画和交互学习软件架构的核心概念</p>
        </div>

        <!-- 概念解释部分 -->
        <div class="learning-section">
            <h2 class="section-title">什么是软件系统架构？</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <h3>🏗️ 软件系统架构的三大要素：</h3>
                <p><strong>结构（Structure）</strong>：系统的组织方式和组件布局</p>
                <p><strong>行为（Behavior）</strong>：组件之间如何交互和协作</p>
                <p><strong>属性（Properties）</strong>：系统的质量特征，如性能、可靠性等</p>
            </div>
            <button class="interactive-btn" onclick="startArchitectureAnimation()">🎬 播放架构动画</button>
        </div>

        <!-- 阶段演示部分 -->
        <div class="learning-section">
            <h2 class="section-title">架构的两个阶段</h2>
            <div class="canvas-container">
                <canvas id="phasesCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <h3>📋 描述阶段：</h3>
                <p>定义抽象组件和它们的<strong>交互关系</strong></p>
                <h3>🔧 实现阶段：</h3>
                <p>将抽象组件细化为具体的类和对象</p>
            </div>
            <button class="interactive-btn" onclick="startPhasesAnimation()">🎬 播放阶段动画</button>
        </div>

        <!-- 测验部分 -->
        <div class="learning-section quiz-section">
            <h2 class="section-title">知识测验</h2>
            <div class="quiz-question">
                软件系统架构是关于软件系统的结构、（____）和属性的高级抽象。
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">A. 行为</div>
                <div class="option" onclick="selectOption(this, false)">B. 组织</div>
                <div class="option" onclick="selectOption(this, false)">C. 性能</div>
                <div class="option" onclick="selectOption(this, false)">D. 功能</div>
            </div>
            <div class="result" id="quizResult" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 架构概念动画
        function startArchitectureAnimation() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#f8f9ff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制三个核心要素
                const elements = [
                    { name: '结构', x: 150, y: 200, color: '#667eea' },
                    { name: '行为', x: 400, y: 200, color: '#764ba2' },
                    { name: '属性', x: 650, y: 200, color: '#ff9a9e' }
                ];

                elements.forEach((element, index) => {
                    const scale = 1 + 0.1 * Math.sin(frame * 0.1 + index);
                    
                    ctx.save();
                    ctx.translate(element.x, element.y);
                    ctx.scale(scale, scale);
                    
                    // 绘制圆形
                    ctx.beginPath();
                    ctx.arc(0, 0, 60, 0, Math.PI * 2);
                    ctx.fillStyle = element.color;
                    ctx.fill();
                    
                    // 绘制文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(element.name, 0, 6);
                    
                    ctx.restore();
                });

                // 绘制连接线
                if (frame > 60) {
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(210, 200);
                    ctx.lineTo(340, 200);
                    ctx.moveTo(460, 200);
                    ctx.lineTo(590, 200);
                    ctx.stroke();
                }

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 阶段动画
        function startPhasesAnimation() {
            const canvas = document.getElementById('phasesCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#f8f9ff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 描述阶段
                if (frame > 30) {
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(50, 50, 300, 150);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('描述阶段', 200, 100);
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('抽象组件', 200, 130);
                    ctx.fillText('交互关系', 200, 155);
                }

                // 实现阶段
                if (frame > 90) {
                    ctx.fillStyle = '#764ba2';
                    ctx.fillRect(450, 50, 300, 150);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('实现阶段', 600, 100);
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('具体类', 600, 130);
                    ctx.fillText('对象实例', 600, 155);
                }

                // 箭头动画
                if (frame > 120) {
                    const arrowX = 350 + (frame - 120) * 2;
                    if (arrowX < 450) {
                        ctx.fillStyle = '#ff9a9e';
                        ctx.beginPath();
                        ctx.moveTo(arrowX, 125);
                        ctx.lineTo(arrowX + 20, 115);
                        ctx.lineTo(arrowX + 20, 135);
                        ctx.fill();
                    }
                }

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 测验功能
        let quizAnswered = false;

        function selectOption(element, isCorrect) {
            if (quizAnswered) return;
            
            const options = document.querySelectorAll('.option');
            const result = document.getElementById('quizResult');
            
            // 显示正确答案（A. 行为）
            options[0].classList.add('correct');
            
            if (element === options[0]) {
                result.innerHTML = '🎉 恭喜答对了！<br>软件系统架构确实是关于结构、<strong>行为</strong>和属性的高级抽象。';
                result.style.background = 'rgba(76, 175, 80, 0.8)';
            } else {
                element.classList.add('wrong');
                result.innerHTML = '❌ 答案不正确。<br>正确答案是 A. 行为。软件架构描述的是系统的结构、行为和属性。';
                result.style.background = 'rgba(244, 67, 54, 0.8)';
            }
            
            result.style.display = 'block';
            quizAnswered = true;
        }

        // 页面加载时启动动画
        window.onload = function() {
            setTimeout(startArchitectureAnimation, 500);
            setTimeout(startPhasesAnimation, 1000);
        };
    </script>
</body>
</html>
