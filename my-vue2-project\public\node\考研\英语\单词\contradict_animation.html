<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习单词: Contradict</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            color: #1a73e8;
            margin-bottom: 10px;
        }
        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 24px;
            font-size: 1em;
            cursor: pointer;
            border: none;
            border-radius: 8px;
            background-color: #1a73e8;
            color: white;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        button.disabled, button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .info-box {
            background-color: #e8f0fe;
            border-left: 5px solid #1a73e8;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>学习单词: Contradict (反驳)</h1>
    <p id="explanation">我将通过一个动画故事，帮你理解和记忆这个单词。</p>
    <canvas id="wordCanvas" width="800" height="400"></canvas>
    <div class="controls">
        <button id="startBtn">开始动画</button>
        <button id="resetBtn" style="display: none;">重新开始</button>
    </div>
    <div id="interactive-section" style="display: none;">
        <p class="info-box"><strong>轮到你来练习了！</strong><br>请判断下面的陈述，并做出你的选择：</p>
        <p><strong>陈述: "鱼可以在天上飞。"</strong></p>
        <button id="agreeBtn">同意</button>
        <button id="contradictBtn">反驳</button>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const explanation = document.getElementById('explanation');
    const startBtn = document.getElementById('startBtn');
    const resetBtn = document.getElementById('resetBtn');
    const interactiveSection = document.getElementById('interactive-section');
    const agreeBtn = document.getElementById('agreeBtn');
    const contradictBtn = document.getElementById('contradictBtn');

    let animationFrameId;
    let stage = 0; // 0: initial, 1: contra, 2: dict, 3: story, 4: interactive, 5: final
    
    function clearCanvas() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    function drawText(text, x, y, size = 20, color = '#333') {
        ctx.font = `bold ${size}px Arial`;
        ctx.fillStyle = color;
        ctx.textAlign = 'center';
        ctx.fillText(text, x, y);
    }

    function drawStickMan(x, y, color = 'black') {
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        // Head
        ctx.beginPath();
        ctx.arc(x, y - 40, 20, 0, Math.PI * 2);
        ctx.stroke();
        // Body
        ctx.beginPath();
        ctx.moveTo(x, y - 20);
        ctx.lineTo(x, y + 40);
        ctx.stroke();
        // Arms
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x - 30, y - 10);
        ctx.moveTo(x, y);
        ctx.lineTo(x + 30, y - 10);
        ctx.stroke();
        // Legs
        ctx.beginPath();
        ctx.moveTo(x, y + 40);
        ctx.lineTo(x - 20, y + 80);
        ctx.moveTo(x, y + 40);
        ctx.lineTo(x + 20, y + 80);
        ctx.stroke();
    }
    
    function drawSpeechBubble(x, y, width, height, text, direction = 'left') {
        ctx.fillStyle = '#f0f0f0';
        ctx.strokeStyle = '#999';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + width, y);
        ctx.lineTo(x + width, y - height);
        ctx.lineTo(x, y - height);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();

        ctx.beginPath();
        if (direction === 'left') {
            ctx.moveTo(x + 20, y);
            ctx.lineTo(x + 30, y + 20);
            ctx.lineTo(x + 40, y);
        } else {
            ctx.moveTo(x + width - 20, y);
            ctx.lineTo(x + width - 30, y + 20);
            ctx.lineTo(x + width - 40, y);
        }
        ctx.fill();
        ctx.stroke();

        drawText(text, x + width / 2, y - height / 2, 16);
    }

    function animateContra() {
        let arrow1X = 250;
        let arrow2X = 550;
        const animate = () => {
            clearCanvas();
            drawText("前缀 'contra-' = 相反 / 对抗", canvas.width / 2, 50, 24, '#1a73e8');
            
            // Draw arrows
            ctx.fillStyle = '#ff6347';
            ctx.beginPath(); // Arrow 1
            ctx.moveTo(arrow1X, 200); ctx.lineTo(arrow1X + 50, 180); ctx.lineTo(arrow1X + 50, 220); ctx.closePath();
            ctx.fill();
            ctx.fillRect(arrow1X + 50, 190, 100, 20);

            ctx.fillStyle = '#4682b4';
            ctx.beginPath(); // Arrow 2
            ctx.moveTo(arrow2X, 200); ctx.lineTo(arrow2X - 50, 180); ctx.lineTo(arrow2X - 50, 220); ctx.closePath();
            ctx.fill();
            ctx.fillRect(arrow2X - 150, 190, 100, 20);

            if (arrow1X < 370) {
                arrow1X += 2;
                arrow2X -= 2;
                animationFrameId = requestAnimationFrame(animate);
            } else {
                drawText("想象两股力量互相碰撞", canvas.width / 2, 350, 20);
            }
        };
        animate();
    }

    function animateDict() {
        clearCanvas();
        drawText("词根 'dict' = 说 / 言语", canvas.width / 2, 50, 24, '#1a73e8');
        drawStickMan(canvas.width / 2, 200);
        drawSpeechBubble(canvas.width / 2 + 30, 170, 150, 50, "Blah blah blah...", 'left');
        drawText("比如 'dictionary' (字典)", canvas.width / 2, 350, 20);
    }

    function animateStory() {
        let step = 0;
        let alpha = 0;
        const animate = () => {
            clearCanvas();
            drawText("'contra' + 'dict' = 说相反的话", canvas.width / 2, 50, 24, '#1a73e8');
            
            // Person A
            drawStickMan(200, 250);
            drawSpeechBubble(230, 220, 200, 60, "太阳从东边升起。", 'left');
            
            if (step > 0) {
                // Person B
                drawStickMan(600, 250, 'red');
                drawSpeechBubble(370, 220, 200, 60, "不！太阳从西边升起！", 'right');
            }
            if (step > 1) {
                ctx.strokeStyle = 'red';
                ctx.lineWidth = 10;
                ctx.globalAlpha = alpha;
                ctx.beginPath();
                ctx.moveTo(250, 150);
                ctx.lineTo(450, 200);
                ctx.moveTo(450, 150);
                ctx.lineTo(250, 200);
                ctx.stroke();
                ctx.globalAlpha = 1.0;
                if (alpha < 1) alpha += 0.05;
            }
        };
        animate();
        setTimeout(() => { step = 1; animate(); }, 2000);
        setTimeout(() => { step = 2; animate(); }, 4000);
        setTimeout(() => {
            drawText("B 反驳了 A。这就是 'contradict'。", canvas.width / 2, 350, 20);
        }, 5000);
    }

    function runAnimation() {
        cancelAnimationFrame(animationFrameId);
        switch(stage) {
            case 0:
                clearCanvas();
                explanation.textContent = "首先，我们来看前缀 'contra-'。";
                drawText("Contradict", canvas.width / 2, 200, 50, '#1a73e8');
                setTimeout(() => { stage++; runAnimation(); }, 2000);
                break;
            case 1:
                explanation.textContent = "前缀 'contra-' 的意思是 '相反' 或 '对抗'。";
                animateContra();
                setTimeout(() => { stage++; runAnimation(); }, 5000);
                break;
            case 2:
                explanation.textContent = "然后是词根 'dict'，意思是 '说' 或 '言语'。";
                animateDict();
                setTimeout(() => { stage++; runAnimation(); }, 4000);
                break;
            case 3:
                explanation.textContent = "把它们合起来，'contradict' 的故事就开始了...";
                animateStory();
                setTimeout(() => { stage++; runAnimation(); }, 7000);
                break;
            case 4:
                explanation.innerHTML = "<strong>现在轮到你来练习了！</strong>";
                clearCanvas();
                drawText("'contradict' = contra (相反) + dict (说)", canvas.width/2, 150, 25);
                drawText("= 反驳，说相反的话", canvas.width/2, 200, 25);
                interactiveSection.style.display = 'block';
                resetBtn.style.display = 'inline-block';
                startBtn.style.display = 'none';
                break;
        }
    }

    function handleInteraction(choice) {
        agreeBtn.disabled = true;
        contradictBtn.disabled = true;
        clearCanvas();
        if (choice === 'agree') {
            explanation.textContent = "嗯... 你确定吗？鱼好像并不会飞哦。";
            let y = 200;
            const animateFish = () => {
                clearCanvas();
                drawText("🐟", 200, y); // Fish
                drawText("❌", 250, y);
                if (y > 150) y -= 1; else y = 200; // Flapping motion
                animationFrameId = requestAnimationFrame(animateFish);
            }
            animateFish();
            setTimeout(() => cancelAnimationFrame(animationFrameId), 2000);
        } else {
            explanation.textContent = "说得对！鱼不能飞，但它们能在水里游。你成功地 '反驳' 了！";
            let x = 100;
            const animateFish = () => {
                clearCanvas();
                ctx.fillStyle = '#87ceeb'; // water
                ctx.fillRect(0, 200, canvas.width, 200);
                drawText("🐟", x, 300); // Fish
                if (x < canvas.width - 100) x += 2; else x = 100;
                animationFrameId = requestAnimationFrame(animateFish);
            }
            animateFish();
            setTimeout(() => cancelAnimationFrame(animationFrameId), 4000);
        }
    }

    startBtn.addEventListener('click', () => {
        startBtn.disabled = true;
        runAnimation();
    });

    resetBtn.addEventListener('click', () => {
        stage = 0;
        startBtn.style.display = 'inline-block';
        startBtn.disabled = false;
        resetBtn.style.display = 'none';
        interactiveSection.style.display = 'none';
        agreeBtn.disabled = false;
        contradictBtn.disabled = false;
        explanation.textContent = "我将通过一个动画故事，帮你理解和记忆这个单词。";
        clearCanvas();
    });

    agreeBtn.addEventListener('click', () => handleInteraction('agree'));
    contradictBtn.addEventListener('click', () => handleInteraction('contradict'));

</script>
</body>
</html> 