<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 平台无关性 (Write Once, Run Anywhere)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #1a73e8;
            font-size: 2em;
            margin-bottom: 10px;
        }
        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
            text-align: left;
        }
        .explanation {
            background-color: #e8f0fe;
            border-left: 5px solid #1a73e8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 20px;
            background-color: #fafafa;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #1558b8;
        }
        button:active {
            transform: scale(0.98);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>2. 平台无关性 (Write Once, Run Anywhere)</h1>
        <div class="explanation">
            <p><strong>核心思想：</strong>通过 Java 虚拟机 (JVM) 实现跨平台。</p>
            <p><strong>过程：</strong>我们编写的 Java 源代码，会被编译成一种称为"字节码"的中间文件。这个字节码文件不依赖于任何具体的操作系统。</p>
            <p><strong>运行：</strong>任何安装了 Java 虚拟机 (JVM) 的操作系统（如 Windows, macOS, Linux），都可以运行这个相同的字节码文件，实现了"一次编写，到处运行"。</p>
        </div>
        <canvas id="animationCanvas" width="740" height="400"></canvas>
        <div>
            <button id="runButton">开始演示</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        const runButton = document.getElementById('runButton');

        let animationState = 'idle'; // idle, compiling, distributing, running, done
        let progress = 0;

        const elements = {
            sourceCode: { x: 50, y: 150, w: 120, h: 80, text: '源代码\n(.java)' },
            compiler: { x: 250, y: 160, text: 'Java\n编译器' },
            byteCode: { x: 240, y: 150, w: 120, h: 80, text: '字节码\n(.class)' },
            os: [
                { x: 580, y: 50, w: 140, h: 70, text: 'Windows', jvmColor: '#f06292', jvmText: 'JVM' },
                { x: 580, y: 165, w: 140, h: 70, text: 'macOS', jvmColor: '#4fc3f7', jvmText: 'JVM' },
                { x: 580, y: 280, w: 140, h: 70, text: 'Linux', jvmColor: '#ffd54f', jvmText: 'JVM' }
            ],
            byteCodeCopies: [],
            outputs: []
        };

        function drawBox(x, y, w, h, color, text, textColor = '#333') {
            ctx.fillStyle = color;
            ctx.strokeStyle = '#555';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(x, y, w, h);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = textColor;
            ctx.font = 'bold 14px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            const lines = text.split('\n');
            if (lines.length === 1) {
                ctx.fillText(text, x + w / 2, y + h / 2);
            } else {
                ctx.fillText(lines[0], x + w / 2, y + h / 2 - 8);
                ctx.fillText(lines[1], x + w / 2, y + h / 2 + 10);
            }
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#555';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw source code
            if (animationState !== 'compiling' || progress < 1) {
                 const src = elements.sourceCode;
                 let currentX = src.x;
                 if (animationState === 'compiling') {
                     currentX = src.x + (elements.compiler.x - 80 - src.x) * progress;
                 }
                 drawBox(currentX, src.y, src.w, src.h, '#a5d6a7', src.text);
            }
           
            // Draw compiler
            const compiler = elements.compiler;
            ctx.font = '16px sans-serif';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText(compiler.text, compiler.x, compiler.y);

            // Draw ByteCode
            if (animationState === 'compiling' && progress >= 1) {
                 drawBox(elements.byteCode.x, elements.byteCode.y, elements.byteCode.w, elements.byteCode.h, '#ffcc80', elements.byteCode.text);
            }
            if (animationState === 'distributing' || animationState === 'running' || animationState === 'done') {
                if (elements.byteCodeCopies.length === 0) { // initialize
                    elements.byteCodeCopies = elements.os.map(os => ({
                        x: elements.byteCode.x,
                        y: elements.byteCode.y,
                        targetX: os.x - elements.byteCode.w / 2,
                        targetY: os.y + os.h / 2 - elements.byteCode.h / 2,
                    }));
                }
                elements.byteCodeCopies.forEach((copy, i) => {
                    let currentX = copy.x;
                    let currentY = copy.y;
                     if(animationState === 'distributing'){
                         currentX = copy.x + (copy.targetX - copy.x) * progress;
                         currentY = copy.y + (copy.targetY - copy.y) * progress;
                     } else {
                         currentX = copy.targetX;
                         currentY = copy.targetY;
                     }
                    if (animationState !== 'running' || progress < 1 || elements.outputs.includes(i)) {
                       drawBox(currentX, currentY, elements.byteCode.w, elements.byteCode.h, '#ffcc80', elements.byteCode.text);
                    }
                });
            }

            // Draw OSes and JVMs
            elements.os.forEach((os, i) => {
                ctx.fillStyle = '#eee';
                ctx.fillRect(os.x - 10, os.y - 10, os.w + 20, os.h + 20);
                ctx.strokeStyle = '#ccc';
                ctx.strokeRect(os.x - 10, os.y - 10, os.w + 20, os.h + 20);

                ctx.font = 'bold 16px sans-serif';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText(os.text, os.x + os.w/2, os.y - 25);
                
                let jvmW = os.w * 0.7;
                let jvmH = os.h * 0.8;
                if(animationState === 'running' && elements.outputs.includes(i)){
                    let scale = 1 + 0.2 * Math.sin(progress * Math.PI); // Pulse effect
                    jvmW *= scale;
                    jvmH *= scale;
                }
                drawBox(os.x + (os.w - jvmW)/2, os.y + (os.h - jvmH)/2, jvmW, jvmH, os.jvmColor, os.jvmText, 'white');
            });
            
            // Draw arrows
            if (animationState === 'idle' || animationState === 'compiling') {
                 drawArrow(180, 190, 220, 190);
            }
             if (animationState === 'compiling' && progress >= 1 || animationState === 'distributing') {
                 elements.os.forEach(os => {
                     drawArrow(370, 190, 540, os.y + os.h/2);
                 });
            }

            // Draw output
            if(animationState === 'running' || animationState === 'done') {
                elements.outputs.forEach(i => {
                    ctx.font = 'bold 16px sans-serif';
                    ctx.fillStyle = 'green';
                    ctx.textAlign = 'left';
                    ctx.fillText('Hello World!', elements.os[i].x + elements.os[i].w + 5, elements.os[i].y + elements.os[i].h / 2);
                });
            }

            // Draw instructions
            if(animationState === 'idle') {
                 ctx.font = '16px sans-serif';
                 ctx.fillStyle = '#888';
                 ctx.textAlign = 'center';
                 ctx.fillText('点击 "开始演示" 按钮，查看执行过程', canvas.width / 2, 20);
            }
        }
        
        function animate() {
            if (animationState === 'idle' || animationState === 'done') return;

            progress += 0.01;
            if (progress > 1) {
                progress = 0;
                if (animationState === 'compiling') {
                    animationState = 'distributing';
                } else if (animationState === 'distributing') {
                    animationState = 'running';
                } else if (animationState === 'running') {
                    if(elements.outputs.length < elements.os.length) {
                        elements.outputs.push(elements.outputs.length);
                    } else {
                       animationState = 'done';
                       runButton.disabled = false;
                       runButton.textContent = "重新演示";
                    }
                }
            }
            
            drawScene();
            requestAnimationFrame(animate);
        }

        runButton.addEventListener('click', () => {
            runButton.disabled = true;
            runButton.textContent = "演示中...";
            animationState = 'compiling';
            progress = 0;
            elements.byteCodeCopies = [];
            elements.outputs = [];
            animate();
        });

        // Initial draw
        drawScene();
    </script>
</body>
</html> 