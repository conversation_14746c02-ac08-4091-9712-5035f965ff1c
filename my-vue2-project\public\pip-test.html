<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>画中画功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #2c3e50;
            border-radius: 5px;
        }
        .error {
            background: #e74c3c;
        }
        .success {
            background: #27ae60;
        }
        .warning {
            background: #f39c12;
        }
        video {
            width: 100%;
            max-width: 400px;
            border: 2px solid #3498db;
            border-radius: 8px;
        }
        .debug-info {
            background: #34495e;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 画中画功能测试</h1>
        
        <div class="controls">
            <button onclick="checkSupport()">检查浏览器支持</button>
            <button id="startBtn" onclick="startPip()" disabled>开启画中画</button>
            <button id="stopBtn" onclick="stopPip()" disabled>停止画中画</button>
        </div>
        
        <div id="status" class="status">点击"检查浏览器支持"开始测试</div>
        
        <div class="debug-info">
            <h3>调试信息:</h3>
            <div id="debugInfo">等待检测...</div>
        </div>
        
        <video id="testVideo" style="display: none;"></video>
    </div>

    <script>
        let video = null;
        let canvas = null;
        let ctx = null;
        let animationId = null;

        function updateStatus(message, type = '') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }

        function updateDebugInfo(info) {
            document.getElementById('debugInfo').innerHTML = info;
        }

        function checkSupport() {
            const checks = {
                pictureInPictureEnabled: 'pictureInPictureEnabled' in document,
                documentPipEnabled: document.pictureInPictureEnabled,
                videoSupport: 'requestPictureInPicture' in HTMLVideoElement.prototype,
                mediaDevices: 'mediaDevices' in navigator,
                getUserMedia: navigator.mediaDevices && 'getUserMedia' in navigator.mediaDevices
            };

            const supported = checks.pictureInPictureEnabled && 
                            checks.documentPipEnabled && 
                            checks.videoSupport;

            let debugHtml = '';
            for (const [key, value] of Object.entries(checks)) {
                debugHtml += `${key}: <span style="color: ${value ? '#27ae60' : '#e74c3c'}">${value}</span><br>`;
            }
            debugHtml += `<br>最终支持: <span style="color: ${supported ? '#27ae60' : '#e74c3c'}">${supported}</span>`;

            updateDebugInfo(debugHtml);

            if (supported) {
                updateStatus('✅ 浏览器支持画中画功能！', 'success');
                document.getElementById('startBtn').disabled = false;
            } else {
                updateStatus('❌ 浏览器不支持画中画功能', 'error');
            }
        }

        async function startPip() {
            try {
                updateStatus('正在启动画中画...', 'warning');

                // 创建canvas
                canvas = document.createElement('canvas');
                ctx = canvas.getContext('2d');
                canvas.width = 640;
                canvas.height = 480;

                // 创建video元素
                video = document.getElementById('testVideo');
                video.style.display = 'block';
                video.width = 640;
                video.height = 480;
                video.muted = true;
                video.autoplay = true;

                // 开始绘制内容到canvas
                startDrawing();

                // 将canvas流设置为video源
                const stream = canvas.captureStream(30);
                video.srcObject = stream;

                // 等待video加载
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('Video加载超时'));
                    }, 5000);

                    video.addEventListener('loadedmetadata', () => {
                        clearTimeout(timeout);
                        resolve();
                    }, { once: true });

                    video.addEventListener('error', (e) => {
                        clearTimeout(timeout);
                        reject(new Error('Video加载失败'));
                    }, { once: true });
                });

                // 请求画中画
                const pipWindow = await video.requestPictureInPicture();
                
                updateStatus('✅ 画中画启动成功！', 'success');
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;

                // 监听退出事件
                video.addEventListener('leavepictureinpicture', () => {
                    stopPip();
                }, { once: true });

                console.log('画中画窗口:', pipWindow);

            } catch (error) {
                console.error('画中画启动失败:', error);
                updateStatus('❌ 画中画启动失败: ' + error.message, 'error');
                cleanup();
            }
        }

        function stopPip() {
            try {
                if (document.pictureInPictureElement) {
                    document.exitPictureInPicture();
                }
            } catch (error) {
                console.error('退出画中画失败:', error);
            }

            cleanup();
            updateStatus('画中画已停止', '');
        }

        function cleanup() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }

            if (video) {
                video.style.display = 'none';
                video.srcObject = null;
            }

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        function startDrawing() {
            let frame = 0;

            function draw() {
                if (!ctx || !canvas) return;

                // 清空canvas
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('画中画测试', canvas.width / 2, 50);

                // 绘制时间
                const now = new Date();
                ctx.font = '18px Arial';
                ctx.fillText(now.toLocaleTimeString(), canvas.width / 2, 80);

                // 绘制动画圆圈
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 50 + Math.sin(frame * 0.1) * 20;

                ctx.fillStyle = `hsl(${frame % 360}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.fill();

                // 绘制帧数
                ctx.fillStyle = '#ffffff';
                ctx.font = '16px Arial';
                ctx.fillText(`帧数: ${frame}`, canvas.width / 2, canvas.height - 50);

                frame++;
                animationId = requestAnimationFrame(draw);
            }

            draw();
        }

        // 页面加载时自动检查支持
        window.addEventListener('load', checkSupport);
    </script>
</body>
</html>
