<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全协议互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 0.8s ease-out;
        }

        .question-text {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.selected {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .option.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #d4fc79 0%, #96e6a1 100%);
            animation: correctPulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            animation: wrongShake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .explanation {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .protocol-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .protocol-card:hover {
            transform: translateX(10px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="floating-icon" style="top: 10%; left: 10%;">🔒</div>
    <div class="floating-icon" style="top: 20%; right: 15%; animation-delay: -2s;">🌐</div>
    <div class="floating-icon" style="bottom: 20%; left: 20%; animation-delay: -4s;">💻</div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔐 网络安全协议学习</h1>
            <p class="subtitle">通过互动动画学习网络安全协议知识</p>
        </div>

        <div class="question-card">
            <h2 class="question-text">
                📝 下列协议中，属于安全远程登录协议的是（ ）？
            </h2>
            
            <div class="options-container">
                <div class="option" data-option="A">
                    <h3>A. TLS</h3>
                    <p>传输层安全协议</p>
                </div>
                <div class="option" data-option="B">
                    <h3>B. TCP</h3>
                    <p>传输控制协议</p>
                </div>
                <div class="option" data-option="C">
                    <h3>C. SSH</h3>
                    <p>安全外壳协议</p>
                </div>
                <div class="option" data-option="D">
                    <h3>D. TFTP</h3>
                    <p>简单文件传输协议</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="startAnimation()">🎮 开始协议动画演示</button>
                <button class="btn" onclick="showExplanation()">📚 查看详细解析</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>📖 详细解析</h3>
            
            <div class="protocol-card">
                <h4>🔒 TLS (传输层安全协议)</h4>
                <p><strong>作用：</strong>在两个通信应用程序之间提供保密性和数据完整性</p>
                <p><strong>特点：</strong>加密传输数据，但不是专门的远程登录协议</p>
            </div>

            <div class="protocol-card">
                <h4>📡 TCP (传输控制协议)</h4>
                <p><strong>作用：</strong>传输层的通信协议，负责可靠的数据传输</p>
                <p><strong>特点：</strong>基础传输协议，不涉及安全和登录功能</p>
            </div>

            <div class="protocol-card" style="border: 3px solid #4CAF50;">
                <h4>✅ SSH (安全外壳协议) - 正确答案</h4>
                <p><strong>作用：</strong>专门用于安全远程登录和命令执行</p>
                <p><strong>特点：</strong>提供加密的远程登录、文件传输和命令执行功能</p>
            </div>

            <div class="protocol-card">
                <h4>📁 TFTP (简单文件传输协议)</h4>
                <p><strong>作用：</strong>简单的文件传输协议</p>
                <p><strong>特点：</strong>不提供安全功能，主要用于简单文件传输</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationId;
        let particles = [];
        let protocols = [];

        // 协议对象类
        class Protocol {
            constructor(name, x, y, color, isSecure = false) {
                this.name = name;
                this.x = x;
                this.y = y;
                this.color = color;
                this.isSecure = isSecure;
                this.radius = 40;
                this.pulse = 0;
                this.selected = false;
            }

            draw() {
                ctx.save();
                
                // 绘制光环效果
                if (this.isSecure) {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius + 10 + Math.sin(this.pulse) * 5, 0, Math.PI * 2);
                    ctx.strokeStyle = 'rgba(76, 175, 80, 0.5)';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                }

                // 绘制协议圆圈
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = this.color;
                ctx.fill();
                ctx.strokeStyle = this.selected ? '#4CAF50' : '#333';
                ctx.lineWidth = this.selected ? 4 : 2;
                ctx.stroke();

                // 绘制协议名称
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, this.x, this.y + 5);

                this.pulse += 0.1;
                ctx.restore();
            }

            isClicked(mouseX, mouseY) {
                const distance = Math.sqrt((mouseX - this.x) ** 2 + (mouseY - this.y) ** 2);
                return distance < this.radius;
            }
        }

        // 粒子类
        class Particle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.life = 1;
                this.decay = 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        function initProtocols() {
            protocols = [
                new Protocol('TLS', canvas.width * 0.2, canvas.height * 0.3, '#FF6B6B'),
                new Protocol('TCP', canvas.width * 0.4, canvas.height * 0.7, '#4ECDC4'),
                new Protocol('SSH', canvas.width * 0.6, canvas.height * 0.3, '#45B7D1', true),
                new Protocol('TFTP', canvas.width * 0.8, canvas.height * 0.7, '#96CEB4')
            ];
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            drawGrid();

            // 更新和绘制协议
            protocols.forEach(protocol => {
                protocol.draw();
            });

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            animationId = requestAnimationFrame(animate);
        }

        function drawGrid() {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            
            for (let x = 0; x < canvas.width; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            for (let y = 0; y < canvas.height; y += 50) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            initProtocols();
            animate();
        }

        function showExplanation() {
            const explanation = document.getElementById('explanation');
            explanation.classList.add('show');
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'wrong');
                });

                // 标记当前选择
                this.classList.add('selected');

                // 检查答案
                setTimeout(() => {
                    const selectedOption = this.dataset.option;
                    if (selectedOption === 'C') {
                        this.classList.add('correct');
                        // 创建成功粒子效果
                        for (let i = 0; i < 20; i++) {
                            particles.push(new Particle(
                                this.offsetLeft + this.offsetWidth / 2,
                                this.offsetTop + this.offsetHeight / 2
                            ));
                        }
                        setTimeout(() => {
                            showExplanation();
                        }, 1000);
                    } else {
                        this.classList.add('wrong');
                        // 显示正确答案
                        setTimeout(() => {
                            document.querySelector('[data-option="C"]').classList.add('correct');
                        }, 500);
                    }
                }, 500);
            });
        });

        // Canvas点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            protocols.forEach(protocol => {
                if (protocol.isClicked(mouseX, mouseY)) {
                    // 清除之前的选择
                    protocols.forEach(p => p.selected = false);
                    protocol.selected = true;

                    // 创建点击效果
                    for (let i = 0; i < 10; i++) {
                        particles.push(new Particle(mouseX, mouseY));
                    }

                    // 显示协议信息
                    showProtocolInfo(protocol);
                }
            });
        });

        function showProtocolInfo(protocol) {
            const info = {
                'TLS': '🔒 TLS用于加密通信，保护数据传输安全',
                'TCP': '📡 TCP是可靠的传输层协议，确保数据完整传输',
                'SSH': '✅ SSH是安全远程登录协议，这是正确答案！',
                'TFTP': '📁 TFTP是简单文件传输协议，功能有限'
            };

            // 创建临时提示
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 15px;
                border-radius: 10px;
                font-size: 14px;
                z-index: 1000;
                pointer-events: none;
                animation: fadeInOut 3s ease-in-out;
            `;
            tooltip.textContent = info[protocol.name];
            document.body.appendChild(tooltip);

            // 定位提示框
            const rect = canvas.getBoundingClientRect();
            tooltip.style.left = (rect.left + protocol.x - tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = (rect.top + protocol.y - 60) + 'px';

            // 3秒后移除
            setTimeout(() => {
                document.body.removeChild(tooltip);
            }, 3000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translateY(10px); }
                20%, 80% { opacity: 1; transform: translateY(0); }
                100% { opacity: 0; transform: translateY(-10px); }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        startAnimation();
    </script>
</body>
</html>
