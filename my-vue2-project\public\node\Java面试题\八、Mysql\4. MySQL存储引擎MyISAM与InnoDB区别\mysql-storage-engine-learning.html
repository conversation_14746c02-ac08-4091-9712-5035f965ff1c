<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL存储引擎互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #34495e;
            margin: 20px 0;
            text-align: center;
        }

        .engine-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .engine-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            transition: all 0.4s ease;
            cursor: pointer;
            border: 3px solid transparent;
        }

        .engine-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .engine-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .engine-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .engine-description {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.8;
        }

        .comparison-table {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 40px rgba(0,0,0,0.08);
            margin: 30px 0;
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            font-weight: 600;
            text-align: center;
        }

        .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            padding: 20px;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }

        .table-row:hover {
            background: #f8f9fa;
        }

        .table-cell {
            text-align: center;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .feature-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating-icon {
            position: fixed;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        #helpIcon {
            bottom: 30px;
            right: 30px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 24px;
            padding: 40px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">MySQL存储引擎</h1>
            <p class="subtitle">互动学习体验 - 从零开始掌握数据库核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section" id="introSection">
            <h2 class="section-title">什么是存储引擎？</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <p class="explanation">
                存储引擎是MySQL中数据、索引以及其他对象的存储方式，就像不同的文件系统一样。
                点击上方动画了解存储引擎的工作原理！
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startIntroAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="section" id="enginesSection">
            <h2 class="section-title">三大主要存储引擎</h2>
            <div class="engine-cards">
                <div class="engine-card" onclick="selectEngine('innodb')">
                    <div class="engine-name">InnoDB引擎</div>
                    <div class="engine-description">
                        支持ACID事务、行级锁和外键约束，专为处理大数据容量设计，是现代MySQL的默认引擎。
                    </div>
                </div>
                <div class="engine-card" onclick="selectEngine('myisam')">
                    <div class="engine-name">MyISAM引擎</div>
                    <div class="engine-description">
                        MySQL原本的默认引擎，不支持事务和行级锁，但在某些场景下性能优异。
                    </div>
                </div>
                <div class="engine-card" onclick="selectEngine('memory')">
                    <div class="engine-name">MEMORY引擎</div>
                    <div class="engine-description">
                        所有数据存储在内存中，处理速度极快，但数据安全性较低，重启后数据丢失。
                    </div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="engineCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="section" id="comparisonSection">
            <h2 class="section-title">存储引擎详细对比</h2>
            <div class="comparison-table">
                <div class="table-header">
                    <div>特性</div>
                    <div>MyISAM</div>
                    <div>InnoDB</div>
                </div>
                <div class="table-row" onclick="highlightFeature('storage')">
                    <div class="feature-name">存储结构</div>
                    <div class="table-cell">每张表被存在三个文件：frm表格定义、MYD数据文件、MYI索引文件</div>
                    <div class="table-cell">所有的表都保存在同一个数据文件中，InnoDB表的大小只受限于操作系统文件的大小，一般为2GB</div>
                </div>
                <div class="table-row" onclick="highlightFeature('space')">
                    <div class="feature-name">存储空间</div>
                    <div class="table-cell">MyISAM可被压缩，存储空间较小</div>
                    <div class="table-cell">InnoDB的表需要更多的内存和存储，它会在主内存中建立其专用的缓冲池用于高速缓冲数据和索引</div>
                </div>
                <div class="table-row" onclick="highlightFeature('portability')">
                    <div class="feature-name">可移植性、备份及恢复</div>
                    <div class="table-cell">由于MyISAM的数据是以文件的形式存储，所以在跨平台的数据转移中会很方便。在备份和恢复时可单独针对某个表进行操作</div>
                    <div class="table-cell">免费的方案可以是拷贝数据文件、备份binlog，或者用mysqldump，在数据量达到几十G的时候就相对痛苦了</div>
                </div>
                <div class="table-row" onclick="highlightFeature('transaction')">
                    <div class="feature-name">事务</div>
                    <div class="table-cell">不支持</div>
                    <div class="table-cell">支持</div>
                </div>
                <div class="table-row" onclick="highlightFeature('foreignkey')">
                    <div class="feature-name">外键</div>
                    <div class="table-cell">不支持</div>
                    <div class="table-cell">支持</div>
                </div>
                <div class="table-row" onclick="highlightFeature('lock')">
                    <div class="feature-name">锁支持</div>
                    <div class="table-cell">表级锁定</div>
                    <div class="table-cell">行级锁定、表级锁定、锁定力度小并发能力高</div>
                </div>
                <div class="table-row" onclick="highlightFeature('select')">
                    <div class="feature-name">SELECT</div>
                    <div class="table-cell">MyISAM更优</div>
                    <div class="table-cell">InnoDB更优</div>
                </div>
                <div class="table-row" onclick="highlightFeature('insert_update_delete')">
                    <div class="feature-name">INSERT、UPDATE、DELETE</div>
                    <div class="table-cell">MyISAM更优</div>
                    <div class="table-cell">InnoDB更优</div>
                </div>
                <div class="table-row" onclick="highlightFeature('select_count')">
                    <div class="feature-name">select count(*)</div>
                    <div class="table-cell">myisam更快，因为myisam内部维护了一个计数器，可以直接调取</div>
                    <div class="table-cell">B+树索引，myisam是堆表</div>
                </div>
                <div class="table-row" onclick="highlightFeature('index_implementation')">
                    <div class="feature-name">索引的实现方式</div>
                    <div class="table-cell">B+树索引，myisam是堆表</div>
                    <div class="table-cell">B+树索引，Innodb是索引组织表</div>
                </div>
                <div class="table-row" onclick="highlightFeature('hash_index')">
                    <div class="feature-name">哈希索引</div>
                    <div class="table-cell">不支持</div>
                    <div class="table-cell">支持</div>
                </div>
                <div class="table-row" onclick="highlightFeature('fulltext_index')">
                    <div class="feature-name">全文索引</div>
                    <div class="table-cell">支持</div>
                    <div class="table-cell">不支持</div>
                </div>
            </div>
        </div>

        <div class="section" id="detailsSection">
            <h2 class="section-title">深度解析关键特性</h2>
            <div class="canvas-container">
                <canvas id="detailCanvas" width="800" height="400"></canvas>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <button class="interactive-btn" onclick="showFeatureDetail('count')">COUNT(*)性能对比</button>
                <button class="interactive-btn" onclick="showFeatureDetail('index')">索引实现方式</button>
                <button class="interactive-btn" onclick="showFeatureDetail('lock')">锁机制对比</button>
                <button class="interactive-btn" onclick="showFeatureDetail('crud')">CRUD操作性能</button>
            </div>
            <div id="featureExplanation" class="explanation" style="min-height: 100px; padding: 20px; background: #f8f9fa; border-radius: 12px; margin-top: 20px;">
                点击上方按钮查看各个特性的详细解析和动画演示
            </div>
        </div>

        <div class="section" id="testSection">
            <h2 class="section-title">互动测试</h2>
            <div class="canvas-container">
                <canvas id="testCanvas" width="800" height="500"></canvas>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="interactive-btn" onclick="startQuiz()">开始知识测试</button>
                <button class="interactive-btn" onclick="resetQuiz()">重新开始</button>
            </div>
        </div>
    </div>

    <div class="floating-icon" id="helpIcon" onclick="showHelp()">
        ❓
    </div>

    <div class="modal" id="helpModal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeHelp()">&times;</button>
            <h3>学习指南</h3>
            <p>1. 点击动画按钮观看演示</p>
            <p>2. 点击存储引擎卡片了解详情</p>
            <p>3. 查看对比表格理解差异</p>
            <p>4. 完成互动测试巩固知识</p>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        let animationRunning = false;
        let selectedEngine = null;
        let quizData = [
            {
                question: "哪个存储引擎支持事务？",
                options: ["MyISAM", "InnoDB", "MEMORY", "都不支持"],
                correct: 1,
                explanation: "InnoDB引擎提供了对数据库ACID事务的支持"
            },
            {
                question: "为什么MyISAM的COUNT(*)查询比InnoDB快？",
                options: ["索引更优化", "内部维护计数器", "表结构简单", "锁机制不同"],
                correct: 1,
                explanation: "MyISAM内部维护了一个计数器，可以直接调取，而InnoDB需要扫描索引统计"
            },
            {
                question: "InnoDB相比MyISAM的锁机制优势是什么？",
                options: ["表级锁更快", "支持行级锁", "不需要锁", "锁定时间更长"],
                correct: 1,
                explanation: "InnoDB支持行级锁，锁定力度小，并发能力高"
            },
            {
                question: "MyISAM的存储结构包含哪三个文件？",
                options: [".frm .MYD .MYI", ".ibd .frm .log", ".MYD .IDB .frm", ".frm .ibd .MYI"],
                correct: 0,
                explanation: "MyISAM每张表被存在三个文件：frm表格定义、MYD数据文件、MYI索引文件"
            },
            {
                question: "哪个存储引擎支持全文索引？",
                options: ["InnoDB", "MyISAM", "MEMORY", "都支持"],
                correct: 1,
                explanation: "MyISAM支持全文索引，而InnoDB不支持全文索引"
            },
            {
                question: "InnoDB的索引实现方式是？",
                options: ["堆表+B+树索引", "索引组织表+B+树索引", "哈希索引", "二叉树索引"],
                correct: 1,
                explanation: "InnoDB是索引组织表，使用B+树索引，主键索引叶子节点直接存储数据"
            }
        ];
        let currentQuiz = 0;
        let quizScore = 0;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            observeSections();
            updateProgress(10);
            initializeCanvases();
        });

        // 监听滚动，显示section
        function observeSections() {
            const sections = document.querySelectorAll('.section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                observer.observe(section);
            });
        }

        // 更新进度条
        function updateProgress(percent) {
            currentProgress = Math.max(currentProgress, percent);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 初始化画布
        function initializeCanvases() {
            drawEngineComparison();
            drawTestInterface();
            drawDetailCanvas();
        }

        // 绘制详解画布
        function drawDetailCanvas() {
            const canvas = document.getElementById('detailCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制欢迎界面
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('深度解析存储引擎特性', canvas.width / 2, 100);

            ctx.font = '16px Arial';
            ctx.fillText('点击下方按钮查看详细的特性对比和动画演示', canvas.width / 2, 140);

            // 绘制装饰图标
            const features = ['📊', '🔍', '🔒', '⚡'];
            features.forEach((icon, index) => {
                const x = 150 + index * 150;
                const y = 250;

                ctx.font = '48px Arial';
                ctx.fillText(icon, x, y);

                ctx.font = '14px Arial';
                const labels = ['性能统计', '索引分析', '锁机制', '操作速度'];
                ctx.fillText(labels[index], x, y + 40);
            });
        }

        // 介绍动画
        function startIntroAnimation() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let frame = 0;
            const maxFrames = 180;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制数据库图标
                const dbX = 100;
                const dbY = 150;
                const progress = frame / maxFrames;

                // 数据库圆柱体
                ctx.fillStyle = '#3498db';
                ctx.fillRect(dbX - 30, dbY - 40, 60, 80);
                ctx.beginPath();
                ctx.ellipse(dbX, dbY - 40, 30, 10, 0, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.ellipse(dbX, dbY + 40, 30, 10, 0, 0, 2 * Math.PI);
                ctx.fill();

                // 存储引擎选择器
                const engineX = 300;
                const engineY = 150;

                // 绘制三个存储引擎选项
                const engines = ['InnoDB', 'MyISAM', 'MEMORY'];
                const colors = ['#e74c3c', '#f39c12', '#9b59b6'];

                engines.forEach((engine, index) => {
                    const y = engineY - 60 + index * 60;
                    const alpha = progress > index * 0.3 ? 1 : 0.3;

                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = colors[index];
                    ctx.fillRect(engineX - 40, y - 20, 80, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(engine, engineX, y + 5);
                    ctx.globalAlpha = 1;
                });

                // 连接线动画
                if (progress > 0.5) {
                    ctx.strokeStyle = '#2ecc71';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(dbX + 30, dbY);
                    const lineProgress = (progress - 0.5) * 2;
                    ctx.lineTo(dbX + 30 + (engineX - dbX - 70) * lineProgress, dbY);
                    ctx.stroke();
                }

                // 文字说明
                if (progress > 0.8) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('选择合适的存储引擎', canvas.width / 2, 50);
                    ctx.fillText('决定数据如何存储和访问', canvas.width / 2, 280);
                }

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                    updateProgress(25);
                }
            }

            animate();
        }

        // 选择存储引擎
        function selectEngine(engine) {
            selectedEngine = engine;

            // 更新卡片样式
            document.querySelectorAll('.engine-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.engine-card').classList.add('active');

            // 绘制引擎详细信息
            drawEngineDetails(engine);
            updateProgress(50);
        }

        // 绘制引擎对比
        function drawEngineComparison() {
            const canvas = document.getElementById('engineCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制三个存储引擎的特性对比图
            const engines = [
                { name: 'InnoDB', x: 150, features: ['事务支持', '行级锁', '外键', '崩溃恢复'], color: '#e74c3c' },
                { name: 'MyISAM', x: 400, features: ['表级锁', '压缩存储', '快速查询', '简单结构'], color: '#f39c12' },
                { name: 'MEMORY', x: 650, features: ['内存存储', '极速访问', '临时数据', '易失性'], color: '#9b59b6' }
            ];

            engines.forEach((engine, index) => {
                // 绘制引擎名称
                ctx.fillStyle = engine.color;
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(engine.name, engine.x, 50);

                // 绘制特性列表
                engine.features.forEach((feature, fIndex) => {
                    const y = 100 + fIndex * 40;

                    // 特性框
                    ctx.fillStyle = engine.color;
                    ctx.globalAlpha = 0.8;
                    ctx.fillRect(engine.x - 60, y - 15, 120, 30);
                    ctx.globalAlpha = 1;

                    // 特性文字
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(feature, engine.x, y + 5);
                });

                // 绘制连接线
                if (index < engines.length - 1) {
                    ctx.strokeStyle = '#bdc3c7';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(engine.x + 70, 200);
                    ctx.lineTo(engines[index + 1].x - 70, 200);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
            });
        }

        // 绘制引擎详细信息
        function drawEngineDetails(engine) {
            const canvas = document.getElementById('engineCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const engineData = {
                'innodb': {
                    name: 'InnoDB引擎',
                    color: '#e74c3c',
                    features: [
                        '✓ 支持ACID事务',
                        '✓ 行级锁定',
                        '✓ 外键约束',
                        '✓ 崩溃恢复',
                        '✓ 多版本并发控制',
                        '✓ 聚集索引'
                    ],
                    description: '现代MySQL的默认引擎，适合高并发、事务性应用'
                },
                'myisam': {
                    name: 'MyISAM引擎',
                    color: '#f39c12',
                    features: [
                        '✓ 表级锁定',
                        '✓ 压缩存储',
                        '✓ 快速SELECT',
                        '✗ 不支持事务',
                        '✗ 不支持外键',
                        '✓ 全文索引'
                    ],
                    description: '适合读多写少的应用场景'
                },
                'memory': {
                    name: 'MEMORY引擎',
                    color: '#9b59b6',
                    features: [
                        '✓ 内存存储',
                        '✓ 极速访问',
                        '✓ 哈希索引',
                        '✗ 数据易失',
                        '✗ 重启丢失',
                        '✓ 临时表'
                    ],
                    description: '适合临时数据和缓存场景'
                }
            };

            const data = engineData[engine];
            if (!data) return;

            // 绘制标题
            ctx.fillStyle = data.color;
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(data.name, canvas.width / 2, 50);

            // 绘制特性列表
            data.features.forEach((feature, index) => {
                const x = canvas.width / 2 - 150;
                const y = 100 + index * 35;

                // 特性背景
                const isSupported = feature.startsWith('✓');
                ctx.fillStyle = isSupported ? '#2ecc71' : '#e74c3c';
                ctx.globalAlpha = 0.1;
                ctx.fillRect(x - 10, y - 15, 300, 25);
                ctx.globalAlpha = 1;

                // 特性文字
                ctx.fillStyle = isSupported ? '#27ae60' : '#c0392b';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(feature, x, y);
            });

            // 绘制描述
            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(data.description, canvas.width / 2, 350);
        }

        // 高亮特性
        function highlightFeature(feature) {
            // 添加视觉反馈
            event.target.closest('.table-row').style.background = '#f8f9fa';
            setTimeout(() => {
                event.target.closest('.table-row').style.background = '';
            }, 1000);

            // 显示对应的特性详解
            showFeatureDetail(feature);
            updateProgress(75);
        }

        // 显示特性详解
        function showFeatureDetail(feature) {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            const explanationDiv = document.getElementById('featureExplanation');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const featureData = {
                'count': {
                    title: 'COUNT(*)性能对比',
                    explanation: `
                        <h3>🔢 COUNT(*)查询性能差异</h3>
                        <p><strong>MyISAM引擎：</strong>内部维护了一个行计数器，执行COUNT(*)时直接返回计数器值，速度极快。</p>
                        <p><strong>InnoDB引擎：</strong>需要扫描索引或表来统计行数，因为支持事务和MVCC，无法维护准确的全局计数器。</p>
                        <p><strong>实际影响：</strong>在大表上，MyISAM的COUNT(*)可能比InnoDB快几十倍甚至更多。</p>
                    `,
                    animation: 'count'
                },
                'index': {
                    title: '索引实现方式对比',
                    explanation: `
                        <h3>🌳 索引结构差异</h3>
                        <p><strong>MyISAM：</strong>使用B+树索引，但是堆表结构，索引文件和数据文件分离存储。</p>
                        <p><strong>InnoDB：</strong>使用B+树索引，但是索引组织表，主键索引的叶子节点直接存储数据行。</p>
                        <p><strong>性能影响：</strong>InnoDB的主键查询更快，但辅助索引需要回表查询。</p>
                    `,
                    animation: 'index'
                },
                'lock': {
                    title: '锁机制深度对比',
                    explanation: `
                        <h3>🔒 锁定粒度差异</h3>
                        <p><strong>MyISAM：</strong>只支持表级锁，读写操作会锁定整张表，并发性能较差。</p>
                        <p><strong>InnoDB：</strong>支持行级锁和表级锁，可以精确锁定需要修改的行，并发性能优秀。</p>
                        <p><strong>适用场景：</strong>InnoDB适合高并发读写，MyISAM适合读多写少的场景。</p>
                    `,
                    animation: 'lock'
                },
                'crud': {
                    title: 'CRUD操作性能分析',
                    explanation: `
                        <h3>⚡ 增删改查性能特点</h3>
                        <p><strong>SELECT查询：</strong>MyISAM在简单查询上略有优势，InnoDB在复杂查询上更优。</p>
                        <p><strong>INSERT/UPDATE/DELETE：</strong>InnoDB由于行级锁和事务支持，在并发环境下性能更好。</p>
                        <p><strong>批量操作：</strong>MyISAM在批量插入时可能更快，但InnoDB的事务保证更安全。</p>
                    `,
                    animation: 'crud'
                },
                'storage': {
                    title: '存储结构详解',
                    explanation: `
                        <h3>💾 文件存储方式</h3>
                        <p><strong>MyISAM：</strong>每张表对应三个文件：.frm(表结构)、.MYD(数据)、.MYI(索引)</p>
                        <p><strong>InnoDB：</strong>所有表共享表空间文件，或每表一个.ibd文件，结构更紧凑</p>
                        <p><strong>优缺点：</strong>MyISAM便于单表备份，InnoDB空间利用率更高</p>
                    `,
                    animation: 'storage'
                }
            };

            const data = featureData[feature];
            if (!data) {
                explanationDiv.innerHTML = '点击上方按钮查看各个特性的详细解析和动画演示';
                return;
            }

            // 更新解释文本
            explanationDiv.innerHTML = data.explanation;

            // 绘制对应的动画
            drawFeatureAnimation(data.animation, ctx);
        }

        // 绘制特性动画
        function drawFeatureAnimation(type, ctx) {
            const canvas = ctx.canvas;

            switch(type) {
                case 'count':
                    drawCountAnimation(ctx);
                    break;
                case 'index':
                    drawIndexAnimation(ctx);
                    break;
                case 'lock':
                    drawLockAnimation(ctx);
                    break;
                case 'crud':
                    drawCrudAnimation(ctx);
                    break;
                case 'storage':
                    drawStorageAnimation(ctx);
                    break;
                default:
                    drawDetailCanvas();
            }
        }

        // COUNT(*)动画
        function drawCountAnimation(ctx) {
            const canvas = ctx.canvas;

            // MyISAM部分
            ctx.fillStyle = '#f39c12';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM', 200, 50);

            // 绘制计数器
            ctx.fillStyle = '#e67e22';
            ctx.fillRect(150, 80, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.fillText('计数器', 200, 110);
            ctx.font = '20px Arial';
            ctx.fillText('1000000', 200, 130);

            // 查询箭头
            ctx.strokeStyle = '#27ae60';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(200, 160);
            ctx.lineTo(200, 200);
            ctx.stroke();

            ctx.fillStyle = '#27ae60';
            ctx.fillText('直接返回 ⚡', 200, 220);

            // InnoDB部分
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('InnoDB', 600, 50);

            // 绘制表扫描
            for(let i = 0; i < 5; i++) {
                ctx.fillStyle = '#c0392b';
                ctx.fillRect(550 + i * 20, 80 + i * 15, 15, 40);
            }

            ctx.fillStyle = '#e74c3c';
            ctx.font = '12px Arial';
            ctx.fillText('扫描所有行', 600, 200);

            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(550, 220);
            ctx.lineTo(650, 220);
            ctx.stroke();

            ctx.fillStyle = '#e74c3c';
            ctx.font = '16px Arial';
            ctx.fillText('逐行统计 🐌', 600, 240);
        }

        // 索引动画
        function drawIndexAnimation(ctx) {
            // MyISAM索引结构
            ctx.fillStyle = '#f39c12';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM - 堆表', 200, 30);

            // 索引文件
            ctx.fillStyle = '#e67e22';
            ctx.fillRect(150, 50, 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('索引文件', 200, 70);
            ctx.fillText('.MYI', 200, 85);

            // 数据文件
            ctx.fillStyle = '#d35400';
            ctx.fillRect(150, 150, 100, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('数据文件', 200, 170);
            ctx.fillText('.MYD', 200, 185);

            // 连接线
            ctx.strokeStyle = '#95a5a6';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(200, 130);
            ctx.lineTo(200, 150);
            ctx.stroke();
            ctx.setLineDash([]);

            // InnoDB索引结构
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('InnoDB - 索引组织表', 600, 30);

            // 聚集索引
            ctx.fillStyle = '#c0392b';
            ctx.fillRect(550, 50, 100, 120);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('聚集索引', 600, 70);
            ctx.fillText('(主键)', 600, 85);
            ctx.fillText('包含数据', 600, 110);
            ctx.fillText('行记录', 600, 125);

            // 辅助索引
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(550, 190, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('辅助索引', 600, 210);
            ctx.fillText('指向主键', 600, 225);
        }

        // 锁机制动画
        function drawLockAnimation(ctx) {
            // MyISAM表级锁
            ctx.fillStyle = '#f39c12';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM - 表级锁', 200, 30);

            // 绘制表
            ctx.fillStyle = '#e67e22';
            ctx.fillRect(150, 60, 100, 120);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('整张表', 200, 90);
            ctx.fillText('被锁定', 200, 110);

            // 锁图标
            ctx.fillStyle = '#d35400';
            ctx.font = '24px Arial';
            ctx.fillText('🔒', 200, 150);

            // 等待的操作
            ctx.fillStyle = '#95a5a6';
            ctx.font = '12px Arial';
            ctx.fillText('其他操作等待', 200, 200);

            // InnoDB行级锁
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('InnoDB - 行级锁', 600, 30);

            // 绘制行
            for(let i = 0; i < 6; i++) {
                const color = i === 2 ? '#c0392b' : '#ecf0f1';
                const textColor = i === 2 ? 'white' : '#2c3e50';

                ctx.fillStyle = color;
                ctx.fillRect(550, 60 + i * 25, 100, 20);
                ctx.fillStyle = textColor;
                ctx.font = '10px Arial';
                ctx.fillText(i === 2 ? '锁定行 🔒' : `行 ${i + 1}`, 600, 72 + i * 25);
            }

            ctx.fillStyle = '#27ae60';
            ctx.font = '12px Arial';
            ctx.fillText('其他行可并发访问', 600, 220);
        }

        // CRUD操作动画
        function drawCrudAnimation(ctx) {
            const operations = ['SELECT', 'INSERT', 'UPDATE', 'DELETE'];
            const myisamPerf = [95, 80, 70, 75];
            const innodbPerf = [90, 85, 90, 85];

            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CRUD操作性能对比', 400, 30);

            // 绘制性能条形图
            operations.forEach((op, index) => {
                const x = 100 + index * 150;
                const y = 80;

                // 操作名称
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText(op, x + 50, y);

                // MyISAM性能条
                const myisamHeight = myisamPerf[index] * 1.5;
                ctx.fillStyle = '#f39c12';
                ctx.fillRect(x, y + 20, 40, myisamHeight);
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('MyISAM', x + 20, y + 35);
                ctx.fillText(myisamPerf[index] + '%', x + 20, y + myisamHeight + 10);

                // InnoDB性能条
                const innodbHeight = innodbPerf[index] * 1.5;
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(x + 50, y + 20, 40, innodbHeight);
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('InnoDB', x + 70, y + 35);
                ctx.fillStyle = '#2c3e50';
                ctx.fillText(innodbPerf[index] + '%', x + 70, y + innodbHeight + 10);
            });

            // 图例
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(250, 300, 20, 15);
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('MyISAM', 280, 312);

            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(350, 300, 20, 15);
            ctx.fillStyle = '#2c3e50';
            ctx.fillText('InnoDB', 380, 312);
        }

        // 存储结构动画
        function drawStorageAnimation(ctx) {
            // MyISAM存储结构
            ctx.fillStyle = '#f39c12';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM存储结构', 200, 30);

            const files = [
                { name: '.frm', desc: '表结构', color: '#3498db' },
                { name: '.MYD', desc: '数据文件', color: '#e67e22' },
                { name: '.MYI', desc: '索引文件', color: '#9b59b6' }
            ];

            files.forEach((file, index) => {
                const y = 60 + index * 80;

                ctx.fillStyle = file.color;
                ctx.fillRect(150, y, 100, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(file.name, 200, y + 25);
                ctx.font = '12px Arial';
                ctx.fillText(file.desc, 200, y + 45);
            });

            // InnoDB存储结构
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('InnoDB存储结构', 600, 30);

            // 共享表空间
            ctx.fillStyle = '#c0392b';
            ctx.fillRect(550, 60, 100, 120);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('共享表空间', 600, 80);
            ctx.fillText('ibdata1', 600, 100);
            ctx.fillText('所有表数据', 600, 120);
            ctx.fillText('索引+数据', 600, 140);
            ctx.fillText('事务日志', 600, 160);

            // 独立表空间选项
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(550, 200, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('独立表空间', 600, 220);
            ctx.fillText('.ibd文件', 600, 240);

            // 优势说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 便于单表备份', 120, 320);
            ctx.fillText('✓ 文件结构清晰', 120, 340);

            ctx.textAlign = 'right';
            ctx.fillText('✓ 空间利用率高', 680, 320);
            ctx.fillText('✓ 事务安全性好', 680, 340);
        }

        // 绘制测试界面
        function drawTestInterface() {
            const canvas = document.getElementById('testCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制欢迎界面
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('知识测试', canvas.width / 2, 100);

            ctx.font = '18px Arial';
            ctx.fillText('点击"开始知识测试"按钮开始挑战！', canvas.width / 2, 150);

            // 绘制装饰图形
            for (let i = 0; i < 5; i++) {
                const x = 100 + i * 150;
                const y = 250;

                ctx.fillStyle = `hsl(${i * 60}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.fillText('?', x, y + 7);
            }
        }

        // 开始测试
        function startQuiz() {
            currentQuiz = 0;
            quizScore = 0;
            showQuizQuestion();
        }

        // 显示测试问题
        function showQuizQuestion() {
            if (currentQuiz >= quizData.length) {
                showQuizResult();
                return;
            }

            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const question = quizData[currentQuiz];

            // 绘制问题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`问题 ${currentQuiz + 1}/${quizData.length}`, canvas.width / 2, 50);

            ctx.font = '18px Arial';
            ctx.fillText(question.question, canvas.width / 2, 100);

            // 绘制选项
            question.options.forEach((option, index) => {
                const x = canvas.width / 2 - 200;
                const y = 150 + index * 60;

                // 选项背景
                ctx.fillStyle = '#ecf0f1';
                ctx.fillRect(x, y - 20, 400, 40);

                // 选项文字
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`${String.fromCharCode(65 + index)}. ${option}`, x + 20, y + 5);
            });

            // 添加点击事件
            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                // 检查点击的选项
                for (let i = 0; i < question.options.length; i++) {
                    const optionY = 150 + i * 60;
                    if (y >= optionY - 20 && y <= optionY + 20) {
                        selectAnswer(i);
                        break;
                    }
                }
            };
        }

        // 选择答案
        function selectAnswer(selectedIndex) {
            const question = quizData[currentQuiz];
            const isCorrect = selectedIndex === question.correct;

            if (isCorrect) {
                quizScore++;
            }

            // 显示答案反馈
            showAnswerFeedback(selectedIndex, isCorrect, question.explanation);
        }

        // 显示答案反馈
        function showAnswerFeedback(selectedIndex, isCorrect, explanation) {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');

            // 高亮正确/错误答案
            const question = quizData[currentQuiz];
            question.options.forEach((option, index) => {
                const x = canvas.width / 2 - 200;
                const y = 150 + index * 60;

                let color = '#ecf0f1';
                if (index === question.correct) {
                    color = '#2ecc71'; // 正确答案绿色
                } else if (index === selectedIndex && !isCorrect) {
                    color = '#e74c3c'; // 错误选择红色
                }

                ctx.fillStyle = color;
                ctx.fillRect(x, y - 20, 400, 40);

                ctx.fillStyle = index === question.correct || (index === selectedIndex && !isCorrect) ? 'white' : '#2c3e50';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`${String.fromCharCode(65 + index)}. ${option}`, x + 20, y + 5);
            });

            // 显示解释
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(explanation, canvas.width / 2, 400);

            // 2秒后显示下一题
            setTimeout(() => {
                currentQuiz++;
                showQuizQuestion();
            }, 3000);
        }

        // 显示测试结果
        function showQuizResult() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const percentage = Math.round((quizScore / quizData.length) * 100);

            // 绘制结果
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试完成！', canvas.width / 2, 100);

            ctx.font = '24px Arial';
            ctx.fillText(`得分：${quizScore}/${quizData.length} (${percentage}%)`, canvas.width / 2, 150);

            // 根据得分显示不同的反馈
            let feedback = '';
            let color = '';
            if (percentage >= 80) {
                feedback = '优秀！你已经很好地掌握了存储引擎的知识！';
                color = '#2ecc71';
            } else if (percentage >= 60) {
                feedback = '不错！继续学习会更好！';
                color = '#f39c12';
            } else {
                feedback = '需要再复习一下哦！';
                color = '#e74c3c';
            }

            ctx.fillStyle = color;
            ctx.font = '18px Arial';
            ctx.fillText(feedback, canvas.width / 2, 200);

            // 移除点击事件
            canvas.onclick = null;

            updateProgress(100);
        }

        // 重置测试
        function resetQuiz() {
            currentQuiz = 0;
            quizScore = 0;
            drawTestInterface();
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 关闭帮助
        function closeHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
