<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络系统设计阶段学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .phase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .phase-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .phase-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .phase-card.needs {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .phase-card.logical {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .phase-card.physical {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .phase-card.implementation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .phase-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .phase-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .phase-number {
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .timeline {
            position: relative;
            margin: 30px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 40px 0;
            width: 45%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            left: 55%;
            text-align: left;
        }

        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 20px;
            height: 20px;
            background: #667eea;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 0 0 4px #667eea;
        }

        .timeline-item:nth-child(odd)::before {
            right: -60px;
        }

        .timeline-item:nth-child(even)::before {
            left: -60px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .stage-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 网络系统设计阶段学习</h1>
            <p>探索网络设计的完整流程和各阶段任务</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>网络系统设计过程中，逻辑网络设计阶段的任务是（ ）。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 依据逻辑网络设计的要求，确定设备的物理分布和运行环境
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 分析现有网络和新网络的资源分布，掌握网络的运行状态
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 根据用户需求，描述网络行为和性能
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 理解网络应该具有的功能和性能，设计出符合用户需求的网络
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 网络设计的四个阶段</h2>
            <div class="explanation">
                <p>网络系统设计是一个<span class="highlight">系统性工程</span>，需要按照科学的流程进行。就像建造一座大楼，需要先有需求分析、设计图纸、施工图纸，最后才是实际建造。</p>
            </div>
            
            <div class="phase-grid">
                <div class="phase-card needs" onclick="showPhaseDemo('needs')">
                    <div class="phase-number">1</div>
                    <span class="icon">🎯</span>
                    <h3>需求分析阶段</h3>
                    <p>理解用户需求</p>
                    <p>确定功能和性能要求</p>
                </div>
                <div class="phase-card logical" onclick="showPhaseDemo('logical')">
                    <div class="phase-number">2</div>
                    <span class="icon">🧠</span>
                    <h3>逻辑网络设计</h3>
                    <p>描述网络行为和性能</p>
                    <p>设计数据流动方式</p>
                </div>
                <div class="phase-card physical" onclick="showPhaseDemo('physical')">
                    <div class="phase-number">3</div>
                    <span class="icon">🔧</span>
                    <h3>物理网络设计</h3>
                    <p>确定设备物理分布</p>
                    <p>规划运行环境</p>
                </div>
                <div class="phase-card implementation" onclick="showPhaseDemo('implementation')">
                    <div class="phase-number">4</div>
                    <span class="icon">🚀</span>
                    <h3>实施和测试</h3>
                    <p>网络部署和配置</p>
                    <p>测试和优化</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 网络设计流程动画演示</h2>
            <div class="canvas-container">
                <canvas id="designCanvas"></canvas>
                <div class="stage-indicator" id="stageIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startFullDemo()">🎬 完整流程演示</button>
                <button class="btn" onclick="showLogicalFocus()">🧠 逻辑设计重点</button>
                <button class="btn" onclick="showComparison()">📊 阶段对比</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：依据逻辑网络设计的要求，确定设备的物理分布和运行环境 ❌</h3>
                <p><strong>错误原因：</strong>这是<span class="highlight">物理网络设计阶段</span>的任务</p>
                <ul>
                    <li>🔧 <strong>物理设计特点：</strong>关注"在哪里放置"设备</li>
                    <li>📍 <strong>具体任务：</strong>确定服务器机房位置、网络设备摆放</li>
                    <li>🌡️ <strong>环境考虑：</strong>温度、湿度、电源、安全等物理环境</li>
                    <li>📏 <strong>布线规划：</strong>网络线缆的具体走向和长度</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项B：分析现有网络和新网络的资源分布，掌握网络的运行状态 ❌</h3>
                <p><strong>错误原因：</strong>这是<span class="highlight">需求分析阶段</span>的任务</p>
                <ul>
                    <li>🎯 <strong>需求分析特点：</strong>了解现状，明确需求</li>
                    <li>📊 <strong>具体任务：</strong>调研现有网络架构和性能</li>
                    <li>📈 <strong>数据收集：</strong>网络流量、用户数量、应用需求</li>
                    <li>🔍 <strong>问题识别：</strong>发现现有网络的瓶颈和不足</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：根据用户需求，描述网络行为和性能 ✅</h3>
                <p><strong>正确答案！</strong>这正是<span class="highlight">逻辑网络设计阶段</span>的核心任务</p>
                <ul>
                    <li>🧠 <strong>逻辑设计特点：</strong>关注"如何工作"而非"在哪里放置"</li>
                    <li>📋 <strong>网络行为：</strong>描述数据如何在网络中流动</li>
                    <li>⚡ <strong>性能指标：</strong>带宽、延迟、吞吐量等要求</li>
                    <li>🔄 <strong>协议选择：</strong>确定使用的网络协议和标准</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>逻辑设计阶段产出的是<strong>逻辑网络设计文档</strong>，描述网络的功能架构，不涉及具体的物理位置。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项D：理解网络应该具有的功能和性能，设计出符合用户需求的网络 ❌</h3>
                <p><strong>错误原因：</strong>这个描述太宽泛，更像是<span class="highlight">整个设计过程</span>的总体目标</p>
                <ul>
                    <li>🎯 <strong>描述特点：</strong>涵盖了多个阶段的内容</li>
                    <li>📝 <strong>缺乏针对性：</strong>没有突出逻辑设计的特定任务</li>
                    <li>🔄 <strong>过于宽泛：</strong>可以适用于任何设计阶段</li>
                    <li>❌ <strong>不够具体：</strong>没有明确"描述网络行为"这一核心任务</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧠 逻辑网络设计深度解析</h2>

            <div class="concept-box">
                <h4>🎯 逻辑设计的核心任务</h4>
                <p><strong>主要目标：</strong>描述网络应该"如何工作"，而不是"在哪里部署"</p>
                <p><strong>关键输出：</strong>逻辑网络设计文档，包含网络架构图和性能规范</p>
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>📊 网络行为描述</h4>
                        <p>详细说明数据如何在网络中流动，包括数据路径、处理方式和传输协议</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>⚡ 性能指标定义</h4>
                        <p>确定带宽需求、延迟要求、可靠性指标和安全性要求</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>🔄 协议和标准选择</h4>
                        <p>选择合适的网络协议、通信标准和技术架构</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>📋 逻辑架构设计</h4>
                        <p>设计网络的逻辑拓扑结构，不涉及具体的物理位置</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 四个阶段详细对比</h2>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>阶段</th>
                        <th>主要任务</th>
                        <th>关注重点</th>
                        <th>输出文档</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>需求分析</strong></td>
                        <td>理解用户需求，分析现有网络</td>
                        <td>用户需要什么</td>
                        <td>需求分析报告</td>
                    </tr>
                    <tr style="background: rgba(0, 212, 170, 0.1);">
                        <td><strong>逻辑设计</strong></td>
                        <td>描述网络行为和性能</td>
                        <td>网络如何工作</td>
                        <td>逻辑设计文档</td>
                    </tr>
                    <tr>
                        <td><strong>物理设计</strong></td>
                        <td>确定设备物理分布和环境</td>
                        <td>设备放在哪里</td>
                        <td>物理设计图纸</td>
                    </tr>
                    <tr>
                        <td><strong>实施测试</strong></td>
                        <td>部署网络并测试优化</td>
                        <td>如何建设运行</td>
                        <td>实施方案和测试报告</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "需求分析问什么，逻辑设计说怎么，<br>
                    物理设计定哪里，实施测试去建设"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>需求分析：</strong>"分析"、"现有网络"、"用户需求"</p>
                <p><strong>逻辑设计：</strong>"描述"、"网络行为"、"性能"</p>
                <p><strong>物理设计：</strong>"物理分布"、"运行环境"、"设备位置"</p>
                <p><strong>实施测试：</strong>"部署"、"配置"、"测试"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"描述网络行为"、"网络性能" → <span class="highlight">逻辑设计</span></li>
                    <li>看到"物理分布"、"运行环境" → <span class="highlight">物理设计</span></li>
                    <li>看到"分析现有网络"、"用户需求" → <span class="highlight">需求分析</span></li>
                    <li>看到"部署"、"测试"、"配置" → <span class="highlight">实施测试</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">逻辑网络设计</span>：描述网络"如何工作"，不涉及"在哪里放置"</li>
                    <li><span class="highlight">主要任务</span>：根据用户需求，描述网络行为和性能</li>
                    <li><span class="highlight">关键输出</span>：逻辑网络设计文档</li>
                    <li><span class="highlight">设计重点</span>：数据流动方式、性能指标、协议选择</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>企业网络架构设计中的逻辑拓扑规划</li>
                    <li>数据中心网络的逻辑分层设计</li>
                    <li>云网络的虚拟网络架构设计</li>
                    <li>网络安全策略的逻辑架构规划</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('designCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'C') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：逻辑网络设计阶段的主要任务是根据用户需求，描述网络的行为和性能，详细说明数据如何在网络上流动，此阶段不涉及具体的物理位置。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'A':
                                hint = '这是物理网络设计阶段的任务，不是逻辑设计阶段。';
                                break;
                            case 'B':
                                hint = '这是需求分析阶段的任务，用于了解现状。';
                                break;
                            case 'D':
                                hint = '这是需求分析阶段的任务，用于理解用户需求。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：逻辑设计阶段重点是描述"如何工作"，不涉及"在哪里放置"！');
                    }, 500);
                }
            });
        });

        // 绘制阶段图标
        function drawPhaseIcon(x, y, phase, active = false, size = 40) {
            ctx.save();

            const colors = {
                'needs': '#fd79a8',
                'logical': '#00d4aa',
                'physical': '#fdcb6e',
                'implementation': '#74b9ff'
            };

            const icons = {
                'needs': '🎯',
                'logical': '🧠',
                'physical': '🔧',
                'implementation': '🚀'
            };

            // 背景圆圈
            ctx.fillStyle = active ? colors[phase] : '#bdc3c7';
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();

            if (active) {
                // 发光效果
                ctx.shadowColor = colors[phase];
                ctx.shadowBlur = 20;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // 图标
            ctx.font = `${size * 0.6}px Microsoft YaHei`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(icons[phase], x, y);

            ctx.restore();
        }

        // 绘制连接箭头
        function drawArrow(fromX, fromY, toX, toY, active = false) {
            ctx.save();

            ctx.strokeStyle = active ? '#667eea' : '#bdc3c7';
            ctx.lineWidth = active ? 4 : 2;

            // 箭头线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const headLength = 15;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(
                toX - headLength * Math.cos(angle - Math.PI / 6),
                toY - headLength * Math.sin(angle - Math.PI / 6)
            );
            ctx.moveTo(toX, toY);
            ctx.lineTo(
                toX - headLength * Math.cos(angle + Math.PI / 6),
                toY - headLength * Math.sin(angle + Math.PI / 6)
            );
            ctx.stroke();

            ctx.restore();
        }

        // 绘制文档
        function drawDocument(x, y, title, active = false) {
            ctx.save();

            const width = 80;
            const height = 100;

            // 文档背景
            ctx.fillStyle = active ? '#fff' : '#f8f9fa';
            ctx.strokeStyle = active ? '#667eea' : '#bdc3c7';
            ctx.lineWidth = 2;

            ctx.fillRect(x - width/2, y - height/2, width, height);
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 文档内容线条
            for (let i = 0; i < 5; i++) {
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(x - width/2 + 10, y - height/2 + 20 + i * 12);
                ctx.lineTo(x + width/2 - 10, y - height/2 + 20 + i * 12);
                ctx.stroke();
            }

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x, y + height/2 + 15);

            ctx.restore();
        }

        // 完整流程演示
        function startFullDemo() {
            currentDemo = 'full';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);
            animateFullProcess();
        }

        function animateFullProcess() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;
            const spacing = 150;

            const phases = ['needs', 'logical', 'physical', 'implementation'];
            const phaseNames = ['需求分析', '逻辑设计', '物理设计', '实施测试'];

            const step = Math.floor(animationStep / 60);
            const currentPhase = Math.min(step, phases.length - 1);

            // 绘制所有阶段
            phases.forEach((phase, index) => {
                const x = centerX - spacing * 1.5 + index * spacing;
                const y = centerY;
                const active = index <= currentPhase;

                drawPhaseIcon(x, y, phase, active);

                // 阶段名称
                ctx.fillStyle = active ? '#2c3e50' : '#bdc3c7';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(phaseNames[index], x, y + 70);

                // 连接箭头
                if (index < phases.length - 1) {
                    drawArrow(x + 40, y, x + spacing - 40, y, index < currentPhase);
                }
            });

            // 当前阶段说明
            const stageIndicator = document.getElementById('stageIndicator');
            if (step < phases.length) {
                const descriptions = [
                    '🎯 分析用户需求和现有网络状况',
                    '🧠 描述网络行为和性能要求',
                    '🔧 确定设备物理位置和环境',
                    '🚀 部署网络并进行测试优化'
                ];
                stageIndicator.textContent = descriptions[step];
            } else {
                stageIndicator.textContent = '✅ 网络设计流程完成！';
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateFullProcess);
        }

        // 逻辑设计重点演示
        function showLogicalFocus() {
            currentDemo = 'logical';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const stageIndicator = document.getElementById('stageIndicator');
            stageIndicator.textContent = '🧠 逻辑网络设计阶段重点';

            animateLogicalDesign();
        }

        function animateLogicalDesign() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 中心逻辑设计图标
            drawPhaseIcon(centerX, centerY, 'logical', true, 60);

            // 周围的任务
            const tasks = [
                { text: '描述网络行为', angle: 0, color: '#00d4aa' },
                { text: '定义性能指标', angle: Math.PI / 2, color: '#01a3a4' },
                { text: '设计数据流', angle: Math.PI, color: '#00b894' },
                { text: '制定协议规范', angle: 3 * Math.PI / 2, color: '#00cec9' }
            ];

            const radius = 120;
            const step = Math.floor(animationStep / 30);

            tasks.forEach((task, index) => {
                if (index <= step) {
                    const x = centerX + Math.cos(task.angle) * radius;
                    const y = centerY + Math.sin(task.angle) * radius;

                    // 任务圆圈
                    ctx.fillStyle = task.color;
                    ctx.beginPath();
                    ctx.arc(x, y, 30, 0, Math.PI * 2);
                    ctx.fill();

                    // 连接线
                    ctx.strokeStyle = task.color;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x, y);
                    ctx.stroke();

                    // 任务文字
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(task.text, x, y + 50);
                }
            });

            // 强调文字
            if (step >= tasks.length) {
                ctx.fillStyle = '#00d4aa';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('重点：描述"如何工作"，不涉及"在哪里放置"', centerX, centerY + 180);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateLogicalDesign);
        }

        // 阶段对比演示
        function showComparison() {
            currentDemo = 'comparison';
            if (animationId) cancelAnimationFrame(animationId);

            const stageIndicator = document.getElementById('stageIndicator');
            stageIndicator.textContent = '📊 各阶段任务对比';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制对比表格
            const phases = [
                { name: '需求分析', color: '#fd79a8', task: '理解用户需求' },
                { name: '逻辑设计', color: '#00d4aa', task: '描述网络行为' },
                { name: '物理设计', color: '#fdcb6e', task: '确定设备位置' },
                { name: '实施测试', color: '#74b9ff', task: '部署和优化' }
            ];

            const startY = centerY - 100;
            const rowHeight = 50;

            phases.forEach((phase, index) => {
                const y = startY + index * rowHeight;
                const highlight = phase.name === '逻辑设计';

                // 背景
                ctx.fillStyle = highlight ? 'rgba(0, 212, 170, 0.2)' : 'rgba(255, 255, 255, 0.8)';
                ctx.fillRect(centerX - 200, y - 20, 400, 40);

                if (highlight) {
                    ctx.strokeStyle = '#00d4aa';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(centerX - 200, y - 20, 400, 40);
                }

                // 阶段名称
                ctx.fillStyle = phase.color;
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText(phase.name, centerX - 180, y + 5);

                // 任务描述
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(phase.task, centerX - 50, y + 5);
            });

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('网络设计各阶段任务对比', centerX, startY - 40);
        }

        // 阶段演示
        function showPhaseDemo(phase) {
            if (phase === 'logical') {
                showLogicalFocus();
            } else {
                // 其他阶段的简单演示
                currentDemo = phase;
                if (animationId) cancelAnimationFrame(animationId);

                const stageIndicator = document.getElementById('stageIndicator');
                const descriptions = {
                    'needs': '🎯 需求分析：理解用户需求和现有网络状况',
                    'physical': '🔧 物理设计：确定设备物理位置和运行环境',
                    'implementation': '🚀 实施测试：部署网络并进行测试优化'
                };
                stageIndicator.textContent = descriptions[phase];

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2 / window.devicePixelRatio;
                const centerY = canvas.height / 2 / window.devicePixelRatio;

                drawPhaseIcon(centerX, centerY, phase, true, 80);

                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`${descriptions[phase]}`, centerX, centerY + 120);
            }
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const stageIndicator = document.getElementById('stageIndicator');
            stageIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看网络设计流程演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 网络系统设计阶段学习总结

✅ 正确答案：C - 根据用户需求，描述网络行为和性能

📚 核心概念：
• 网络设计是一个系统性工程，分为四个阶段
• 每个阶段有明确的任务和输出文档

🏗️ 四个设计阶段：
1️⃣ 需求分析阶段：
   • 任务：理解用户需求，分析现有网络
   • 重点：用户需要什么
   • 输出：需求分析报告

2️⃣ 逻辑网络设计阶段：✅
   • 任务：描述网络行为和性能
   • 重点：网络如何工作（不涉及物理位置）
   • 输出：逻辑网络设计文档

3️⃣ 物理网络设计阶段：
   • 任务：确定设备物理分布和运行环境
   • 重点：设备放在哪里
   • 输出：物理设计图纸

4️⃣ 实施和测试阶段：
   • 任务：部署网络并测试优化
   • 重点：如何建设运行
   • 输出：实施方案和测试报告

🧠 记忆技巧：
• "需求分析问什么，逻辑设计说怎么"
• "物理设计定哪里，实施测试去建设"
• 逻辑设计关注"如何工作"，不关注"在哪里放置"

⚡ 考试要点：
• 看到"描述网络行为"、"网络性能" → 逻辑设计
• 看到"物理分布"、"运行环境" → 物理设计
• 看到"分析现有网络"、"用户需求" → 需求分析
• 看到"部署"、"测试"、"配置" → 实施测试

🔑 关键理解：
逻辑网络设计阶段的核心是描述网络应该"如何工作"，
包括数据流动方式、性能指标和协议选择，
但不涉及设备的具体物理位置。

🎉 恭喜掌握网络系统设计阶段知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到网络设计学习世界！</h3>
                    <p>让我们一起探索网络系统设计的完整流程</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
