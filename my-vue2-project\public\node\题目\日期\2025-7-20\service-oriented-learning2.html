<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向服务架构 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }

        .level-indicator {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .level {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .level.operation { background: #ff6b6b; }
        .level.service { background: #4ecdc4; }
        .level.process { background: #45b7d1; }

        .level:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .animated {
            animation: pulse 2s infinite;
        }

        .score {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }

        /* 选择题样式 */
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .quiz-question {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .option.correct {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
            animation: correctPulse 0.6s ease-out;
        }

        .option.incorrect {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
            animation: incorrectShake 0.6s ease-out;
        }

        .option-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .option.correct .option-label {
            background: #4caf50;
        }

        .option.incorrect .option-label {
            background: #f44336;
        }

        .option-text {
            font-size: 1rem;
            color: #333;
        }

        .quiz-result {
            text-align: center;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            margin-top: 20px;
        }

        .result-content {
            margin-bottom: 20px;
        }

        .result-correct {
            color: #4caf50;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .result-incorrect {
            color: #f44336;
            font-size: 1.2rem;
            font-weight: bold;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">面向服务架构</h1>
            <p class="subtitle">Service-Oriented Architecture Learning</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                面向服务（Service-Oriented，SO）的开发方法将<span class="highlight">接口</span>的定义与实现进行解耦，并将跨构件的功能调用暴露出来。该方法有三个主要的抽象级别：
                <br><br>
                • 最低层的<span class="highlight">操作</span>代表单个逻辑单元的事物，包含特定的结构化接口，并且返回结构化的响应
                <br>
                • 第二层的<span class="highlight">服务</span>代表操作的逻辑分组
                <br>
                • 最高层的<span class="highlight">业务流程</span>则是为了实现特定业务目标而执行的一组长期运行的动作或者活动
            </div>

            <div class="level-indicator">
                <div class="level operation" data-level="operation">操作</div>
                <div class="level service" data-level="service">服务</div>
                <div class="level process" data-level="process">流程</div>
            </div>

            <div class="canvas-container">
                <canvas id="architectureCanvas"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">开始演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
                <button class="btn" onclick="showInteraction()">交互模式</button>
            </div>

            <div class="score" id="score">点击上方层级圆圈开始学习！</div>
        </div>

        <!-- 选择题部分 -->
        <div class="question-card" id="quizSection">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">📝 知识测试</h2>

            <div class="quiz-container" id="quizContainer">
                <div class="quiz-question" id="quizQuestion">
                    面向服务（Service-Oriented，SO）的开发方法将（ ）的定义与实现进行解耦，并将跨构件的功能调用暴露出来。该方法有三个主要的抽象级别，最低层的（ ）代表单个逻辑单元的事物，包含特定的结构化接口，并且返回结构化的响应；第二层的服务代表操作的逻辑分组；最高层的（<strong style="color: #667eea;">请作答此空</strong>）则是为了实现特定业务目标而执行的一组长期运行的动作或者活动。
                </div>

                <div class="quiz-options" id="quizOptions">
                    <div class="option" data-answer="A">
                        <span class="option-label">A</span>
                        <span class="option-text">业务规则</span>
                    </div>
                    <div class="option" data-answer="B">
                        <span class="option-label">B</span>
                        <span class="option-text">业务流程</span>
                    </div>
                    <div class="option" data-answer="C">
                        <span class="option-label">C</span>
                        <span class="option-text">数据流</span>
                    </div>
                    <div class="option" data-answer="D">
                        <span class="option-label">D</span>
                        <span class="option-text">控制流</span>
                    </div>
                </div>

                <div class="quiz-result" id="quizResult" style="display: none;">
                    <div class="result-content" id="resultContent"></div>
                    <button class="btn" onclick="resetQuiz()">重新答题</button>
                    <button class="btn" onclick="showDetailedExplanation()">详细解析</button>
                </div>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 知识点解析</h3>
            <p>点击上方的层级圆圈，观看动画演示，了解面向服务架构的三个抽象层次！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentLevel = null;
        let score = 0;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 动画状态
        let animationState = {
            time: 0,
            showOperations: false,
            showServices: false,
            showProcess: false,
            particles: []
        };

        // 粒子系统
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 绘制函数
        function drawOperation(x, y, size, alpha = 1) {
            ctx.save();
            ctx.globalAlpha = alpha;

            // 操作圆形
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();

            // 接口线条
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x - size/2, y);
            ctx.lineTo(x + size/2, y);
            ctx.stroke();

            // 标签
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('操作', x, y + 5);

            ctx.restore();
        }

        function drawService(x, y, width, height, alpha = 1) {
            ctx.save();
            ctx.globalAlpha = alpha;

            // 服务矩形
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(x - width/2, y - height/2, width, height);

            // 边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 标签
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('服务', x, y + 5);

            ctx.restore();
        }

        function drawProcess(x, y, width, height, alpha = 1) {
            ctx.save();
            ctx.globalAlpha = alpha;

            // 流程椭圆
            ctx.fillStyle = '#45b7d1';
            ctx.beginPath();
            ctx.ellipse(x, y, width/2, height/2, 0, 0, Math.PI * 2);
            ctx.fill();

            // 边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();

            // 标签
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('业务流程', x, y + 5);

            ctx.restore();
        }

        function drawConnections() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);

            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            // 连接线动画
            const offset = animationState.time * 2;
            ctx.lineDashOffset = offset;

            if (animationState.showOperations && animationState.showServices) {
                ctx.beginPath();
                ctx.moveTo(centerX - 150, centerY + 80);
                ctx.lineTo(centerX - 50, centerY + 20);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(centerX + 150, centerY + 80);
                ctx.lineTo(centerX + 50, centerY + 20);
                ctx.stroke();
            }

            if (animationState.showServices && animationState.showProcess) {
                ctx.beginPath();
                ctx.moveTo(centerX, centerY - 20);
                ctx.lineTo(centerX, centerY - 80);
                ctx.stroke();
            }

            ctx.setLineDash([]);
        }

        function animate() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);

            // 清空画布
            ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio);

            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height / window.devicePixelRatio);
            gradient.addColorStop(0, 'rgba(102, 126, 234, 0.1)');
            gradient.addColorStop(1, 'rgba(118, 75, 162, 0.1)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio);

            // 绘制连接线
            drawConnections();

            // 绘制操作层
            if (animationState.showOperations) {
                const bounce = Math.sin(animationState.time * 3) * 5;
                drawOperation(centerX - 150, centerY + 80 + bounce, 25);
                drawOperation(centerX + 150, centerY + 80 - bounce, 25);
                drawOperation(centerX, centerY + 120 + bounce, 25);
            }

            // 绘制服务层
            if (animationState.showServices) {
                const pulse = 1 + Math.sin(animationState.time * 2) * 0.1;
                drawService(centerX, centerY + 20, 120 * pulse, 40);
            }

            // 绘制流程层
            if (animationState.showProcess) {
                const rotate = animationState.time * 0.5;
                ctx.save();
                ctx.translate(centerX, centerY - 80);
                ctx.rotate(rotate);
                ctx.translate(-centerX, -centerY + 80);
                drawProcess(centerX, centerY - 80, 200, 60);
                ctx.restore();
            }

            // 更新和绘制粒子
            animationState.particles = animationState.particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            animationState.time += 0.02;
            animationId = requestAnimationFrame(animate);
        }

        // 控制函数
        function startAnimation() {
            resetAnimation();

            setTimeout(() => {
                animationState.showOperations = true;
                updateExplanation('operation');
                addParticles(canvas.width / (4 * window.devicePixelRatio), canvas.height / (2 * window.devicePixelRatio), '#ff6b6b');
            }, 500);

            setTimeout(() => {
                animationState.showServices = true;
                updateExplanation('service');
                addParticles(canvas.width / (2 * window.devicePixelRatio), canvas.height / (2 * window.devicePixelRatio), '#4ecdc4');
            }, 2000);

            setTimeout(() => {
                animationState.showProcess = true;
                updateExplanation('process');
                addParticles(canvas.width / (2 * window.devicePixelRatio), canvas.height / (4 * window.devicePixelRatio), '#45b7d1');
            }, 3500);
        }

        function resetAnimation() {
            animationState.showOperations = false;
            animationState.showServices = false;
            animationState.showProcess = false;
            animationState.particles = [];
            animationState.time = 0;
            currentLevel = null;
            updateScore();
        }

        function showInteraction() {
            resetAnimation();
            document.getElementById('score').textContent = '点击层级圆圈进行交互学习！';
            updateExplanation('interactive');
        }

        function addParticles(x, y, color) {
            for (let i = 0; i < 10; i++) {
                animationState.particles.push(new Particle(x, y, color));
            }
        }

        function updateExplanation(level) {
            const explanations = {
                operation: {
                    title: '🔧 操作层 (Operation)',
                    content: '最底层的操作代表单个逻辑单元，就像一个个小齿轮。每个操作都有明确的输入和输出，执行特定的功能，比如"读取用户信息"、"计算价格"等。'
                },
                service: {
                    title: '⚙️ 服务层 (Service)',
                    content: '服务层将相关的操作组合在一起，形成更大的功能模块。比如"用户管理服务"包含注册、登录、修改信息等操作。服务是操作的逻辑分组。'
                },
                process: {
                    title: '🔄 业务流程层 (Business Process)',
                    content: '最高层的业务流程协调多个服务，实现完整的业务目标。比如"在线购物流程"包含用户服务、商品服务、支付服务等的协调工作。'
                },
                interactive: {
                    title: '🎮 交互模式',
                    content: '现在您可以点击上方的层级圆圈，深入了解每个层次的特点和作用！每次点击都会有不同的动画效果。'
                },
                detailed: {
                    title: '📚 详细解析',
                    content: `
                        <div style="text-align: left;">
                            <h4>🎯 题目解析</h4>
                            <p><strong>正确答案：B. 业务流程</strong></p>

                            <h4>📖 知识要点</h4>
                            <p>面向服务（SO）架构有三个递进的抽象层次：</p>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(255, 107, 107, 0.1); border-radius: 8px;">
                                <strong>1️⃣ 操作层 (Operation)</strong><br>
                                • 最底层，代表单个逻辑单元<br>
                                • 有特定的结构化接口<br>
                                • 返回结构化响应<br>
                                • 例如：读取数据、计算价格
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(78, 205, 196, 0.1); border-radius: 8px;">
                                <strong>2️⃣ 服务层 (Service)</strong><br>
                                • 中间层，操作的逻辑分组<br>
                                • 将相关操作组合成功能模块<br>
                                • 例如：用户管理服务、支付服务
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(69, 183, 209, 0.1); border-radius: 8px;">
                                <strong>3️⃣ 业务流程层 (Business Process)</strong><br>
                                • 最高层，协调多个服务<br>
                                • 实现特定业务目标<br>
                                • 长期运行的动作或活动<br>
                                • 例如：在线购物流程、订单处理流程
                            </div>

                            <h4>🔍 为什么选择"业务流程"？</h4>
                            <p>题目明确提到"为了实现特定业务目标而执行的一组长期运行的动作或活动"，这正是<strong>业务流程</strong>的定义特征。业务流程是最高层的抽象，它协调和编排多个服务来完成复杂的业务目标。</p>
                        </div>
                    `
                }
            };

            const explanation = explanations[level];
            if (explanation) {
                document.getElementById('explanation').innerHTML = `
                    <h3>${explanation.title}</h3>
                    <div>${explanation.content}</div>
                `;
            }
        }

        function updateScore() {
            const completedLevels = [
                animationState.showOperations,
                animationState.showServices,
                animationState.showProcess
            ].filter(Boolean).length;

            score = completedLevels * 100;
            document.getElementById('score').textContent = `学习进度: ${completedLevels}/3 层级 (${score}分)`;
        }

        // 事件监听
        document.querySelectorAll('.level').forEach(level => {
            level.addEventListener('click', function() {
                const levelType = this.dataset.level;
                currentLevel = levelType;

                // 重置状态
                resetAnimation();

                // 添加动画效果
                this.classList.add('animated');
                setTimeout(() => this.classList.remove('animated'), 1000);

                // 显示对应层级
                switch(levelType) {
                    case 'operation':
                        animationState.showOperations = true;
                        addParticles(canvas.width / (4 * window.devicePixelRatio), canvas.height / (2 * window.devicePixelRatio), '#ff6b6b');
                        break;
                    case 'service':
                        animationState.showOperations = true;
                        animationState.showServices = true;
                        addParticles(canvas.width / (2 * window.devicePixelRatio), canvas.height / (2 * window.devicePixelRatio), '#4ecdc4');
                        break;
                    case 'process':
                        animationState.showOperations = true;
                        animationState.showServices = true;
                        animationState.showProcess = true;
                        addParticles(canvas.width / (2 * window.devicePixelRatio), canvas.height / (4 * window.devicePixelRatio), '#45b7d1');
                        break;
                }

                updateExplanation(levelType);
                updateScore();
            });
        });

        // 画布点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 添加点击粒子效果
            addParticles(x, y, '#ffffff');
        });

        // 选择题功能
        let quizAnswered = false;
        let selectedAnswer = null;

        function initQuiz() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    if (quizAnswered) return;

                    // 清除之前的选择
                    options.forEach(opt => opt.classList.remove('selected'));

                    // 选择当前选项
                    this.classList.add('selected');
                    selectedAnswer = this.dataset.answer;

                    // 延迟显示结果
                    setTimeout(() => {
                        checkAnswer();
                    }, 500);
                });
            });
        }

        function checkAnswer() {
            if (quizAnswered) return;

            quizAnswered = true;
            const correctAnswer = 'B';
            const options = document.querySelectorAll('.option');
            const resultDiv = document.getElementById('quizResult');
            const resultContent = document.getElementById('resultContent');

            // 显示正确答案
            options.forEach(option => {
                if (option.dataset.answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('incorrect');
                }
            });

            // 显示结果
            if (selectedAnswer === correctAnswer) {
                resultContent.innerHTML = `
                    <div class="result-correct">
                        🎉 恭喜答对了！
                    </div>
                    <p>正确答案是 <strong>B. 业务流程</strong></p>
                    <p>您已经掌握了面向服务架构的三层结构！</p>
                `;

                // 添加庆祝粒子效果
                addCelebrationParticles();

                // 更新分数
                score += 200;
                document.getElementById('score').textContent = `总分: ${score}分 🏆`;

            } else {
                resultContent.innerHTML = `
                    <div class="result-incorrect">
                        ❌ 答案不正确
                    </div>
                    <p>正确答案是 <strong>B. 业务流程</strong></p>
                    <p>让我们重新学习一下架构层次吧！</p>
                `;
            }

            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function resetQuiz() {
            quizAnswered = false;
            selectedAnswer = null;

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });

            document.getElementById('quizResult').style.display = 'none';
        }

        function showDetailedExplanation() {
            updateExplanation('detailed');
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        function addCelebrationParticles() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);

            // 添加多彩粒子效果
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#feca57', '#ff9ff3'];
            for (let i = 0; i < 30; i++) {
                const color = colors[Math.floor(Math.random() * colors.length)];
                animationState.particles.push(new Particle(
                    centerX + (Math.random() - 0.5) * 200,
                    centerY + (Math.random() - 0.5) * 200,
                    color
                ));
            }
        }

        // 初始化
        animate();
        updateExplanation('interactive');
        initQuiz();
    </script>
</body>
</html>
