<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件分类方法学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .intro {
            background-color: #eaf7ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e0e0e0;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #3498db;
            color: white;
        }
        .content {
            display: none;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 0 0 8px 8px;
        }
        .content.active {
            display: block;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px auto;
            display: block;
        }
        .description {
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .interaction {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>构件分类方法学习</h1>
        
        <div class="intro">
            <p>基于构件的软件开发中，构件分类方法可以归纳为三大类：关键字分类法、剖面分类法和超文本方法。通过这个交互式页面，您可以了解每种方法的特点和应用。</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab(0)">关键字分类法</div>
            <div class="tab" onclick="switchTab(1)">剖面分类法</div>
            <div class="tab" onclick="switchTab(2)">超文本方法</div>
        </div>
        
        <div class="content active" id="keyword">
            <div class="description">
                <h3>关键字分类法</h3>
                <p>根据领域分析的结果将应用领域的概念按照从抽象到具体的顺序逐次分解为树形或有向无回路图结构。</p>
            </div>
            <canvas id="keywordCanvas" width="800" height="400"></canvas>
            <div class="interaction">
                <p>交互区：点击下面的按钮查看关键字分类法的演示</p>
                <button onclick="animateKeyword()">开始演示</button>
                <button onclick="resetKeyword()">重置</button>
            </div>
        </div>
        
        <div class="content" id="facet">
            <div class="description">
                <h3>剖面分类法</h3>
                <p>利用Facet（剖面）描述构件执行的功能、被操作的数据、构件应用的语境或任意其他特征。</p>
            </div>
            <canvas id="facetCanvas" width="800" height="400"></canvas>
            <div class="interaction">
                <p>交互区：点击下面的按钮查看剖面分类法的演示</p>
                <button onclick="animateFacet()">开始演示</button>
                <button onclick="resetFacet()">重置</button>
            </div>
        </div>
        
        <div class="content" id="hypertext">
            <div class="description">
                <h3>超文本方法</h3>
                <p>基于全文检索技术，使得检索者在阅读文档过程中可以按照人类的联想思维方式任意地转到包含相关概念或构件的文档。</p>
            </div>
            <canvas id="hypertextCanvas" width="800" height="400"></canvas>
            <div class="interaction">
                <p>交互区：点击下面的按钮查看超文本方法的演示</p>
                <button onclick="animateHypertext()">开始演示</button>
                <button onclick="resetHypertext()">重置</button>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换功能
        function switchTab(index) {
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.remove('active'));
            
            tabs[index].classList.add('active');
            contents[index].classList.add('active');
        }
        
        // 关键字分类法动画
        const keywordCanvas = document.getElementById('keywordCanvas');
        const keywordCtx = keywordCanvas.getContext('2d');
        let keywordAnimationId;
        
        function resetKeyword() {
            if (keywordAnimationId) {
                cancelAnimationFrame(keywordAnimationId);
            }
            keywordCtx.clearRect(0, 0, keywordCanvas.width, keywordCanvas.height);
            drawKeywordInitial();
        }
        
        function drawKeywordInitial() {
            keywordCtx.fillStyle = '#2c3e50';
            keywordCtx.font = 'bold 16px Microsoft YaHei';
            keywordCtx.fillText('关键字分类法示例 - 软件构件分类', 20, 30);
            
            keywordCtx.fillStyle = '#3498db';
            keywordCtx.fillRect(350, 60, 100, 40);
            keywordCtx.fillStyle = 'white';
            keywordCtx.fillText('软件构件', 365, 85);
        }
        
        function animateKeyword() {
            resetKeyword();
            
            let step = 0;
            const maxStep = 100;
            
            function animate() {
                step++;
                
                if (step === 20) {
                    // 绘制第一级分类
                    keywordCtx.beginPath();
                    keywordCtx.moveTo(400, 100);
                    keywordCtx.lineTo(400, 130);
                    keywordCtx.lineTo(200, 130);
                    keywordCtx.lineTo(600, 130);
                    keywordCtx.strokeStyle = '#3498db';
                    keywordCtx.stroke();
                    
                    keywordCtx.fillStyle = '#16a085';
                    keywordCtx.fillRect(150, 150, 100, 40);
                    keywordCtx.fillRect(350, 150, 100, 40);
                    keywordCtx.fillRect(550, 150, 100, 40);
                    
                    keywordCtx.fillStyle = 'white';
                    keywordCtx.fillText('UI构件', 175, 175);
                    keywordCtx.fillText('业务构件', 365, 175);
                    keywordCtx.fillText('数据构件', 565, 175);
                }
                
                if (step === 40) {
                    // 绘制第二级分类
                    keywordCtx.beginPath();
                    keywordCtx.moveTo(200, 190);
                    keywordCtx.lineTo(200, 220);
                    keywordCtx.lineTo(150, 220);
                    keywordCtx.lineTo(250, 220);
                    keywordCtx.stroke();
                    
                    keywordCtx.fillStyle = '#e74c3c';
                    keywordCtx.fillRect(100, 240, 100, 30);
                    keywordCtx.fillRect(200, 240, 100, 30);
                    
                    keywordCtx.fillStyle = 'white';
                    keywordCtx.fillText('表单', 130, 260);
                    keywordCtx.fillText('报表', 230, 260);
                    
                    // 添加说明文字
                    keywordCtx.fillStyle = '#333';
                    keywordCtx.font = '14px Microsoft YaHei';
                    keywordCtx.fillText('按照从抽象到具体的层次结构进行分类', 500, 300);
                }
                
                if (step === 60) {
                    // 高亮显示路径
                    keywordCtx.strokeStyle = '#e74c3c';
                    keywordCtx.lineWidth = 2;
                    keywordCtx.beginPath();
                    keywordCtx.moveTo(400, 80);
                    keywordCtx.lineTo(400, 130);
                    keywordCtx.lineTo(200, 130);
                    keywordCtx.lineTo(200, 175);
                    keywordCtx.lineTo(200, 220);
                    keywordCtx.lineTo(150, 220);
                    keywordCtx.lineTo(150, 240);
                    keywordCtx.stroke();
                    keywordCtx.lineWidth = 1;
                    
                    keywordCtx.fillStyle = '#333';
                    keywordCtx.fillText('构件查找路径示例', 100, 300);
                }
                
                if (step < maxStep) {
                    keywordAnimationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }
        
        // 剖面分类法动画
        const facetCanvas = document.getElementById('facetCanvas');
        const facetCtx = facetCanvas.getContext('2d');
        let facetAnimationId;
        
        function resetFacet() {
            if (facetAnimationId) {
                cancelAnimationFrame(facetAnimationId);
            }
            facetCtx.clearRect(0, 0, facetCanvas.width, facetCanvas.height);
            drawFacetInitial();
        }
        
        function drawFacetInitial() {
            facetCtx.fillStyle = '#2c3e50';
            facetCtx.font = 'bold 16px Microsoft YaHei';
            facetCtx.fillText('剖面分类法示例', 20, 30);
        }
        
        function animateFacet() {
            resetFacet();
            
            let step = 0;
            const maxStep = 100;
            
            function animate() {
                step++;
                
                if (step === 10) {
                    // 绘制中心构件
                    facetCtx.fillStyle = '#3498db';
                    facetCtx.fillRect(350, 180, 100, 40);
                    facetCtx.fillStyle = 'white';
                    facetCtx.fillText('付款构件', 365, 205);
                }
                
                if (step === 30) {
                    // 绘制功能剖面
                    facetCtx.beginPath();
                    facetCtx.moveTo(400, 180);
                    facetCtx.lineTo(400, 130);
                    facetCtx.strokeStyle = '#16a085';
                    facetCtx.stroke();
                    
                    facetCtx.fillStyle = '#16a085';
                    facetCtx.fillRect(350, 80, 100, 40);
                    facetCtx.fillStyle = 'white';
                    facetCtx.fillText('功能剖面', 365, 105);
                    
                    // 添加功能项
                    facetCtx.fillStyle = '#333';
                    facetCtx.font = '14px Microsoft YaHei';
                    facetCtx.fillText('- 支付处理', 460, 90);
                    facetCtx.fillText('- 交易验证', 460, 110);
                }
                
                if (step === 50) {
                    // 绘制数据剖面
                    facetCtx.beginPath();
                    facetCtx.moveTo(350, 200);
                    facetCtx.lineTo(250, 200);
                    facetCtx.strokeStyle = '#e74c3c';
                    facetCtx.stroke();
                    
                    facetCtx.fillStyle = '#e74c3c';
                    facetCtx.fillRect(150, 180, 100, 40);
                    facetCtx.fillStyle = 'white';
                    facetCtx.fillText('数据剖面', 165, 205);
                    
                    // 添加数据项
                    facetCtx.fillStyle = '#333';
                    facetCtx.font = '14px Microsoft YaHei';
                    facetCtx.fillText('- 账户信息', 50, 190);
                    facetCtx.fillText('- 支付金额', 50, 210);
                }
                
                if (step === 70) {
                    // 绘制环境剖面
                    facetCtx.beginPath();
                    facetCtx.moveTo(400, 220);
                    facetCtx.lineTo(400, 270);
                    facetCtx.strokeStyle = '#9b59b6';
                    facetCtx.stroke();
                    
                    facetCtx.fillStyle = '#9b59b6';
                    facetCtx.fillRect(350, 280, 100, 40);
                    facetCtx.fillStyle = 'white';
                    facetCtx.fillText('环境剖面', 365, 305);
                    
                    // 添加环境项
                    facetCtx.fillStyle = '#333';
                    facetCtx.font = '14px Microsoft YaHei';
                    facetCtx.fillText('- Web环境', 460, 290);
                    facetCtx.fillText('- 安全环境', 460, 310);
                }
                
                if (step === 90) {
                    // 添加说明文字
                    facetCtx.fillStyle = '#2c3e50';
                    facetCtx.font = '15px Microsoft YaHei';
                    facetCtx.fillText('剖面分类法从多个维度描述构件特征，便于灵活检索', 200, 350);
                }
                
                if (step < maxStep) {
                    facetAnimationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }
        
        // 超文本方法动画
        const hypertextCanvas = document.getElementById('hypertextCanvas');
        const hypertextCtx = hypertextCanvas.getContext('2d');
        let hypertextAnimationId;
        
        function resetHypertext() {
            if (hypertextAnimationId) {
                cancelAnimationFrame(hypertextAnimationId);
            }
            hypertextCtx.clearRect(0, 0, hypertextCanvas.width, hypertextCanvas.height);
            drawHypertextInitial();
        }
        
        function drawHypertextInitial() {
            hypertextCtx.fillStyle = '#2c3e50';
            hypertextCtx.font = 'bold 16px Microsoft YaHei';
            hypertextCtx.fillText('超文本方法示例', 20, 30);
            
            // 绘制文档框
            drawDocument(100, 100, '用户认证文档', '#3498db');
        }
        
        function drawDocument(x, y, title, color) {
            hypertextCtx.fillStyle = color;
            hypertextCtx.fillRect(x, y, 150, 80);
            hypertextCtx.fillStyle = 'white';
            hypertextCtx.fillText(title, x + 20, y + 30);
            
            // 绘制文档内容线条
            hypertextCtx.fillStyle = 'rgba(255,255,255,0.7)';
            hypertextCtx.fillRect(x + 10, y + 45, 130, 3);
            hypertextCtx.fillRect(x + 10, y + 55, 130, 3);
            hypertextCtx.fillRect(x + 10, y + 65, 80, 3);
        }
        
        function animateHypertext() {
            resetHypertext();
            
            let step = 0;
            const maxStep = 120;
            
            function animate() {
                step++;
                
                if (step === 10) {
                    // 绘制初始文档
                    drawDocument(100, 100, '用户认证文档', '#3498db');
                }
                
                if (step === 30) {
                    // 高亮关键词
                    hypertextCtx.fillStyle = '#e74c3c';
                    hypertextCtx.fillRect(130, 155, 60, 20);
                    hypertextCtx.fillStyle = 'white';
                    hypertextCtx.font = '14px Microsoft YaHei';
                    hypertextCtx.fillText('安全策略', 135, 170);
                    
                    // 绘制光标
                    hypertextCtx.fillStyle = '#333';
                    hypertextCtx.fillText('👆', 160, 145);
                }
                
                if (step === 50) {
                    // 绘制连接线
                    hypertextCtx.beginPath();
                    hypertextCtx.moveTo(160, 175);
                    hypertextCtx.lineTo(160, 220);
                    hypertextCtx.lineTo(320, 220);
                    hypertextCtx.lineTo(320, 250);
                    hypertextCtx.strokeStyle = '#e74c3c';
                    hypertextCtx.stroke();
                    
                    // 绘制相关文档
                    drawDocument(250, 250, '安全策略文档', '#e74c3c');
                }
                
                if (step === 70) {
                    // 高亮另一个关键词
                    hypertextCtx.fillStyle = '#16a085';
                    hypertextCtx.fillRect(270, 305, 60, 20);
                    hypertextCtx.fillStyle = 'white';
                    hypertextCtx.font = '14px Microsoft YaHei';
                    hypertextCtx.fillText('加密组件', 275, 320);
                    
                    // 绘制光标
                    hypertextCtx.fillStyle = '#333';
                    hypertextCtx.fillText('👆', 300, 295);
                }
                
                if (step === 90) {
                    // 绘制另一个连接线
                    hypertextCtx.beginPath();
                    hypertextCtx.moveTo(300, 320);
                    hypertextCtx.lineTo(300, 350);
                    hypertextCtx.lineTo(500, 350);
                    hypertextCtx.lineTo(500, 250);
                    hypertextCtx.strokeStyle = '#16a085';
                    hypertextCtx.stroke();
                    
                    // 绘制第三个文档
                    drawDocument(450, 170, '加密组件文档', '#16a085');
                }
                
                if (step === 110) {
                    // 添加说明文字
                    hypertextCtx.fillStyle = '#2c3e50';
                    hypertextCtx.font = '15px Microsoft YaHei';
                    hypertextCtx.fillText('超文本方法通过关联链接实现人类联想式思维导航', 200, 380);
                }
                
                if (step < maxStep) {
                    hypertextAnimationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }
        
        // 初始化绘制
        window.onload = function() {
            drawKeywordInitial();
            drawFacetInitial();
            drawHypertextInitial();
        };
    </script>
</body>
</html> 