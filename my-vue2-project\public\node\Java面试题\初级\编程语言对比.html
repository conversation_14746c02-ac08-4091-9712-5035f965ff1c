<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java与C++对比 | JDK对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #4a69bd, #1e3799);
            color: white;
            text-align: center;
            padding: 30px 0;
            border-radius: 8px 8px 0 0;
        }
        
        h1 {
            margin: 0;
            font-size: 2.2em;
        }
        
        .tab-container {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .tabs {
            display: flex;
            background-color: #eee;
        }
        
        .tab {
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-align: center;
            flex: 1;
            font-weight: bold;
        }
        
        .tab.active {
            background-color: white;
            border-bottom: 3px solid #4a69bd;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .comparison-item {
            margin-bottom: 30px;
            border-left: 4px solid #4a69bd;
            padding-left: 15px;
        }
        
        .animation-container {
            width: 100%;
            height: 300px;
            position: relative;
            margin: 20px 0;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            border-radius: 8px;
            overflow: hidden;
        }
        
        canvas {
            display: block;
        }
        
        .button-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;
        }
        
        button {
            background-color: #4a69bd;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #1e3799;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            margin-top: 0;
            color: #4a69bd;
        }
        
        .java {
            background-color: #f8981d;
            color: white;
        }
        
        .cpp {
            background-color: #659ad2;
            color: white;
        }
        
        .oracle {
            background-color: #f80000;
            color: white;
        }
        
        .open {
            background-color: #5382a1;
            color: white;
        }
        
        .lang-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            margin-right: 5px;
        }
        
        .info-box {
            margin: 15px 0;
            padding: 15px;
            background-color: #e8f4fd;
            border-left: 4px solid #4a69bd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Java与C++对比 | JDK对比</h1>
            <p>通过动画和交互式演示，轻松理解编程语言和JDK的区别</p>
        </header>
        
        <div class="tab-container">
            <div class="tabs">
                <div class="tab active" data-tab="java-cpp">Java与C++对比</div>
                <div class="tab" data-tab="jdk-compare">Oracle JDK与OpenJDK对比</div>
            </div>
            
            <div id="java-cpp" class="tab-content active">
                <h2>Java与C++的主要区别</h2>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>内存管理机制</h3>
                        <p><span class="lang-badge java">Java</span> 自动垃圾回收，无需手动释放内存</p>
                        <p><span class="lang-badge cpp">C++</span> 需要程序员手动管理内存</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>指针操作</h3>
                        <p><span class="lang-badge java">Java</span> 没有指针，更加安全</p>
                        <p><span class="lang-badge cpp">C++</span> 提供指针直接访问内存</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>继承方式</h3>
                        <p><span class="lang-badge java">Java</span> 类单继承，接口可多继承</p>
                        <p><span class="lang-badge cpp">C++</span> 支持类多重继承</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>面向对象特性</h3>
                        <p><span class="lang-badge java">Java</span> 封装、继承、多态</p>
                        <p><span class="lang-badge cpp">C++</span> 封装、继承、多态</p>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h3>内存管理动画演示</h3>
                    <div class="animation-container">
                        <canvas id="memoryCanvas" width="900" height="300"></canvas>
                    </div>
                    <div class="button-container">
                        <button id="showJavaMemory">Java内存管理</button>
                        <button id="showCppMemory">C++内存管理</button>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h3>继承模型演示</h3>
                    <div class="animation-container">
                        <canvas id="inheritanceCanvas" width="900" height="300"></canvas>
                    </div>
                    <div class="button-container">
                        <button id="showJavaInheritance">Java继承模型</button>
                        <button id="showCppInheritance">C++继承模型</button>
                    </div>
                </div>
            </div>
            
            <div id="jdk-compare" class="tab-content">
                <h2>Oracle JDK与OpenJDK对比</h2>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>发布周期</h3>
                        <p><span class="lang-badge oracle">Oracle JDK</span> 每三年发布一次</p>
                        <p><span class="lang-badge open">OpenJDK</span> 每三个月发布一次</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>开源程度</h3>
                        <p><span class="lang-badge oracle">Oracle JDK</span> 不是完全开源的</p>
                        <p><span class="lang-badge open">OpenJDK</span> 完全开源</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>稳定性与性能</h3>
                        <p><span class="lang-badge oracle">Oracle JDK</span> 更稳定，性能更好</p>
                        <p><span class="lang-badge open">OpenJDK</span> 可能存在一些稳定性问题</p>
                    </div>
                    
                    <div class="feature-card">
                        <h3>许可协议</h3>
                        <p><span class="lang-badge oracle">Oracle JDK</span> 二进制代码许可协议</p>
                        <p><span class="lang-badge open">OpenJDK</span> GPL v2许可</p>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h3>JDK发布周期动画</h3>
                    <div class="animation-container">
                        <canvas id="releaseCanvas" width="900" height="300"></canvas>
                    </div>
                    <div class="button-container">
                        <button id="showReleases">展示发布周期</button>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h3>性能对比模拟</h3>
                    <div class="animation-container">
                        <canvas id="performanceCanvas" width="900" height="300"></canvas>
                    </div>
                    <div class="button-container">
                        <button id="startPerformanceDemo">开始性能演示</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // 激活标签
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 内存管理动画
        const memoryCanvas = document.getElementById('memoryCanvas');
        const memoryCtx = memoryCanvas.getContext('2d');
        
        function clearCanvas(canvas, ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#f9f9f9';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        function showJavaMemory() {
            clearCanvas(memoryCanvas, memoryCtx);
            
            // 绘制Java内存模型
            memoryCtx.fillStyle = '#e8f4fd';
            memoryCtx.fillRect(50, 30, 800, 240);
            
            // JVM部分
            memoryCtx.fillStyle = '#f8981d';
            memoryCtx.fillRect(100, 60, 700, 50);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.font = '18px Arial';
            memoryCtx.textAlign = 'center';
            memoryCtx.fillText('Java虚拟机 (JVM)', 450, 90);
            
            // 堆内存
            memoryCtx.fillStyle = '#5382a1';
            memoryCtx.fillRect(100, 130, 300, 100);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.fillText('堆内存', 250, 180);
            
            // 对象
            const objects = [
                {x: 120, y: 150, w: 40, h: 40, color: '#ffcc00'},
                {x: 180, y: 160, w: 50, h: 30, color: '#ff6b6b'},
                {x: 250, y: 140, w: 60, h: 45, color: '#48dbfb'},
                {x: 330, y: 165, w: 45, h: 45, color: '#1dd1a1'}
            ];
            
            objects.forEach(obj => {
                memoryCtx.fillStyle = obj.color;
                memoryCtx.fillRect(obj.x, obj.y, obj.w, obj.h);
            });
            
            // 垃圾回收器
            memoryCtx.fillStyle = '#10ac84';
            memoryCtx.fillRect(500, 130, 200, 100);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.fillText('垃圾回收器', 600, 180);
            
            // 箭头
            memoryCtx.strokeStyle = '#333';
            memoryCtx.lineWidth = 2;
            memoryCtx.beginPath();
            memoryCtx.moveTo(410, 180);
            memoryCtx.lineTo(490, 180);
            memoryCtx.stroke();
            
            // 箭头头部
            memoryCtx.beginPath();
            memoryCtx.moveTo(490, 180);
            memoryCtx.lineTo(480, 175);
            memoryCtx.lineTo(480, 185);
            memoryCtx.fill();
            
            // 文字说明
            memoryCtx.fillStyle = '#333';
            memoryCtx.textAlign = 'left';
            memoryCtx.font = '16px Arial';
            memoryCtx.fillText('Java: 自动垃圾回收 - 系统自动识别并清理不再使用的对象', 100, 260);
        }
        
        function showCppMemory() {
            clearCanvas(memoryCanvas, memoryCtx);
            
            // 绘制C++内存模型
            memoryCtx.fillStyle = '#e8f4fd';
            memoryCtx.fillRect(50, 30, 800, 240);
            
            // 程序内存
            memoryCtx.fillStyle = '#659ad2';
            memoryCtx.fillRect(100, 60, 700, 50);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.font = '18px Arial';
            memoryCtx.textAlign = 'center';
            memoryCtx.fillText('C++程序内存空间', 450, 90);
            
            // 栈内存
            memoryCtx.fillStyle = '#5382a1';
            memoryCtx.fillRect(100, 130, 200, 100);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.fillText('栈内存', 200, 180);
            
            // 堆内存
            memoryCtx.fillStyle = '#f8981d';
            memoryCtx.fillRect(350, 130, 200, 100);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.fillText('堆内存', 450, 180);
            
            // 指针
            memoryCtx.strokeStyle = '#ff6b6b';
            memoryCtx.lineWidth = 2;
            memoryCtx.beginPath();
            memoryCtx.moveTo(150, 170);
            memoryCtx.lineTo(400, 150);
            memoryCtx.stroke();
            
            // 指针头部
            memoryCtx.fillStyle = '#ff6b6b';
            memoryCtx.beginPath();
            memoryCtx.moveTo(400, 150);
            memoryCtx.lineTo(390, 145);
            memoryCtx.lineTo(390, 155);
            memoryCtx.fill();
            
            // 删除操作
            memoryCtx.fillStyle = '#ff4757';
            memoryCtx.fillRect(600, 130, 200, 100);
            memoryCtx.fillStyle = '#fff';
            memoryCtx.fillText('delete / free()', 700, 180);
            
            // 箭头
            memoryCtx.strokeStyle = '#333';
            memoryCtx.beginPath();
            memoryCtx.moveTo(560, 180);
            memoryCtx.lineTo(590, 180);
            memoryCtx.stroke();
            
            // 箭头头部
            memoryCtx.fillStyle = '#333';
            memoryCtx.beginPath();
            memoryCtx.moveTo(590, 180);
            memoryCtx.lineTo(580, 175);
            memoryCtx.lineTo(580, 185);
            memoryCtx.fill();
            
            // 文字说明
            memoryCtx.fillStyle = '#333';
            memoryCtx.textAlign = 'left';
            memoryCtx.font = '16px Arial';
            memoryCtx.fillText('C++: 手动内存管理 - 程序员需要通过delete/free()手动释放不再使用的内存', 100, 260);
        }
        
        document.getElementById('showJavaMemory').addEventListener('click', showJavaMemory);
        document.getElementById('showCppMemory').addEventListener('click', showCppMemory);
        
        // 继承模型动画
        const inheritanceCanvas = document.getElementById('inheritanceCanvas');
        const inheritanceCtx = inheritanceCanvas.getContext('2d');
        
        function drawClass(ctx, x, y, width, height, name, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            
            ctx.fillStyle = '#fff';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.font = '16px Arial';
            ctx.fillText(name, x + width/2, y + height/2);
        }
        
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            const headLen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            ctx.fillStyle = '#333';
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(
                toX - headLen * Math.cos(angle - Math.PI/6),
                toY - headLen * Math.sin(angle - Math.PI/6)
            );
            ctx.lineTo(
                toX - headLen * Math.cos(angle + Math.PI/6),
                toY - headLen * Math.sin(angle + Math.PI/6)
            );
            ctx.closePath();
            ctx.fill();
        }
        
        function showJavaInheritance() {
            clearCanvas(inheritanceCanvas, inheritanceCtx);
            
            // Java标题
            inheritanceCtx.fillStyle = '#333';
            inheritanceCtx.font = '18px Arial';
            inheritanceCtx.textAlign = 'center';
            inheritanceCtx.fillText('Java: 类单继承，接口可多继承', 450, 30);
            
            // 父类
            drawClass(inheritanceCtx, 350, 60, 200, 50, '父类 (Parent)', '#f8981d');
            
            // 子类
            drawClass(inheritanceCtx, 350, 160, 200, 50, '子类 (Child)', '#5382a1');
            
            // 继承箭头
            drawArrow(inheritanceCtx, 450, 160, 450, 110);
            
            // 接口
            drawClass(inheritanceCtx, 100, 60, 180, 50, '接口1 (Interface1)', '#1dd1a1');
            drawClass(inheritanceCtx, 600, 60, 180, 50, '接口2 (Interface2)', '#1dd1a1');
            
            // 实现箭头
            drawArrow(inheritanceCtx, 350, 170, 200, 90);
            drawArrow(inheritanceCtx, 550, 170, 650, 90);
            
            // 文字说明
            inheritanceCtx.fillStyle = '#333';
            inheritanceCtx.textAlign = 'left';
            inheritanceCtx.font = '16px Arial';
            inheritanceCtx.fillText('Java中，一个类只能继承一个父类，但可以实现多个接口', 200, 240);
        }
        
        function showCppInheritance() {
            clearCanvas(inheritanceCanvas, inheritanceCtx);
            
            // C++标题
            inheritanceCtx.fillStyle = '#333';
            inheritanceCtx.font = '18px Arial';
            inheritanceCtx.textAlign = 'center';
            inheritanceCtx.fillText('C++: 支持类多重继承', 450, 30);
            
            // 父类
            drawClass(inheritanceCtx, 200, 60, 150, 50, '父类A', '#659ad2');
            drawClass(inheritanceCtx, 400, 60, 150, 50, '父类B', '#659ad2');
            drawClass(inheritanceCtx, 600, 60, 150, 50, '父类C', '#659ad2');
            
            // 子类
            drawClass(inheritanceCtx, 400, 180, 150, 50, '子类', '#ff6b6b');
            
            // 继承箭头
            drawArrow(inheritanceCtx, 400, 180, 275, 110);
            drawArrow(inheritanceCtx, 450, 180, 450, 110);
            drawArrow(inheritanceCtx, 500, 180, 600, 110);
            
            // 文字说明
            inheritanceCtx.fillStyle = '#333';
            inheritanceCtx.textAlign = 'left';
            inheritanceCtx.font = '16px Arial';
            inheritanceCtx.fillText('C++中，一个类可以同时继承多个父类，称为多重继承', 200, 240);
        }
        
        document.getElementById('showJavaInheritance').addEventListener('click', showJavaInheritance);
        document.getElementById('showCppInheritance').addEventListener('click', showCppInheritance);
        
        // JDK发布周期动画
        const releaseCanvas = document.getElementById('releaseCanvas');
        const releaseCtx = releaseCanvas.getContext('2d');
        
        function showReleases() {
            clearCanvas(releaseCanvas, releaseCtx);
            
            // 时间轴
            releaseCtx.strokeStyle = '#333';
            releaseCtx.lineWidth = 2;
            releaseCtx.beginPath();
            releaseCtx.moveTo(100, 150);
            releaseCtx.lineTo(800, 150);
            releaseCtx.stroke();
            
            // 箭头头部
            releaseCtx.fillStyle = '#333';
            releaseCtx.beginPath();
            releaseCtx.moveTo(800, 150);
            releaseCtx.lineTo(790, 145);
            releaseCtx.lineTo(790, 155);
            releaseCtx.closePath();
            releaseCtx.fill();
            
            // Oracle JDK 发布点
            const oraclePoints = [100, 200, 300, 400, 500, 600, 700];
            oraclePoints.forEach((x, i) => {
                releaseCtx.fillStyle = '#f80000';
                releaseCtx.beginPath();
                releaseCtx.arc(x, 110, 8, 0, 2 * Math.PI);
                releaseCtx.fill();
                
                releaseCtx.fillStyle = '#333';
                releaseCtx.textAlign = 'center';
                releaseCtx.fillText(`第${i+1}年`, x, 90);
            });
            
            // OpenJDK 发布点
            const openPoints = [];
            for(let i = 0; i <= 24; i++) {
                openPoints.push(100 + i * 25);
            }
            
            openPoints.forEach((x, i) => {
                releaseCtx.fillStyle = '#5382a1';
                releaseCtx.beginPath();
                releaseCtx.arc(x, 190, 5, 0, 2 * Math.PI);
                releaseCtx.fill();
                
                if(i % 4 === 0) {
                    releaseCtx.fillStyle = '#333';
                    releaseCtx.textAlign = 'center';
                    releaseCtx.fillText(`第${i/4+1}年`, x, 210);
                }
            });
            
            // 标签
            releaseCtx.fillStyle = '#f80000';
            releaseCtx.textAlign = 'left';
            releaseCtx.fillText('Oracle JDK: 每三年一个版本', 100, 60);
            
            releaseCtx.fillStyle = '#5382a1';
            releaseCtx.fillText('OpenJDK: 每三个月一个版本', 100, 240);
            
            // 时间单位
            releaseCtx.fillStyle = '#333';
            releaseCtx.fillText('时间 →', 750, 170);
        }
        
        document.getElementById('showReleases').addEventListener('click', showReleases);
        
        // 性能对比动画
        const performanceCanvas = document.getElementById('performanceCanvas');
        const perfCtx = performanceCanvas.getContext('2d');
        
        let animFrameId = null;
        
        function startPerformanceDemo() {
            if (animFrameId) {
                cancelAnimationFrame(animFrameId);
            }
            
            clearCanvas(performanceCanvas, perfCtx);
            
            let oraclePos = 0;
            let openPos = 0;
            let oracleSpeed = 5;
            let openSpeed = 4.2;
            let frame = 0;
            let maxFrame = 150;
            
            function animatePerformance() {
                frame++;
                if(frame > maxFrame) {
                    return;
                }
                
                // 清除画布
                clearCanvas(performanceCanvas, perfCtx);
                
                // 绘制赛道
                perfCtx.fillStyle = '#eee';
                perfCtx.fillRect(50, 100, 700, 50);
                perfCtx.fillRect(50, 200, 700, 50);
                
                // 更新位置
                oraclePos += oracleSpeed;
                openPos += openSpeed;
                
                // 绘制Oracle JDK图标
                perfCtx.fillStyle = '#f80000';
                perfCtx.beginPath();
                perfCtx.arc(50 + oraclePos, 125, 20, 0, 2 * Math.PI);
                perfCtx.fill();
                perfCtx.fillStyle = '#fff';
                perfCtx.textAlign = 'center';
                perfCtx.fillText('O', 50 + oraclePos, 125);
                
                // 绘制OpenJDK图标
                perfCtx.fillStyle = '#5382a1';
                perfCtx.beginPath();
                perfCtx.arc(50 + openPos, 225, 20, 0, 2 * Math.PI);
                perfCtx.fill();
                perfCtx.fillStyle = '#fff';
                perfCtx.textAlign = 'center';
                perfCtx.fillText('O', 50 + openPos, 225);
                
                // 赛道标签
                perfCtx.fillStyle = '#333';
                perfCtx.textAlign = 'left';
                perfCtx.fillText('Oracle JDK', 760, 125);
                perfCtx.fillText('OpenJDK', 760, 225);
                
                // 性能指示器
                perfCtx.fillStyle = '#333';
                perfCtx.textAlign = 'center';
                perfCtx.fillText('性能对比演示 (Oracle JDK通常有更好的性能)', 450, 30);
                
                animFrameId = requestAnimationFrame(animatePerformance);
            }
            
            animFrameId = requestAnimationFrame(animatePerformance);
        }
        
        document.getElementById('startPerformanceDemo').addEventListener('click', startPerformanceDemo);
        
        // 初始显示
        showJavaMemory();
        showJavaInheritance();
    </script>
</body>
</html> 