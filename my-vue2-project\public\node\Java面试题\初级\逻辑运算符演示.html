<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逻辑运算符的区别</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .section {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .demo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .description {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }
        .condition {
            display: inline-block;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px;
            font-weight: bold;
        }
        .true {
            background-color: #2ecc71;
            color: white;
        }
        .false {
            background-color: #e74c3c;
            color: white;
        }
        .explanation {
            margin-top: 20px;
            line-height: 1.6;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>逻辑运算符的区别演示</h1>
    
    <div class="section">
        <h2>& 和 && 的区别</h2>
        <div class="description">
            <p><strong>用法：</strong>&amp; 和 &amp;&amp; 都表示"与"操作，只有当所有条件都为true时，结果才为true。</p>
            <p><strong>区别：</strong>&amp;&amp; 是短路运算符，如果第一个条件为false，后面的条件就不再判断。而 &amp; 会对所有条件都进行判断。</p>
        </div>
        
        <div class="demo-container">
            <canvas id="andCanvas" width="800" height="300"></canvas>
            <div class="controls">
                <button id="andDemo1">演示: true && true</button>
                <button id="andDemo2">演示: false && true</button>
                <button id="andDemo3">演示: true & true</button>
                <button id="andDemo4">演示: false & true</button>
            </div>
        </div>
        
        <div class="explanation">
            <p>在实际编程中：</p>
            <p>- <span class="highlight">&amp;&amp;</span> 常用于条件判断，可以提高效率，因为它会短路</p>
            <p>- <span class="highlight">&amp;</span> 常用于位操作，或需要强制计算所有表达式时使用</p>
        </div>
    </div>
    
    <div class="section">
        <h2>| 和 || 的区别</h2>
        <div class="description">
            <p><strong>用法：</strong>| 和 || 都表示"或"操作，只要有一个条件为true，结果就为true。</p>
            <p><strong>区别：</strong>|| 是短路运算符，如果第一个条件为true，后面的条件就不再判断。而 | 会对所有条件都进行判断。</p>
        </div>
        
        <div class="demo-container">
            <canvas id="orCanvas" width="800" height="300"></canvas>
            <div class="controls">
                <button id="orDemo1">演示: true || false</button>
                <button id="orDemo2">演示: false || true</button>
                <button id="orDemo3">演示: true | false</button>
                <button id="orDemo4">演示: false | true</button>
            </div>
        </div>
        
        <div class="explanation">
            <p>在实际编程中：</p>
            <p>- <span class="highlight">||</span> 常用于条件判断和设置默认值，因为它会短路</p>
            <p>- <span class="highlight">|</span> 常用于位操作，或需要强制计算所有表达式时使用</p>
        </div>
    </div>

    <script>
        // 通用绘制函数
        function drawBox(ctx, x, y, width, height, text, color, isActive = true) {
            ctx.fillStyle = isActive ? color : '#aaaaaa';
            ctx.fillRect(x, y, width, height);
            
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            ctx.fillStyle = '#fff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width/2, y + height/2);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#333') {
            const headLength = 15;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            // 绘制线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI/6), toY - headLength * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI/6), toY - headLength * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        function drawText(ctx, x, y, text, color = '#000') {
            ctx.fillStyle = color;
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function clearCanvas(canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // AND 演示
        const andCanvas = document.getElementById('andCanvas');
        const andCtx = andCanvas.getContext('2d');

        function drawAndShortCircuit(cond1, cond2, isDoubleAnd) {
            clearCanvas(andCanvas);
            
            const boxWidth = 120;
            const boxHeight = 60;
            
            // 绘制起始点
            drawBox(andCtx, 50, 120, boxWidth, boxHeight, '开始', '#3498db');
            
            // 条件1
            const cond1Color = cond1 ? '#2ecc71' : '#e74c3c';
            drawBox(andCtx, 250, 120, boxWidth, boxHeight, `条件1: ${cond1}`, cond1Color);
            drawArrow(andCtx, 170, 150, 250, 150);
            
            // 如果是短路且条件1为false
            if (isDoubleAnd && !cond1) {
                drawArrow(andCtx, 370, 150, 600, 150, '#e74c3c');
                drawText(andCtx, 485, 130, '短路! 不再判断条件2', '#e74c3c');
                drawBox(andCtx, 600, 120, boxWidth, boxHeight, '结果: false', '#e74c3c');
                return;
            }
            
            // 条件2
            const cond2Color = cond2 ? '#2ecc71' : '#e74c3c';
            drawBox(andCtx, 450, 120, boxWidth, boxHeight, `条件2: ${cond2}`, cond2Color);
            drawArrow(andCtx, 370, 150, 450, 150);
            
            // 结果
            const result = cond1 && cond2;
            const resultColor = result ? '#2ecc71' : '#e74c3c';
            drawBox(andCtx, 650, 120, boxWidth, boxHeight, `结果: ${result}`, resultColor);
            drawArrow(andCtx, 570, 150, 650, 150);
            
            // 标题
            const title = isDoubleAnd ? 
                `&& 短路运算: ${cond1} && ${cond2} = ${result}` : 
                `& 完整运算: ${cond1} & ${cond2} = ${result}`;
            drawText(andCtx, andCanvas.width / 2, 40, title, '#333');
            drawText(andCtx, andCanvas.width / 2, 70, isDoubleAnd ? 
                '短路运算符: 如果第一个条件为假，则不计算第二个条件' : 
                '完整运算符: 无论第一个条件是真是假，都会计算第二个条件', '#333');
        }

        // OR 演示
        const orCanvas = document.getElementById('orCanvas');
        const orCtx = orCanvas.getContext('2d');

        function drawOrShortCircuit(cond1, cond2, isDoubleOr) {
            clearCanvas(orCanvas);
            
            const boxWidth = 120;
            const boxHeight = 60;
            
            // 绘制起始点
            drawBox(orCtx, 50, 120, boxWidth, boxHeight, '开始', '#3498db');
            
            // 条件1
            const cond1Color = cond1 ? '#2ecc71' : '#e74c3c';
            drawBox(orCtx, 250, 120, boxWidth, boxHeight, `条件1: ${cond1}`, cond1Color);
            drawArrow(orCtx, 170, 150, 250, 150);
            
            // 如果是短路且条件1为true
            if (isDoubleOr && cond1) {
                drawArrow(orCtx, 370, 150, 600, 150, '#2ecc71');
                drawText(orCtx, 485, 130, '短路! 不再判断条件2', '#2ecc71');
                drawBox(orCtx, 600, 120, boxWidth, boxHeight, '结果: true', '#2ecc71');
                return;
            }
            
            // 条件2
            const cond2Color = cond2 ? '#2ecc71' : '#e74c3c';
            drawBox(orCtx, 450, 120, boxWidth, boxHeight, `条件2: ${cond2}`, cond2Color);
            drawArrow(orCtx, 370, 150, 450, 150);
            
            // 结果
            const result = cond1 || cond2;
            const resultColor = result ? '#2ecc71' : '#e74c3c';
            drawBox(orCtx, 650, 120, boxWidth, boxHeight, `结果: ${result}`, resultColor);
            drawArrow(orCtx, 570, 150, 650, 150);
            
            // 标题
            const title = isDoubleOr ? 
                `|| 短路运算: ${cond1} || ${cond2} = ${result}` : 
                `| 完整运算: ${cond1} | ${cond2} = ${result}`;
            drawText(orCtx, orCanvas.width / 2, 40, title, '#333');
            drawText(orCtx, orCanvas.width / 2, 70, isDoubleOr ? 
                '短路运算符: 如果第一个条件为真，则不计算第二个条件' : 
                '完整运算符: 无论第一个条件是真是假，都会计算第二个条件', '#333');
        }

        // 事件监听
        document.getElementById('andDemo1').addEventListener('click', () => drawAndShortCircuit(true, true, true));
        document.getElementById('andDemo2').addEventListener('click', () => drawAndShortCircuit(false, true, true));
        document.getElementById('andDemo3').addEventListener('click', () => drawAndShortCircuit(true, true, false));
        document.getElementById('andDemo4').addEventListener('click', () => drawAndShortCircuit(false, true, false));

        document.getElementById('orDemo1').addEventListener('click', () => drawOrShortCircuit(true, false, true));
        document.getElementById('orDemo2').addEventListener('click', () => drawOrShortCircuit(false, true, true));
        document.getElementById('orDemo3').addEventListener('click', () => drawOrShortCircuit(true, false, false));
        document.getElementById('orDemo4').addEventListener('click', () => drawOrShortCircuit(false, true, false));

        // 初始化演示
        window.onload = function() {
            drawAndShortCircuit(true, true, true);
            drawOrShortCircuit(true, false, true);
        };
    </script>
</body>
</html> 