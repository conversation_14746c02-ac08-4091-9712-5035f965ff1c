<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：anti-（反对、抗）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #1a1a2e, #16213e, #0f0f23);
        }

        #shieldCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .defense-academy {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .shield-station {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .shield-station::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.1), transparent);
            transition: left 0.6s;
        }

        .shield-station:hover::before {
            left: 100%;
        }

        .shield-station:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .shield-station.protected {
            opacity: 1;
            transform: translateY(0);
        }

        .defense-mechanism {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .threat-source {
            background: #e74c3c;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
            animation: threatPulse 2s ease-in-out infinite;
        }

        .threat-source::after {
            content: '威胁';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c0392b;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .shield-generator {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, #3498db, #2980b9, #1abc9c);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: shieldRotate 3s linear infinite;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.6);
        }

        .shield-generator::before {
            content: '🛡️';
            font-size: 2rem;
            animation: shieldRotate 2s linear infinite reverse;
        }

        .shield-generator::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px solid rgba(52, 152, 219, 0.3);
            border-radius: 50%;
            animation: shieldPulse 2s ease-in-out infinite;
        }

        .protected-state {
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
            animation: protectedGlow 2s ease-in-out infinite;
        }

        .protected-state::after {
            content: '防护';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #229954;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .defense-explanation {
            background: rgba(231, 76, 60, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .mission-report {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #f39c12;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #f39c12;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .shield-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .shield-status.charging {
            background: #f39c12;
            animation: charge 1.5s infinite;
        }

        .shield-status.active {
            background: #27ae60;
            box-shadow: 0 0 10px #27ae60;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes threatPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
            }
        }

        @keyframes shieldRotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes shieldPulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
        }

        @keyframes protectedGlow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
            }
            50% {
                box-shadow: 0 0 25px rgba(39, 174, 96, 0.8);
            }
        }

        @keyframes charge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes energyBeam {
            0% {
                transform: translateX(-100%) scaleX(0);
                opacity: 0;
            }
            50% {
                transform: translateX(0%) scaleX(1);
                opacity: 1;
            }
            100% {
                transform: translateX(100%) scaleX(0);
                opacity: 0;
            }
        }

        @keyframes deflect {
            0%, 100% {
                transform: translateX(0px);
            }
            50% {
                transform: translateX(-10px);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #e74c3c;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .energy-particles {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #e74c3c;
            border-radius: 50%;
            pointer-events: none;
            animation: energyBeam 2s infinite;
        }

        .defense-sequence {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .sequence-shield {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: radial-gradient(circle, #bdc3c7, #95a5a6);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .sequence-shield.active {
            background: radial-gradient(circle, #3498db, #2980b9);
            transform: scale(1.3);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.6);
            animation: shieldPulse 1s infinite;
        }

        .sequence-shield.protected {
            background: radial-gradient(circle, #27ae60, #229954);
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        }

        .sequence-shield::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #e74c3c, transparent);
            transform: translateY(-50%);
        }

        .sequence-shield:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>防护前缀：anti-</h1>
            <p>在超级英雄防护学院中学会"对抗反击"的力量</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🛡️ 超级英雄防护学院的故事</h2>
                <p>在一座神秘的超级英雄防护学院里，英雄们学习如何对抗各种威胁和危险。学院的核心装备是"anti-"防护盾生成器，它能为任何普通的词汇提供强大的防护能力。当词汇通过这个防护系统时，就会获得"反对"、"抗"、"防"的超能力，从被动状态转变为主动防御和对抗的强大形态！</p>
            </div>

            <div class="canvas-container">
                <canvas id="shieldCanvas"></canvas>
                <div class="defense-sequence" id="defenseSequence">
                    <div class="sequence-shield"></div>
                    <div class="sequence-shield"></div>
                    <div class="sequence-shield"></div>
                    <div class="sequence-shield"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择超级英雄防护学院的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"超级英雄防护学院"的比喻，是因为"anti-"前缀的核心含义就是"反对"、"抗"、"防"，这与超级英雄对抗邪恶、保护正义的使命完美契合。防护盾的视觉效果帮助学生理解"对抗"、"防御"的概念，而英雄学院的设定强调了正义与邪恶的对立关系。通过威胁→防护盾→保护的过程，让抽象的"反对"概念变得生动有力。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="activateShields()">激活防护盾</button>
                <button class="btn" onclick="showStations()">显示防护站</button>
                <button class="btn" onclick="resetAcademy()">重置学院</button>
            </div>

            <div class="interactive-hint">
                ⚔️ 点击"激活防护盾"观看词汇防护过程，点击防护站查看任务报告
            </div>
        </div>

        <div class="defense-academy" id="defenseAcademy">
            <div class="shield-station">
                <div class="shield-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Body → Antibody</h3>
                <div class="defense-mechanism">
                    <div class="threat-source">body</div>
                    <div class="shield-generator"></div>
                    <div class="protected-state"><span class="prefix-highlight">anti</span>body</div>
                </div>
                <div class="defense-explanation">
                    身体 → <span class="prefix-highlight">抗</span>体
                </div>
                <div class="mission-report">
                    <strong>任务报告：</strong><br>
                    <strong>威胁：</strong>The body needs protection. (身体需要保护。)<br>
                    <strong>防护：</strong>Antibodies fight infections. (抗体对抗感染。)<br>
                    <strong>解析：</strong>"body"表示身体，加上"anti-"变成"antibody"，表示抗体。从普通的身体转变为专门对抗病菌的免疫防护系统。
                </div>
            </div>

            <div class="shield-station">
                <div class="shield-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Social → Antisocial</h3>
                <div class="defense-mechanism">
                    <div class="threat-source">social</div>
                    <div class="shield-generator"></div>
                    <div class="protected-state"><span class="prefix-highlight">anti</span>social</div>
                </div>
                <div class="defense-explanation">
                    社交的 → <span class="prefix-highlight">反</span>社交的
                </div>
                <div class="mission-report">
                    <strong>任务报告：</strong><br>
                    <strong>威胁：</strong>He is very social. (他很爱社交。)<br>
                    <strong>防护：</strong>Antisocial behavior is concerning. (反社会行为令人担忧。)<br>
                    <strong>解析：</strong>"social"表示社交的，加上"anti-"变成"antisocial"，表示反社会的。从积极社交转变为对抗社会规范的行为。
                </div>
            </div>

            <div class="shield-station">
                <div class="shield-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Freeze → Antifreeze</h3>
                <div class="defense-mechanism">
                    <div class="threat-source">freeze</div>
                    <div class="shield-generator"></div>
                    <div class="protected-state"><span class="prefix-highlight">anti</span>freeze</div>
                </div>
                <div class="defense-explanation">
                    结冰 → <span class="prefix-highlight">防</span>冻
                </div>
                <div class="mission-report">
                    <strong>任务报告：</strong><br>
                    <strong>威胁：</strong>Water will freeze in winter. (水在冬天会结冰。)<br>
                    <strong>防护：</strong>Add antifreeze to the car. (给汽车加防冻液。)<br>
                    <strong>解析：</strong>"freeze"表示结冰，加上"anti-"变成"antifreeze"，表示防冻液。从容易结冰转变为专门防止结冰的保护液体。
                </div>
            </div>

            <div class="shield-station">
                <div class="shield-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Virus → Antivirus</h3>
                <div class="defense-mechanism">
                    <div class="threat-source">virus</div>
                    <div class="shield-generator"></div>
                    <div class="protected-state"><span class="prefix-highlight">anti</span>virus</div>
                </div>
                <div class="defense-explanation">
                    病毒 → <span class="prefix-highlight">杀</span>毒
                </div>
                <div class="mission-report">
                    <strong>任务报告：</strong><br>
                    <strong>威胁：</strong>The computer has a virus. (电脑有病毒。)<br>
                    <strong>防护：</strong>Install antivirus software. (安装杀毒软件。)<br>
                    <strong>解析：</strong>"virus"表示病毒，加上"anti-"变成"antivirus"，表示杀毒软件。从有害病毒转变为专门对抗病毒的防护程序。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"anti-"前缀表示反对、抗、防、对抗的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"anti-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"anti-"后的词根基本含义</li>
                <li><strong>应用对抗概念：</strong>在词根意思前加上"反"、"抗"、"防"、"杀"</li>
                <li><strong>对立调整：</strong>强调与原词根相对立或对抗的关系</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象超级英雄防护学院的防护盾，"anti-"就像防护盾，专门对抗和防御威胁！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('shieldCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentDefense = 0;
        let threats = [];
        let shields = [];
        let energyBeams = [];
        let shieldRotation = 0;
        
        const defenses = [
            { threat: 'body', defense: 'antibody', x: 150, y: 200 },
            { threat: 'social', defense: 'antisocial', x: 350, y: 300 },
            { threat: 'freeze', defense: 'antifreeze', x: 550, y: 150 },
            { threat: 'virus', defense: 'antivirus', x: 750, y: 250 }
        ];

        class Threat {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.size = 15;
                this.speed = 2;
                this.color = '#e74c3c';
                this.active = false;
            }

            update() {
                if (this.active) {
                    this.x += this.speed;
                    if (this.x > canvas.width + 50) {
                        this.x = -50;
                    }
                }
            }

            draw() {
                if (this.active) {
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 威胁光环
                    ctx.strokeStyle = 'rgba(231, 76, 60, 0.3)';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size + 10, 0, Math.PI * 2);
                    ctx.stroke();
                }
            }

            activate() {
                this.active = true;
            }
        }

        class Shield {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.size = 40;
                this.rotation = 0;
                this.active = false;
                this.energy = 1;
            }

            update() {
                if (this.active) {
                    this.rotation += 0.05;
                    this.energy = 0.8 + Math.sin(Date.now() * 0.005) * 0.2;
                }
            }

            draw() {
                if (this.active) {
                    ctx.save();
                    ctx.translate(this.x, this.y);
                    ctx.rotate(this.rotation);
                    
                    // 防护盾主体
                    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.size);
                    gradient.addColorStop(0, `rgba(52, 152, 219, ${this.energy})`);
                    gradient.addColorStop(0.7, `rgba(41, 128, 185, ${this.energy * 0.7})`);
                    gradient.addColorStop(1, 'transparent');
                    
                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(0, 0, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 防护盾边框
                    ctx.strokeStyle = `rgba(52, 152, 219, ${this.energy})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(0, 0, this.size, 0, Math.PI * 2);
                    ctx.stroke();
                    
                    ctx.restore();
                }
            }

            activate() {
                this.active = true;
            }
        }

        class EnergyBeam {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.width = 0;
                this.maxWidth = 200;
                this.height = 4;
                this.expanding = true;
                this.life = 1;
            }

            update() {
                if (this.expanding) {
                    this.width += 8;
                    if (this.width >= this.maxWidth) {
                        this.expanding = false;
                    }
                } else {
                    this.life -= 0.05;
                }
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                const gradient = ctx.createLinearGradient(this.x - this.width/2, this.y, this.x + this.width/2, this.y);
                gradient.addColorStop(0, 'transparent');
                gradient.addColorStop(0.5, '#e74c3c');
                gradient.addColorStop(1, 'transparent');
                ctx.fillStyle = gradient;
                ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
                ctx.restore();
            }
        }

        function initThreats() {
            threats = [];
            for (let i = 0; i < 4; i++) {
                threats.push(new Threat(-50, 100 + i * 100));
            }
        }

        function initShields() {
            shields = [];
            defenses.forEach(defense => {
                shields.push(new Shield(defense.x, defense.y));
            });
        }

        function drawMainShield() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            if (animationState === 'defending') {
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(shieldRotation);
                
                // 主防护盾
                const mainGradient = ctx.createRadialGradient(0, 0, 30, 0, 0, 80);
                mainGradient.addColorStop(0, 'rgba(52, 152, 219, 0.8)');
                mainGradient.addColorStop(0.5, 'rgba(41, 128, 185, 0.6)');
                mainGradient.addColorStop(1, 'rgba(26, 188, 156, 0.3)');
                
                ctx.fillStyle = mainGradient;
                ctx.beginPath();
                ctx.arc(0, 0, 80, 0, Math.PI * 2);
                ctx.fill();
                
                // 防护盾符号
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('🛡️', 0, 0);
                
                ctx.restore();
                
                shieldRotation += 0.03;
            }
        }

        function drawWordDefense() {
            if (currentDefense < defenses.length && animationState === 'defending') {
                const defense = defenses[currentDefense];
                const centerX = canvas.width / 2;
                
                // 威胁词汇（左侧）
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(centerX - 250, defense.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(defense.threat, centerX - 200, defense.y + 25);
                
                // 防护光束
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.moveTo(centerX - 130, defense.y + 20);
                ctx.lineTo(centerX + 130, defense.y + 20);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 防护词汇（右侧）
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(centerX + 150, defense.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮anti-前缀
                ctx.fillStyle = '#f39c12';
                ctx.fillText('anti', centerX + 185, defense.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(defense.threat, centerX + 225, defense.y + 25);
            }
        }

        function createEnergyBeam(x, y) {
            energyBeams.push(new EnergyBeam(x, y));
        }

        function updateEffects() {
            // 更新威胁
            threats.forEach(threat => {
                threat.update();
                threat.draw();
            });
            
            // 更新防护盾
            shields.forEach(shield => {
                shield.update();
                shield.draw();
            });
            
            // 更新能量光束
            energyBeams = energyBeams.filter(beam => {
                beam.update();
                beam.draw();
                return beam.life > 0;
            });
        }

        function updateDefenseSequence() {
            const shields = document.querySelectorAll('.sequence-shield');
            shields.forEach((shield, index) => {
                shield.classList.remove('active', 'protected');
                if (index < currentDefense) {
                    shield.classList.add('protected');
                } else if (index === currentDefense && animationState === 'defending') {
                    shield.classList.add('active');
                }
            });
        }

        function updateStationStatus() {
            const stations = document.querySelectorAll('.shield-station');
            const statuses = document.querySelectorAll('.shield-status');
            
            stations.forEach((station, index) => {
                const status = statuses[index];
                if (index < currentDefense) {
                    status.classList.remove('charging');
                    status.classList.add('active');
                } else if (index === currentDefense && animationState === 'defending') {
                    status.classList.add('charging');
                    status.classList.remove('active');
                } else {
                    status.classList.remove('charging', 'active');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制夜空背景
            const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            bgGradient.addColorStop(0, '#1a1a2e');
            bgGradient.addColorStop(0.5, '#16213e');
            bgGradient.addColorStop(1, '#0f0f23');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制主防护盾
            drawMainShield();
            
            // 绘制词汇防护过程
            drawWordDefense();
            
            // 更新特效
            updateEffects();
            
            // 更新界面状态
            updateDefenseSequence();
            updateStationStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'defending' && currentDefense < defenses.length) {
                // 激活威胁和防护盾
                if (threats[currentDefense]) {
                    threats[currentDefense].activate();
                }
                if (shields[currentDefense]) {
                    shields[currentDefense].activate();
                }
                
                // 创建能量光束
                if (Math.random() < 0.1) {
                    createEnergyBeam(canvas.width / 2, canvas.height / 2 + (Math.random() - 0.5) * 200);
                }
                
                // 自动切换到下一个防护
                setTimeout(() => {
                    currentDefense++;
                    if (currentDefense >= defenses.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function activateShields() {
            animationState = 'defending';
            currentDefense = 0;
            shieldRotation = 0;
            energyBeams = [];
            initThreats();
            initShields();
        }

        function showStations() {
            const stations = document.querySelectorAll('.shield-station');
            stations.forEach((station, index) => {
                setTimeout(() => {
                    station.classList.add('protected');
                }, index * 400);
            });
        }

        function resetAcademy() {
            animationState = 'idle';
            currentDefense = 0;
            shieldRotation = 0;
            energyBeams = [];
            threats = [];
            shields = [];
            
            const stations = document.querySelectorAll('.shield-station');
            stations.forEach(station => station.classList.remove('protected'));
            
            const statuses = document.querySelectorAll('.shield-status');
            statuses.forEach(status => {
                status.classList.remove('charging', 'active');
            });
        }

        // 初始化
        animate();

        // 点击防护站的交互
        document.querySelectorAll('.shield-station').forEach(station => {
            station.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
