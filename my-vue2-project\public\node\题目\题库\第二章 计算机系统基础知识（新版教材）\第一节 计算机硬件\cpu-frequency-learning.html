<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU频率互动学习 - 主频、外频、倍频</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .learning-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 0.8s ease-out;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            transform: translateY(20px);
            opacity: 0;
            animation: cardSlideIn 0.6s ease-out forwards;
        }

        .concept-card:nth-child(2) { animation-delay: 0.2s; }
        .concept-card:nth-child(3) { animation-delay: 0.4s; }
        .concept-card:nth-child(4) { animation-delay: 0.6s; }

        .concept-title {
            font-size: 1.8rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .concept-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .demo-canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin: 20px 0;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .slider {
            width: 200px;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .value-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            background: #f0f0f0;
            padding: 8px 16px;
            border-radius: 20px;
            min-width: 100px;
        }

        .formula-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .formula {
            font-size: 2rem;
            font-weight: bold;
            margin: 20px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .quiz-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        .result-display {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            background: rgba(255,255,255,0.2);
            text-align: center;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes cardSlideIn {
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse { animation: pulse 2s infinite; }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .controls { flex-direction: column; align-items: center; }
            .slider { width: 250px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ CPU频率互动学习</h1>
            <p>通过动画和交互，轻松理解主频、外频、倍频的关系</p>
        </div>

        <div class="learning-section">
            <h2 style="text-align: center; color: #333; margin-bottom: 30px; font-size: 2rem;">📚 基础概念</h2>
            
            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">🔄</div>
                    外频 (External Frequency)
                </div>
                <p>外频是系统总线的工作频率，就像是CPU与外部世界沟通的"桥梁"频率。它决定了CPU与内存、主板等组件之间数据传输的速度。</p>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">✖️</div>
                    倍频 (Multiplier)
                </div>
                <p>倍频系数是CPU内部工作频率相对于外频的倍数。通过倍频技术，CPU可以在较低的外频基础上实现更高的工作频率。</p>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">⚡</div>
                    主频 (Main Frequency)
                </div>
                <p>主频是CPU的实际工作频率，决定了CPU的运算速度。主频越高，CPU处理数据的能力越强。</p>
            </div>
        </div>

        <div class="formula-section">
            <h2>🧮 神奇的公式</h2>
            <div class="formula pulse">
                主频 = 外频 × 倍频
            </div>
            <p>这个简单的公式揭示了CPU频率的奥秘！</p>
        </div>

        <div class="interactive-demo">
            <h2 style="color: #333; margin-bottom: 20px;">🎮 互动演示</h2>
            <p style="color: #666; margin-bottom: 20px;">拖动滑块，观察频率变化的动画效果</p>
            
            <canvas id="demoCanvas" class="demo-canvas" width="800" height="400"></canvas>
            
            <div class="controls">
                <div class="control-group">
                    <label>外频 (MHz)</label>
                    <input type="range" id="externalFreq" class="slider" min="100" max="400" value="200">
                    <div id="externalValue" class="value-display">200 MHz</div>
                </div>
                
                <div class="control-group">
                    <label>倍频</label>
                    <input type="range" id="multiplier" class="slider" min="5" max="20" value="13">
                    <div id="multiplierValue" class="value-display">13</div>
                </div>
                
                <div class="control-group">
                    <label>主频</label>
                    <div id="mainFreqValue" class="value-display">2.6 GHz</div>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎯 挑战题目</h2>
            <div class="quiz-question">
                某处理器外频是200MHz，倍频是13，该款处理器的主频是？
            </div>
            
            <div class="quiz-options">
                <div class="quiz-option" data-answer="A">A. 2.6GHz</div>
                <div class="quiz-option" data-answer="B">B. 1300MHz</div>
                <div class="quiz-option" data-answer="C">C. 15.38MHz</div>
                <div class="quiz-option" data-answer="D">D. 200MHz</div>
            </div>
            
            <div id="quizResult" class="result-display" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Canvas动画相关变量
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        
        // 频率值
        let externalFreq = 200;
        let multiplier = 13;
        let mainFreq = 2600;
        
        // 动画参数
        let time = 0;
        let particles = [];
        
        // 初始化粒子
        function initParticles() {
            particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }
        }
        
        // 绘制CPU示意图
        function drawCPU() {
            ctx.save();
            
            // 清空画布
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景粒子
            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = particle.color + '30';
                ctx.fill();
                
                // 更新粒子位置
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
            
            // 绘制CPU核心
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // CPU外壳
            ctx.fillStyle = '#333';
            ctx.fillRect(centerX - 80, centerY - 60, 160, 120);
            ctx.fillStyle = '#555';
            ctx.fillRect(centerX - 75, centerY - 55, 150, 110);
            
            // 频率波形动画
            ctx.strokeStyle = '#4facfe';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            const waveAmplitude = 20;
            const waveFrequency = externalFreq / 50;
            
            for (let x = centerX - 70; x < centerX + 70; x += 2) {
                const y = centerY + Math.sin((x - centerX) * 0.1 + time * waveFrequency) * waveAmplitude;
                if (x === centerX - 70) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
            
            // 倍频效果
            ctx.strokeStyle = '#fa709a';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = centerX - 70; x < centerX + 70; x += 1) {
                const y = centerY + Math.sin((x - centerX) * 0.1 * multiplier + time * waveFrequency * multiplier) * (waveAmplitude / 2);
                if (x === centerX - 70) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
            
            // 文字标签
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            
            ctx.fillText('CPU核心', centerX, centerY - 80);
            ctx.fillText(`外频: ${externalFreq} MHz`, centerX - 150, centerY + 20);
            ctx.fillText(`倍频: ${multiplier}`, centerX - 150, centerY + 40);
            ctx.fillText(`主频: ${(mainFreq/1000).toFixed(1)} GHz`, centerX + 150, centerY + 30);
            
            // 箭头和连接线
            drawArrow(centerX - 120, centerY, centerX - 90, centerY, '#4facfe');
            drawArrow(centerX + 90, centerY, centerX + 120, centerY, '#fa709a');
            
            ctx.restore();
        }
        
        // 绘制箭头
        function drawArrow(fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }
        
        // 动画循环
        function animate() {
            time += 0.05;
            drawCPU();
            animationId = requestAnimationFrame(animate);
        }
        
        // 更新频率值
        function updateFrequency() {
            externalFreq = parseInt(document.getElementById('externalFreq').value);
            multiplier = parseInt(document.getElementById('multiplier').value);
            mainFreq = externalFreq * multiplier;
            
            document.getElementById('externalValue').textContent = `${externalFreq} MHz`;
            document.getElementById('multiplierValue').textContent = multiplier;
            document.getElementById('mainFreqValue').textContent = `${(mainFreq/1000).toFixed(1)} GHz`;
        }
        
        // 事件监听
        document.getElementById('externalFreq').addEventListener('input', updateFrequency);
        document.getElementById('multiplier').addEventListener('input', updateFrequency);
        
        // 测验功能
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const resultDiv = document.getElementById('quizResult');
                
                // 清除之前的样式
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    resultDiv.innerHTML = `
                        <div style="color: #4CAF50;">🎉 恭喜答对了！</div>
                        <div style="margin-top: 10px; font-size: 1.2rem;">
                            计算过程：200MHz × 13 = 2600MHz = 2.6GHz
                        </div>
                        <div style="margin-top: 10px; color: #666;">
                            记住公式：主频 = 外频 × 倍频
                        </div>
                    `;
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                    resultDiv.innerHTML = `
                        <div style="color: #f44336;">❌ 答案不正确</div>
                        <div style="margin-top: 10px;">
                            正确答案是 A. 2.6GHz<br>
                            计算：200MHz × 13 = 2600MHz = 2.6GHz
                        </div>
                    `;
                }
                
                resultDiv.style.display = 'block';
            });
        });
        
        // 初始化
        initParticles();
        updateFrequency();
        animate();
        
        // 响应式处理
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = Math.min(800, container.clientWidth - 40);
            canvas.width = maxWidth;
            canvas.height = Math.max(300, maxWidth * 0.5);
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    </script>
</body>
</html>
