<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：multi-（多个、多重）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: radial-gradient(ellipse at center, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        }

        #multiverseCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .observation-deck {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .universe-viewer {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .universe-viewer::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s;
        }

        .universe-viewer:hover::before {
            left: 100%;
        }

        .universe-viewer:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .universe-viewer.discovered {
            opacity: 1;
            transform: translateY(0);
        }

        .multiverse-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .single-universe {
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.1rem;
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .single-universe::after {
            content: '单一';
            position: absolute;
            top: -15px;
            right: -15px;
            background: #218838;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .multiplier-device {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24, #fd79a8);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: multiply 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
        }

        .multiplier-device::before {
            content: '✖️';
            font-size: 2rem;
            animation: multiply 1.5s ease-in-out infinite reverse;
        }

        .multiplier-device::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px dashed rgba(255, 107, 107, 0.3);
            border-radius: 50%;
            animation: multiply 3s ease-in-out infinite;
        }

        .multiple-universes {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .mini-universe {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 0.9rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            animation: orbit 4s linear infinite;
        }

        .mini-universe:nth-child(1) { animation-delay: 0s; }
        .mini-universe:nth-child(2) { animation-delay: 1s; }
        .mini-universe:nth-child(3) { animation-delay: 2s; }
        .mini-universe:nth-child(4) { animation-delay: 3s; }

        .multiple-universes::after {
            content: '多重';
            position: absolute;
            top: -15px;
            right: -15px;
            background: #0056b3;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .multiverse-explanation {
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .observation-log {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .observation-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .observation-status.scanning {
            background: #ffc107;
            animation: scan 1.5s infinite;
        }

        .observation-status.multiple {
            background: #007bff;
            animation: multiPulse 1s infinite;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes multiply {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes orbit {
            0% {
                transform: rotate(0deg) translateX(30px) rotate(0deg);
            }
            100% {
                transform: rotate(360deg) translateX(30px) rotate(-360deg);
            }
        }

        @keyframes scan {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes multiPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
                box-shadow: 0 0 5px #007bff;
            }
            50% {
                opacity: 0.7;
                transform: scale(1.2);
                box-shadow: 0 0 15px #007bff;
            }
        }

        @keyframes starField {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.2) rotate(180deg);
            }
        }

        @keyframes dimensionShift {
            0% {
                transform: translateX(0px);
                opacity: 0.3;
            }
            50% {
                transform: translateX(20px);
                opacity: 1;
            }
            100% {
                transform: translateX(0px);
                opacity: 0.3;
            }
        }

        .interactive-hint {
            text-align: center;
            color: #ff6b6b;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .cosmic-particles {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffffff;
            border-radius: 50%;
            pointer-events: none;
            animation: starField 4s infinite;
        }

        .dimension-tracker {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .dimension-node {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: radial-gradient(circle, #e3f2fd, #2196f3);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .dimension-node.active {
            background: radial-gradient(circle, #ffecb3, #ff9800);
            transform: scale(1.3);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.6);
            animation: multiply 1s infinite;
        }

        .dimension-node.multiplied {
            background: radial-gradient(circle, #c8e6c9, #4caf50);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .dimension-node::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, transparent);
            transform: translateY(-50%);
        }

        .dimension-node:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>多元前缀：multi-</h1>
            <p>在多元宇宙观测站中学会"倍增扩展"的奥秘</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🌌 多元宇宙观测站的故事</h2>
                <p>在宇宙的边缘，有一座神奇的多元宇宙观测站，专门研究平行宇宙和多重维度。观测站的核心设备是"multi-"倍增器，它能将任何单一的概念扩展成多个、多重或多样化的形式。当普通的词汇通过这个倍增器时，就会从单一状态转变为多重状态，获得"多个"、"多种"、"多样"的神奇能力！</p>
            </div>

            <div class="canvas-container">
                <canvas id="multiverseCanvas"></canvas>
                <div class="dimension-tracker" id="dimensionTracker">
                    <div class="dimension-node"></div>
                    <div class="dimension-node"></div>
                    <div class="dimension-node"></div>
                    <div class="dimension-node"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择多元宇宙观测站的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"多元宇宙观测站"的比喻，是因为"multi-"前缀的核心含义就是"多个"、"多重"，这与多元宇宙中存在无数个平行世界的概念完美契合。倍增器的视觉效果帮助学生理解"从一到多"的转换过程，而观测站的设定强调了观察和发现多重性的科学精神。通过单一宇宙到多重宇宙的对比展示，让抽象的"多重"概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="activateMultiplier()">启动倍增器</button>
                <button class="btn" onclick="showObservations()">显示观测</button>
                <button class="btn" onclick="resetStation()">重置观测站</button>
            </div>

            <div class="interactive-hint">
                🔭 点击"启动倍增器"观看词汇倍增过程，点击观测器查看观测日志
            </div>
        </div>

        <div class="observation-deck" id="observationDeck">
            <div class="universe-viewer">
                <div class="observation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Color → Multicolor</h3>
                <div class="multiverse-display">
                    <div class="single-universe">color</div>
                    <div class="multiplier-device"></div>
                    <div class="multiple-universes">
                        <div class="mini-universe"><span class="prefix-highlight">multi</span></div>
                        <div class="mini-universe">color</div>
                        <div class="mini-universe">🌈</div>
                        <div class="mini-universe">✨</div>
                    </div>
                </div>
                <div class="multiverse-explanation">
                    颜色 → <span class="prefix-highlight">多种</span>颜色
                </div>
                <div class="observation-log">
                    <strong>观测日志：</strong><br>
                    <strong>单一：</strong>I like this color. (我喜欢这种颜色。)<br>
                    <strong>多重：</strong>The flag is multicolor. (这面旗子是多彩的。)<br>
                    <strong>解析：</strong>"color"表示颜色，加上"multi-"变成"multicolor"，表示多种颜色的、多彩的。从单一颜色扩展到多种颜色的组合。
                </div>
            </div>

            <div class="universe-viewer">
                <div class="observation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Media → Multimedia</h3>
                <div class="multiverse-display">
                    <div class="single-universe">media</div>
                    <div class="multiplier-device"></div>
                    <div class="multiple-universes">
                        <div class="mini-universe"><span class="prefix-highlight">multi</span></div>
                        <div class="mini-universe">media</div>
                        <div class="mini-universe">📱</div>
                        <div class="mini-universe">💻</div>
                    </div>
                </div>
                <div class="multiverse-explanation">
                    媒体 → <span class="prefix-highlight">多种</span>媒体
                </div>
                <div class="observation-log">
                    <strong>观测日志：</strong><br>
                    <strong>单一：</strong>This media is popular. (这种媒体很受欢迎。)<br>
                    <strong>多重：</strong>We use multimedia in class. (我们在课堂上使用多媒体。)<br>
                    <strong>解析：</strong>"media"表示媒体，加上"multi-"变成"multimedia"，表示多媒体。从单一媒体形式扩展到多种媒体的结合。
                </div>
            </div>

            <div class="universe-viewer">
                <div class="observation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Task → Multitask</h3>
                <div class="multiverse-display">
                    <div class="single-universe">task</div>
                    <div class="multiplier-device"></div>
                    <div class="multiple-universes">
                        <div class="mini-universe"><span class="prefix-highlight">multi</span></div>
                        <div class="mini-universe">task</div>
                        <div class="mini-universe">⚡</div>
                        <div class="mini-universe">🔄</div>
                    </div>
                </div>
                <div class="multiverse-explanation">
                    任务 → <span class="prefix-highlight">多重</span>任务
                </div>
                <div class="observation-log">
                    <strong>观测日志：</strong><br>
                    <strong>单一：</strong>Focus on one task. (专注于一个任务。)<br>
                    <strong>多重：</strong>She can multitask efficiently. (她能高效地处理多项任务。)<br>
                    <strong>解析：</strong>"task"表示任务，加上"multi-"变成"multitask"，表示同时处理多项任务。从单一任务扩展到同时处理多个任务。
                </div>
            </div>

            <div class="universe-viewer">
                <div class="observation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Purpose → Multipurpose</h3>
                <div class="multiverse-display">
                    <div class="single-universe">purpose</div>
                    <div class="multiplier-device"></div>
                    <div class="multiple-universes">
                        <div class="mini-universe"><span class="prefix-highlight">multi</span></div>
                        <div class="mini-universe">purpose</div>
                        <div class="mini-universe">🛠️</div>
                        <div class="mini-universe">🎯</div>
                    </div>
                </div>
                <div class="multiverse-explanation">
                    用途 → <span class="prefix-highlight">多种</span>用途
                </div>
                <div class="observation-log">
                    <strong>观测日志：</strong><br>
                    <strong>单一：</strong>This tool has one purpose. (这个工具有一个用途。)<br>
                    <strong>多重：</strong>It's a multipurpose device. (这是一个多用途设备。)<br>
                    <strong>解析：</strong>"purpose"表示用途，加上"multi-"变成"multipurpose"，表示多用途的。从单一功能扩展到多种功能的组合。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"multi-"前缀表示多个、多种、多重、多样的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"multi-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"multi-"后的词根基本含义</li>
                <li><strong>应用倍增概念：</strong>在词根意思前加上"多个"、"多种"、"多重"</li>
                <li><strong>数量调整：</strong>强调从单一状态扩展到多重状态</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象多元宇宙观测站的倍增器，"multi-"就像倍增器，让单一概念变成多重概念！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('multiverseCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentMultiplication = 0;
        let stars = [];
        let universes = [];
        let multiplierRotation = 0;
        
        const multiplications = [
            { single: 'color', multi: 'multicolor', x: 150, y: 200 },
            { single: 'media', multi: 'multimedia', x: 350, y: 300 },
            { single: 'task', multi: 'multitask', x: 550, y: 150 },
            { single: 'purpose', multi: 'multipurpose', x: 750, y: 250 }
        ];

        class Star {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 2 + 0.5;
                this.twinkle = Math.random() * Math.PI * 2;
                this.twinkleSpeed = 0.02 + Math.random() * 0.02;
            }

            update() {
                this.twinkle += this.twinkleSpeed;
            }

            draw() {
                const alpha = 0.3 + Math.sin(this.twinkle) * 0.4;
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class Universe {
            constructor(x, y, color, size) {
                this.x = x;
                this.y = y;
                this.color = color;
                this.size = size;
                this.originalSize = size;
                this.pulsePhase = Math.random() * Math.PI * 2;
                this.orbitAngle = Math.random() * Math.PI * 2;
                this.orbitRadius = 30;
                this.orbitSpeed = 0.02;
            }

            update() {
                this.pulsePhase += 0.05;
                this.size = this.originalSize + Math.sin(this.pulsePhase) * 3;
                this.orbitAngle += this.orbitSpeed;
            }

            draw() {
                const orbitX = this.x + Math.cos(this.orbitAngle) * this.orbitRadius;
                const orbitY = this.y + Math.sin(this.orbitAngle) * this.orbitRadius;
                
                // 宇宙光晕
                ctx.save();
                ctx.globalAlpha = 0.3;
                const glowGradient = ctx.createRadialGradient(orbitX, orbitY, this.size, orbitX, orbitY, this.size * 2);
                glowGradient.addColorStop(0, this.color);
                glowGradient.addColorStop(1, 'transparent');
                ctx.fillStyle = glowGradient;
                ctx.beginPath();
                ctx.arc(orbitX, orbitY, this.size * 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                
                // 宇宙主体
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(orbitX, orbitY, this.size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function initStars() {
            stars = [];
            for (let i = 0; i < 80; i++) {
                stars.push(new Star());
            }
        }

        function initUniverses() {
            universes = [];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
            for (let i = 0; i < 5; i++) {
                universes.push(new Universe(
                    canvas.width / 2 + (Math.random() - 0.5) * 200,
                    canvas.height / 2 + (Math.random() - 0.5) * 150,
                    colors[i],
                    8 + Math.random() * 6
                ));
            }
        }

        function drawMultiplier() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 倍增器主体
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(multiplierRotation);
            
            // 外环
            const outerGradient = ctx.createRadialGradient(0, 0, 30, 0, 0, 60);
            outerGradient.addColorStop(0, 'transparent');
            outerGradient.addColorStop(0.7, 'rgba(255, 107, 107, 0.3)');
            outerGradient.addColorStop(1, 'rgba(255, 107, 107, 0.8)');
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 60, 0, Math.PI * 2);
            ctx.fill();
            
            // 内环
            const innerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 40);
            innerGradient.addColorStop(0, 'rgba(238, 90, 36, 0.8)');
            innerGradient.addColorStop(0.5, 'rgba(255, 107, 107, 0.6)');
            innerGradient.addColorStop(1, 'transparent');
            ctx.fillStyle = innerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 40, 0, Math.PI * 2);
            ctx.fill();
            
            // 倍增符号
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('×', 0, 0);
            
            ctx.restore();
            
            if (animationState === 'multiplying') {
                multiplierRotation += 0.05;
            }
        }

        function drawWordMultiplication() {
            if (currentMultiplication < multiplications.length && animationState === 'multiplying') {
                const mult = multiplications[currentMultiplication];
                const centerX = canvas.width / 2;
                
                // 单一词汇（左侧）
                ctx.fillStyle = '#28a745';
                ctx.fillRect(centerX - 250, mult.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(mult.single, centerX - 200, mult.y + 25);
                
                // 倍增光束
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 4;
                ctx.setLineDash([8, 4]);
                ctx.beginPath();
                ctx.moveTo(centerX - 130, mult.y + 20);
                ctx.lineTo(centerX + 130, mult.y + 20);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 多重词汇（右侧）
                ctx.fillStyle = '#007bff';
                ctx.fillRect(centerX + 150, mult.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮multi-前缀
                ctx.fillStyle = '#ffc107';
                ctx.fillText('multi', centerX + 185, mult.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(mult.single, centerX + 225, mult.y + 25);
            }
        }

        function updateDimensionTracker() {
            const nodes = document.querySelectorAll('.dimension-node');
            nodes.forEach((node, index) => {
                node.classList.remove('active', 'multiplied');
                if (index < currentMultiplication) {
                    node.classList.add('multiplied');
                } else if (index === currentMultiplication && animationState === 'multiplying') {
                    node.classList.add('active');
                }
            });
        }

        function updateObservationStatus() {
            const viewers = document.querySelectorAll('.universe-viewer');
            const statuses = document.querySelectorAll('.observation-status');
            
            viewers.forEach((viewer, index) => {
                const status = statuses[index];
                if (index < currentMultiplication) {
                    status.classList.remove('scanning');
                    status.classList.add('multiple');
                } else if (index === currentMultiplication && animationState === 'multiplying') {
                    status.classList.add('scanning');
                    status.classList.remove('multiple');
                } else {
                    status.classList.remove('scanning', 'multiple');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制深空背景
            const bgGradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2);
            bgGradient.addColorStop(0, '#0f0f23');
            bgGradient.addColorStop(0.5, '#1a1a2e');
            bgGradient.addColorStop(1, '#16213e');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制星星
            stars.forEach(star => {
                star.update();
                star.draw();
            });
            
            // 绘制多重宇宙
            universes.forEach(universe => {
                universe.update();
                universe.draw();
            });
            
            // 绘制倍增器
            drawMultiplier();
            
            // 绘制词汇倍增过程
            drawWordMultiplication();
            
            // 更新界面状态
            updateDimensionTracker();
            updateObservationStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'multiplying' && currentMultiplication < multiplications.length) {
                // 自动切换到下一个倍增
                setTimeout(() => {
                    currentMultiplication++;
                    if (currentMultiplication >= multiplications.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function activateMultiplier() {
            animationState = 'multiplying';
            currentMultiplication = 0;
            multiplierRotation = 0;
        }

        function showObservations() {
            const viewers = document.querySelectorAll('.universe-viewer');
            viewers.forEach((viewer, index) => {
                setTimeout(() => {
                    viewer.classList.add('discovered');
                }, index * 400);
            });
        }

        function resetStation() {
            animationState = 'idle';
            currentMultiplication = 0;
            multiplierRotation = 0;
            
            const viewers = document.querySelectorAll('.universe-viewer');
            viewers.forEach(viewer => viewer.classList.remove('discovered'));
            
            const statuses = document.querySelectorAll('.observation-status');
            statuses.forEach(status => {
                status.classList.remove('scanning', 'multiple');
            });
        }

        // 初始化
        initStars();
        initUniverses();
        animate();

        // 点击观测器的交互
        document.querySelectorAll('.universe-viewer').forEach(viewer => {
            viewer.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
