<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件组装阶段失配问题 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .nav-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 15px 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .content-section {
            display: none;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-demo {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .quiz-container {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quiz-question {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .quiz-option.correct {
            background: #e8f5e8;
            border-color: #4caf50;
        }

        .quiz-option.wrong {
            background: #ffebee;
            border-color: #f44336;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">构件组装阶段失配问题</h1>
            <p class="subtitle">零基础交互式学习 - 动画演示与实践</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="nav-menu">
            <div class="nav-item active" onclick="showSection('overview')">概述</div>
            <div class="nav-item" onclick="showSection('component-mismatch')">构件失配</div>
            <div class="nav-item" onclick="showSection('connector-mismatch')">连接子失配</div>
            <div class="nav-item" onclick="showSection('architecture-mismatch')">体系结构失配</div>
            <div class="nav-item" onclick="showSection('quiz')">测试练习</div>
        </div>

        <!-- 概述部分 -->
        <div class="content-section active" id="overview">
            <h2 class="section-title">什么是构件组装失配问题？</h2>
            
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <h3>🎯 学习目标</h3>
                <p>理解构件组装阶段可能出现的三种主要失配问题，掌握识别和分析这些问题的方法。</p>
            </div>

            <div class="interactive-demo">
                <h3>🎮 互动演示</h3>
                <p>点击下方按钮，观看构件组装过程的动画演示</p>
                <button class="demo-button" onclick="startOverviewAnimation()">开始演示</button>
                <button class="demo-button" onclick="resetOverviewAnimation()">重置动画</button>
            </div>

            <div class="explanation">
                <h3>📚 基础概念</h3>
                <p>构件组装是软件开发中将不同的软件构件组合成完整系统的过程。在这个过程中，可能会出现各种失配问题，影响系统的正常运行。</p>
            </div>
        </div>

        <!-- 构件失配部分 -->
        <div class="content-section" id="component-mismatch">
            <h2 class="section-title">构件引起的失配</h2>

            <div class="canvas-container">
                <canvas id="componentCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <h3>🔧 构件基础设施失配</h3>
                <p>当系统对构件的基础设施（如运行环境、依赖库等）存在不同假设时产生的冲突。</p>
            </div>

            <div class="explanation">
                <h3>🎛️ 构件控制模型失配</h3>
                <p>不同构件采用不同的控制流程或调用方式时产生的不兼容问题。</p>
            </div>

            <div class="explanation">
                <h3>📊 构件数据模型失配</h3>
                <p>构件之间对数据格式、数据类型或数据结构的理解不一致导致的问题。</p>
            </div>

            <div class="interactive-demo">
                <h3>🎮 互动演示</h3>
                <button class="demo-button" onclick="demonstrateComponentMismatch('infrastructure')">基础设施失配</button>
                <button class="demo-button" onclick="demonstrateComponentMismatch('control')">控制模型失配</button>
                <button class="demo-button" onclick="demonstrateComponentMismatch('data')">数据模型失配</button>
            </div>
        </div>

        <!-- 连接子失配部分 -->
        <div class="content-section" id="connector-mismatch">
            <h2 class="section-title">连接子引起的失配</h2>

            <div class="canvas-container">
                <canvas id="connectorCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <h3>🔗 交互协议失配</h3>
                <p>构件之间的通信协议不匹配，如同步/异步调用方式的冲突。</p>
            </div>

            <div class="explanation">
                <h3>📡 连接子数据模型失配</h3>
                <p>连接子对数据传输格式的假设与构件的实际需求不符。</p>
            </div>

            <div class="interactive-demo">
                <h3>🎮 互动演示</h3>
                <button class="demo-button" onclick="demonstrateConnectorMismatch('protocol')">协议失配演示</button>
                <button class="demo-button" onclick="demonstrateConnectorMismatch('datamodel')">数据模型失配</button>
            </div>
        </div>

        <!-- 体系结构失配部分 -->
        <div class="content-section" id="architecture-mismatch">
            <h2 class="section-title">全局体系结构失配</h2>

            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <h3>🏗️ 全局假设冲突</h3>
                <p>系统各成分对整体架构的理解和假设存在根本性分歧，导致集成困难。</p>
            </div>

            <div class="explanation">
                <h3>🔄 解决方案</h3>
                <p>通过检测失配问题，并采用适配器模式、中间件等技术手段来消除失配。</p>
            </div>

            <div class="interactive-demo">
                <h3>🎮 互动演示</h3>
                <button class="demo-button" onclick="demonstrateArchitectureMismatch()">体系结构冲突</button>
                <button class="demo-button" onclick="demonstrateSolution()">解决方案演示</button>
            </div>
        </div>

        <!-- 测试练习部分 -->
        <div class="content-section" id="quiz">
            <h2 class="section-title">知识测试</h2>

            <div class="quiz-container">
                <div class="quiz-question">
                    在构件组装阶段失配问题主要不包括（）？
                </div>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 由构件引起的失配
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 由连接子引起的失配
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 由于系统成分对全局体系结构的假设存在冲突引起的失配
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        D. 由构件操作不当引起的失配
                    </div>
                </div>
                <div id="quizResult"></div>
            </div>

            <div class="explanation">
                <h3>📝 答案解析</h3>
                <p><strong>正确答案：D</strong></p>
                <p>构件组装阶段的失配问题主要包括三类：</p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li>由构件引起的失配（基础设施、控制模型、数据模型冲突）</li>
                    <li>由连接子引起的失配（交互协议、数据模型冲突）</li>
                    <li>由系统成分对全局体系结构假设冲突引起的失配</li>
                </ul>
                <p>"构件操作不当"属于使用层面的问题，不是组装阶段的结构性失配问题。</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'overview';
        let animationFrameId;
        let canvasContexts = {};

        // 初始化画布
        function initCanvases() {
            const canvases = ['overviewCanvas', 'componentCanvas', 'connectorCanvas', 'architectureCanvas'];
            canvases.forEach(canvasId => {
                const canvas = document.getElementById(canvasId);
                if (canvas) {
                    canvasContexts[canvasId] = canvas.getContext('2d');
                }
            });
        }

        // 显示指定部分
        function showSection(sectionId) {
            // 隐藏所有部分
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示指定部分
            document.getElementById(sectionId).classList.add('active');

            // 激活对应导航项
            event.target.classList.add('active');

            currentSection = sectionId;
            updateProgress();

            // 初始化对应的画布动画
            setTimeout(() => {
                initSectionAnimation(sectionId);
            }, 300);
        }

        // 更新进度条
        function updateProgress() {
            const sections = ['overview', 'component-mismatch', 'connector-mismatch', 'architecture-mismatch', 'quiz'];
            const currentIndex = sections.indexOf(currentSection);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 初始化部分动画
        function initSectionAnimation(sectionId) {
            switch(sectionId) {
                case 'overview':
                    drawOverviewDiagram();
                    break;
                case 'component-mismatch':
                    drawComponentDiagram();
                    break;
                case 'connector-mismatch':
                    drawConnectorDiagram();
                    break;
                case 'architecture-mismatch':
                    drawArchitectureDiagram();
                    break;
            }
        }

        // 绘制概述图
        function drawOverviewDiagram() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvasContexts['overviewCanvas'];
            if (!ctx) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('构件组装失配问题分类', canvas.width/2, 40);

            // 绘制三个主要类别
            const categories = [
                { name: '构件失配', x: 150, y: 150, color: '#ff6b6b' },
                { name: '连接子失配', x: 400, y: 150, color: '#4ecdc4' },
                { name: '体系结构失配', x: 650, y: 150, color: '#45b7d1' }
            ];

            categories.forEach((cat, index) => {
                // 绘制圆形
                ctx.beginPath();
                ctx.arc(cat.x, cat.y, 60, 0, 2 * Math.PI);
                ctx.fillStyle = cat.color;
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();

                // 绘制文字
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(cat.name, cat.x, cat.y + 5);

                // 绘制连接线到中心
                ctx.beginPath();
                ctx.moveTo(cat.x, cat.y + 60);
                ctx.lineTo(400, 280);
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.stroke();
            });

            // 绘制中心圆
            ctx.beginPath();
            ctx.arc(400, 300, 40, 0, 2 * Math.PI);
            ctx.fillStyle = '#6c5ce7';
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillText('失配', 400, 305);
            ctx.fillText('问题', 400, 320);
        }

        // 开始概述动画
        function startOverviewAnimation() {
            let step = 0;
            const animate = () => {
                const canvas = document.getElementById('overviewCanvas');
                const ctx = canvasContexts['overviewCanvas'];
                if (!ctx) return;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 动画效果：逐步显示各个组件
                if (step > 0) {
                    // 绘制标题
                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('构件组装失配问题分类', canvas.width/2, 40);
                }

                if (step > 30) {
                    // 绘制第一个类别
                    drawAnimatedCategory(ctx, '构件失配', 150, 150, '#ff6b6b', step - 30);
                }

                if (step > 60) {
                    // 绘制第二个类别
                    drawAnimatedCategory(ctx, '连接子失配', 400, 150, '#4ecdc4', step - 60);
                }

                if (step > 90) {
                    // 绘制第三个类别
                    drawAnimatedCategory(ctx, '体系结构失配', 650, 150, '#45b7d1', step - 90);
                }

                if (step > 120) {
                    // 绘制中心和连接线
                    drawCenterAndConnections(ctx, step - 120);
                }

                step++;
                if (step < 180) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 绘制动画类别
        function drawAnimatedCategory(ctx, name, x, y, color, animStep) {
            const progress = Math.min(animStep / 30, 1);
            const radius = 60 * progress;

            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();

            if (progress > 0.5) {
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(name, x, y + 5);
            }
        }

        // 绘制中心和连接
        function drawCenterAndConnections(ctx, animStep) {
            const progress = Math.min(animStep / 30, 1);

            // 绘制连接线
            if (progress > 0.3) {
                const categories = [
                    { x: 150, y: 150 },
                    { x: 400, y: 150 },
                    { x: 650, y: 150 }
                ];

                categories.forEach(cat => {
                    ctx.beginPath();
                    ctx.moveTo(cat.x, cat.y + 60);
                    ctx.lineTo(400, 280);
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                });
            }

            // 绘制中心圆
            const radius = 40 * progress;
            ctx.beginPath();
            ctx.arc(400, 300, radius, 0, 2 * Math.PI);
            ctx.fillStyle = '#6c5ce7';
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();

            if (progress > 0.7) {
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('失配', 400, 305);
                ctx.fillText('问题', 400, 320);
            }
        }

        // 重置概述动画
        function resetOverviewAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            drawOverviewDiagram();
        }

        // 绘制构件图
        function drawComponentDiagram() {
            const canvas = document.getElementById('componentCanvas');
            const ctx = canvasContexts['componentCanvas'];
            if (!ctx) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('构件失配类型', canvas.width/2, 30);

            // 绘制三种失配类型
            const mismatchTypes = [
                { name: '基础设施失配', x: 150, y: 120, color: '#ff6b6b', desc: '运行环境冲突' },
                { name: '控制模型失配', x: 400, y: 120, color: '#4ecdc4', desc: '调用方式不兼容' },
                { name: '数据模型失配', x: 650, y: 120, color: '#45b7d1', desc: '数据格式冲突' }
            ];

            mismatchTypes.forEach(type => {
                // 绘制矩形
                ctx.fillStyle = type.color;
                ctx.fillRect(type.x - 80, type.y - 40, 160, 80);
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.strokeRect(type.x - 80, type.y - 40, 160, 80);

                // 绘制标题
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(type.name, type.x, type.y - 10);

                // 绘制描述
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(type.desc, type.x, type.y + 10);
            });

            // 绘制示例
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('常见失配场景：', 50, 250);

            const examples = [
                '• 构件A需要Java 8，构件B需要Java 11',
                '• 构件采用同步调用，系统期望异步处理',
                '• 构件输出XML格式，接收方期望JSON格式'
            ];

            examples.forEach((example, index) => {
                ctx.fillText(example, 70, 280 + index * 30);
            });
        }

        // 演示构件失配
        function demonstrateComponentMismatch(type) {
            const canvas = document.getElementById('componentCanvas');
            const ctx = canvasContexts['componentCanvas'];
            if (!ctx) return;

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制基础图形
                drawComponentDiagram();

                // 根据类型添加动画效果
                switch(type) {
                    case 'infrastructure':
                        animateInfrastructureMismatch(ctx, step);
                        break;
                    case 'control':
                        animateControlMismatch(ctx, step);
                        break;
                    case 'data':
                        animateDataMismatch(ctx, step);
                        break;
                }

                step++;
                if (step < 120) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 动画：基础设施失配
        function animateInfrastructureMismatch(ctx, step) {
            const x = 150;
            const y = 300;

            // 绘制冲突的环境要求
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';

            if (step % 60 < 30) {
                ctx.fillText('Java 8', x - 50, y);
                ctx.fillText('vs', x, y);
                ctx.fillText('Java 11', x + 50, y);

                // 绘制冲突符号
                ctx.strokeStyle = '#ff0000';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(x - 30, y - 20);
                ctx.lineTo(x + 30, y + 20);
                ctx.moveTo(x + 30, y - 20);
                ctx.lineTo(x - 30, y + 20);
                ctx.stroke();
            }
        }

        // 动画：控制模型失配
        function animateControlMismatch(ctx, step) {
            const x = 400;
            const y = 300;

            ctx.fillStyle = '#4ecdc4';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';

            if (step % 60 < 30) {
                ctx.fillText('同步调用', x - 50, y);
                ctx.fillText('vs', x, y);
                ctx.fillText('异步处理', x + 50, y);

                // 绘制箭头表示调用方式
                drawArrow(ctx, x - 80, y + 20, x - 20, y + 20, '#4ecdc4');
                drawCurvedArrow(ctx, x + 20, y + 20, x + 80, y + 20, '#4ecdc4');
            }
        }

        // 动画：数据模型失配
        function animateDataMismatch(ctx, step) {
            const x = 650;
            const y = 300;

            ctx.fillStyle = '#45b7d1';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';

            if (step % 60 < 30) {
                ctx.fillText('XML', x - 50, y);
                ctx.fillText('vs', x, y);
                ctx.fillText('JSON', x + 50, y);

                // 绘制数据格式示例
                ctx.font = '10px Microsoft YaHei';
                ctx.fillText('<data>', x - 50, y + 20);
                ctx.fillText('{"data"}', x + 50, y + 20);
            }
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // 绘制弯曲箭头
        function drawCurvedArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.quadraticCurveTo((fromX + toX) / 2, fromY - 20, toX, toY);
            ctx.stroke();
        }

        // 绘制连接子图
        function drawConnectorDiagram() {
            const canvas = document.getElementById('connectorCanvas');
            const ctx = canvasContexts['connectorCanvas'];
            if (!ctx) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('连接子失配问题', canvas.width/2, 30);

            // 绘制构件和连接子
            drawComponent(ctx, 150, 150, '构件A', '#ff6b6b');
            drawConnector(ctx, 300, 150, '连接子', '#ffd93d');
            drawComponent(ctx, 450, 150, '构件B', '#4ecdc4');

            // 绘制连接线
            drawArrow(ctx, 200, 150, 270, 150, '#666');
            drawArrow(ctx, 330, 150, 400, 150, '#666');

            // 绘制失配类型说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('连接子失配类型：', 50, 250);

            const connectorMismatches = [
                '1. 交互协议失配 - 通信方式不匹配',
                '2. 数据模型失配 - 数据传输格式冲突'
            ];

            connectorMismatches.forEach((mismatch, index) => {
                ctx.fillText(mismatch, 70, 280 + index * 30);
            });
        }

        // 绘制构件
        function drawComponent(ctx, x, y, name, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x - 50, y - 30, 100, 60);
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 50, y - 30, 100, 60);

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 5);
        }

        // 绘制连接子
        function drawConnector(ctx, x, y, name, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 3);
        }

        // 演示连接子失配
        function demonstrateConnectorMismatch(type) {
            const canvas = document.getElementById('connectorCanvas');
            const ctx = canvasContexts['connectorCanvas'];
            if (!ctx) return;

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawConnectorDiagram();

                if (type === 'protocol') {
                    animateProtocolMismatch(ctx, step);
                } else if (type === 'datamodel') {
                    animateDataModelMismatch(ctx, step);
                }

                step++;
                if (step < 120) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 动画：协议失配
        function animateProtocolMismatch(ctx, step) {
            const phase = Math.floor(step / 40) % 3;

            ctx.fillStyle = '#ff0000';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';

            switch(phase) {
                case 0:
                    ctx.fillText('构件A发送同步请求', 400, 320);
                    break;
                case 1:
                    ctx.fillText('连接子期望异步消息', 400, 320);
                    break;
                case 2:
                    ctx.fillText('协议不匹配！', 400, 320);
                    // 绘制错误标识
                    ctx.strokeStyle = '#ff0000';
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.arc(300, 150, 50, 0, 2 * Math.PI);
                    ctx.stroke();
                    break;
            }
        }

        // 动画：数据模型失配
        function animateDataModelMismatch(ctx, step) {
            const phase = Math.floor(step / 40) % 3;

            ctx.fillStyle = '#ff0000';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';

            switch(phase) {
                case 0:
                    ctx.fillText('构件A输出: {id: 1, name: "test"}', 400, 320);
                    break;
                case 1:
                    ctx.fillText('连接子期望: <item id="1">test</item>', 400, 320);
                    break;
                case 2:
                    ctx.fillText('数据格式不匹配！', 400, 320);
                    break;
            }
        }

        // 绘制体系结构图
        function drawArchitectureDiagram() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvasContexts['architectureCanvas'];
            if (!ctx) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('全局体系结构失配', canvas.width/2, 30);

            // 绘制系统组件
            const components = [
                { name: '子系统A', x: 150, y: 100, assumption: '分层架构' },
                { name: '子系统B', x: 400, y: 100, assumption: '微服务架构' },
                { name: '子系统C', x: 650, y: 100, assumption: '事件驱动架构' }
            ];

            components.forEach((comp, index) => {
                // 绘制组件
                ctx.fillStyle = ['#ff6b6b', '#4ecdc4', '#45b7d1'][index];
                ctx.fillRect(comp.x - 60, comp.y - 40, 120, 80);
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.strokeRect(comp.x - 60, comp.y - 40, 120, 80);

                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(comp.name, comp.x, comp.y - 10);

                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(comp.assumption, comp.x, comp.y + 10);
            });

            // 绘制冲突指示
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);

            // 连接线表示冲突
            ctx.beginPath();
            ctx.moveTo(210, 100);
            ctx.lineTo(340, 100);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(460, 100);
            ctx.lineTo(590, 100);
            ctx.stroke();

            ctx.setLineDash([]);

            // 绘制说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('不同子系统对整体架构的理解存在根本分歧', 400, 220);

            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('导致集成困难和系统不稳定', 400, 250);
        }

        // 演示体系结构失配
        function demonstrateArchitectureMismatch() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvasContexts['architectureCanvas'];
            if (!ctx) return;

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawArchitectureDiagram();

                // 添加动画效果
                const phase = Math.floor(step / 30) % 4;

                ctx.fillStyle = '#ff0000';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';

                switch(phase) {
                    case 0:
                        ctx.fillText('子系统A期望分层调用', 400, 300);
                        break;
                    case 1:
                        ctx.fillText('子系统B采用服务发现', 400, 300);
                        break;
                    case 2:
                        ctx.fillText('子系统C使用事件总线', 400, 300);
                        break;
                    case 3:
                        ctx.fillText('架构假设冲突！', 400, 300);
                        // 绘制警告符号
                        ctx.fillStyle = '#ff0000';
                        ctx.font = 'bold 48px Microsoft YaHei';
                        ctx.fillText('⚠', 400, 350);
                        break;
                }

                step++;
                if (step < 120) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 演示解决方案
        function demonstrateSolution() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvasContexts['architectureCanvas'];
            if (!ctx) return;

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制解决方案
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('失配问题解决方案', canvas.width/2, 30);

                const solutions = [
                    { name: '检测失配', x: 200, y: 120, color: '#ff6b6b' },
                    { name: '适配器模式', x: 400, y: 120, color: '#4ecdc4' },
                    { name: '中间件技术', x: 600, y: 120, color: '#45b7d1' }
                ];

                solutions.forEach((sol, index) => {
                    if (step > index * 30) {
                        const progress = Math.min((step - index * 30) / 30, 1);
                        const radius = 50 * progress;

                        ctx.beginPath();
                        ctx.arc(sol.x, sol.y, radius, 0, 2 * Math.PI);
                        ctx.fillStyle = sol.color;
                        ctx.fill();
                        ctx.strokeStyle = '#fff';
                        ctx.lineWidth = 3;
                        ctx.stroke();

                        if (progress > 0.5) {
                            ctx.fillStyle = '#fff';
                            ctx.font = 'bold 14px Microsoft YaHei';
                            ctx.textAlign = 'center';
                            ctx.fillText(sol.name, sol.x, sol.y + 5);
                        }
                    }
                });

                if (step > 90) {
                    // 绘制流程箭头
                    drawArrow(ctx, 250, 120, 350, 120, '#666');
                    drawArrow(ctx, 450, 120, 550, 120, '#666');

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('通过系统化方法解决失配问题', 400, 220);
                }

                step++;
                if (step < 150) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            // 移除所有选项的样式
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 添加对应样式
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('quizResult').innerHTML =
                    '<div style="color: #4caf50; font-weight: bold; margin-top: 15px;">✓ 回答正确！</div>';
            } else {
                element.classList.add('wrong');
                // 同时显示正确答案
                document.querySelectorAll('.quiz-option')[3].classList.add('correct');
                document.getElementById('quizResult').innerHTML =
                    '<div style="color: #f44336; font-weight: bold; margin-top: 15px;">✗ 回答错误，正确答案是D</div>';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCanvases();
            updateProgress();
            drawOverviewDiagram();
        });
    </script>
</body>
</html>
