<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java中 == 和 equals() 的区别</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            line-height: 1.6;
            color: #333;
            background-color: #f4f7f9;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 25px 40px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        canvas {
            background-color: #ecf0f1;
            border-radius: 8px;
            width: 100%;
        }
        .interactive-area {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            align-items: flex-start;
        }
        .controls {
            flex-basis: 250px;
            flex-shrink: 0;
        }
        .canvas-container {
            flex-grow: 1;
        }
        button {
            display: block;
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s, transform 0.1s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:active {
            transform: scale(0.98);
        }
        .explanation {
            background-color: #e9f5ff;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        code-block {
            display: block;
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            position: relative;
        }
        pre {
            margin: 0;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 14px;
        }
        .run-button {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        .run-button:hover {
            background-color: #229954;
        }
        .output {
            background-color: #333;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            display: none;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Java中 <code>==</code> 和 <code>equals()</code> 的区别</h1>

        <div class="explanation">
            <p><strong>核心思想:</strong></p>
            <ul>
                <li><code>==</code>: 如果比较的是<strong>基本数据类型</strong>（如 <code>int</code>, <code>double</code>），它比较的是<strong>值</strong>。如果比较的是<strong>引用类型</strong>（如 <code>String</code>, 或其他对象），它比较的是对象的<strong>内存地址</strong>。</li>
                <li><code>.equals()</code>: 这是<code>Object</code>类的一个方法，所有对象都有。它默认的行为和 <code>==</code> 一样（比较内存地址）。但是，很多类（比如 <code>String</code>）都<strong>重写（override）</strong>了这个方法，让它比较对象的<strong>内容</strong>是否相等。</li>
            </ul>
        </div>

        <h2>交互动画演示</h2>
        <p>点击下方按钮，观看不同场景下 <code>==</code> 和 <code>equals()</code> 的工作方式。</p>

        <div class="interactive-area">
            <div class="controls">
                <h3>选择场景</h3>
                <button onclick="setScene(1)">1. 基本类型比较 (==)</button>
                <button onclick="setScene(2)">2. 引用类型比较 (==)</button>
                <button onclick="setScene(3)">3. 引用类型比较 (equals)</button>
                <button onclick="setScene(4)">4. String常量池 (==)</button>
            </div>
            <div class="canvas-container">
                <canvas id="demoCanvas" width="700" height="400"></canvas>
            </div>
        </div>

        <h2>代码示例</h2>
        <p>现在，我们来看看实际代码中的表现。点击"运行"按钮查看结果。</p>

        <code-block>
            <button class="run-button" onclick="runCode('code1', 'output1')">运行</button>
            <pre id="code1">
String a = new String("ab"); // a 是一个新的引用
String b = new String("ab"); // b 是另一个新的引用, 但对象内容一样
String aa = "ab"; // 字符串放入常量池
String bb = "ab"; // 从常量池中查找，发现已存在，直接使用

System.out.println("a == b: " + (a == b));
System.out.println("a.equals(b): " + a.equals(b));
System.out.println("aa == bb: " + (aa == bb));
System.out.println("42 == 42.0: " + (42 == 42.0));</pre>
            <div class="output" id="output1"></div>
        </code-block>

    </div>

    <script>
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 1;

        const colors = {
            stack: '#3498db',
            heap: '#2ecc71',
            pool: '#f1c40f',
            text: '#ffffff',
            arrow: '#e74c3c',
            comparison: '#e74c3c',
            darkText: '#2c3e50'
        };

        function drawBox(x, y, width, height, color, label, value) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + width / 2, y + 20);

            if (value) {
                ctx.font = 'bold 24px sans-serif';
                ctx.fillText(value, x + width / 2, y + height / 2 + 10);
            }
        }
        
        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = colors.arrow;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Draw arrowhead
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI / 6), toY - 15 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI / 6), toY - 15 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = colors.arrow;
            ctx.fill();
        }
        
        function drawText(text, x, y, size = 20, color = colors.darkText) {
             ctx.fillStyle = color;
             ctx.font = `bold ${size}px sans-serif`;
             ctx.textAlign = 'center';
             ctx.fillText(text, x, y);
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        let animationFrame;
        function setScene(scene) {
            currentScene = scene;
            if (animationFrame) cancelAnimationFrame(animationFrame);
            animate();
        }

        let progress = 0;
        let resultText = '';

        function animate() {
            progress += 0.02;
            if (progress > 1) {
                progress = 1;
            }

            clearCanvas();

            switch(currentScene) {
                case 1: drawScene1(); break;
                case 2: drawScene2(); break;
                case 3: drawScene3(); break;
                case 4: drawScene4(); break;
            }
            
            if (resultText) {
                drawText(resultText, canvas.width / 2, canvas.height - 20, 24, colors.comparison);
            }

            if(progress < 1) {
                animationFrame = requestAnimationFrame(animate);
            }
        }
        
        function drawScene1() {
            drawText("场景1: 比较基本类型 int a = 10; int b = 10;", canvas.width/2, 30);
            
            const boxWidth = 120, boxHeight = 80;
            const y = 150;
            const x1 = canvas.width/2 - boxWidth - 50;
            const x2 = canvas.width/2 + 50;

            drawBox(x1, y, boxWidth, boxHeight, colors.stack, "int a", "10");
            drawBox(x2, y, boxWidth, boxHeight, colors.stack, "int b", "10");

            drawText("a == b ?", canvas.width / 2, y - 40);

            if(progress === 1) {
                ctx.strokeStyle = colors.comparison;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(x1 + boxWidth/2, y + 45);
                ctx.lineTo(x2 + boxWidth/2, y + 45);
                ctx.stroke();
                resultText = "结果: true (值相等)";
            }
        }

        function drawMemoryLayout() {
            // Stack area
            ctx.strokeStyle = colors.stack;
            ctx.lineWidth = 2;
            ctx.strokeRect(20, 80, 200, 280);
            drawText("栈 (Stack)", 120, 70, 18);
            drawText("存储变量和地址", 120, 380, 14);

            // Heap area
            ctx.strokeStyle = colors.heap;
            ctx.strokeRect(300, 80, 380, 280);
            drawText("堆 (Heap)", 490, 70, 18);
            drawText("存储对象实例", 490, 380, 14);
        }

        function drawScene2() {
            drawText("场景2: new String() 引用比较 (==)", canvas.width/2, 30);
            drawMemoryLayout();

            // Stack variables
            drawBox(40, 120, 160, 50, colors.stack, "String a", "@addr1");
            drawBox(40, 220, 160, 50, colors.stack, "String b", "@addr2");
            
            // Heap objects
            const heapX = 350, heapW = 280, heapH = 60;
            drawBox(heapX, 110, heapW, heapH, colors.heap, "String Object (@addr1)", '"ab"');
            drawBox(heapX, 210, heapW, heapH, colors.heap, "String Object (@addr2)", '"ab"');

            // Pointers
            drawArrow(200, 145, heapX, 140);
            drawArrow(200, 245, heapX, 240);

            drawText("a == b ?", canvas.width / 2, canvas.height/2 + 10);
            
            if (progress === 1) {
                ctx.strokeStyle = colors.comparison;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(120, 170);
                ctx.lineTo(120, 220);
                ctx.stroke();
                resultText = "结果: false (内存地址不同)";
            }
        }
        
        function drawScene3() {
            drawText("场景3: new String() 引用比较 (equals)", canvas.width/2, 30);
            drawMemoryLayout();

            drawBox(40, 120, 160, 50, colors.stack, "String a", "@addr1");
            drawBox(40, 220, 160, 50, colors.stack, "String b", "@addr2");
            const heapX = 350, heapW = 280, heapH = 60;
            drawBox(heapX, 110, heapW, heapH, colors.heap, "String Object (@addr1)", '"ab"');
            drawBox(heapX, 210, heapW, heapH, colors.heap, "String Object (@addr2)", '"ab"');
            drawArrow(200, 145, heapX, 140);
            drawArrow(200, 245, heapX, 240);

            drawText("a.equals(b) ?", canvas.width / 2, canvas.height/2 + 10);

            if (progress === 1) {
                ctx.strokeStyle = colors.comparison;
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]);
                // line from a to object
                ctx.beginPath();
                ctx.moveTo(200, 145);
                ctx.lineTo(heapX + heapW/2, 140);
                ctx.stroke();
                // line from b to object
                ctx.beginPath();
                ctx.moveTo(200, 245);
                ctx.lineTo(heapX + heapW/2, 240);
                ctx.stroke();
                
                ctx.setLineDash([]);
                // line between object contents
                ctx.beginPath();
                ctx.moveTo(heapX + heapW/2, 170);
                ctx.lineTo(heapX + heapW/2, 210);
                ctx.stroke();
                
                resultText = "结果: true (内容相同)";
            }
        }

        function drawScene4() {
             drawText('场景4: String 常量池比较 (==)', canvas.width / 2, 30);
             drawMemoryLayout();

            // String Constant Pool
            ctx.strokeStyle = colors.pool;
            ctx.lineWidth = 2;
            const poolX=320, poolY=100, poolW=340, poolH=240;
            ctx.strokeRect(poolX, poolY, poolW, poolH);
            drawText("字符串常量池", poolX + poolW/2, poolY-10, 16);


            // Stack variables
            drawBox(40, 120, 160, 50, colors.stack, 'String aa', "@addr3");
            drawBox(40, 220, 160, 50, colors.stack, 'String bb', "@addr3");
            
            // Pool object
            const heapX = 380, heapW = 220, heapH = 60;
            drawBox(heapX, 180, heapW, heapH, colors.pool, 'String Object (@addr3)', '"ab"');

            // Pointers
            drawArrow(200, 145, heapX, 210);
            drawArrow(200, 245, heapX, 210);

            drawText("aa == bb ?", canvas.width / 2, canvas.height/2 + 40);

            if (progress === 1) {
                 ctx.strokeStyle = colors.comparison;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(120, 170);
                ctx.lineTo(120, 220);
                ctx.stroke();
                resultText = "结果: true (指向同一个内存地址)";
            }
        }
        
        // Initial draw
        setScene(1);

        // Code runner
        function runCode(codeId, outputId) {
            const outputEl = document.getElementById(outputId);
            let outputText = '';
            if (codeId === 'code1') {
                const a = new String("ab");
                const b = new String("ab");
                const aa = "ab";
                const bb = "ab";
                outputText = `a == b: ${a == b}\na.equals(b): ${a.equals(b)}\naa == bb: ${aa == bb}\n42 == 42.0: ${42 == 42.0}`;
            }
            outputEl.innerText = outputText;
            outputEl.style.display = 'block';
        }

        // Reset animation when button is clicked
        document.querySelectorAll('.controls button').forEach(button => {
            button.addEventListener('click', () => {
                progress = 0;
                resultText = '';
            });
        });

    </script>
</body>
</html> 