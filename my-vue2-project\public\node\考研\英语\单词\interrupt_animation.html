<!DOCTYPE html>
<html>
<head>
<title>Interrupt Animation</title>
<meta charset="UTF-8">
<style>
    body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #f5f5dc; /* Beige */
    }
    #canvas {
        border: 1px solid #000;
        background-color: #ffffff;
    }
    #explanation {
        width: 800px;
        text-align: left;
        margin-top: 20px;
    }
    #controls {
        margin-top: 10px;
    }
    button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
    }
</style>
</head>
<body>

<h1>Word Animation: Interrupt (打断)</h1>
<canvas id="canvas" width="800" height="400"></canvas>
<div id="controls">
    <button id="playBtn">Play Animation</button>
</div>
<div id="explanation">
    <h2>Interrupt (打断) = inter- (在...之间) + rupt (断裂)</h2>
    <p><b>故事:</b> 想象一下，有两个人（左边和右边）正在愉快地交谈，他们之间有一条看不见的"对话线"。突然，第三个人从他们中间跑了过去，把这条"对话线"给"弄断"了。这个"闯入并弄断对话"的动作，就是 "interrupt"。</p>
    <p><b>inter- (在...之间):</b> 动画中，我们会看到一个奔跑的人冲入到正在交谈的两个人<b>之间</b>。</p>
    <p><b>rupt (断裂):</b> 当他穿过时，代表对话的连线会发生<b>断裂</b>。</p>
    <p><b>交互:</b> 点击 "Play Animation" 按钮，观看这个打断别人谈话的故事。</p>
</div>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let animationId;
let progress = 0; // 0 to 1

const personA = { x: 150, y: 300 };
const personB = { x: 650, y: 300 };
const interrupter = { startX: 400, startY: 450, endX: 400, endY: 250 };

function drawPerson(x, y, label) {
    // Body
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x, y - 40);
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Head
    ctx.beginPath();
    ctx.arc(x, y - 50, 10, 0, Math.PI * 2);
    ctx.fillStyle = 'black';
    ctx.fill();
    
    // Arms
    ctx.beginPath();
    ctx.moveTo(x - 15, y - 30);
    ctx.lineTo(x + 15, y - 30);
    ctx.stroke();

    // Label
    ctx.fillStyle = 'black';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(label, x, y + 20);
}

function drawInterrupter(x, y) {
    ctx.save();
    ctx.translate(x, y);

    // Body
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(0, -30);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Head
    ctx.beginPath();
    ctx.arc(0, -40, 8, 0, Math.PI * 2);
    ctx.fillStyle = 'red';
    ctx.fill();

    // Legs (running)
    const legAngle = Math.sin(Date.now() / 100) * (Math.PI / 6);
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(10 * Math.sin(legAngle), 10 * Math.cos(legAngle));
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(-10 * Math.sin(legAngle), 10 * Math.cos(legAngle));
    ctx.stroke();

    ctx.restore();
}


function drawConversationLine(p1, p2, breakPoint) {
    ctx.beginPath();
    ctx.setLineDash([5, 5]);
    ctx.strokeStyle = 'blue';
    ctx.lineWidth = 2;
    
    const midY = p1.y - 100;

    if (breakPoint === null) {
        // Draw full line
        ctx.moveTo(p1.x, midY);
        ctx.lineTo(p2.x, midY);
        ctx.stroke();
    } else {
        // Draw broken line
        const breakGap = 20;
        ctx.moveTo(p1.x, midY);
        ctx.lineTo(breakPoint - breakGap, midY);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(breakPoint + breakGap, midY);
        ctx.lineTo(p2.x, midY);
        ctx.stroke();
    }
    
    ctx.setLineDash([]); // Reset line dash
}

function drawAnnotation(text, x, y, color = 'red', size = '24px') {
    ctx.font = `${size} Arial`;
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.fillText(text, x, y);
}

function animate() {
    progress += 0.01;
    if (progress > 1) {
        progress = 1;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const interrupterX = interrupter.startX;
    const interrupterY = interrupter.startY - (interrupter.startY - interrupter.endY) * progress;

    drawPerson(personA.x, personA.y, 'Person A');
    drawPerson(personB.x, personB.y, 'Person B');
    drawInterrupter(interrupterX, interrupterY);

    const conversationLineY = personA.y - 100;
    let breakPoint = null;

    // The interrupter crosses the line when its Y position is close
    if (Math.abs(interrupterY - conversationLineY) < 50 && progress < 0.9) {
        breakPoint = interrupterX;
    }

    drawConversationLine(personA, personB, breakPoint);

    // Annotations
    if (progress > 0.1 && progress < 0.9) {
        // Label the interrupter's action
        drawAnnotation('inter- (在...之间)', interrupterX, interrupterY + 40);
    }
    
    if (breakPoint !== null) {
         drawAnnotation('rupt (断裂)', interrupterX, conversationLineY - 20, 'purple');
    }
    
    if (progress < 0.2) {
        drawAnnotation('谈话中...', canvas.width / 2, 50, 'blue', '20px');
    }

    if (progress >= 1) {
        cancelAnimationFrame(animationId);
        playBtn.disabled = false;
        playBtn.textContent = "Play Again";
        drawAnnotation('被打断了!', canvas.width / 2, 50, 'red', '20px');
    } else {
        animationId = requestAnimationFrame(animate);
    }
}

function startAnimation() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    progress = 0;
    playBtn.disabled = true;
    playBtn.textContent = "Animating...";
    animate();
}

playBtn.addEventListener('click', startAnimation);

// Initial draw
ctx.clearRect(0, 0, canvas.width, canvas.height);
drawPerson(personA.x, personA.y, 'Person A');
drawPerson(personB.x, personB.y, 'Person B');
drawInterrupter(interrupter.startX, interrupter.startY);
drawConversationLine(personA, personB, null);

</script>

</body>
</html> 