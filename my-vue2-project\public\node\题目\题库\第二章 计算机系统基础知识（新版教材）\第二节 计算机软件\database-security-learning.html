<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库安全机制 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #animationCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        .question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
            animation: wrongShake 0.6s ease-out;
        }

        .option-label {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #333;
        }

        .result {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            border-radius: 15px;
            font-size: 1.2rem;
            opacity: 0;
            transform: translateY(20px);
        }

        .result.show {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.5s ease;
        }

        .result.correct {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }

        .result.wrong {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }

        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">数据库安全机制</h1>
            <p class="subtitle">通过动画学习数据库安全的核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🔒 数据库安全概念动画演示</h2>
            
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="500"></canvas>
            </div>
            
            <div class="control-buttons">
                <button class="btn" onclick="startAnimation()">开始演示</button>
                <button class="btn" onclick="resetAnimation()">重新开始</button>
            </div>

            <div class="explanation">
                <h3>💡 核心概念解释</h3>
                <p>数据库安全机制的核心是<strong>保护数据结构不被外部直接访问</strong>。想象数据库就像一个银行金库，我们不能让外人直接进入金库查看所有保险箱的布局和结构，而是通过专门的服务窗口来处理业务。</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">📝 知识测试</h2>
            
            <div class="question">
                数据库的安全机制中，通过提供（ ）供第三方开发人员调用进行数据更新，从而保证数据库的关系模式不被第三方所获取。
            </div>

            <div class="options">
                <div class="option" onclick="selectOption(this, 'A', false)">
                    <div class="option-label">A</div>
                    <div class="option-text">索引</div>
                </div>
                <div class="option" onclick="selectOption(this, 'B', false)">
                    <div class="option-label">B</div>
                    <div class="option-text">视图</div>
                </div>
                <div class="option" onclick="selectOption(this, 'C', true)">
                    <div class="option-label">C</div>
                    <div class="option-text">存储过程</div>
                </div>
                <div class="option" onclick="selectOption(this, 'D', false)">
                    <div class="option-label">D</div>
                    <div class="option-text">触发器</div>
                </div>
            </div>

            <div id="result" class="result"></div>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.querySelector('.floating-elements');
            for (let i = 0; i < 6; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.width = (Math.random() * 60 + 20) + 'px';
                element.style.height = element.style.width;
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // Canvas 动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;

        function drawDatabase() {
            // 绘制数据库图标
            ctx.fillStyle = '#4a90e2';
            ctx.fillRect(100, 200, 120, 80);
            ctx.fillStyle = '#357abd';
            ctx.fillRect(100, 190, 120, 20);
            ctx.fillStyle = '#2c5f8a';
            ctx.fillRect(100, 280, 120, 20);
            
            // 数据库标签
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('数据库', 160, 320);
        }

        function drawThirdParty() {
            // 绘制第三方开发者
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(600, 240, 40, 0, 2 * Math.PI);
            ctx.fill();
            
            // 开发者标签
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('第三方开发者', 600, 300);
        }

        function drawStoredProcedure() {
            // 绘制存储过程盒子
            ctx.fillStyle = '#26d0ce';
            ctx.fillRect(350, 180, 100, 120);
            ctx.fillStyle = '#1ba3a1';
            ctx.strokeRect(350, 180, 100, 120);
            
            // 存储过程标签
            ctx.fillStyle = '#fff';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('存储', 400, 230);
            ctx.fillText('过程', 400, 250);
        }

        function drawArrows() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            
            // 从第三方到存储过程的箭头
            ctx.beginPath();
            ctx.moveTo(560, 240);
            ctx.lineTo(460, 240);
            ctx.stroke();
            
            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(460, 240);
            ctx.lineTo(470, 235);
            ctx.moveTo(460, 240);
            ctx.lineTo(470, 245);
            ctx.stroke();
            
            // 从存储过程到数据库的箭头
            ctx.beginPath();
            ctx.moveTo(340, 240);
            ctx.lineTo(230, 240);
            ctx.stroke();
            
            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(230, 240);
            ctx.lineTo(240, 235);
            ctx.moveTo(230, 240);
            ctx.lineTo(240, 245);
            ctx.stroke();
        }

        function drawProtectionShield() {
            // 绘制保护盾牌
            ctx.fillStyle = 'rgba(40, 167, 69, 0.3)';
            ctx.beginPath();
            ctx.arc(400, 240, 80, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 盾牌图标
            ctx.fillStyle = '#28a745';
            ctx.font = '30px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🛡️', 400, 250);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            if (animationStep >= 1) drawDatabase();
            if (animationStep >= 2) drawThirdParty();
            if (animationStep >= 3) drawStoredProcedure();
            if (animationStep >= 4) drawArrows();
            if (animationStep >= 5) drawProtectionShield();
            
            if (animationStep >= 6) {
                // 添加说明文字
                ctx.fillStyle = '#333';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('存储过程保护数据库结构安全', 400, 100);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('第三方只能调用存储过程，无法直接访问数据库结构', 400, 130);
            }
        }

        function startAnimation() {
            animationStep = 0;
            const interval = setInterval(() => {
                animationStep++;
                animate();
                if (animationStep > 6) {
                    clearInterval(interval);
                }
            }, 1000);
        }

        function resetAnimation() {
            animationStep = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.width);
        }

        // 选择答案
        function selectOption(element, option, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            const resultDiv = document.getElementById('result');
            
            if (isCorrect) {
                element.classList.add('correct');
                resultDiv.innerHTML = `
                    <div>🎉 恭喜你答对了！</div>
                    <div style="margin-top: 15px; font-size: 1rem;">
                        <strong>解析：</strong>存储过程是数据库提供的安全机制，它允许第三方调用预定义的代码来操作数据，而不需要暴露数据库的内部结构。就像银行的ATM机，你可以取钱但看不到金库的布局。
                    </div>
                `;
                resultDiv.className = 'result correct show';
            } else {
                element.classList.add('wrong');
                // 同时显示正确答案
                document.querySelectorAll('.option')[2].classList.add('correct');
                
                let explanation = '';
                switch(option) {
                    case 'A':
                        explanation = '索引主要用于提高查询性能，不是安全机制。';
                        break;
                    case 'B':
                        explanation = '视图虽然可以隐藏部分数据，但主要用于简化查询，不是专门的安全调用接口。';
                        break;
                    case 'D':
                        explanation = '触发器是自动执行的，不是供第三方主动调用的接口。';
                        break;
                }
                
                resultDiv.innerHTML = `
                    <div>❌ 答案不正确</div>
                    <div style="margin-top: 10px;">${explanation}</div>
                    <div style="margin-top: 15px; font-size: 1rem;">
                        <strong>正确答案是C - 存储过程</strong><br>
                        存储过程就像一个安全的服务窗口，第三方可以调用它来操作数据，但无法看到数据库的内部结构。
                    </div>
                `;
                resultDiv.className = 'result wrong show';
            }
        }

        // 初始化
        createFloatingElements();
        resetAnimation();
    </script>
</body>
</html>
