<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Induce Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
        }
        .translation {
            margin-top: 10px;
            font-size: 1.2em;
        }
        .explanation {
            margin-top: 10px;
            text-align: left;
            max-width: 600px;
        }
        button {
            margin-top: 10px;
            padding: 10px 20px;
            font-size: 1em;
            cursor: pointer;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Induce</h1>
    <canvas id="wordCanvas" width="600" height="300"></canvas>
    <div class="translation">
        <p><strong>翻译:</strong> v. 引诱；劝说；引起；导致</p>
    </div>
    <div class="explanation">
        <p><strong>词源拆解:</strong></p>
        <p>单词 <strong>induce</strong> 由两部分组成：</p>
        <ul>
            <li><strong>in-</strong>: 一个前缀，意思是 "进入" (into) 或 "在...之内" (in)。</li>
            <li><strong>duce</strong>: 一个词根，源自拉丁语 'ducere'，意思是 "引导" (to lead)。</li>
        </ul>
        <p><strong>故事记忆法:</strong></p>
        <p>想象一个场景：一位智者（代表词根 'duce' - 引导）正在温和地<strong>引导</strong>一个犹豫不决的人（代表被影响的对象）<strong>进入</strong>（代表前缀 'in-'）一个充满机遇的新大门。智者没有强迫，而是通过智慧的言语和令人信服的理由，<strong>劝说</strong>他迈出第一步。这个行为就是 "induce" —— 不是强迫，而是有说服力地引导，最终<strong>导致</strong>了对方态度的转变和行动的发生。</p>
        <p>这个动画将为你生动地展示这个"引导进入"的过程。</p>
    </div>
    <button id="replay">Replay Animation</button>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const replayBtn = document.getElementById('replay');

    let animationFrameId;

    const stickMan = {
        x: 100,
        y: 200,
        targetX: 450,
        speed: 1,
        draw() {
            // Body
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x, this.y - 40);
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 3;
            ctx.stroke();

            // Head
            ctx.beginPath();
            ctx.arc(this.x, this.y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();

            // Arms
            ctx.beginPath();
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x - 15, this.y - 20);
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x + 15, this.y - 20);
            ctx.stroke();

            // Legs
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x - 10, this.y + 20);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x + 10, this.y + 20);
            ctx.stroke();
        }
    };
    
    const guide = {
        x: 50,
        y: 200,
        draw() {
            // A simple representation of a guide/force
            ctx.font = '20px Arial';
            ctx.fillStyle = 'blue';
            ctx.fillText('persuasion', this.x, this.y - 50);
             // arrow
            ctx.beginPath();
            ctx.moveTo(this.x + 80, this.y-55);
            ctx.lineTo(stickMan.x - 20, this.y-55);
            ctx.lineTo(stickMan.x-30, this.y-50);
            ctx.moveTo(stickMan.x - 20, this.y-55);
            ctx.lineTo(stickMan.x-30, this.y-60);
            ctx.strokeStyle = 'blue';
            ctx.stroke();
        }
    }

    const door = {
        x: 500,
        y: 150,
        width: 50,
        height: 80,
        draw() {
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(this.x, this.y, this.width, this.height);
            ctx.fillStyle = 'yellow';
            ctx.beginPath();
            ctx.arc(this.x + 40, this.y + 40, 5, 0, Math.PI * 2);
            ctx.fill();
            ctx.font = '20px Arial';
            ctx.fillStyle = 'green';
            ctx.fillText('Action', this.x -5, this.y - 10);
        }
    };
    
    let inText = { alpha: 0 };
    let duceText = { alpha: 0 };

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw elements
        stickMan.draw();
        guide.draw();
        door.draw();

        // Draw "in-"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(0, 0, 255, ${inText.alpha})`;
        ctx.fillText('in-', 250, 100);
        
        // Draw "duce"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(255, 0, 0, ${duceText.alpha})`;
        ctx.fillText('duce', 320, 100);

    }

    function animate() {
        // Update state
        if (stickMan.x < stickMan.targetX) {
            stickMan.x += stickMan.speed;
        }

        // Animate text visibility
        if (stickMan.x < 250) {
            duceText.alpha = Math.min(1, duceText.alpha + 0.02);
        } else {
            inText.alpha = Math.min(1, inText.alpha + 0.02);
        }

        draw();

        if (stickMan.x < stickMan.targetX || inText.alpha < 1 || duceText.alpha < 1) {
            animationFrameId = requestAnimationFrame(animate);
        }
    }
    
    function startAnimation() {
        cancelAnimationFrame(animationFrameId);
        stickMan.x = 100;
        inText.alpha = 0;
        duceText.alpha = 0;
        animate();
    }

    replayBtn.addEventListener('click', startAnimation);

    // Initial animation start
    startAnimation();

</script>
</body>
</html> 