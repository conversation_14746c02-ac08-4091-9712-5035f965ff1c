<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合框架的优势 - 互动学习版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #f5576c 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 50px 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 100px;
            animation: fadeInDown 1.8s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 4.5rem;
            margin-bottom: 40px;
            text-shadow: 5px 5px 10px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #ffeaa7, #fd79a8, #74b9ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
        }

        .subtitle {
            color: rgba(255,255,255,0.95);
            font-size: 1.6rem;
            margin-bottom: 50px;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 35px;
            padding: 70px;
            margin-bottom: 80px;
            box-shadow: 0 35px 70px rgba(0,0,0,0.15);
            backdrop-filter: blur(25px);
            border: 2px solid rgba(255,255,255,0.3);
            opacity: 0;
            transform: translateY(100px);
            animation: slideInUp 1.5s ease-out forwards;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.8s;
        }

        .section:hover::before {
            left: 100%;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }
        .section:nth-child(6) { animation-delay: 1.0s; }
        .section:nth-child(7) { animation-delay: 1.2s; }
        .section:nth-child(8) { animation-delay: 1.4s; }

        .section-title {
            font-size: 3.5rem;
            color: #2d3436;
            margin-bottom: 60px;
            text-align: center;
            position: relative;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 3px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 60px 0;
            position: relative;
        }

        canvas {
            border-radius: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.5s ease;
        }

        canvas:hover {
            transform: scale(1.03) translateY(-8px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.3);
        }

        .text-content {
            font-size: 1.4rem;
            line-height: 2.5;
            color: #2d3436;
            margin: 50px 0;
            text-align: center;
        }

        .highlight {
            background: linear-gradient(120deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            padding: 8px 18px;
            border-radius: 15px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin: 0 10px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            transform: translateY(-3px);
            transition: all 0.3s ease;
        }

        .highlight:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 12px 30px rgba(0,0,0,0.3);
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            color: white;
            border: none;
            padding: 25px 50px;
            border-radius: 40px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.5s ease;
            margin: 25px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .interactive-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .interactive-btn:hover::before {
            left: 100%;
        }

        .interactive-btn:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }

        .benefit-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px;
            border-radius: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .benefit-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.5s ease;
        }

        .benefit-card:hover::before {
            transform: scale(1);
        }

        .benefit-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .benefit-card h3 {
            font-size: 2.2rem;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .benefit-card p {
            font-size: 1.1rem;
            line-height: 1.8;
            position: relative;
            z-index: 1;
        }

        .progress-container {
            background: rgba(255,255,255,0.3);
            border-radius: 25px;
            padding: 30px;
            margin: 40px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255,255,255,0.3);
            border-radius: 6px;
            overflow: hidden;
            margin: 25px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            width: 0%;
            transition: width 1.2s ease;
            border-radius: 6px;
        }

        .game-area {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 30px;
            padding: 50px;
            margin: 50px 0;
            border: 4px dashed #667eea;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }

        .game-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 49%, rgba(102, 126, 234, 0.1) 50%, transparent 51%);
            background-size: 20px 20px;
            animation: movePattern 10s linear infinite;
        }

        @keyframes movePattern {
            0% { background-position: 0 0; }
            100% { background-position: 20px 20px; }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-80px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-30px); }
        }

        .floating {
            animation: float 5s ease-in-out infinite;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .bounce {
            animation: bounce 1s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 集合框架的优势</h1>
            <p class="subtitle">探索现代编程的强大工具</p>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p style="color: white; margin: 0; font-size: 1.2rem; font-weight: bold;">学习进度</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 优势概览</h2>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="1000" height="600"></canvas>
            </div>
            <div class="text-content">
                <p>集合框架为现代编程带来了五大核心优势：</p>
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <h3>📈 容量自增长</h3>
                        <p>动态调整大小，无需担心容量限制</p>
                    </div>
                    <div class="benefit-card">
                        <h3>⚡ 高性能算法</h3>
                        <p>提供优化的数据结构和算法</p>
                    </div>
                    <div class="benefit-card">
                        <h3>🔄 API互操作</h3>
                        <p>不同API之间无缝传递数据</p>
                    </div>
                    <div class="benefit-card">
                        <h3>🛠️ 易于扩展</h3>
                        <p>方便扩展和改写，提高复用性</p>
                    </div>
                    <div class="benefit-card">
                        <h3>💰 降低成本</h3>
                        <p>减少维护成本和学习成本</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateOverview()">🎬 播放概览动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📈 优势一：容量自增长</h2>
            <div class="canvas-container">
                <canvas id="capacityCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">容量自增长</span></p>
                <p>就像一个神奇的魔法袋，可以根据需要自动扩容，永远不会装满！</p>
                <button class="interactive-btn" onclick="animateCapacity()">📈 观看自增长演示</button>
                <button class="interactive-btn" onclick="playCapacityGame()">🎮 容量增长游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 优势二：高性能数据结构和算法</h2>
            <div class="canvas-container">
                <canvas id="performanceCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">提供了高性能的数据结构和算法，使编码更轻松，提高了程序速度和质量</span></p>
                <p>就像拥有了一套专业的工具箱，每个工具都经过精心优化！</p>
                <button class="interactive-btn" onclick="animatePerformance()">⚡ 观看性能演示</button>
                <button class="interactive-btn" onclick="playPerformanceGame()">🎮 性能对比游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔄 优势三：API互操作性</h2>
            <div class="canvas-container">
                <canvas id="interoperabilityCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">允许不同API之间的互操作，API之间可以来回传递集合</span></p>
                <p>就像通用的数据护照，可以在不同的系统间自由通行！</p>
                <button class="interactive-btn" onclick="animateInteroperability()">🔄 观看互操作演示</button>
                <button class="interactive-btn" onclick="playInteroperabilityGame()">🎮 数据传递游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🛠️ 优势四：易于扩展和改写</h2>
            <div class="canvas-container">
                <canvas id="extensibilityCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">可以方便地扩展或改写集合，提高代码复用性和可操作性</span></p>
                <p>就像搭积木一样，可以轻松组合和重构！</p>
                <button class="interactive-btn" onclick="animateExtensibility()">🛠️ 观看扩展演示</button>
                <button class="interactive-btn" onclick="playExtensibilityGame()">🎮 代码重构游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">💰 优势五：降低开发成本</h2>
            <div class="canvas-container">
                <canvas id="costCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">通过使用JDK自带的集合类，可以降低代码维护和学习新API成本</span></p>
                <p>就像使用标准化的零件，既省钱又省心！</p>
                <button class="interactive-btn" onclick="animateCost()">💰 观看成本分析</button>
                <button class="interactive-btn" onclick="playCostGame()">🎮 成本对比游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="900" height="400"></canvas>
            </div>
            <div class="text-content">
                <p>🎉 恭喜！你已经掌握了集合框架的五大优势：</p>
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <h3>📈 容量自增长</h3>
                        <p>动态扩容，无限可能</p>
                    </div>
                    <div class="benefit-card">
                        <h3>⚡ 高性能算法</h3>
                        <p>专业优化，事半功倍</p>
                    </div>
                    <div class="benefit-card">
                        <h3>🔄 API互操作</h3>
                        <p>无缝传递，畅通无阻</p>
                    </div>
                    <div class="benefit-card">
                        <h3>🛠️ 易于扩展</h3>
                        <p>灵活重构，代码复用</p>
                    </div>
                    <div class="benefit-card">
                        <h3>💰 降低成本</h3>
                        <p>标准化工具，省时省力</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateSummary()">🎊 播放总结动画</button>
                <button class="interactive-btn" onclick="playAllAnimations()">🎬 播放全部动画</button>
            </div>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        
        function updateProgress(progress) {
            currentProgress = Math.min(100, currentProgress + progress);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 概览动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            const benefits = [
                { name: '容量自增长', icon: '📈', color: '#fd79a8', x: 200, y: 200 },
                { name: '高性能算法', icon: '⚡', color: '#fdcb6e', x: 400, y: 150 },
                { name: 'API互操作', icon: '🔄', color: '#74b9ff', x: 600, y: 200 },
                { name: '易于扩展', icon: '🛠️', color: '#00b894', x: 400, y: 350 },
                { name: '降低成本', icon: '💰', color: '#6c5ce7', x: 800, y: 250 }
            ];
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景渐变
                const gradient = ctx.createRadialGradient(500, 300, 0, 500, 300, 400);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(0.5, '#764ba2');
                gradient.addColorStop(1, '#f093fb');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 中心标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🚀 集合框架五大优势', 500, 80);
                
                // 绘制优势节点
                benefits.forEach((benefit, index) => {
                    const delay = index * 30;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 80));
                    
                    if (progress > 0) {
                        const scale = progress;
                        const alpha = progress;
                        
                        ctx.save();
                        ctx.globalAlpha = alpha;
                        ctx.translate(benefit.x, benefit.y);
                        ctx.scale(scale, scale);
                        
                        // 优势圆圈
                        ctx.fillStyle = benefit.color;
                        ctx.beginPath();
                        ctx.arc(0, 0, 60, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 白色边框
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 4;
                        ctx.stroke();
                        
                        // 图标
                        ctx.font = '32px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(benefit.icon, 0, 10);
                        
                        // 名称
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText(benefit.name, 0, 90);
                        
                        ctx.restore();
                        
                        // 连接线到中心
                        if (progress > 0.5) {
                            const lineProgress = (progress - 0.5) * 2;
                            ctx.strokeStyle = 'rgba(255,255,255,0.6)';
                            ctx.lineWidth = 3;
                            ctx.setLineDash([10, 5]);
                            ctx.lineDashOffset = -frame * 0.5;
                            
                            const centerX = 500;
                            const centerY = 300;
                            const dx = (centerX - benefit.x) * lineProgress;
                            const dy = (centerY - benefit.y) * lineProgress;
                            
                            ctx.beginPath();
                            ctx.moveTo(benefit.x, benefit.y);
                            ctx.lineTo(benefit.x + dx, benefit.y + dy);
                            ctx.stroke();
                        }
                    }
                });
                
                // 中心核心圆圈
                if (frame > 150) {
                    const coreProgress = Math.min(1, (frame - 150) / 60);
                    ctx.save();
                    ctx.globalAlpha = coreProgress;
                    ctx.translate(500, 300);
                    ctx.scale(coreProgress, coreProgress);
                    
                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(0, 0, 50, 0, Math.PI * 2);
                    ctx.fill();
                    
                    ctx.fillStyle = '#667eea';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('集合', 0, -5);
                    ctx.fillText('框架', 0, 20);
                    
                    ctx.restore();
                }
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(20);
                }
            }
            
            draw();
        }

        // 容量自增长动画
        function animateCapacity() {
            const canvas = document.getElementById('capacityCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            let capacity = 4;
            let elements = 1;
            let isExpanding = false;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fd79a8');
                gradient.addColorStop(0.5, '#fdcb6e');
                gradient.addColorStop(1, '#74b9ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📈 容量自增长演示', centerX, 60);

                // 自动添加元素
                if (frame % 80 === 0 && elements < 20) {
                    elements++;
                    if (elements > capacity) {
                        isExpanding = true;
                        capacity *= 2; // 容量翻倍
                    }
                }

                // 容器
                const containerWidth = Math.max(200, capacity * 40);
                const containerHeight = 100;

                // 扩容动画效果
                if (isExpanding) {
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.scale(1 + Math.sin(frame * 0.3) * 0.1, 1 + Math.sin(frame * 0.3) * 0.1);

                    // 扩容光效
                    ctx.strokeStyle = '#ffeaa7';
                    ctx.lineWidth = 5;
                    ctx.setLineDash([10, 5]);
                    ctx.lineDashOffset = -frame * 2;
                    ctx.strokeRect(-containerWidth/2, -containerHeight/2, containerWidth, containerHeight);

                    ctx.restore();

                    if (frame % 80 === 40) {
                        isExpanding = false;
                    }
                }

                // 主容器
                ctx.fillStyle = 'rgba(255,255,255,0.9)';
                ctx.fillRect(centerX - containerWidth/2, centerY - containerHeight/2, containerWidth, containerHeight);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.strokeRect(centerX - containerWidth/2, centerY - containerHeight/2, containerWidth, containerHeight);

                // 容器标签
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(`容量: ${capacity}`, centerX, centerY - containerHeight/2 - 20);

                // 元素
                for (let i = 0; i < elements; i++) {
                    const x = centerX - containerWidth/2 + 20 + (i % capacity) * 35;
                    const y = centerY - 15 + Math.floor(i / capacity) * 30;

                    // 元素动画
                    const elementScale = i === elements - 1 ? 1 + Math.sin(frame * 0.2) * 0.2 : 1;

                    ctx.save();
                    ctx.translate(x, y);
                    ctx.scale(elementScale, elementScale);

                    ctx.fillStyle = '#fd79a8';
                    ctx.beginPath();
                    ctx.arc(0, 0, 12, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, 0, 3);

                    ctx.restore();
                }

                // 状态信息
                ctx.fillStyle = 'white';
                ctx.font = '18px Arial';
                ctx.fillText(`当前元素: ${elements}`, centerX, centerY + containerHeight/2 + 40);

                if (isExpanding) {
                    ctx.fillStyle = '#ffeaa7';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('🚀 正在自动扩容...', centerX, centerY + containerHeight/2 + 70);
                }

                // 扩容历史
                ctx.fillStyle = 'rgba(255,255,255,0.8)';
                ctx.fillRect(50, 350, 300, 100);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, 350, 300, 100);

                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('扩容历史:', 70, 375);
                ctx.font = '14px Arial';
                ctx.fillText('初始容量: 4', 70, 395);
                ctx.fillText('第1次扩容: 4 → 8', 70, 415);
                if (capacity >= 16) ctx.fillText('第2次扩容: 8 → 16', 70, 435);

                frame++;
                if (frame < 800) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 高性能算法动画
        function animatePerformance() {
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const algorithms = [
                { name: 'ArrayList', operation: '随机访问', time: 'O(1)', color: '#fd79a8', x: 150, y: 200 },
                { name: 'LinkedList', operation: '插入删除', time: 'O(1)', color: '#74b9ff', x: 350, y: 200 },
                { name: 'HashMap', operation: '查找元素', time: 'O(1)', color: '#00b894', x: 550, y: 200 },
                { name: 'TreeMap', operation: '有序遍历', time: 'O(log n)', color: '#6c5ce7', x: 750, y: 200 }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#a29bfe');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('⚡ 高性能数据结构和算法', canvas.width / 2, 60);

                // 性能对比图表
                algorithms.forEach((algo, index) => {
                    const delay = index * 40;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 80));

                    if (progress > 0) {
                        ctx.save();
                        ctx.translate(algo.x, algo.y);
                        ctx.scale(progress, progress);

                        // 算法容器
                        ctx.fillStyle = algo.color;
                        ctx.fillRect(-60, -60, 120, 120);
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(-60, -60, 120, 120);

                        // 算法名称
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(algo.name, 0, -30);

                        // 操作类型
                        ctx.font = '12px Arial';
                        ctx.fillText(algo.operation, 0, -10);

                        // 时间复杂度
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText(algo.time, 0, 15);

                        // 性能指示器
                        const barHeight = algo.time === 'O(1)' ? 40 : 25;
                        ctx.fillStyle = algo.time === 'O(1)' ? '#00b894' : '#fdcb6e';
                        ctx.fillRect(-30, 25, 60, barHeight);

                        ctx.restore();

                        // 性能连线
                        if (progress > 0.7) {
                            ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                            ctx.lineWidth = 2;
                            ctx.setLineDash([5, 5]);
                            ctx.lineDashOffset = -frame * 0.3;

                            ctx.beginPath();
                            ctx.moveTo(algo.x, algo.y + 80);
                            ctx.lineTo(algo.x, 350);
                            ctx.stroke();
                        }
                    }
                });

                // 性能总结
                if (frame > 160) {
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(200, 350, 500, 100);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(200, 350, 500, 100);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('🎯 性能优势', 450, 375);
                    ctx.font = '14px Arial';
                    ctx.fillText('• 经过优化的算法实现', 450, 395);
                    ctx.fillText('• 针对不同场景选择最佳数据结构', 450, 415);
                    ctx.fillText('• 提高程序运行速度和质量', 450, 435);
                }

                frame++;
                if (frame < 250) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 容量增长游戏
        function playCapacityGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px; position: relative; z-index: 1;">🎮 容量自增长体验游戏</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px; position: relative; z-index: 1;">点击添加元素，观察集合如何自动扩容！</p>

                <div style="text-align: center; margin-bottom: 30px; position: relative; z-index: 1;">
                    <button class="interactive-btn" onclick="addElementToCollection()" style="margin: 10px; padding: 15px 30px; font-size: 16px;">➕ 添加元素</button>
                    <button class="interactive-btn" onclick="clearCollection()" style="margin: 10px; padding: 15px 30px; font-size: 16px;">🗑️ 清空集合</button>
                </div>

                <div style="background: white; border-radius: 20px; padding: 30px; margin: 20px 0; position: relative; z-index: 1;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <span style="font-weight: bold; color: #667eea;">当前容量: <span id="currentCapacity">4</span></span>
                        <span style="font-weight: bold; color: #fd79a8;">元素数量: <span id="elementCount">0</span></span>
                    </div>

                    <div id="collectionContainer" style="border: 3px solid #667eea; border-radius: 15px; padding: 20px; min-height: 100px; background: rgba(102, 126, 234, 0.1); display: flex; flex-wrap: wrap; gap: 10px; justify-content: center; align-items: center;">
                        <p style="color: #667eea; margin: 0;">集合容器（容量会自动增长）</p>
                    </div>

                    <div id="expansionLog" style="margin-top: 20px; padding: 15px; background: rgba(253, 121, 168, 0.1); border-radius: 10px; max-height: 100px; overflow-y: auto;">
                        <h4 style="color: #fd79a8; margin-bottom: 10px;">📈 扩容日志</h4>
                        <div id="logContent" style="font-size: 14px; color: #636e72;">
                            <p>等待添加元素...</p>
                        </div>
                    </div>
                </div>
            `;

            const currentSection = document.querySelector('.section:nth-child(3)');
            currentSection.appendChild(gameArea);

            let capacity = 4;
            let elementCount = 0;
            let expansionCount = 0;

            window.addElementToCollection = function() {
                elementCount++;

                // 检查是否需要扩容
                if (elementCount > capacity) {
                    const oldCapacity = capacity;
                    capacity *= 2;
                    expansionCount++;

                    // 更新扩容日志
                    const logContent = document.getElementById('logContent');
                    const newLog = document.createElement('p');
                    newLog.innerHTML = `🚀 第${expansionCount}次扩容: ${oldCapacity} → ${capacity}`;
                    newLog.style.color = '#fd79a8';
                    newLog.style.fontWeight = 'bold';
                    newLog.style.animation = 'bounce 0.5s ease';
                    logContent.appendChild(newLog);

                    // 容器扩容动画
                    const container = document.getElementById('collectionContainer');
                    container.style.animation = 'pulse 0.5s ease';
                    setTimeout(() => {
                        container.style.animation = '';
                    }, 500);
                }

                // 添加新元素
                const newElement = document.createElement('div');
                newElement.style.cssText = `
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    padding: 10px 15px;
                    border-radius: 12px;
                    font-weight: bold;
                    animation: bounce 0.5s ease;
                    min-width: 40px;
                    text-align: center;
                `;
                newElement.textContent = elementCount;

                const container = document.getElementById('collectionContainer');
                if (container.children.length === 1 && container.children[0].tagName === 'P') {
                    container.removeChild(container.children[0]);
                }
                container.appendChild(newElement);

                // 更新显示
                document.getElementById('currentCapacity').textContent = capacity;
                document.getElementById('elementCount').textContent = elementCount;

                if (elementCount >= 10) {
                    setTimeout(() => {
                        alert('🎉 恭喜！你已经体验了集合的自动扩容功能！\n集合会根据需要自动增长容量，无需手动管理。');
                        updateProgress(10);
                    }, 500);
                }
            };

            window.clearCollection = function() {
                capacity = 4;
                elementCount = 0;
                expansionCount = 0;

                const container = document.getElementById('collectionContainer');
                container.innerHTML = '<p style="color: #667eea; margin: 0;">集合容器（容量会自动增长）</p>';

                document.getElementById('currentCapacity').textContent = capacity;
                document.getElementById('elementCount').textContent = elementCount;

                const logContent = document.getElementById('logContent');
                logContent.innerHTML = '<p>等待添加元素...</p>';
            };
        }

        // API互操作性动画
        function animateInteroperability() {
            const canvas = document.getElementById('interoperabilityCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const apis = [
                { name: 'Web API', color: '#fd79a8', x: 150, y: 150 },
                { name: 'Database API', color: '#74b9ff', x: 750, y: 150 },
                { name: 'File API', color: '#00b894', x: 150, y: 350 },
                { name: 'Network API', color: '#6c5ce7', x: 750, y: 350 }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#74b9ff');
                gradient.addColorStop(1, '#0984e3');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🔄 API互操作性演示', canvas.width / 2, 60);

                // 中心集合
                if (frame > 30) {
                    const centerProgress = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = centerProgress;
                    ctx.translate(450, 250);
                    ctx.scale(centerProgress, centerProgress);

                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(0, 0, 80, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.strokeStyle = '#fdcb6e';
                    ctx.lineWidth = 4;
                    ctx.stroke();

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('集合', 0, -10);
                    ctx.fillText('数据', 0, 15);

                    ctx.restore();
                }

                // API节点
                apis.forEach((api, index) => {
                    const delay = 90 + index * 30;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 60));

                    if (progress > 0) {
                        ctx.save();
                        ctx.translate(api.x, api.y);
                        ctx.scale(progress, progress);

                        ctx.fillStyle = api.color;
                        ctx.fillRect(-60, -30, 120, 60);
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(-60, -30, 120, 60);

                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(api.name, 0, 5);

                        ctx.restore();

                        // 数据流动动画
                        if (progress > 0.5) {
                            const flowProgress = (frame % 120) / 120;
                            const centerX = 450;
                            const centerY = 250;

                            // 计算数据包位置
                            const dx = centerX - api.x;
                            const dy = centerY - api.y;
                            const dataX = api.x + dx * flowProgress;
                            const dataY = api.y + dy * flowProgress;

                            // 绘制数据包
                            ctx.fillStyle = api.color;
                            ctx.beginPath();
                            ctx.arc(dataX, dataY, 8, 0, Math.PI * 2);
                            ctx.fill();

                            // 绘制连接线
                            ctx.strokeStyle = 'rgba(255,255,255,0.6)';
                            ctx.lineWidth = 2;
                            ctx.setLineDash([10, 5]);
                            ctx.lineDashOffset = -frame * 0.5;
                            ctx.beginPath();
                            ctx.moveTo(api.x, api.y);
                            ctx.lineTo(centerX, centerY);
                            ctx.stroke();
                        }
                    }
                });

                // 互操作说明
                if (frame > 200) {
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(250, 400, 400, 80);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(250, 400, 400, 80);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('🌐 无缝数据传递', 450, 425);
                    ctx.font = '14px Arial';
                    ctx.fillText('集合可以在不同API之间自由传递', 450, 445);
                    ctx.fillText('无需格式转换，提高开发效率', 450, 465);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 扩展性动画
        function animateExtensibility() {
            const canvas = document.getElementById('extensibilityCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#00b894');
                gradient.addColorStop(1, '#00cec9');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🛠️ 易于扩展和改写', canvas.width / 2, 60);

                // 基础集合类
                if (frame > 30) {
                    const baseProgress = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = baseProgress;

                    ctx.fillStyle = '#fdcb6e';
                    ctx.fillRect(350, 150, 200, 80);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(350, 150, 200, 80);

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('ArrayList', 450, 185);
                    ctx.font = '14px Arial';
                    ctx.fillText('基础实现', 450, 205);

                    ctx.restore();
                }

                // 扩展类
                const extensions = [
                    { name: 'CustomList', desc: '自定义功能', x: 200, y: 300, color: '#fd79a8' },
                    { name: 'SortedList', desc: '自动排序', x: 450, y: 300, color: '#74b9ff' },
                    { name: 'FilteredList', desc: '过滤功能', x: 700, y: 300, color: '#6c5ce7' }
                ];

                extensions.forEach((ext, index) => {
                    const delay = 90 + index * 40;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 60));

                    if (progress > 0) {
                        ctx.save();
                        ctx.translate(ext.x, ext.y);
                        ctx.scale(progress, progress);

                        ctx.fillStyle = ext.color;
                        ctx.fillRect(-75, -40, 150, 80);
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(-75, -40, 150, 80);

                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(ext.name, 0, -10);
                        ctx.font = '12px Arial';
                        ctx.fillText(ext.desc, 0, 10);

                        ctx.restore();

                        // 继承箭头
                        if (progress > 0.5) {
                            ctx.strokeStyle = 'white';
                            ctx.lineWidth = 3;
                            ctx.beginPath();
                            ctx.moveTo(450, 230);
                            ctx.lineTo(ext.x, ext.y - 40);
                            ctx.stroke();

                            // 箭头头部
                            const angle = Math.atan2(ext.y - 40 - 230, ext.x - 450);
                            ctx.save();
                            ctx.translate(ext.x, ext.y - 40);
                            ctx.rotate(angle);
                            ctx.beginPath();
                            ctx.moveTo(0, 0);
                            ctx.lineTo(-15, -5);
                            ctx.lineTo(-15, 5);
                            ctx.closePath();
                            ctx.fillStyle = 'white';
                            ctx.fill();
                            ctx.restore();
                        }
                    }
                });

                // 代码复用说明
                if (frame > 200) {
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(250, 400, 400, 80);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(250, 400, 400, 80);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('🔧 灵活扩展', 450, 425);
                    ctx.font = '14px Arial';
                    ctx.fillText('继承现有类，添加新功能', 450, 445);
                    ctx.fillText('提高代码复用性和可维护性', 450, 465);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 成本分析动画
        function animateCost() {
            const canvas = document.getElementById('costCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#6c5ce7');
                gradient.addColorStop(1, '#a29bfe');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('💰 降低开发成本', canvas.width / 2, 60);

                // 成本对比
                const costComparison = [
                    { label: '自己实现', cost: 100, color: '#e17055', x: 200 },
                    { label: '使用集合框架', cost: 20, color: '#00b894', x: 700 }
                ];

                costComparison.forEach((item, index) => {
                    const delay = 60 + index * 80;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 100));

                    if (progress > 0) {
                        // 成本柱状图
                        const barHeight = (item.cost / 100) * 200 * progress;

                        ctx.fillStyle = item.color;
                        ctx.fillRect(item.x - 50, 350 - barHeight, 100, barHeight);
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(item.x - 50, 350 - barHeight, 100, barHeight);

                        // 标签
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(item.label, item.x, 380);

                        // 成本数值
                        ctx.font = 'bold 20px Arial';
                        ctx.fillText(`${Math.floor(item.cost * progress)}%`, item.x, 350 - barHeight - 20);

                        // 成本细节
                        if (index === 0) {
                            ctx.font = '12px Arial';
                            ctx.fillText('• 设计数据结构', item.x, 400);
                            ctx.fillText('• 实现算法', item.x, 415);
                            ctx.fillText('• 测试调试', item.x, 430);
                            ctx.fillText('• 维护更新', item.x, 445);
                        } else {
                            ctx.font = '12px Arial';
                            ctx.fillText('• 直接使用', item.x, 400);
                            ctx.fillText('• 经过验证', item.x, 415);
                            ctx.fillText('• 持续优化', item.x, 430);
                            ctx.fillText('• 社区支持', item.x, 445);
                        }
                    }
                });

                // 节省指示
                if (frame > 220) {
                    ctx.strokeStyle = '#ffeaa7';
                    ctx.lineWidth = 4;
                    ctx.setLineDash([10, 5]);
                    ctx.lineDashOffset = -frame * 0.5;

                    ctx.beginPath();
                    ctx.moveTo(300, 200);
                    ctx.lineTo(600, 200);
                    ctx.stroke();

                    ctx.fillStyle = '#ffeaa7';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('节省80%成本！', 450, 190);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 总结动画
        function animateSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createRadialGradient(450, 200, 0, 450, 200, 400);
                gradient.addColorStop(0, '#fd79a8');
                gradient.addColorStop(0.3, '#74b9ff');
                gradient.addColorStop(0.6, '#00b894');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 庆祝烟花效果
                for (let i = 0; i < 25; i++) {
                    const angle = (frame * 0.04 + i * 0.25) % (Math.PI * 2);
                    const radius = 80 + Math.sin(frame * 0.03 + i) * 50;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;

                    ctx.fillStyle = `hsl(${(frame * 3 + i * 15) % 360}, 85%, 70%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 4 + Math.sin(frame * 0.15 + i) * 3, 0, Math.PI * 2);
                    ctx.fill();

                    // 闪烁效果
                    if (Math.sin(frame * 0.25 + i) > 0.8) {
                        ctx.fillStyle = 'white';
                        ctx.beginPath();
                        ctx.arc(x, y, 2, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                // 中心成就徽章
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(frame * 0.02);

                // 徽章外圈
                ctx.fillStyle = 'gold';
                ctx.beginPath();
                ctx.arc(0, 0, 60, 0, Math.PI * 2);
                ctx.fill();

                // 徽章内圈
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(0, 0, 45, 0, Math.PI * 2);
                ctx.fill();

                // 徽章图标
                ctx.fillStyle = '#fd79a8';
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🚀', 0, 12);

                ctx.restore();

                // 成就文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 学习完成！', centerX, centerY - 120);

                ctx.font = '18px Arial';
                ctx.fillText('你已经掌握了集合框架的五大优势', centerX, centerY + 120);

                // 优势总结
                if (frame > 80) {
                    const advantages = [
                        '📈 容量自增长',
                        '⚡ 高性能算法',
                        '🔄 API互操作',
                        '🛠️ 易于扩展',
                        '💰 降低成本'
                    ];

                    ctx.fillStyle = 'rgba(255,255,255,0.95)';
                    ctx.fillRect(centerX - 200, centerY + 140, 400, 140);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(centerX - 200, centerY + 140, 400, 140);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('🏆 五大核心优势', centerX, centerY + 165);

                    ctx.font = '14px Arial';
                    advantages.forEach((advantage, i) => {
                        ctx.fillText(advantage, centerX, centerY + 190 + i * 20);
                    });
                }

                frame++;
                if (frame < 400) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(100);
                }
            }

            draw();
        }

        // 播放全部动画
        async function playAllAnimations() {
            const animations = [
                animateOverview,
                animateCapacity,
                animatePerformance,
                animateInteroperability,
                animateExtensibility,
                animateCost,
                animateSummary
            ];

            for (let i = 0; i < animations.length; i++) {
                animations[i]();
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }

        // 性能对比游戏
        function playPerformanceGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px; position: relative; z-index: 1;">🎮 性能对比挑战</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px; position: relative; z-index: 1;">选择最适合的数据结构来完成任务！</p>

                <div style="background: white; border-radius: 20px; padding: 30px; margin: 20px 0; position: relative; z-index: 1;">
                    <div id="performanceChallenge" style="text-align: center; margin-bottom: 20px;">
                        <h4 style="color: #667eea; margin-bottom: 15px;">🎯 任务：需要频繁进行随机访问操作</h4>
                        <p style="color: #636e72; margin-bottom: 20px;">选择最适合的数据结构：</p>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <button class="interactive-btn" onclick="selectDataStructure('ArrayList', 'O(1)', true)" style="padding: 15px; font-size: 14px;">
                                ArrayList<br><small>随机访问 O(1)</small>
                            </button>
                            <button class="interactive-btn" onclick="selectDataStructure('LinkedList', 'O(n)', false)" style="padding: 15px; font-size: 14px;">
                                LinkedList<br><small>随机访问 O(n)</small>
                            </button>
                            <button class="interactive-btn" onclick="selectDataStructure('HashMap', 'O(1)', false)" style="padding: 15px; font-size: 14px;">
                                HashMap<br><small>键值查找 O(1)</small>
                            </button>
                        </div>
                    </div>

                    <div id="performanceResult" style="text-align: center; margin-top: 20px; min-height: 50px;">
                        <p style="color: #636e72;">请选择一个数据结构...</p>
                    </div>
                </div>
            `;

            const currentSection = document.querySelector('.section:nth-child(4)');
            currentSection.appendChild(gameArea);

            let challengeCount = 0;
            const challenges = [
                {
                    task: '需要频繁进行随机访问操作',
                    correct: 'ArrayList',
                    explanation: 'ArrayList基于数组实现，支持O(1)时间复杂度的随机访问'
                },
                {
                    task: '需要频繁在中间位置插入和删除元素',
                    correct: 'LinkedList',
                    explanation: 'LinkedList基于链表实现，在已知位置插入删除只需O(1)时间'
                },
                {
                    task: '需要快速查找键值对',
                    correct: 'HashMap',
                    explanation: 'HashMap基于哈希表实现，平均O(1)时间复杂度查找'
                }
            ];

            window.selectDataStructure = function(choice, complexity, isCorrect) {
                const resultDiv = document.getElementById('performanceResult');

                if (isCorrect) {
                    resultDiv.innerHTML = `
                        <div style="background: linear-gradient(45deg, #00b894, #00cec9); color: white; padding: 20px; border-radius: 15px; animation: bounce 0.5s ease;">
                            <h4>🎉 正确！</h4>
                            <p>${challenges[challengeCount].explanation}</p>
                        </div>
                    `;

                    challengeCount++;
                    if (challengeCount < challenges.length) {
                        setTimeout(() => {
                            const challengeDiv = document.getElementById('performanceChallenge');
                            challengeDiv.querySelector('h4').textContent = `🎯 任务：${challenges[challengeCount].task}`;
                            resultDiv.innerHTML = '<p style="color: #636e72;">请选择一个数据结构...</p>';
                        }, 2000);
                    } else {
                        setTimeout(() => {
                            alert('🏆 恭喜完成所有挑战！你已经理解了不同数据结构的性能特点！');
                            updateProgress(10);
                        }, 1000);
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div style="background: linear-gradient(45deg, #e17055, #d63031); color: white; padding: 20px; border-radius: 15px;">
                            <h4>❌ 不是最佳选择</h4>
                            <p>${choice}的时间复杂度是${complexity}，不适合当前任务</p>
                            <p>请重新选择...</p>
                        </div>
                    `;
                }
            };
        }

        // 页面加载完成后自动播放概览动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateOverview();
            }, 1000);
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    animateOverview();
                    break;
                case '2':
                    animateCapacity();
                    break;
                case '3':
                    animatePerformance();
                    break;
                case '4':
                    animateInteroperability();
                    break;
                case '5':
                    animateExtensibility();
                    break;
                case '6':
                    animateCost();
                    break;
                case '7':
                    animateSummary();
                    break;
                case ' ':
                    e.preventDefault();
                    playAllAnimations();
                    break;
            }
        });

        // 添加提示信息
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1000;
            max-width: 250px;
        `;
        hint.innerHTML = '💡 提示：按数字键1-7播放对应动画<br>空格键播放全部动画<br>参与互动游戏深度体验！';
        document.body.appendChild(hint);

        // 5秒后隐藏提示
        setTimeout(() => {
            hint.style.opacity = '0';
            hint.style.transition = 'opacity 1s';
        }, 6000);

        // 鼠标悬停效果
        document.querySelectorAll('.section').forEach(section => {
            section.addEventListener('mouseenter', () => {
                section.style.transform = 'translateY(-12px) scale(1.02)';
                section.style.boxShadow = '0 40px 80px rgba(0,0,0,0.2)';
            });

            section.addEventListener('mouseleave', () => {
                section.style.transform = 'translateY(0) scale(1)';
                section.style.boxShadow = '0 35px 70px rgba(0,0,0,0.15)';
            });
        });
    </script>
</body>
</html>
