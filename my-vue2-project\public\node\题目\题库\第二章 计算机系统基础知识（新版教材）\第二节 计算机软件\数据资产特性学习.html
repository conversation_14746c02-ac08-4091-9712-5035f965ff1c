<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据资产特性 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transform: translateY(20px);
            opacity: 0;
            animation: slideUp 0.8s ease-out forwards;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .feature-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: white;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            text-align: center;
            font-weight: 500;
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.correct {
            border-color: #4CAF50;
            background: #e8f5e8;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffeaea;
            animation: shake 0.6s ease-in-out;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">📊 数据资产特性学习</h1>
            <p class="subtitle">通过动画和交互，轻松掌握数据资产的核心特性</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是数据资产？</h2>
            <div class="canvas-container">
                <canvas id="dataAssetCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <strong>数据资产</strong>就像是企业的"数字宝藏"！想象一下，企业收集的所有信息（客户资料、销售记录、产品数据等）都是有价值的资产，就像金子一样珍贵。这些数据可以帮助企业做决策、创造价值。
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">✨ 数据资产的特性</h2>
            <div class="canvas-container">
                <canvas id="featuresCanvas" width="800" height="400"></canvas>
            </div>
            <div class="features-grid" id="featuresGrid">
                <!-- 特性卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🧠 知识测试</h2>
            <div class="explanation">
                <h3>题目：数据资产的特性包括：</h3>
                <p>①可控制，可量化，可变现</p>
                <p>②虚拟性、共享性、时效性、安全性、交换性和规模性</p>
                <p>请选择正确答案：</p>
            </div>
            <div class="options" id="options">
                <div class="option" data-answer="A">A. 仅①正确</div>
                <div class="option" data-answer="B">B. 仅②正确</div>
                <div class="option" data-answer="C">C. ①②都不正确</div>
                <div class="option" data-answer="D">D. ①②都正确</div>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="showAnswer()">查看答案解析</button>
                <button class="btn" onclick="resetQuiz()">重新答题</button>
            </div>
            <div id="answerExplanation" style="display: none; margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.2); border-radius: 10px;">
                <!-- 答案解析将通过JavaScript显示 -->
            </div>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 60 + 20 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 数据资产概念动画
        function animateDataAsset() {
            const canvas = document.getElementById('dataAssetCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数据流动效果
                for (let i = 0; i < 5; i++) {
                    const x = (frame + i * 120) % (canvas.width + 100) - 50;
                    const y = 150 + Math.sin((frame + i * 50) * 0.02) * 30;
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 8, 0, Math.PI * 2);
                    ctx.fillStyle = `hsl(${220 + i * 20}, 70%, 60%)`;
                    ctx.fill();
                    
                    // 数据标签
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(['客户', '销售', '产品', '财务', '运营'][i], x, y + 25);
                }
                
                // 中央宝箱
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const pulse = Math.sin(frame * 0.05) * 0.1 + 1;
                
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.scale(pulse, pulse);
                
                // 宝箱主体
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(-40, -20, 80, 40);
                ctx.fillStyle = '#FFA500';
                ctx.fillRect(-40, -30, 80, 20);
                
                // 宝箱装饰
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(-45, -25, 10, 30);
                ctx.fillRect(35, -25, 10, 30);
                
                ctx.restore();
                
                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据资产宝库', centerX, 50);
                
                frame++;
                requestAnimationFrame(draw);
            }
            
            draw();
        }

        // 特性展示动画
        function animateFeatures() {
            const canvas = document.getElementById('featuresCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            const features = [
                { name: '虚拟性', icon: '💾', color: '#FF6B6B' },
                { name: '共享性', icon: '🤝', color: '#4ECDC4' },
                { name: '时效性', icon: '⏰', color: '#45B7D1' },
                { name: '安全性', icon: '🔒', color: '#96CEB4' },
                { name: '交换性', icon: '🔄', color: '#FFEAA7' },
                { name: '规模性', icon: '📈', color: '#DDA0DD' }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 120;
                
                features.forEach((feature, index) => {
                    const angle = (index / features.length) * Math.PI * 2 + frame * 0.01;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    const pulse = Math.sin(frame * 0.05 + index) * 0.2 + 1;
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x, y);
                    ctx.strokeStyle = feature.color;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // 特性圆圈
                    ctx.beginPath();
                    ctx.arc(x, y, 30 * pulse, 0, Math.PI * 2);
                    ctx.fillStyle = feature.color;
                    ctx.fill();
                    
                    // 图标
                    ctx.font = '20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(feature.icon, x, y + 5);
                    
                    // 名称
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.fillText(feature.name, x, y + 50);
                });
                
                // 中心圆
                ctx.beginPath();
                ctx.arc(centerX, centerY, 40, 0, Math.PI * 2);
                ctx.fillStyle = '#667eea';
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据', centerX, centerY - 5);
                ctx.fillText('资产', centerX, centerY + 15);
                
                frame++;
                requestAnimationFrame(draw);
            }
            
            draw();
        }

        // 生成特性卡片
        function generateFeatureCards() {
            const features = [
                {
                    title: '可控制性',
                    description: '企业可以控制数据的访问、使用和分发，就像管理自己的财产一样。',
                    delay: '0.1s'
                },
                {
                    title: '可量化性',
                    description: '数据的价值可以被测量和评估，比如用户数量、交易金额等。',
                    delay: '0.2s'
                },
                {
                    title: '可变现性',
                    description: '数据可以转化为经济价值，通过分析产生商业洞察。',
                    delay: '0.3s'
                },
                {
                    title: '虚拟性',
                    description: '数据是无形的，存储在电子设备中，不占用物理空间。',
                    delay: '0.4s'
                },
                {
                    title: '共享性',
                    description: '同一份数据可以被多个人同时使用，不会因为使用而消耗。',
                    delay: '0.5s'
                },
                {
                    title: '时效性',
                    description: '数据的价值会随时间变化，新鲜的数据通常更有价值。',
                    delay: '0.6s'
                }
            ];

            const container = document.getElementById('featuresGrid');
            features.forEach(feature => {
                const card = document.createElement('div');
                card.className = 'feature-card';
                card.style.animationDelay = feature.delay;
                card.innerHTML = `
                    <h3>${feature.title}</h3>
                    <p>${feature.description}</p>
                `;
                container.appendChild(card);
            });
        }

        // 测试功能
        let quizAnswered = false;

        document.getElementById('options').addEventListener('click', function(e) {
            if (e.target.classList.contains('option') && !quizAnswered) {
                const selectedAnswer = e.target.dataset.answer;
                const options = document.querySelectorAll('.option');
                
                options.forEach(option => {
                    if (option.dataset.answer === 'D') {
                        option.classList.add('correct');
                    } else if (option === e.target && selectedAnswer !== 'D') {
                        option.classList.add('wrong');
                    }
                });
                
                quizAnswered = true;
                setTimeout(() => {
                    showAnswer();
                }, 1000);
            }
        });

        function showAnswer() {
            const explanation = document.getElementById('answerExplanation');
            explanation.style.display = 'block';
            explanation.innerHTML = `
                <h3>🎉 正确答案：D</h3>
                <p><strong>解析：</strong></p>
                <p>数据资产确实具有题目中提到的所有特性：</p>
                <ul style="text-align: left; margin: 15px 0;">
                    <li><strong>可控制、可量化、可变现</strong> - 这是数据资产作为企业资产的基本特征</li>
                    <li><strong>虚拟性</strong> - 数据是无形的，以电子形式存在</li>
                    <li><strong>共享性</strong> - 可以被多方同时使用而不会损耗</li>
                    <li><strong>时效性</strong> - 数据的价值随时间变化</li>
                    <li><strong>安全性</strong> - 需要保护措施防止泄露</li>
                    <li><strong>交换性</strong> - 可以在不同系统间流转</li>
                    <li><strong>规模性</strong> - 数据量越大，潜在价值越高</li>
                </ul>
                <p>所以①②都是正确的，答案是D！</p>
            `;
        }

        function resetQuiz() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            document.getElementById('answerExplanation').style.display = 'none';
            quizAnswered = false;
        }

        // 初始化
        window.addEventListener('load', function() {
            createFloatingElements();
            animateDataAsset();
            animateFeatures();
            generateFeatureCards();
        });
    </script>
</body>
</html>
