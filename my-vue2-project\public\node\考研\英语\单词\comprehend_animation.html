<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehend - 词缀动画学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 950px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #4a5568;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .pronunciation {
            color: #718096;
            font-size: 1.3em;
            margin-bottom: 20px;
            font-style: italic;
        }

        .word-info {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #4299e1;
        }

        .word-info p {
            margin: 10px 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .example {
            background: #e6fffa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            border-left: 4px solid #38b2ac;
        }

        canvas {
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8fafc, #edf2f7);
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .controls {
            margin: 20px 0;
        }

        button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }

        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .morpheme-explanation {
            text-align: left;
            background: #fff5f5;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border: 2px solid #fed7d7;
        }

        .morpheme-explanation h3 {
            color: #c53030;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .morpheme-part {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border-left: 4px solid #f56565;
        }

        .story-section {
            background: #f0fff4;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border: 2px solid #c6f6d5;
            text-align: left;
        }

        .story-section h3 {
            color: #2f855a;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .interactive-hint {
            background: #fef5e7;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px dashed #f6ad55;
            color: #c05621;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #38b2ac);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        .step-indicator {
            background: #e2e8f0;
            padding: 10px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Comprehend</h1>
        <p class="pronunciation">[ˌkɒmprɪˈhend]</p>
        
        <div class="word-info">
            <p><strong>词性：</strong>v. 理解，领会，包含</p>
            <p><strong>核心含义：</strong>完全抓住或理解某事物的意义</p>
            <div class="example">
                <p><strong>例句：</strong>It's difficult to comprehend the vastness of the universe.</p>
                <p><strong>翻译：</strong>很难理解宇宙的浩瀚无垠。</p>
            </div>
        </div>

        <div class="interactive-hint">
            🎬 观看动画故事：看看大脑如何"完全抓住"知识的过程！
        </div>

        <canvas id="animationCanvas" width="850" height="450"></canvas>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="step-indicator" id="stepIndicator">
            点击"开始动画"观看词缀故事
        </div>

        <div class="controls">
            <button id="startBtn">开始动画</button>
            <button id="nextStepBtn" disabled>下一步</button>
            <button id="resetBtn">重新播放</button>
        </div>

        <div class="morpheme-explanation">
            <h3>📚 词缀详解</h3>
            <div class="morpheme-part">
                <p><strong>com-</strong> (前缀)</p>
                <p><strong>含义：</strong>完全地，彻底地，一起</p>
                <p><strong>来源：</strong>拉丁语，表示"完全"或"一起"</p>
                <p><strong>例词：</strong>complete (完成), combine (结合), compose (组成)</p>
            </div>
            <div class="morpheme-part">
                <p><strong>pre-</strong> (前缀)</p>
                <p><strong>含义：</strong>在...之前，预先</p>
                <p><strong>来源：</strong>拉丁语，表示"在前面"</p>
                <p><strong>例词：</strong>predict (预测), prepare (准备), prevent (预防)</p>
            </div>
            <div class="morpheme-part">
                <p><strong>-hend</strong> (词根)</p>
                <p><strong>含义：</strong>抓住，拿住</p>
                <p><strong>来源：</strong>拉丁语 "hendere"，意为抓住</p>
                <p><strong>例词：</strong>apprehend (逮捕，理解), prehensile (能抓的)</p>
            </div>
        </div>

        <div class="story-section">
            <h3>🧠 记忆故事</h3>
            <p><strong>大脑的知识捕捉器：</strong></p>
            <p>想象你的大脑是一个超级智能的捕捉器。当面对复杂的知识时：</p>
            <p>1. <strong>"pre-"</strong> 表示大脑<strong>预先</strong>准备好捕捉网</p>
            <p>2. <strong>"-hend"</strong> 表示大脑开始<strong>抓住</strong>知识碎片</p>
            <p>3. <strong>"com-"</strong> 表示<strong>完全地</strong>将所有碎片整合在一起</p>
            <p>最终，<strong>comprehend</strong> = com(完全地) + pre(预先) + hend(抓住) = 完全抓住并理解！</p>
            <p>就像一个熟练的渔夫，不仅要抓住鱼，还要完全掌握整个捕鱼的过程。</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        const startBtn = document.getElementById('startBtn');
        const nextStepBtn = document.getElementById('nextStepBtn');
        const resetBtn = document.getElementById('resetBtn');
        const progressFill = document.getElementById('progressFill');
        const stepIndicator = document.getElementById('stepIndicator');

        let animationStep = 0;
        let animationId = null;
        let knowledgePieces = [];
        let captureNet = null;

        // Animation objects
        const brain = {
            x: 400,
            y: 200,
            radius: 80,
            pulseScale: 1,
            color: '#FF69B4'
        };

        const morphemes = {
            com: { x: 100, y: 100, opacity: 0, scale: 1, color: '#FF4500' },
            pre: { x: 300, y: 100, opacity: 0, scale: 1, color: '#32CD32' },
            hend: { x: 500, y: 100, opacity: 0, scale: 1, color: '#4169E1' }
        };

        function initKnowledgePieces() {
            knowledgePieces = [];
            for (let i = 0; i < 8; i++) {
                knowledgePieces.push({
                    x: Math.random() * 700 + 50,
                    y: Math.random() * 300 + 100,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 15 + 10,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    captured: false,
                    targetX: brain.x,
                    targetY: brain.y
                });
            }
        }

        function drawBrain() {
            // Brain pulsing effect
            brain.pulseScale = 1 + Math.sin(Date.now() * 0.005) * 0.1;
            
            ctx.save();
            ctx.translate(brain.x, brain.y);
            ctx.scale(brain.pulseScale, brain.pulseScale);
            
            // Brain outline
            ctx.fillStyle = brain.color;
            ctx.beginPath();
            ctx.arc(0, 0, brain.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Brain details
            ctx.strokeStyle = '#FF1493';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(-20, -10, 15, 0, Math.PI);
            ctx.arc(20, -10, 15, 0, Math.PI);
            ctx.arc(0, 20, 25, Math.PI, 0);
            ctx.stroke();
            
            ctx.restore();
            
            // Brain label
            ctx.font = '16px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('大脑', brain.x, brain.y + brain.radius + 25);
        }

        function drawMorphemes() {
            Object.keys(morphemes).forEach(key => {
                const morpheme = morphemes[key];
                ctx.save();
                ctx.globalAlpha = morpheme.opacity;
                ctx.translate(morpheme.x, morpheme.y);
                ctx.scale(morpheme.scale, morpheme.scale);
                
                ctx.font = 'bold 32px Arial';
                ctx.fillStyle = morpheme.color;
                ctx.textAlign = 'center';
                ctx.fillText(key + '-', 0, 0);
                
                ctx.restore();
            });
        }

        function drawKnowledgePieces() {
            knowledgePieces.forEach(piece => {
                if (!piece.captured) {
                    // Floating knowledge pieces
                    piece.x += piece.vx;
                    piece.y += piece.vy;
                    
                    // Bounce off walls
                    if (piece.x < piece.size || piece.x > canvas.width - piece.size) piece.vx *= -1;
                    if (piece.y < piece.size || piece.y > canvas.height - piece.size) piece.vy *= -1;
                } else {
                    // Move towards brain
                    const dx = piece.targetX - piece.x;
                    const dy = piece.targetY - piece.y;
                    piece.x += dx * 0.05;
                    piece.y += dy * 0.05;
                }
                
                ctx.fillStyle = piece.color;
                ctx.beginPath();
                ctx.arc(piece.x, piece.y, piece.size, 0, Math.PI * 2);
                ctx.fill();
                
                // Add sparkle effect
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(piece.x - piece.size/3, piece.y - piece.size/3, 2, 0, Math.PI * 2);
                ctx.fill();
            });
        }

        function drawCaptureNet() {
            if (!captureNet) return;
            
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.7)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            // Draw net around brain
            ctx.beginPath();
            ctx.arc(brain.x, brain.y, captureNet.radius, 0, Math.PI * 2);
            ctx.stroke();
            
            ctx.setLineDash([]);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawBrain();
            drawMorphemes();
            drawKnowledgePieces();
            drawCaptureNet();
            
            // Check if knowledge pieces are captured
            if (captureNet) {
                knowledgePieces.forEach(piece => {
                    const distance = Math.sqrt(
                        Math.pow(piece.x - brain.x, 2) + Math.pow(piece.y - brain.y, 2)
                    );
                    if (distance < captureNet.radius && !piece.captured) {
                        piece.captured = true;
                    }
                });
            }
            
            if (animationStep > 0) {
                animationId = requestAnimationFrame(animate);
            }
        }

        function updateProgress() {
            const progress = (animationStep / 4) * 100;
            progressFill.style.width = progress + '%';
        }

        function updateStepIndicator() {
            const steps = [
                '点击"开始动画"观看词缀故事',
                '🎯 "pre-" 大脑预先准备捕捉网',
                '✋ "-hend" 开始抓住知识碎片',
                '🔄 "com-" 完全整合所有知识',
                '🎉 完全理解！comprehend 完成'
            ];
            stepIndicator.textContent = steps[animationStep] || steps[0];
        }

        function nextStep() {
            animationStep++;
            updateProgress();
            updateStepIndicator();
            
            switch(animationStep) {
                case 1:
                    // Show "pre-" and prepare capture net
                    morphemes.pre.opacity = 1;
                    morphemes.pre.scale = 1.2;
                    captureNet = { radius: 120 };
                    nextStepBtn.textContent = '抓住知识';
                    break;
                    
                case 2:
                    // Show "-hend" and start capturing
                    morphemes.hend.opacity = 1;
                    morphemes.hend.scale = 1.2;
                    // Knowledge pieces will be captured by the net
                    nextStepBtn.textContent = '完全整合';
                    break;
                    
                case 3:
                    // Show "com-" and complete comprehension
                    morphemes.com.opacity = 1;
                    morphemes.com.scale = 1.2;
                    brain.color = '#FFD700'; // Golden brain
                    nextStepBtn.textContent = '完成理解';
                    break;
                    
                case 4:
                    // Animation complete
                    nextStepBtn.disabled = true;
                    nextStepBtn.textContent = '理解完成！';
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                        animationId = null;
                    }
                    break;
            }
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            animationStep = 0;
            brain.color = '#FF69B4';
            captureNet = null;
            
            // Reset morphemes
            Object.keys(morphemes).forEach(key => {
                morphemes[key].opacity = 0;
                morphemes[key].scale = 1;
            });
            
            initKnowledgePieces();
            
            nextStepBtn.disabled = false;
            nextStepBtn.textContent = '准备捕捉';
            updateProgress();
            updateStepIndicator();
            
            // Initial draw
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBrain();
            drawKnowledgePieces();
        }

        // Event listeners
        startBtn.addEventListener('click', () => {
            if (animationStep === 0) {
                nextStep();
                animate();
            }
        });

        nextStepBtn.addEventListener('click', nextStep);
        resetBtn.addEventListener('click', resetAnimation);

        // Initialize
        initKnowledgePieces();
        resetAnimation();
    </script>
</body>
</html>
