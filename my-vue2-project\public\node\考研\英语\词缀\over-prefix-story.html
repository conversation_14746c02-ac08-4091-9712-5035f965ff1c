<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Over- 词缀故事：超级英雄的超越之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 超级英雄能量场效果 */
        .energy-field {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            background: radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
            animation: energyPulse 4s infinite ease-in-out;
        }

        @keyframes energyPulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.6; }
        }

        /* 飞行轨迹效果 */
        .flight-trails {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .trail {
            position: absolute;
            width: 3px;
            height: 100px;
            background: linear-gradient(to bottom, #ff6b6b, transparent);
            border-radius: 50%;
            animation: flyTrail 3s infinite linear;
            opacity: 0.7;
        }

        @keyframes flyTrail {
            0% { transform: translateY(-100px) translateX(0); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(100vh) translateX(50px); opacity: 0; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }

        .title {
            text-align: center;
            color: #FFD700;
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 
                0 0 10px rgba(255, 215, 0, 0.8),
                0 0 20px rgba(255, 215, 0, 0.6),
                0 0 30px rgba(255, 215, 0, 0.4);
            animation: fadeInDown 1.8s ease-out;
            position: relative;
            font-weight: 900;
            letter-spacing: 3px;
            text-transform: uppercase;
        }

        .title::before {
            content: '⚡';
            position: absolute;
            left: -120px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            animation: lightning 1.5s infinite ease-in-out;
        }

        .title::after {
            content: '🦸‍♂️';
            position: absolute;
            right: -120px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            animation: heroFly 3s infinite ease-in-out;
        }

        @keyframes lightning {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.3); filter: brightness(1.5); }
        }

        @keyframes heroFly {
            0%, 100% { transform: translateY(-50%) rotate(0deg); }
            25% { transform: translateY(-60%) rotate(-5deg); }
            75% { transform: translateY(-40%) rotate(5deg); }
        }

        .subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.6rem;
            margin-bottom: 60px;
            animation: fadeInUp 1.8s ease-out 0.3s both;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .story-canvas {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            box-shadow: 
                0 35px 70px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            margin-bottom: 70px;
            overflow: hidden;
            animation: slideInUp 1.8s ease-out 0.6s both;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            position: relative;
        }

        .power-level {
            position: absolute;
            top: 30px;
            left: 30px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 18px 30px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            z-index: 100;
            animation: powerPulse 2s infinite;
        }

        @keyframes powerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6); }
        }

        canvas {
            display: block;
            width: 100%;
            height: 600px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 80px;
            animation: fadeIn 1.8s ease-out 1.8s both;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 25px 50px;
            border-radius: 50px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
            transition: left 1s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-12px) scale(1.08);
            box-shadow: 0 25px 60px rgba(255, 107, 107, 0.6);
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
        }

        .btn:active {
            transform: translateY(-6px) scale(1.04);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            padding: 70px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
            margin-bottom: 60px;
            animation: slideInUp 1.8s ease-out 1.5s both;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 215, 0, 0.2);
        }

        .explanation h2 {
            color: #FFD700;
            margin-bottom: 40px;
            font-size: 2.5rem;
            position: relative;
            padding-left: 50px;
            font-weight: 300;
        }

        .explanation h2::before {
            content: '💪';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 2.2rem;
            animation: powerUp 2s infinite;
        }

        @keyframes powerUp {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .explanation p {
            color: rgba(255, 255, 255, 0.95);
            line-height: 2.4;
            font-size: 1.4rem;
            margin-bottom: 35px;
        }

        .word-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #c44569 100%);
            color: white;
            padding: 40px;
            border-radius: 30px;
            margin: 35px 0;
            cursor: pointer;
            transition: all 0.7s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateY(60px);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .word-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.25), transparent);
            transform: translateX(-100%);
            transition: transform 1.2s;
        }

        .word-card:hover::before {
            transform: translateX(100%);
        }

        .word-card.show {
            transform: translateY(0);
            opacity: 1;
        }

        .word-card:hover {
            transform: scale(1.08) translateY(-15px);
            box-shadow: 0 40px 80px rgba(255, 107, 107, 0.5);
        }

        .prefix {
            font-size: 2.2rem;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
        }

        .meaning {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.95);
            margin-top: 20px;
            font-weight: 300;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-80px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(80px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(120px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .interactive-area {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 35px;
            padding: 70px;
            margin-top: 60px;
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 215, 0, 0.2);
        }

        .quiz-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 22px 45px;
            border-radius: 40px;
            margin: 15px;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 1.2rem;
            font-weight: 600;
        }

        .quiz-btn:hover {
            transform: scale(1.15) translateY(-8px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5);
        }

        .correct {
            background: linear-gradient(45deg, #00d2ff, #3a7bd5) !important;
            animation: correctShine 1.2s ease;
        }

        .wrong {
            background: linear-gradient(45deg, #ff416c, #ff4b2b) !important;
            animation: wrongShake 1.2s ease;
        }

        @keyframes correctShine {
            0%, 100% { box-shadow: 0 0 30px rgba(0, 210, 255, 0.6); }
            50% { box-shadow: 0 0 60px rgba(0, 210, 255, 0.9); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-10px); }
            40%, 80% { transform: translateX(10px); }
        }
    </style>
</head>
<body>
    <!-- 超级英雄能量场 -->
    <div class="energy-field"></div>
    
    <!-- 飞行轨迹 -->
    <div class="flight-trails" id="flightTrails"></div>

    <div class="container">
        <h1 class="title">Over- 词缀英雄学院</h1>
        <p class="subtitle">跟随超级英雄探索"超越、过度"的力量奥秘</p>
        
        <div class="story-canvas">
            <div class="power-level" id="powerLevel">能量等级：待激活</div>
            <canvas id="storyCanvas" width="1200" height="600"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">⚡ 激活超能力</button>
            <button class="btn" onclick="nextScene()">🚀 升级能力</button>
            <button class="btn" onclick="prevScene()">⬅️ 回到基地</button>
            <button class="btn" onclick="resetStory()">🔄 重置训练</button>
        </div>

        <div class="explanation">
            <h2>为什么选择"超级英雄"的故事？</h2>
            <p>
                我选择用"超级英雄"来讲解 <span class="prefix">over-</span> 词缀，是因为这个词缀的核心含义就是"超越、过度、在...之上"。
                超级英雄最大的特点就是拥有"超越"常人的能力，这完美体现了"over-"的"超越"概念。
            </p>
            <p>
                通过英雄的成长历程，你可以直观地理解overcome(克服困难)、overlook(俯视)、
                overflow(溢出)等词汇的含义。每当英雄使用超能力时，都体现了"over-"的"超越"特性，
                让抽象的程度概念变得生动具体。
            </p>
            
            <div id="wordCards">
                <!-- 词汇卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #FFD700; margin-bottom: 40px; font-size: 2rem;">🎯 超能力测试：选择正确的翻译</h3>
            <div id="quizArea">
                <!-- 测试题目将通过JavaScript生成 -->
            </div>
            
            <div style="margin-top: 70px; padding-top: 60px; border-top: 3px solid rgba(255, 215, 0, 0.3);">
                <h3 style="color: #FFD700; margin-bottom: 35px;">🔊 超级发音训练</h3>
                <p style="color: rgba(255, 255, 255, 0.9); margin-bottom: 35px;">点击下面的按钮听发音，感受超级英雄的声音：</p>
                <div id="pronunciationArea" style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
                    <!-- 发音按钮将通过JavaScript生成 -->
                </div>
            </div>
            
            <div style="margin-top: 70px; padding-top: 60px; border-top: 3px solid rgba(255, 215, 0, 0.3);">
                <h3 style="color: #FFD700; margin-bottom: 35px;">💡 超级记忆法</h3>
                <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #c44569 100%); 
                           color: white; padding: 40px; border-radius: 30px; line-height: 2.4;">
                    <p><strong>🎯 记忆技巧：</strong></p>
                    <p>⚡ <strong>over-</strong> = "超越、过度、在...之上"</p>
                    <p>🦸‍♂️ 想象一个超级英雄总是能"超越"普通人的能力</p>
                    <p>📝 每次看到 over- 开头的单词，就想到"超过正常程度"</p>
                    <p>🎯 练习方法：遇到新单词时，问自己"这是超过了什么？"</p>
                    <p>🌟 常见搭配：over- + 动词 = 超过某种程度地做某事</p>
                    <p>⚡ 超越记忆：overcome(超越困难)、overflow(超过容量)、overlook(从上往下看)</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建飞行轨迹
        function createFlightTrails() {
            const container = document.getElementById('flightTrails');
            setInterval(() => {
                const trail = document.createElement('div');
                trail.className = 'trail';
                trail.style.left = Math.random() * 100 + '%';
                trail.style.animationDuration = (Math.random() * 2 + 2) + 's';
                trail.style.animationDelay = Math.random() * 1 + 's';
                container.appendChild(trail);
                
                setTimeout(() => {
                    if (trail.parentNode) {
                        trail.remove();
                    }
                }, 4000);
            }, 500);
        }

        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;
        let isAnimating = false;

        // 故事场景数据
        const scenes = [
            {
                title: "英雄基地",
                description: "英雄准备克服挑战 (overcome)",
                words: ["overcome", "overlook", "overflow"],
                power: "能量等级：新手",
                bgColor: "#667eea"
            },
            {
                title: "城市上空",
                description: "英雄俯视城市全貌 (overlook)",
                words: ["overlook", "overpass", "overhead"],
                power: "能量等级：进阶",
                bgColor: "#764ba2"
            },
            {
                title: "能量爆发",
                description: "力量溢出控制范围 (overflow)",
                words: ["overflow", "overload", "overpower"],
                power: "能量等级：专家",
                bgColor: "#ff6b6b"
            },
            {
                title: "终极形态",
                description: "完全超越自我极限 (overcome)",
                words: ["overcome", "overall", "outstanding"],
                power: "能量等级：传奇",
                bgColor: "#FFD700"
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "overcome",
                prefix: "over-",
                root: "come (来)",
                meaning: "克服、战胜",
                explanation: "over(超越) + come(来) = 超越困难而来",
                sentence: "The hero can overcome any challenge. (英雄能够克服任何挑战。)"
            },
            {
                word: "overlook",
                prefix: "over-",
                root: "look (看)",
                meaning: "俯视、忽视",
                explanation: "over(在上面) + look(看) = 从上面往下看",
                sentence: "The hero overlooks the city from above. (英雄从上方俯视城市。)"
            },
            {
                word: "overflow",
                prefix: "over-",
                root: "flow (流)",
                meaning: "溢出、泛滥",
                explanation: "over(超过) + flow(流) = 流量超过容器",
                sentence: "His power overflows with energy. (他的力量充满能量溢出。)"
            },
            {
                word: "overpower",
                prefix: "over-",
                root: "power (力量)",
                meaning: "压倒、制服",
                explanation: "over(超过) + power(力量) = 力量超过对手",
                sentence: "The hero can overpower any villain. (英雄能够制服任何反派。)"
            }
        ];

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = true;
            updatePowerLevel();
            drawScene();
            showWordCards();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                animationFrame = 0;
                updatePowerLevel();
                drawScene();
                updateWordCards();
            }
        }

        function prevScene() {
            if (currentScene > 0) {
                currentScene--;
                animationFrame = 0;
                updatePowerLevel();
                drawScene();
                updateWordCards();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('wordCards').innerHTML = '';
            updatePowerLevel();
        }

        function updatePowerLevel() {
            document.getElementById('powerLevel').textContent = scenes[currentScene].power;
        }

        function drawScene() {
            if (!isAnimating) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制超级英雄背景
            drawHeroBackground();

            // 绘制城市景观
            drawCityscape();

            // 绘制超级英雄
            drawSuperhero();

            // 绘制场景特效
            drawSceneEffects();

            // 绘制能量效果
            drawEnergyEffects();

            // 绘制文字说明
            drawSceneText();

            animationFrame++;
            if (isAnimating) {
                requestAnimationFrame(drawScene);
            }
        }

        function drawHeroBackground() {
            // 动态天空背景
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, scenes[currentScene].bgColor);
            gradient.addColorStop(0.7, darkenColor(scenes[currentScene].bgColor, 0.3));
            gradient.addColorStop(1, '#1a1a2e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制云朵
            drawClouds();

            // 绘制能量光线
            drawEnergyBeams();
        }

        function drawClouds() {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            for (let i = 0; i < 8; i++) {
                const x = (i * 180 + animationFrame * 0.3) % (canvas.width + 100);
                const y = 80 + Math.sin(animationFrame * 0.01 + i) * 20;
                const size = 30 + Math.sin(animationFrame * 0.02 + i) * 10;

                // 绘制云朵
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.arc(x + size * 0.8, y, size * 0.8, 0, Math.PI * 2);
                ctx.arc(x + size * 1.6, y, size, 0, Math.PI * 2);
                ctx.arc(x + size * 0.8, y - size * 0.5, size * 0.6, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawEnergyBeams() {
            // 背景能量光束
            for (let i = 0; i < 5; i++) {
                const x = (i * 250 + animationFrame * 0.5) % canvas.width;
                const alpha = Math.sin(animationFrame * 0.02 + i) * 0.3 + 0.2;

                ctx.strokeStyle = `rgba(255, 215, 0, ${alpha})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x + 50, canvas.height);
                ctx.stroke();
            }
        }

        function drawCityscape() {
            // 绘制城市建筑轮廓
            const buildings = [
                { x: 50, width: 80, height: 200 },
                { x: 150, width: 60, height: 150 },
                { x: 230, width: 100, height: 250 },
                { x: 350, width: 70, height: 180 },
                { x: 440, width: 90, height: 220 },
                { x: 550, width: 75, height: 160 },
                { x: 650, width: 85, height: 240 },
                { x: 760, width: 65, height: 170 },
                { x: 850, width: 95, height: 200 },
                { x: 970, width: 80, height: 190 }
            ];

            buildings.forEach((building, index) => {
                const y = canvas.height - building.height;

                // 建筑主体
                ctx.fillStyle = `rgba(30, 30, 60, 0.8)`;
                ctx.fillRect(building.x, y, building.width, building.height);

                // 建筑边框
                ctx.strokeStyle = 'rgba(100, 100, 150, 0.5)';
                ctx.lineWidth = 1;
                ctx.strokeRect(building.x, y, building.width, building.height);

                // 窗户
                ctx.fillStyle = '#ffff00';
                for (let row = 0; row < Math.floor(building.height / 25); row++) {
                    for (let col = 0; col < Math.floor(building.width / 20); col++) {
                        const windowAlpha = Math.sin(animationFrame * 0.03 + index + row + col) * 0.5 + 0.5;
                        ctx.globalAlpha = windowAlpha;
                        ctx.fillRect(
                            building.x + 5 + col * 20,
                            y + 5 + row * 25,
                            8, 12
                        );
                    }
                }
                ctx.globalAlpha = 1;
            });
        }

        function drawSuperhero() {
            const heroX = 200 + Math.sin(animationFrame * 0.02) * 20;
            const heroY = 150 + Math.cos(animationFrame * 0.025) * 15;

            // 英雄斗篷
            ctx.fillStyle = '#c44569';
            ctx.beginPath();
            ctx.moveTo(heroX - 20, heroY + 10);
            ctx.quadraticCurveTo(heroX - 40, heroY + 30, heroX - 25, heroY + 80);
            ctx.quadraticCurveTo(heroX - 10, heroY + 90, heroX + 5, heroY + 80);
            ctx.quadraticCurveTo(heroX + 20, heroY + 30, heroX + 20, heroY + 10);
            ctx.fill();

            // 英雄身体
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(heroX - 15, heroY, 30, 50);

            // 英雄头部
            ctx.fillStyle = '#fdbcb4';
            ctx.beginPath();
            ctx.arc(heroX, heroY - 15, 20, 0, Math.PI * 2);
            ctx.fill();

            // 英雄面具
            ctx.fillStyle = '#2c3e50';
            ctx.beginPath();
            ctx.ellipse(heroX, heroY - 15, 25, 12, 0, 0, Math.PI);
            ctx.fill();

            // 英雄标志
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('O', heroX, heroY + 20);

            // 英雄手臂
            ctx.strokeStyle = '#fdbcb4';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(heroX - 15, heroY + 15);
            ctx.lineTo(heroX - 35, heroY + 5);
            ctx.moveTo(heroX + 15, heroY + 15);
            ctx.lineTo(heroX + 35, heroY + 5);
            ctx.stroke();

            // 英雄腿部
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 10;
            ctx.beginPath();
            ctx.moveTo(heroX - 8, heroY + 50);
            ctx.lineTo(heroX - 8, heroY + 80);
            ctx.moveTo(heroX + 8, heroY + 50);
            ctx.lineTo(heroX + 8, heroY + 80);
            ctx.stroke();

            // 飞行姿态效果
            drawFlightEffects(heroX, heroY);
        }

        function drawFlightEffects(heroX, heroY) {
            // 飞行尾迹
            for (let i = 0; i < 8; i++) {
                const trailX = heroX - 30 - i * 15;
                const trailY = heroY + 25 + Math.sin(animationFrame * 0.1 + i) * 8;
                const alpha = (8 - i) / 8 * 0.7;

                ctx.fillStyle = `rgba(255, 107, 107, ${alpha})`;
                ctx.beginPath();
                ctx.arc(trailX, trailY, 4 - i * 0.3, 0, Math.PI * 2);
                ctx.fill();
            }

            // 能量光环
            const glowIntensity = Math.sin(animationFrame * 0.1) * 0.5 + 0.5;
            ctx.strokeStyle = `rgba(255, 215, 0, ${glowIntensity})`;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(heroX, heroY + 25, 40 + Math.sin(animationFrame * 0.05) * 10, 0, Math.PI * 2);
            ctx.stroke();
        }

        function drawSceneEffects() {
            switch(currentScene) {
                case 0:
                    drawTrainingEffects();
                    break;
                case 1:
                    drawOverlookEffects();
                    break;
                case 2:
                    drawOverflowEffects();
                    break;
                case 3:
                    drawUltimateEffects();
                    break;
            }
        }

        function drawTrainingEffects() {
            // 训练目标
            const targetX = 600;
            const targetY = 300;

            // 目标圆环
            for (let i = 0; i < 3; i++) {
                const radius = 30 + i * 20;
                const alpha = Math.sin(animationFrame * 0.05 + i) * 0.3 + 0.4;

                ctx.strokeStyle = `rgba(255, 107, 107, ${alpha})`;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.arc(targetX, targetY, radius, 0, Math.PI * 2);
                ctx.stroke();
            }

            // 中心点
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(targetX, targetY, 8, 0, Math.PI * 2);
            ctx.fill();

            // 障碍物
            drawObstacles();
        }

        function drawObstacles() {
            const obstacles = [
                { x: 400, y: 400, width: 60, height: 20 },
                { x: 500, y: 350, width: 20, height: 60 },
                { x: 700, y: 380, width: 80, height: 25 }
            ];

            obstacles.forEach((obstacle, index) => {
                const pulse = Math.sin(animationFrame * 0.04 + index) * 0.2 + 0.8;
                ctx.fillStyle = `rgba(200, 50, 50, ${pulse})`;
                ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);

                // 障碍物边框
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 2;
                ctx.strokeRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
            });
        }

        function drawOverlookEffects() {
            // 俯视视角线条
            const heroX = 200 + Math.sin(animationFrame * 0.02) * 20;
            const heroY = 150 + Math.cos(animationFrame * 0.025) * 15;

            // 视线范围
            ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            for (let i = 0; i < 5; i++) {
                const angle = (i - 2) * 0.3;
                const endX = heroX + Math.cos(angle) * 400;
                const endY = heroY + Math.sin(angle) * 400 + 200;

                ctx.beginPath();
                ctx.moveTo(heroX, heroY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }

            ctx.setLineDash([]);

            // 观察点标记
            const observationPoints = [
                { x: 400, y: 450 }, { x: 600, y: 480 }, { x: 800, y: 460 }
            ];

            observationPoints.forEach((point, index) => {
                const pulse = Math.sin(animationFrame * 0.06 + index) * 0.4 + 0.6;
                ctx.fillStyle = `rgba(0, 255, 136, ${pulse})`;
                ctx.beginPath();
                ctx.arc(point.x, point.y, 8, 0, Math.PI * 2);
                ctx.fill();

                // 标记文字
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`观察点${index + 1}`, point.x, point.y + 25);
            });
        }

        function drawOverflowEffects() {
            // 能量容器
            const containerX = 500;
            const containerY = 300;
            const containerWidth = 100;
            const containerHeight = 150;

            // 容器边框
            ctx.strokeStyle = '#87ceeb';
            ctx.lineWidth = 4;
            ctx.strokeRect(containerX, containerY, containerWidth, containerHeight);

            // 溢出的能量
            const overflowLevel = Math.sin(animationFrame * 0.05) * 30 + 120;
            const energyHeight = Math.min(overflowLevel, containerHeight);

            // 容器内能量
            const energyGradient = ctx.createLinearGradient(0, containerY + containerHeight, 0, containerY);
            energyGradient.addColorStop(0, '#ff6b6b');
            energyGradient.addColorStop(0.5, '#FFD700');
            energyGradient.addColorStop(1, '#00d4ff');

            ctx.fillStyle = energyGradient;
            ctx.fillRect(containerX + 2, containerY + containerHeight - energyHeight, containerWidth - 4, energyHeight);

            // 溢出效果
            if (overflowLevel > containerHeight) {
                const overflowAmount = overflowLevel - containerHeight;

                // 溢出的能量粒子
                for (let i = 0; i < 20; i++) {
                    const particleX = containerX + 20 + Math.random() * 60;
                    const particleY = containerY - Math.random() * overflowAmount;
                    const alpha = Math.random() * 0.8 + 0.2;

                    ctx.fillStyle = `rgba(255, 215, 0, ${alpha})`;
                    ctx.beginPath();
                    ctx.arc(particleX, particleY, Math.random() * 4 + 2, 0, Math.PI * 2);
                    ctx.fill();
                }

                // 溢出警告
                ctx.fillStyle = '#ff4757';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('OVERFLOW!', containerX + containerWidth / 2, containerY - 20);
            }
        }

        function drawUltimateEffects() {
            // 终极能量爆发
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 能量波纹
            for (let i = 0; i < 5; i++) {
                const radius = 50 + i * 40 + (animationFrame * 2) % 200;
                const alpha = (1 - (animationFrame * 2) % 200 / 200) * 0.6;

                ctx.strokeStyle = `rgba(255, 215, 0, ${alpha})`;
                ctx.lineWidth = 5;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
            }

            // 能量射线
            for (let i = 0; i < 12; i++) {
                const angle = (i * Math.PI * 2 / 12) + animationFrame * 0.02;
                const length = 150 + Math.sin(animationFrame * 0.1 + i) * 50;
                const endX = centerX + Math.cos(angle) * length;
                const endY = centerY + Math.sin(angle) * length;

                ctx.strokeStyle = `rgba(255, 215, 0, 0.8)`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }

            // 中心能量核心
            const coreGlow = Math.sin(animationFrame * 0.2) * 0.5 + 0.5;
            ctx.fillStyle = `rgba(255, 255, 255, ${coreGlow})`;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 20, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawEnergyEffects() {
            // 漂浮的能量粒子
            for (let i = 0; i < 15; i++) {
                const x = (i * 80 + animationFrame * 0.5) % canvas.width;
                const y = 100 + Math.sin(animationFrame * 0.02 + i) * 50;
                const size = 2 + Math.sin(animationFrame * 0.05 + i) * 2;

                ctx.fillStyle = `hsl(${(animationFrame + i * 24) % 360}, 80%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawSceneText() {
            // 场景标题
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 40px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(255, 215, 0, 0.8)';
            ctx.shadowBlur = 15;
            ctx.fillText(scenes[currentScene].title, canvas.width / 2, 70);

            // 场景描述
            ctx.font = '24px Microsoft YaHei';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillText(scenes[currentScene].description, canvas.width / 2, 105);
            ctx.shadowBlur = 0;
        }

        function darkenColor(color, factor) {
            // 简单的颜色变暗函数
            if (color.startsWith('#')) {
                const hex = color.replace('#', '');
                const r = Math.max(0, parseInt(hex.substr(0, 2), 16) * (1 - factor));
                const g = Math.max(0, parseInt(hex.substr(2, 2), 16) * (1 - factor));
                const b = Math.max(0, parseInt(hex.substr(4, 2), 16) * (1 - factor));
                return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`;
            }
            return color;
        }

        function showWordCards() {
            const container = document.getElementById('wordCards');
            container.innerHTML = '';

            vocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.innerHTML = `
                        <div style="font-size: 2.2rem; font-weight: bold; margin-bottom: 20px;">
                            <span class="prefix">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div class="meaning">${item.meaning}</div>
                        <div style="font-size: 1.15rem; margin-top: 18px; opacity: 0.9;">${item.explanation}</div>
                        <div style="font-size: 1.1rem; margin-top: 15px; font-style: italic; opacity: 0.85;
                                   background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px;">
                            "${item.sentence}"
                        </div>
                    `;

                    card.addEventListener('click', () => {
                        speakWord(item.word);
                        showWordBreakdown(item);
                    });

                    container.appendChild(card);

                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 700);
            });
        }

        function updateWordCards() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => {
                card.style.opacity = '0.4';
                card.style.transform = 'scale(0.92)';
            });

            // 高亮当前场景的词汇
            const currentWords = scenes[currentScene].words;
            cards.forEach((card, index) => {
                if (currentWords.includes(vocabulary[index]?.word)) {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1.08)';
                    card.style.boxShadow = '0 45px 90px rgba(255, 107, 107, 0.6)';
                }
            });
        }

        function showWordBreakdown(wordData) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.6s ease;
                backdrop-filter: blur(12px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #c44569 100%);
                color: white;
                padding: 80px;
                border-radius: 35px;
                max-width: 750px;
                text-align: center;
                animation: slideInUp 0.8s ease;
                box-shadow: 0 40px 80px rgba(255, 107, 107, 0.5);
                border: 3px solid rgba(255,255,255,0.2);
            `;

            content.innerHTML = `
                <h2 style="margin-bottom: 40px; font-size: 2.5rem; font-weight: 300;">⚡ 超级词汇解析</h2>
                <div style="font-size: 4rem; margin: 40px 0;">
                    <span style="color: #FFD700; font-weight: bold;">${wordData.prefix}</span>
                    <span style="color: #87CEEB; font-weight: bold;">${wordData.root}</span>
                </div>
                <div style="font-size: 2.5rem; margin: 35px 0; font-weight: bold;">${wordData.word}</div>
                <div style="font-size: 1.6rem; margin: 35px 0; opacity: 0.9;">${wordData.meaning}</div>
                <div style="line-height: 2.2; margin: 35px 0; font-size: 1.25rem; opacity: 0.85;">${wordData.explanation}</div>
                <div style="background: rgba(255,255,255,0.2); padding: 30px; border-radius: 18px; margin: 35px 0;
                           font-style: italic; font-size: 1.2rem;">
                    ${wordData.sentence}
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 40px; padding: 20px 40px; background: linear-gradient(45deg, #667eea, #764ba2);
                               color: white; border: none; border-radius: 35px; cursor: pointer; font-size: 1.3rem;
                               transition: all 0.3s ease; font-weight: bold;">
                    返回英雄基地
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function speakWord(word) {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                utterance.pitch = 1.1;

                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                    color: white;
                    padding: 40px 60px;
                    border-radius: 50px;
                    font-size: 2.2rem;
                    z-index: 1000;
                    animation: fadeIn 0.6s ease;
                    box-shadow: 0 30px 60px rgba(255, 107, 107, 0.6);
                    border: 3px solid rgba(255,255,255,0.3);
                `;
                feedback.innerHTML = `⚡ <strong>${word}</strong><br><small style="opacity: 0.8; font-size: 0.6em;">超级英雄发音中...</small>`;
                document.body.appendChild(feedback);

                utterance.onend = () => {
                    setTimeout(() => {
                        if (feedback.parentNode) {
                            feedback.remove();
                        }
                    }, 1500);
                };

                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音功能');
            }
        }

        function initQuiz() {
            const quizData = [
                {
                    question: "overcome 的意思是？",
                    options: ["克服", "俯视", "溢出", "压倒"],
                    correct: 0,
                    explanation: "over(超越) + come(来) = 超越困难而来"
                },
                {
                    question: "overlook 的意思是？",
                    options: ["克服", "俯视", "溢出", "压倒"],
                    correct: 1,
                    explanation: "over(在上面) + look(看) = 从上面往下看"
                },
                {
                    question: "overflow 的意思是？",
                    options: ["克服", "俯视", "溢出", "压倒"],
                    correct: 2,
                    explanation: "over(超过) + flow(流) = 流量超过容器"
                },
                {
                    question: "overpower 的意思是？",
                    options: ["克服", "俯视", "溢出", "压倒"],
                    correct: 3,
                    explanation: "over(超过) + power(力量) = 力量超过对手"
                }
            ];

            const quizArea = document.getElementById('quizArea');

            quizData.forEach((quiz, qIndex) => {
                const quizDiv = document.createElement('div');
                quizDiv.style.marginBottom = '40px';
                quizDiv.innerHTML = `<h4 style="margin-bottom: 25px; color: #FFD700; font-size: 1.5rem;">${quiz.question}</h4>`;

                quiz.options.forEach((option, oIndex) => {
                    const btn = document.createElement('button');
                    btn.className = 'quiz-btn';
                    btn.textContent = option;
                    btn.onclick = () => checkAnswer(btn, oIndex === quiz.correct, quiz.explanation, qIndex);
                    quizDiv.appendChild(btn);
                });

                quizArea.appendChild(quizDiv);
            });
        }

        function checkAnswer(btn, isCorrect, explanation, questionIndex) {
            const buttons = btn.parentNode.querySelectorAll('.quiz-btn');
            buttons.forEach(b => b.disabled = true);

            if (isCorrect) {
                btn.classList.add('correct');
                btn.innerHTML += ' ✓';

                // 显示解释
                setTimeout(() => {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.style.cssText = `
                        margin-top: 20px;
                        padding: 30px;
                        background: linear-gradient(135deg, #00d2ff, #3a7bd5);
                        color: white;
                        border-radius: 18px;
                        font-size: 1.1rem;
                        animation: slideInUp 0.8s ease;
                        box-shadow: 0 20px 40px rgba(0, 210, 255, 0.4);
                    `;
                    explanationDiv.innerHTML = `⚡ ${explanation}`;
                    btn.parentNode.appendChild(explanationDiv);
                }, 800);
            } else {
                btn.classList.add('wrong');
                btn.innerHTML += ' ✗';
            }
        }

        function initPronunciation() {
            const pronunciationArea = document.getElementById('pronunciationArea');

            vocabulary.forEach((item, index) => {
                const btn = document.createElement('button');
                btn.className = 'quiz-btn';
                btn.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a24)';
                btn.style.color = 'white';
                btn.innerHTML = `⚡ ${item.word}`;

                btn.onclick = () => {
                    speakWord(item.word);
                    btn.style.transform = 'scale(0.85)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 250);
                };

                pronunciationArea.appendChild(btn);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextScene();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    prevScene();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetStory();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    startStory();
                    break;
            }
        });

        // 页面加载时创建飞行轨迹
        window.addEventListener('load', () => {
            createFlightTrails();
            initQuiz();
            initPronunciation();
            setTimeout(startStory, 2000);

            // 显示快捷键提示
            setTimeout(() => {
                const hints = document.createElement('div');
                hints.style.cssText = `
                    position: fixed;
                    bottom: 40px;
                    right: 40px;
                    background: rgba(0,0,0,0.9);
                    color: #FFD700;
                    padding: 35px;
                    border-radius: 30px;
                    font-size: 1.1rem;
                    z-index: 1000;
                    animation: slideInUp 0.8s ease;
                    backdrop-filter: blur(18px);
                    border: 3px solid rgba(255, 215, 0, 0.3);
                `;
                hints.innerHTML = `
                    <div style="margin-bottom: 15px;"><strong>⚡ 超级快捷键：</strong></div>
                    <div>→ 或 空格：升级能力</div>
                    <div>← ：回到基地</div>
                    <div>S：激活超能力</div>
                    <div>R：重置训练</div>
                `;

                document.body.appendChild(hints);

                setTimeout(() => {
                    hints.style.animation = 'fadeOut 0.8s ease';
                    setTimeout(() => hints.remove(), 800);
                }, 9000);
            }, 3500);
        });
    </script>
</body>
</html>
