<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中断概念交互式演示</title>
    <style>
        :root {
            --primary-bg: #f0f4f8;
            --text-color: #333;
            --header-color: #4a6fa5;
            --accent-color: #6ac0a9;
            --cpu-color: #f7a072;
            --io-color: #a3d5d3;
            --stack-color: #b39ddb;
            --signal-color: #f44336;
            --code-bg: #e8eaed;
            --button-bg: #4a6fa5;
            --button-hover-bg: #3b5998;
            --white: #fff;
            --border-color: #d1d9e6;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--primary-bg);
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        header {
            background-color: var(--header-color);
            color: var(--white);
            padding: 20px;
            text-align: center;
        }
        header h1 {
            margin: 0;
            font-size: 1.8em;
        }
        main {
            padding: 25px;
        }
        .question, .explanation, .interactive-demo {
            margin-bottom: 25px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }
        .interactive-demo {
            border-bottom: none;
        }
        h2 {
            color: var(--header-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
        }
        p, li {
            font-size: 1.1em;
        }
        .code {
            background-color: var(--code-bg);
            padding: 15px;
            border-radius: 8px;
            font-family: "Courier New", Courier, monospace;
            border: 1px solid var(--border-color);
        }
        #canvas-container {
            position: relative;
            width: 100%;
            max-width: 850px;
            margin: 0 auto;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: #fdfdfd;
        }
        canvas {
            display: block;
            width: 100%;
            height: auto;
        }
        .controls {
            text-align: center;
            padding: 20px;
        }
        button {
            background-color: var(--button-bg);
            color: var(--white);
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        button:hover:not(:disabled) {
            background-color: var(--button-hover-bg);
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #9db2bf;
            cursor: not-allowed;
        }
        .explanation-text {
            text-align: center;
            font-size: 1.2em;
            color: var(--header-color);
            margin-top: 15px;
            font-weight: bold;
            height: 30px;
        }
    </style>
</head>
<body>

<div class="container">
    <header>
        <h1>中断：CPU的高效工作法</h1>
    </header>

    <main>
        <section class="question">
            <h2>题目回顾</h2>
            <div class="code">
                <p><strong>题目13：</strong>嵌入式系统中采用中断方式实现输入输出的主要原因是（ ）。在中断时，CPU断点信息一般保存到（ ）中。</p>
                <p><strong>[单选题]</strong></p>
                <ul>
                    <li>A. 通用寄存器</li>
                    <li>B. 堆</li>
                    <li><strong>C. 栈</strong> (正确答案)</li>
                    <li>D. I/O接口</li>
                </ul>
            </div>
        </section>

        <section class="explanation">
            <h2>知识点解释</h2>
            <p>想象一下CPU是一位非常忙碌的厨师，正在按部就班地切菜（执行主程序）。如果他需要等微波炉热好食物（等待I/O设备），他有两种选择：</p>
            <ul>
                <li><strong>轮询 (Polling)</strong>: 每隔几秒就跑去看一下微波炉好了没。这很浪费时间，厨师没法专心切菜。</li>
                <li><strong>中断 (Interrupt)</strong>: 设置好微波炉后，继续切自己的菜。当微波炉"叮"的一声（发出中断信号），厨师才放下手中的活，去处理热好的食物（执行中断服务程序）。</li>
            </ul>
            <p>显然，<strong>中断更高效</strong>。当中断发生时，厨师需要记住自己菜切到哪了（保存断点信息），才能在处理完微波炉后无缝衔接。这个"小本本"就是计算机里的<strong>"栈" (Stack)</strong>。</p>
        </section>

        <section class="interactive-demo">
            <h2>交互式动画演示</h2>
            <div id="canvas-container">
                <canvas id="interrupt-canvas" width="850" height="400"></canvas>
            </div>
            <div class="explanation-text" id="explanation-text">点击按钮，开始动画演示</div>
            <div class="controls">
                <button id="polling-btn">观看"轮询"模式 (效率低)</button>
                <button id="interrupt-btn">观看"中断"模式 (效率高)</button>
            </div>
        </section>
    </main>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('interrupt-canvas');
    const ctx = canvas.getContext('2d');
    const explanationText = document.getElementById('explanation-text');
    const pollingBtn = document.getElementById('polling-btn');
    const interruptBtn = document.getElementById('interrupt-btn');

    const elements = {
        cpu: { x: 50, y: 150, width: 120, height: 100, color: 'var(--cpu-color)', text: 'CPU' },
        io: { x: 680, y: 150, width: 120, height: 100, color: 'var(--io-color)', text: 'I/O 设备' },
        stack: { x: 350, y: 280, width: 150, height: 100, color: 'var(--stack-color)', text: '栈' },
        mainProgram: { x: 50, y: 50, width: 750, height: 40, text: '主程序' },
        isr: { x: 50, y: 50, width: 750, height: 40, text: '中断服务程序 (ISR)' }
    };

    let animationState = {
        mode: 'idle', // 'idle', 'polling', 'interrupt'
        progress: 0,
        interruptStep: 0,
        contextBlock: null,
        ioWorking: false,
        ioProgress: 0
    };

    function drawBox(el, label, progress = 0) {
        ctx.fillStyle = el.color;
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.fillRect(el.x, el.y, el.width, el.height);
        ctx.strokeRect(el.x, el.y, el.width, el.height);

        ctx.fillStyle = '#fff';
        ctx.font = 'bold 18px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(label || el.text, el.x + el.width / 2, el.y + el.height / 2);
        
        if (progress > 0) {
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.fillRect(el.x, el.y + el.height - (el.height * progress), el.width, el.height * progress);
        }
    }

    function drawArrow(fromX, fromY, toX, toY, text = '', color = 'var(--signal-color)') {
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.stroke();

        // Arrowhead
        const headlen = 10;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        ctx.beginPath();
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
        ctx.closePath();
        ctx.fillStyle = color;
        ctx.fill();

        if (text) {
            ctx.fillStyle = 'var(--text-color)';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
        }
    }
    
    function drawProgramBar(el, progress, text, color) {
        ctx.strokeStyle = '#333';
        ctx.strokeRect(el.x, el.y, el.width, el.height);
        ctx.fillStyle = color;
        ctx.fillRect(el.x, el.y, el.width * progress, el.height);
        
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(text, el.x + el.width/2, el.y + el.height/2);
    }

    function drawStackContent(stack) {
        if(animationState.contextBlock) {
             const block = animationState.contextBlock;
             ctx.fillStyle = block.color;
             ctx.fillRect(block.x, block.y, block.width, block.height);
             ctx.strokeRect(block.x, block.y, block.width, block.height);
             ctx.fillStyle = '#fff';
             ctx.font = 'bold 14px sans-serif';
             ctx.fillText(block.text, block.x + block.width / 2, block.y + block.height / 2);
        }
    }

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        drawBox(elements.cpu);
        drawBox(elements.io, animationState.ioWorking ? 'I/O工作中...' : 'I/O设备');
        drawBox(elements.stack);
        drawStackContent(elements.stack);

        switch (animationState.mode) {
            case 'idle':
                explanationText.textContent = "点击按钮，开始动画演示";
                drawProgramBar(elements.mainProgram, 0, '主程序', 'var(--accent-color)');
                break;
            case 'polling':
                drawProgramBar(elements.mainProgram, animationState.progress, '主程序', 'var(--accent-color)');
                const arrowX = elements.cpu.x + elements.cpu.width;
                const arrowY = elements.cpu.y + elements.cpu.height / 2;
                if (Math.floor(animationState.progress * 100) % 20 < 10) {
                     drawArrow(arrowX, arrowY, elements.io.x, arrowY, '在忙吗?', 'gray');
                }
                break;
            case 'interrupt':
                 if (animationState.interruptStep < 4) { // Before ISR
                     drawProgramBar(elements.mainProgram, animationState.progress, '主程序', 'var(--accent-color)');
                 } else { // During ISR
                     drawProgramBar(elements.isr, animationState.progress, '中断服务程序 (ISR)', 'var(--header-color)');
                 }

                if (animationState.interruptStep === 2) { // Signal interrupt
                     drawArrow(elements.io.x, elements.io.y + elements.io.height/2, elements.cpu.x + elements.cpu.width, elements.cpu.y + elements.cpu.height/2, '中断请求!', 'var(--signal-color)');
                }
                
                if (animationState.interruptStep === 5) { // Communicate with IO
                    drawArrow(elements.cpu.x + elements.cpu.width, elements.cpu.y + elements.cpu.height/2, elements.io.x, elements.io.y + elements.io.height/2, '数据交换', 'var(--accent-color)');
                }

                break;
        }
    }

    function resetState() {
        animationState = {
            mode: 'idle',
            progress: 0,
            interruptStep: 0,
            contextBlock: null,
            ioWorking: false,
            ioProgress: 0
        };
    }

    function animatePolling() {
        resetState();
        animationState.mode = 'polling';
        pollingBtn.disabled = true;
        interruptBtn.disabled = true;
        explanationText.textContent = "CPU 不断检查I/O设备，无法专心执行主程序。";

        let startTime = Date.now();
        function loop() {
            const elapsedTime = Date.now() - startTime;
            animationState.progress = (elapsedTime / 10000) % 1;
            draw();
            if (elapsedTime < 5000) {
                requestAnimationFrame(loop);
            } else {
                explanationText.textContent = "轮询效率太低了！CPU被卡住了。";
                pollingBtn.disabled = false;
                interruptBtn.disabled = false;
                resetState();
                draw();
            }
        }
        loop();
    }

    function animateInterrupt() {
        resetState();
        animationState.mode = 'interrupt';
        pollingBtn.disabled = true;
        interruptBtn.disabled = true;
        
        const steps = [
            // 0: CPU runs main program
            () => {
                explanationText.textContent = "1. CPU 正在高效地执行主程序...";
                animationState.progress += 0.005;
                return animationState.progress > 0.4;
            },
            // 1: IO starts working
            () => {
                explanationText.textContent = "2. 同时，I/O设备接收到任务，开始工作。";
                animationState.ioWorking = true;
                animationState.ioProgress += 0.01;
                animationState.progress += 0.005; // CPU continues
                return animationState.ioProgress > 1;
            },
            // 2: IO sends interrupt signal
            () => {
                explanationText.textContent = "3. I/O完成！发送\"中断信号\"给CPU。";
                 animationState.progress += 0.005; // CPU still running for a bit
                 return animationState.progress > 0.6;
            },
            // 3: CPU saves context to stack
            () => {
                explanationText.textContent = "4. CPU响应中断，保存当前工作状态（上下文）到栈。";
                if (!animationState.contextBlock) {
                    animationState.contextBlock = {
                        x: elements.cpu.x + 20, y: elements.cpu.y + 35, width: 80, height: 30, text: '断点/上下文', color: 'orange'
                    };
                }
                const targetX = elements.stack.x + (elements.stack.width - 80) / 2;
                const targetY = elements.stack.y - 30; // on top
                animationState.contextBlock.x += (targetX - animationState.contextBlock.x) * 0.1;
                animationState.contextBlock.y += (targetY - animationState.contextBlock.y) * 0.1;
                
                return Math.abs(animationState.contextBlock.x - targetX) < 1 && Math.abs(animationState.contextBlock.y - targetY) < 1;
            },
            // 4: CPU runs ISR
            () => {
                explanationText.textContent = "5. CPU跳转执行\"中断服务程序 (ISR)\"。";
                animationState.progress = 0; // Reset progress for ISR
                animationState.interruptStep++; // Move to next state immediately
                return true;
            },
            // 5: ISR runs and handles IO
            () => {
                explanationText.textContent = "6. 在ISR中，CPU与I/O设备交换数据。";
                animationState.progress += 0.01;
                return animationState.progress > 1;
            },
            // 6: Context restored from stack
            () => {
                explanationText.textContent = "7. ISR执行完毕，从栈中恢复上下文。";
                const targetX = elements.cpu.x + 20;
                const targetY = elements.cpu.y + 35;
                animationState.contextBlock.x += (targetX - animationState.contextBlock.x) * 0.1;
                animationState.contextBlock.y += (targetY - animationState.contextBlock.y) * 0.1;

                return Math.abs(animationState.contextBlock.x - targetX) < 1 && Math.abs(animationState.contextBlock.y - targetY) < 1;
            },
            // 7: CPU resumes main program
            () => {
                explanationText.textContent = "8. CPU回到主程序断点处，继续执行！";
                animationState.contextBlock = null;
                animationState.progress += 0.005;
                return animationState.progress > 1;
            },
             // 8: End
            () => {
                explanationText.textContent = "中断完成！CPU效率得到了极大提升。";
                pollingBtn.disabled = false;
                interruptBtn.disabled = false;
                return false; // Stop animation
            }
        ];

        function loop() {
            const done = steps[animationState.interruptStep]();
            draw();
            if (done) {
                animationState.interruptStep++;
            }
            if(animationState.interruptStep < steps.length -1) {
                requestAnimationFrame(loop);
            } else {
                 steps[animationState.interruptStep]();
                 draw();
            }
        }
        
        animationState.interruptStep = 0;
        loop();
    }
    
    pollingBtn.addEventListener('click', animatePolling);
    interruptBtn.addEventListener('click', animateInterrupt);

    // Initial draw
    draw();
});
</script>

</body>
</html>
