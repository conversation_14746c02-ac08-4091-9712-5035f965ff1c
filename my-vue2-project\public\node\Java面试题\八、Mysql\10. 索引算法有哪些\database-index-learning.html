<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库索引算法学习 - BTree vs Hash</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .section h2 {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 30px;
            text-align: center;
        }

        .algorithm-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .algorithm-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .algorithm-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .algorithm-card h3 {
            color: #3498db;
            font-size: 2rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .canvas-container {
            width: 100%;
            height: 400px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .btn.hash {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn.hash:hover {
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .description {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }

        .description p {
            color: #2c3e50;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            overflow-x: auto;
        }

        .code-example pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .highlight {
            background: rgba(52, 152, 219, 0.1);
            padding: 2px 4px;
            border-radius: 4px;
            color: #3498db;
            font-weight: 600;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .demo-input {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }

        .demo-input input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
        }

        .demo-input input:focus {
            outline: none;
            border-color: #3498db;
        }

        @media (max-width: 768px) {
            .algorithm-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据库索引算法学习</h1>
            <p>探索 BTree 和 Hash 算法的奥秘</p>
        </div>

        <div class="section">
            <h2>什么是数据库索引？</h2>
            <div class="description">
                <p>数据库索引就像书籍的目录，帮助我们快速找到需要的信息。想象一下，如果没有目录，你要在一本厚厚的书中找到某个章节，就需要一页一页地翻阅。</p>
                <p>索引的作用就是<span class="highlight">加速数据查找</span>，减少数据库需要扫描的数据量。</p>
            </div>
        </div>

        <div class="algorithm-container">
            <div class="algorithm-card">
                <h3>🌳 BTree 算法</h3>
                <div class="canvas-container">
                    <canvas id="btreeCanvas" width="500" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateBTreeSearch()">搜索演示</button>
                    <button class="btn" onclick="resetBTree()">重置</button>
                </div>
                <div class="description">
                    <p><strong>BTree是MySQL默认的索引算法</strong></p>
                    <p>• 支持范围查询：=, >, >=, <, <=, BETWEEN</p>
                    <p>• 支持LIKE查询（不以通配符开头）</p>
                    <p>• 数据有序存储，支持排序</p>
                </div>
            </div>

            <div class="algorithm-card">
                <h3>🔗 Hash 算法</h3>
                <div class="canvas-container">
                    <canvas id="hashCanvas" width="500" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn hash" onclick="animateHashSearch()">搜索演示</button>
                    <button class="btn hash" onclick="resetHash()">重置</button>
                </div>
                <div class="description">
                    <p><strong>Hash索引检索效率极高</strong></p>
                    <p>• 只支持等值查询：=, <=></p>
                    <p>• 一次定位，无需多次IO访问</p>
                    <p>• 检索速度远超BTree</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>SQL查询示例对比</h2>
            <div class="interactive-demo">
                <h3>🔍 交互式查询演示</h3>
                <div class="demo-input">
                    <input type="text" id="searchInput" placeholder="输入搜索值 (例如: jack)" value="jack">
                    <button class="btn" onclick="demonstrateQuery()">执行查询</button>
                </div>

                <div class="code-example">
                    <h4>✅ BTree 支持的查询（推荐）：</h4>
                    <pre id="btreeQueries">
-- 等值查询
SELECT * FROM user WHERE name = 'jack';

-- 范围查询
SELECT * FROM user WHERE age BETWEEN 18 AND 30;

-- 前缀匹配
SELECT * FROM user WHERE name LIKE 'jack%';

-- 排序查询
SELECT * FROM user WHERE age > 25 ORDER BY age;
                    </pre>
                </div>

                <div class="code-example">
                    <h4>⚡ Hash 支持的查询（超快速）：</h4>
                    <pre id="hashQueries">
-- 等值查询（极快）
SELECT * FROM user WHERE id = 12345;

-- 相等比较
SELECT * FROM user WHERE status <=> 'active';

-- 注意：Hash索引不支持范围查询和排序
                    </pre>
                </div>

                <div class="code-example" style="background: #e74c3c;">
                    <h4>❌ 不会使用索引的查询：</h4>
                    <pre id="badQueries">
-- 通配符开头的LIKE查询
SELECT * FROM user WHERE name LIKE '%jack';

-- 函数操作
SELECT * FROM user WHERE UPPER(name) = 'JACK';

-- 不等于操作（Hash索引）
SELECT * FROM user WHERE id != 12345;
                    </pre>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>性能对比游戏</h2>
            <div class="interactive-demo">
                <h3>🎮 算法竞速挑战</h3>
                <p>点击按钮，看看两种算法在不同查询场景下的表现！</p>
                <div class="controls">
                    <button class="btn" onclick="raceAlgorithms('equal')">等值查询竞赛</button>
                    <button class="btn" onclick="raceAlgorithms('range')">范围查询竞赛</button>
                    <button class="btn" onclick="raceAlgorithms('like')">模糊查询竞赛</button>
                </div>
                <div class="canvas-container">
                    <canvas id="raceCanvas" width="800" height="200"></canvas>
                </div>
                <div id="raceResult" style="text-align: center; margin-top: 20px; font-size: 1.2rem; font-weight: bold;"></div>
            </div>
        </div>
    </div>

    <script>
        // BTree 动画相关变量
        let btreeCanvas, btreeCtx;
        let hashCanvas, hashCtx;
        let raceCanvas, raceCtx;
        let animationId;

        // 初始化画布
        function initCanvases() {
            btreeCanvas = document.getElementById('btreeCanvas');
            btreeCtx = btreeCanvas.getContext('2d');
            hashCanvas = document.getElementById('hashCanvas');
            hashCtx = hashCanvas.getContext('2d');
            raceCanvas = document.getElementById('raceCanvas');
            raceCtx = raceCanvas.getContext('2d');

            // 设置画布实际尺寸
            btreeCanvas.width = 500;
            btreeCanvas.height = 400;
            hashCanvas.width = 500;
            hashCanvas.height = 400;
            raceCanvas.width = 800;
            raceCanvas.height = 200;

            drawBTree();
            drawHashTable();
        }

        // 绘制BTree结构
        function drawBTree() {
            btreeCtx.clearRect(0, 0, btreeCanvas.width, btreeCanvas.height);

            // 绘制根节点
            drawBTreeNode(btreeCtx, 250, 50, ['15', '30'], '#3498db');

            // 绘制第二层节点
            drawBTreeNode(btreeCtx, 150, 150, ['5', '10'], '#2ecc71');
            drawBTreeNode(btreeCtx, 250, 150, ['20', '25'], '#2ecc71');
            drawBTreeNode(btreeCtx, 350, 150, ['35', '40'], '#2ecc71');

            // 绘制第三层节点（叶子节点）
            drawBTreeNode(btreeCtx, 80, 250, ['1', '3'], '#f39c12');
            drawBTreeNode(btreeCtx, 150, 250, ['7', '8'], '#f39c12');
            drawBTreeNode(btreeCtx, 220, 250, ['12', '13'], '#f39c12');
            drawBTreeNode(btreeCtx, 280, 250, ['22', '23'], '#f39c12');
            drawBTreeNode(btreeCtx, 350, 250, ['32', '33'], '#f39c12');
            drawBTreeNode(btreeCtx, 420, 250, ['37', '38'], '#f39c12');

            // 绘制连接线
            drawBTreeConnections(btreeCtx);

            // 添加标题
            btreeCtx.fillStyle = '#2c3e50';
            btreeCtx.font = 'bold 16px Arial';
            btreeCtx.textAlign = 'center';
            btreeCtx.fillText('BTree 索引结构', 250, 30);
            btreeCtx.font = '12px Arial';
            btreeCtx.fillText('多层树状结构，支持范围查询', 250, 380);
        }

        // 绘制BTree节点
        function drawBTreeNode(ctx, x, y, values, color) {
            const width = 60;
            const height = 30;

            // 绘制节点背景
            ctx.fillStyle = color;
            ctx.fillRect(x - width/2, y - height/2, width, height);

            // 绘制边框
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(values.join(','), x, y + 4);
        }

        // 绘制BTree连接线
        function drawBTreeConnections(ctx) {
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 2;

            // 根节点到第二层
            drawLine(ctx, 220, 65, 150, 135);
            drawLine(ctx, 250, 65, 250, 135);
            drawLine(ctx, 280, 65, 350, 135);

            // 第二层到第三层
            drawLine(ctx, 130, 165, 80, 235);
            drawLine(ctx, 170, 165, 150, 235);
            drawLine(ctx, 230, 165, 220, 235);
            drawLine(ctx, 270, 165, 280, 235);
            drawLine(ctx, 330, 165, 350, 235);
            drawLine(ctx, 370, 165, 420, 235);
        }

        // 绘制线条
        function drawLine(ctx, x1, y1, x2, y2) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // 绘制Hash表
        function drawHashTable() {
            hashCtx.clearRect(0, 0, hashCanvas.width, hashCanvas.height);

            // 绘制Hash表结构
            const buckets = [
                { key: 'jack', value: 'User1', hash: 0 },
                { key: 'mary', value: 'User2', hash: 1 },
                { key: 'tom', value: 'User3', hash: 2 },
                { key: 'alice', value: 'User4', hash: 3 },
                { key: 'bob', value: 'User5', hash: 4 }
            ];

            // 绘制Hash函数
            hashCtx.fillStyle = '#e74c3c';
            hashCtx.fillRect(50, 50, 120, 40);
            hashCtx.fillStyle = 'white';
            hashCtx.font = 'bold 14px Arial';
            hashCtx.textAlign = 'center';
            hashCtx.fillText('Hash函数', 110, 75);

            // 绘制箭头
            drawArrow(hashCtx, 170, 70, 220, 70);

            // 绘制Hash表桶
            buckets.forEach((bucket, index) => {
                const y = 120 + index * 50;

                // 绘制索引
                hashCtx.fillStyle = '#95a5a6';
                hashCtx.fillRect(250, y, 40, 35);
                hashCtx.fillStyle = 'white';
                hashCtx.font = 'bold 12px Arial';
                hashCtx.textAlign = 'center';
                hashCtx.fillText(index.toString(), 270, y + 22);

                // 绘制键值对
                hashCtx.fillStyle = '#3498db';
                hashCtx.fillRect(300, y, 150, 35);
                hashCtx.fillStyle = 'white';
                hashCtx.font = '11px Arial';
                hashCtx.fillText(`${bucket.key} → ${bucket.value}`, 375, y + 22);
            });

            // 添加标题
            hashCtx.fillStyle = '#2c3e50';
            hashCtx.font = 'bold 16px Arial';
            hashCtx.textAlign = 'center';
            hashCtx.fillText('Hash 索引结构', 250, 30);
            hashCtx.font = '12px Arial';
            hashCtx.fillText('直接定位，等值查询极快', 250, 380);
        }

        // 绘制箭头
        function drawArrow(ctx, x1, y1, x2, y2) {
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // BTree搜索动画
        function animateBTreeSearch() {
            drawBTree();
            let step = 0;
            const searchPath = [
                { x: 250, y: 50, text: '1. 从根节点开始' },
                { x: 150, y: 150, text: '2. 比较并选择路径' },
                { x: 150, y: 250, text: '3. 找到目标叶子节点' }
            ];

            function animate() {
                if (step < searchPath.length) {
                    const current = searchPath[step];

                    // 高亮当前节点
                    btreeCtx.fillStyle = 'rgba(231, 76, 60, 0.7)';
                    btreeCtx.fillRect(current.x - 35, current.y - 20, 70, 40);

                    // 显示步骤文字
                    btreeCtx.fillStyle = '#e74c3c';
                    btreeCtx.font = 'bold 14px Arial';
                    btreeCtx.textAlign = 'center';
                    btreeCtx.fillText(current.text, current.x, current.y + 60);

                    step++;
                    setTimeout(animate, 1500);
                } else {
                    // 显示完成信息
                    btreeCtx.fillStyle = '#27ae60';
                    btreeCtx.font = 'bold 16px Arial';
                    btreeCtx.textAlign = 'center';
                    btreeCtx.fillText('✅ 找到数据！需要3次IO操作', 250, 350);
                }
            }
            animate();
        }

        // Hash搜索动画
        function animateHashSearch() {
            drawHashTable();

            setTimeout(() => {
                // 高亮Hash函数
                hashCtx.fillStyle = 'rgba(231, 76, 60, 0.7)';
                hashCtx.fillRect(45, 45, 130, 50);

                hashCtx.fillStyle = '#e74c3c';
                hashCtx.font = 'bold 14px Arial';
                hashCtx.textAlign = 'center';
                hashCtx.fillText('1. 计算Hash值', 110, 110);

                setTimeout(() => {
                    // 高亮目标桶
                    hashCtx.fillStyle = 'rgba(231, 76, 60, 0.7)';
                    hashCtx.fillRect(245, 115, 210, 45);

                    hashCtx.fillStyle = '#e74c3c';
                    hashCtx.font = 'bold 14px Arial';
                    hashCtx.fillText('2. 直接定位到数据', 350, 180);

                    setTimeout(() => {
                        hashCtx.fillStyle = '#27ae60';
                        hashCtx.font = 'bold 16px Arial';
                        hashCtx.textAlign = 'center';
                        hashCtx.fillText('⚡ 找到数据！仅需1次IO操作', 250, 350);
                    }, 1000);
                }, 1500);
            }, 500);
        }

        // 重置BTree
        function resetBTree() {
            drawBTree();
        }

        // 重置Hash
        function resetHash() {
            drawHashTable();
        }

        // 演示查询
        function demonstrateQuery() {
            const searchValue = document.getElementById('searchInput').value;
            const btreeQueries = document.getElementById('btreeQueries');
            const hashQueries = document.getElementById('hashQueries');

            // 更新查询示例
            btreeQueries.innerHTML = `
-- 等值查询
SELECT * FROM user WHERE name = '${searchValue}';

-- 范围查询
SELECT * FROM user WHERE age BETWEEN 18 AND 30;

-- 前缀匹配
SELECT * FROM user WHERE name LIKE '${searchValue}%';

-- 排序查询
SELECT * FROM user WHERE age > 25 ORDER BY age;
            `;

            hashQueries.innerHTML = `
-- 等值查询（极快）
SELECT * FROM user WHERE name = '${searchValue}';

-- 相等比较
SELECT * FROM user WHERE status <=> 'active';

-- 注意：Hash索引不支持范围查询和排序
            `;

            // 添加高亮效果
            btreeQueries.style.background = '#2ecc71';
            hashQueries.style.background = '#3498db';

            setTimeout(() => {
                btreeQueries.style.background = '#2c3e50';
                hashQueries.style.background = '#2c3e50';
            }, 2000);
        }

        // 算法竞赛
        function raceAlgorithms(queryType) {
            raceCtx.clearRect(0, 0, raceCanvas.width, raceCanvas.height);

            // 绘制赛道
            raceCtx.fillStyle = '#ecf0f1';
            raceCtx.fillRect(50, 50, 700, 40);
            raceCtx.fillRect(50, 110, 700, 40);

            // 绘制标签
            raceCtx.fillStyle = '#2c3e50';
            raceCtx.font = 'bold 16px Arial';
            raceCtx.textAlign = 'left';
            raceCtx.fillText('🌳 BTree', 10, 75);
            raceCtx.fillText('🔗 Hash', 10, 135);

            // 绘制起跑线
            raceCtx.strokeStyle = '#34495e';
            raceCtx.lineWidth = 3;
            raceCtx.beginPath();
            raceCtx.moveTo(50, 40);
            raceCtx.lineTo(50, 160);
            raceCtx.stroke();

            // 绘制终点线
            raceCtx.beginPath();
            raceCtx.moveTo(750, 40);
            raceCtx.lineTo(750, 160);
            raceCtx.stroke();

            let btreePos = 50;
            let hashPos = 50;
            let btreeSpeed, hashSpeed;

            // 根据查询类型设置速度
            switch(queryType) {
                case 'equal':
                    btreeSpeed = 3;
                    hashSpeed = 8;
                    break;
                case 'range':
                    btreeSpeed = 5;
                    hashSpeed = 0; // Hash不支持范围查询
                    break;
                case 'like':
                    btreeSpeed = 4;
                    hashSpeed = 0; // Hash不支持模糊查询
                    break;
            }

            function animateRace() {
                raceCtx.clearRect(50, 50, 700, 100);

                // 重绘赛道
                raceCtx.fillStyle = '#ecf0f1';
                raceCtx.fillRect(50, 50, 700, 40);
                raceCtx.fillRect(50, 110, 700, 40);

                // 绘制选手
                raceCtx.fillStyle = '#3498db';
                raceCtx.fillRect(btreePos, 55, 30, 30);
                raceCtx.fillStyle = 'white';
                raceCtx.font = '20px Arial';
                raceCtx.textAlign = 'center';
                raceCtx.fillText('🌳', btreePos + 15, 75);

                raceCtx.fillStyle = '#e74c3c';
                raceCtx.fillRect(hashPos, 115, 30, 30);
                raceCtx.fillStyle = 'white';
                raceCtx.fillText('🔗', hashPos + 15, 135);

                // 更新位置
                if (btreePos < 720) btreePos += btreeSpeed;
                if (hashPos < 720) hashPos += hashSpeed;

                // 检查胜负
                if (btreePos >= 720 || hashPos >= 720) {
                    let result = '';
                    if (queryType === 'equal') {
                        result = hashPos >= 720 ? '🏆 Hash算法获胜！等值查询速度极快' : '🏆 BTree算法获胜！';
                    } else if (queryType === 'range') {
                        result = '🏆 BTree算法获胜！Hash不支持范围查询';
                    } else if (queryType === 'like') {
                        result = '🏆 BTree算法获胜！Hash不支持模糊查询';
                    }
                    document.getElementById('raceResult').innerHTML = result;
                    return;
                }

                requestAnimationFrame(animateRace);
            }

            animateRace();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initCanvases();

            // 添加一些交互提示
            setTimeout(() => {
                const cards = document.querySelectorAll('.algorithm-card');
                cards.forEach(card => {
                    card.style.animation = 'pulse 2s infinite';
                });
            }, 2000);
        });

        // 添加键盘事件
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && event.target.id === 'searchInput') {
                demonstrateQuery();
            }
        });
    </script>
</body>
</html>
