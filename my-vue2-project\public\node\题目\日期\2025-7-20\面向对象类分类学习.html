<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象类分类 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .class-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .class-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .class-card:nth-child(2) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .class-card:nth-child(3) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .class-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .class-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .class-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .game-title {
            font-size: 1.5rem;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .drag-drop-area {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .drop-zone {
            width: 200px;
            height: 150px;
            border: 3px dashed #ccc;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
            position: relative;
        }

        .drop-zone.active {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .drop-zone.correct {
            border-color: #43e97b;
            background: rgba(67, 233, 123, 0.2);
        }

        .draggable-items {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        .draggable-item {
            background: white;
            padding: 15px 20px;
            border-radius: 25px;
            cursor: grab;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            user-select: none;
        }

        .draggable-item:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .draggable-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .question-area {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #4facfe;
        }

        .question {
            font-size: 1.2rem;
            color: #333;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .option {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .option.selected {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.1);
        }

        .option.correct {
            border-color: #43e97b;
            background: rgba(67, 233, 123, 0.2);
        }

        .option.wrong {
            border-color: #f5576c;
            background: rgba(245, 87, 108, 0.2);
        }

        .feedback {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .feedback.show {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        .feedback.correct {
            background: rgba(67, 233, 123, 0.2);
            border: 1px solid #43e97b;
            color: #2d5a3d;
        }

        .feedback.wrong {
            background: rgba(245, 87, 108, 0.2);
            border: 1px solid #f5576c;
            color: #8b2635;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.5s ease;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">面向对象编程 - 类的分类</h1>
            <p class="subtitle">通过有趣的动画和互动游戏学习实体类、边界类和控制类</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识讲解</h2>
            
            <div class="class-types">
                <div class="class-card" onclick="showClassAnimation('entity')">
                    <h3>🏢 实体类 (Entity Class)</h3>
                    <p>映射需求中的每个实体，保存需要存储在永久存储体中的信息。通常是名词，如"用户"、"订单"、"商品"等。</p>
                </div>
                
                <div class="class-card" onclick="showClassAnimation('boundary')">
                    <h3>🌐 边界类 (Boundary Class)</h3>
                    <p>封装系统内外流动的信息，处理用户界面和外部系统交互，如"登录界面"、"通信协议"等。</p>
                </div>
                
                <div class="class-card" onclick="showClassAnimation('control')">
                    <h3>⚙️ 控制类 (Control Class)</h3>
                    <p>控制用例工作流程，协调其他对象，通常是动宾结构，如"身份验证"、"查询余额"等。</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="learning-section">
            <div class="game-area">
                <h3 class="game-title">🎮 拖拽分类游戏</h3>
                <p style="text-align: center; margin-bottom: 20px; color: #666;">将下面的概念拖拽到正确的类别中</p>
                
                <div class="drag-drop-area">
                    <div class="drop-zone" data-type="entity">
                        <span>实体类</span>
                    </div>
                    <div class="drop-zone" data-type="boundary">
                        <span>边界类</span>
                    </div>
                    <div class="drop-zone" data-type="control">
                        <span>控制类</span>
                    </div>
                </div>

                <div class="draggable-items">
                    <div class="draggable-item" draggable="true" data-type="boundary">通信协议</div>
                    <div class="draggable-item" draggable="true" data-type="entity">用户</div>
                    <div class="draggable-item" draggable="true" data-type="control">身份验证</div>
                    <div class="draggable-item" draggable="true" data-type="control">查询余额</div>
                    <div class="draggable-item" draggable="true" data-type="entity">账户</div>
                    <div class="draggable-item" draggable="true" data-type="boundary">登录界面</div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="gameProgress"></div>
                </div>
            </div>
        </div>

        <div class="learning-section">
            <div class="question-area">
                <h3 style="color: #4facfe; margin-bottom: 20px;">📝 原题练习</h3>
                <div class="question">
                    类封装了信息和行为，是面向对象的重要组成部分。在系统设计过程中，类可以分为实体类、边界类和控制类。下面用例描述中属于边界类的是（），属于实体类的是（）。
                </div>
                
                <div style="margin: 20px 0;">
                    <strong>选项：</strong>
                </div>
                <div class="options" id="questionOptions">
                    <div class="option" data-answer="A">A. 身份验证</div>
                    <div class="option" data-answer="B">B. 用户</div>
                    <div class="option" data-answer="C">C. 通信协议</div>
                    <div class="option" data-answer="D">D. 查询余额</div>
                </div>

                <div class="feedback" id="feedback"></div>
            </div>
        </div>
    </div>

    <script>
        // Canvas动画系统
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationFrame;
        let particles = [];
        
        // 粒子类
        class Particle {
            constructor(x, y, color, text) {
                this.x = x;
                this.y = y;
                this.color = color;
                this.text = text;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.life = 1;
                this.decay = 0.02;
            }
            
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vy += 0.1; // 重力
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.text, this.x, this.y);
                ctx.restore();
            }
        }
        
        // 显示类动画
        function showClassAnimation(type) {
            particles = [];
            
            const colors = {
                entity: '#f5576c',
                boundary: '#4facfe',
                control: '#43e97b'
            };
            
            const texts = {
                entity: ['用户', '订单', '商品', '账户', '客户'],
                boundary: ['界面', '协议', '接口', '表单', '对话框'],
                control: ['验证', '查询', '计算', '处理', '管理']
            };
            
            // 创建粒子
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const text = texts[type][Math.floor(Math.random() * texts[type].length)];
                particles.push(new Particle(x, y, colors[type], text));
            }
            
            animate();
        }
        
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            if (particles.length > 0) {
                animationFrame = requestAnimationFrame(animate);
            }
        }
        
        // 拖拽游戏
        let draggedElement = null;
        let correctAnswers = 0;
        const totalItems = 6;
        
        // 拖拽事件
        document.querySelectorAll('.draggable-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                draggedElement = e.target;
                e.target.classList.add('dragging');
            });
            
            item.addEventListener('dragend', (e) => {
                e.target.classList.remove('dragging');
            });
        });
        
        // 放置区域事件
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('active');
            });
            
            zone.addEventListener('dragleave', () => {
                zone.classList.remove('active');
            });
            
            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('active');
                
                if (draggedElement) {
                    const itemType = draggedElement.dataset.type;
                    const zoneType = zone.dataset.type;
                    
                    if (itemType === zoneType) {
                        zone.appendChild(draggedElement);
                        zone.classList.add('correct');
                        draggedElement.style.background = '#43e97b';
                        draggedElement.style.color = 'white';
                        correctAnswers++;
                        
                        // 更新进度
                        const progress = (correctAnswers / totalItems) * 100;
                        document.getElementById('gameProgress').style.width = progress + '%';
                        
                        // 成功动画
                        zone.classList.add('bounce');
                        setTimeout(() => zone.classList.remove('bounce'), 1000);
                        
                        if (correctAnswers === totalItems) {
                            setTimeout(() => {
                                alert('🎉 恭喜！你已经掌握了类的分类概念！');
                            }, 500);
                        }
                    } else {
                        // 错误反馈
                        draggedElement.style.background = '#f5576c';
                        draggedElement.style.color = 'white';
                        setTimeout(() => {
                            draggedElement.style.background = 'white';
                            draggedElement.style.color = 'black';
                        }, 1000);
                    }
                }
            });
        });
        
        // 题目答题系统
        let selectedAnswers = [];
        
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', () => {
                option.classList.toggle('selected');
                const answer = option.dataset.answer;
                
                if (selectedAnswers.includes(answer)) {
                    selectedAnswers = selectedAnswers.filter(a => a !== answer);
                } else {
                    selectedAnswers.push(answer);
                }
                
                if (selectedAnswers.length === 2) {
                    checkAnswers();
                }
            });
        });
        
        function checkAnswers() {
            const feedback = document.getElementById('feedback');
            const correctBoundary = 'C'; // 通信协议
            const correctEntity = 'B';   // 用户
            
            const hasBoundary = selectedAnswers.includes(correctBoundary);
            const hasEntity = selectedAnswers.includes(correctEntity);
            
            document.querySelectorAll('.option').forEach(option => {
                const answer = option.dataset.answer;
                if (answer === correctBoundary || answer === correctEntity) {
                    option.classList.add('correct');
                } else if (selectedAnswers.includes(answer)) {
                    option.classList.add('wrong');
                }
            });
            
            if (hasBoundary && hasEntity && selectedAnswers.length === 2) {
                feedback.className = 'feedback correct show';
                feedback.innerHTML = `
                    <h4>🎉 回答正确！</h4>
                    <p><strong>边界类：C. 通信协议</strong> - 通信协议用于封装系统与外部环境的交互，属于边界类。</p>
                    <p><strong>实体类：B. 用户</strong> - 用户是系统中的核心实体，需要持久化存储，属于实体类。</p>
                    <p><strong>解析：</strong>身份验证和查询余额都是控制类，因为它们描述的是系统的行为和流程控制。</p>
                `;
            } else {
                feedback.className = 'feedback wrong show';
                feedback.innerHTML = `
                    <h4>❌ 答案有误，再试试看！</h4>
                    <p><strong>提示：</strong></p>
                    <ul>
                        <li>边界类：处理系统与外部的交互</li>
                        <li>实体类：系统中的核心对象，通常是名词</li>
                        <li>控制类：描述系统行为，通常是动词</li>
                    </ul>
                `;
            }
        }
        
        // 初始化动画
        showClassAnimation('entity');
        
        // 页面加载完成后的欢迎动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                showClassAnimation('boundary');
            }, 2000);
            
            setTimeout(() => {
                showClassAnimation('control');
            }, 4000);
        });
    </script>
</body>
</html>
