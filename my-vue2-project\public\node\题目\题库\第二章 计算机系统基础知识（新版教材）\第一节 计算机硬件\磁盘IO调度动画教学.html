<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磁盘I/O调度动画教学 - 零基础学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .simulation-container {
            grid-column: 1 / -1;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .disk-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .disk-wrapper {
            text-align: center;
        }

        .disk-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .disk {
            width: 300px;
            height: 300px;
            border: 6px solid #4a5568;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle, #f7fafc 0%, #e2e8f0 100%);
            margin: 0 auto;
        }

        .sector {
            position: absolute;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .sector:hover {
            transform: scale(1.15);
            z-index: 10;
        }

        .head {
            position: absolute;
            width: 6px;
            height: 120px;
            background: linear-gradient(to bottom, #e53e3e, #c53030);
            top: 50%;
            left: 50%;
            transform-origin: bottom center;
            transform: translate(-50%, -100%) rotate(0deg);
            z-index: 20;
            border-radius: 3px;
            box-shadow: 0 0 15px rgba(229, 62, 62, 0.6);
            transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .head::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -4px;
            width: 14px;
            height: 14px;
            background: #e53e3e;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(229, 62, 62, 0.8);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #718096, #4a5568);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(113, 128, 150, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e2e8f0;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            width: 0%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 6px;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }

        th, td {
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 500;
        }

        th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        tr:hover {
            background: #f7fafc;
        }

        .explanation-box {
            background: linear-gradient(135deg, #e6fffa, #f0fff4);
            border: 2px solid #48bb78;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .explanation-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 20px;
            background: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 1.2rem;
        }

        .formula-box {
            background: linear-gradient(135deg, #fff5f5, #fed7d7);
            border: 2px solid #e53e3e;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            text-align: center;
            font-weight: 600;
        }

        .highlight {
            background: linear-gradient(135deg, #fef5e7, #fed7aa) !important;
            border-color: #f6ad55 !important;
            animation: pulse 1.5s infinite;
            transform: scale(1.05);
        }

        .processing {
            background: linear-gradient(135deg, #fef5e7, #fed7aa) !important;
            animation: processing 1s infinite;
        }

        .completed {
            background: linear-gradient(135deg, #f0fff4, #c6f6d5) !important;
            border-color: #48bb78 !important;
        }

        .waiting {
            background: linear-gradient(135deg, #e6fffa, #b2f5ea) !important;
            animation: waiting 2s infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1.05); }
            50% { transform: scale(1.1); }
        }

        @keyframes processing {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes waiting {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .disk-area {
                flex-direction: column;
            }
            
            .disk {
                width: 250px;
                height: 250px;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 磁盘I/O调度动画教学</h1>
            <p>零基础学习磁盘存储优化原理 - 通过动画理解每一个细节</p>
        </div>

        <div class="main-grid">
            <div class="card">
                <h2>📚 题目详解</h2>
                <p><strong>🎯 核心问题：</strong>磁盘数据排列方式如何影响I/O服务时间？</p>
                
                <div style="margin: 20px 0;">
                    <p><strong>📊 给定条件：</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>每磁道 <span style="color: #e53e3e; font-weight: bold;">10个物理块</span></li>
                        <li>每块存放 <span style="color: #e53e3e; font-weight: bold;">1个逻辑记录</span></li>
                        <li>旋转速度：<span style="color: #e53e3e; font-weight: bold;">30ms/周</span></li>
                        <li>处理时间：<span style="color: #e53e3e; font-weight: bold;">6ms/记录</span></li>
                        <li>使用 <span style="color: #e53e3e; font-weight: bold;">单缓冲区</span></li>
                    </ul>
                </div>

                <div class="table-container">
                    <table>
                        <tr>
                            <th>物理块</th>
                            <th>1</th><th>2</th><th>3</th><th>4</th><th>5</th>
                            <th>6</th><th>7</th><th>8</th><th>9</th><th>10</th>
                        </tr>
                        <tr style="background: #fff5f5;">
                            <td><strong>原始排列</strong></td>
                            <td>R1</td><td>R2</td><td>R3</td><td>R4</td><td>R5</td>
                            <td>R6</td><td>R7</td><td>R8</td><td>R9</td><td>R10</td>
                        </tr>
                        <tr style="background: #f0fff4;">
                            <td><strong>优化排列</strong></td>
                            <td>R1</td><td>R8</td><td>R5</td><td>R2</td><td>R9</td>
                            <td>R6</td><td>R3</td><td>R10</td><td>R7</td><td>R4</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card">
                <h2>🧮 关键计算公式</h2>
                
                <div class="formula-box">
                    <strong>读取一个扇区时间 = 30ms ÷ 10 = 3ms</strong>
                </div>
                
                <div class="explanation-box">
                    <h4 style="color: #e53e3e; margin-bottom: 10px;">❌ 原始排列问题：</h4>
                    <p>处理R1后，磁头转到R4位置<br>
                    要读R2需要等待：<strong>24ms + 3ms读取 + 6ms处理 = 33ms</strong><br>
                    总时间：<strong>9×33ms + 9ms = 306ms</strong></p>
                </div>
                
                <div class="explanation-box" style="background: linear-gradient(135deg, #f0fff4, #c6f6d5); border-color: #48bb78;">
                    <h4 style="color: #38a169; margin-bottom: 10px;">✅ 优化排列优势：</h4>
                    <p>处理完一个记录后，磁头刚好到达下一个记录位置<br>
                    每个记录只需：<strong>3ms读取 + 6ms处理 = 9ms</strong><br>
                    总时间：<strong>10×9ms = 90ms</strong></p>
                </div>
            </div>
        </div>
