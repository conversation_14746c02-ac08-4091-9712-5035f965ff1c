<template>
  <div class="reading-history-layout">
    <PageHeader>
      <template #actions>
        <TextButton type="default" icon="el-icon-s-home" @click="goHome">返回首页</TextButton>
      </template>
    </PageHeader>

    <div class="container">
      <div class="list-header fade-in">
        <div class="list-header-left">
          <h2 class="page-title">阅读历史</h2>
          <p class="page-subtitle">{{ readingHistory.length }} 条历史记录</p>
        </div>
        <div class="list-header-right">
          <el-button type="danger" plain @click="confirmClearHistory" :disabled="readingHistory.length === 0">
            清空历史记录
          </el-button>
        </div>
      </div>
      
      <div v-if="readingHistory.length === 0" class="empty-state fade-in" style="animation-delay: 0.2s;">
        <el-empty description="暂无阅读历史" :image-size="120">
          <el-button type="primary" @click="goToNoteList">浏览文件</el-button>
        </el-empty>
      </div>
      
      <div v-else class="history-list fade-in">
        <div v-for="(item, index) in paginatedHistory" 
             :key="item.path + index" 
             class="history-item"
             :style="{ animationDelay: `${index * 0.05}s` }"
             @click="viewFile(item)">
          <div class="history-item-content">
            <div class="history-item-info">
              <h3 class="history-item-title">{{ item.name }}</h3>
              <p class="history-item-path">{{ item.path }}</p>
            </div>
            <div class="history-item-time">
              {{ formatDate(item.lastRead) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container fade-in" v-if="readingHistory.length > pageSize">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="readingHistory.length"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>

    <el-dialog
      title="确认清空历史记录"
      :visible.sync="clearHistoryDialogVisible"
      width="400px"
      center>
      <span>确定要清空所有阅读历史记录吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="clearHistoryDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="clearHistory">确定清空</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ReadingHistory',
  data() {
    return {
      clearHistoryDialogVisible: false,
      currentPage: 1,
      pageSize: 10 // 每页显示10条记录
    }
  },
  computed: {
    ...mapState(['readingHistory']),
    paginatedHistory() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.readingHistory.slice(start, end);
    }
  },
  methods: {
    ...mapActions(['clearReadingHistory']),
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
    },
    goHome() {
      this.$router.push('/')
    },
    goToNoteList() {
      this.$router.push('/notes')
    },
    viewFile(item) {
      // 将路径信息传递给NoteList组件
      this.$router.push({
        path: '/notes',
        query: { filePath: item.path }
      })
    },
    formatDate(dateString) {
      if (!dateString) {
        return '日期未知';
      }
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return '日期无效';
      }
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      
      const diffMinutes = Math.floor(diff / (1000 * 60))
      const diffHours = Math.floor(diff / (1000 * 60 * 60))
      const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (diffMinutes < 1) {
        return '刚刚'
      } else if (diffMinutes < 60) {
        return `${diffMinutes} 分钟前`
      } else if (diffHours < 24) {
        return `${diffHours} 小时前`
      } else if (diffDays < 7) {
        return `${diffDays} 天前`
      } else {
        // For dates more than a week old, show the actual date
        return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
      }
    },
    confirmClearHistory() {
      this.clearHistoryDialogVisible = true
    },
    clearHistory() {
      this.clearReadingHistory()
      this.clearHistoryDialogVisible = false
      // 清空历史后，重置回第一页
      this.currentPage = 1;
    }
  }
}
</script>

<style scoped>
.reading-history-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-color);
}

.container {
  flex-grow: 1;
  padding: 20px 60px 60px;
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.page-title {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-light);
  margin: 0;
  font-weight: 400;
}

.history-list {
  display: flex;
  flex-direction: column;
}

.history-item {
  margin-bottom: 12px;
  border-radius: 12px;
  background-color: var(--card-bg);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.history-item:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border-color: var(--primary-color);
}

.history-item-content {
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-item-info {
  flex-grow: 1;
}

.history-item-title {
  font-size: 17px;
  font-weight: 500;
  margin: 0 0 4px;
  color: var(--text-color);
}

.history-item-path {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
}

.history-item-time {
  font-size: 14px;
  color: var(--text-light);
  text-align: right;
  white-space: nowrap;
  margin-left: 20px;
}

.empty-state {
  margin-top: 60px;
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 分页样式 */
.pagination-container {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
  .container {
    padding: 20px 30px 40px;
  }

  .history-item-content {
    padding: 15px 20px;
    flex-direction: column;
    align-items: flex-start;
  }

  .history-item-time {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style> 