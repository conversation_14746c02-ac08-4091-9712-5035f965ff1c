<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式进程同步(PV操作)教程</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            overflow-x: auto;
        }

        #container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            padding: 60px;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 300;
            margin: 0 0 60px 0;
            letter-spacing: -0.5px;
        }

        h2 {
            color: #34495e;
            font-size: 1.5rem;
            font-weight: 400;
            margin: 0 0 30px 0;
            letter-spacing: -0.3px;
        }

        /* 概念分解区域 */
        #concept-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .concept-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .concept-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.12);
        }

        .concept-card h3 {
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .concept-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            font-weight: bold;
        }

        .concept-card p {
            color: #5a6c7d;
            line-height: 1.6;
            margin: 0;
            font-size: 0.95rem;
        }

        #main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        #precedence-graph {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        #precedence-graph img {
            width: 100%;
            max-width: 350px;
            display: block;
            margin: 20px auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        #precedence-graph ul {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        #precedence-graph li {
            padding: 8px 0;
            color: #5a6c7d;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        #animation-area {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        canvas {
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            width: 100%;
            background: #fafbfc;
            transition: all 0.3s ease;
        }

        canvas:hover {
            border-color: #667eea;
        }

        #controls, #explanation, #quiz, #semaphore-status {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        #controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        #controls button {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 500;
            border: none;
            border-radius: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            min-width: 120px;
        }

        #controls button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        #controls button:active:not(:disabled) {
            transform: translateY(0);
        }

        #controls button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        #explanation {
            text-align: center;
        }

        #explanation p {
            font-size: 18px;
            line-height: 1.7;
            color: #2c3e50;
            margin: 0;
            font-weight: 400;
        }

        #quiz-intro {
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .quiz-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .quiz-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quiz-item label {
            font-weight: 600;
            font-size: 16px;
            color: #2c3e50;
        }

        .quiz-item input {
            padding: 12px 16px;
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .quiz-item input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .quiz-item input.correct {
            background: linear-gradient(135deg, #00b894, #00cec9);
            border-color: #00b894;
            color: white;
            animation: correctPulse 0.6s ease;
        }

        .quiz-item input.incorrect {
            background: linear-gradient(135deg, #e17055, #fd79a8);
            border-color: #e17055;
            color: white;
            animation: shake 0.6s ease;
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        #check-answers {
            margin-top: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            border: none;
            border-radius: 50px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        #check-answers:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
        }

        #semaphore-status {
            text-align: center;
        }

        #semaphore-status ul {
            list-style: none;
            padding: 0;
            display: flex;
            gap: 30px;
            justify-content: center;
            margin: 0;
            flex-wrap: wrap;
        }

        #semaphore-status li {
            font-size: 18px;
            font-weight: 600;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 15px 20px;
            border-radius: 16px;
            min-width: 100px;
            text-align: center;
            border: 2px solid #e8ecf0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #2c3e50;
        }

        .highlight-sem {
            transform: scale(1.15) !important;
            background: linear-gradient(135deg, #fdcb6e, #e17055) !important;
            border-color: #fdcb6e !important;
            color: white !important;
            box-shadow: 0 8px 25px rgba(253, 203, 110, 0.4) !important;
            animation: semaphoreGlow 0.6s ease;
        }

        @keyframes semaphoreGlow {
            0%, 100% { box-shadow: 0 8px 25px rgba(253, 203, 110, 0.4); }
            50% { box-shadow: 0 12px 35px rgba(253, 203, 110, 0.6); }
        }

        .process-title {
            text-align: center;
            font-weight: bold;
            margin-top: 5px;
        }

        /* 打字机效果 */
        .typing {
            overflow: hidden;
            border-right: 2px solid #667eea;
            white-space: nowrap;
            animation: typing 1s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #667eea; }
        }

        /* 概念卡片悬停动画 */
        .concept-card {
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }

        .concept-card:hover::before {
            left: 100%;
        }

        /* 按钮波纹效果 */
        #controls button {
            position: relative;
            overflow: hidden;
        }

        #controls button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: width 0.6s, height 0.6s;
            transform: translate(-50%, -50%);
        }

        #controls button:active::before {
            width: 300px;
            height: 300px;
        }

        /* 信号量值变化动画 */
        #semaphore-status span {
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            #main-content {
                grid-template-columns: 1fr;
            }

            #container {
                padding: 40px 30px;
            }

            #concept-breakdown {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 20px 10px;
            }

            #container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            #controls {
                flex-direction: column;
                align-items: center;
            }

            #controls button {
                width: 100%;
                max-width: 200px;
            }

            #concept-breakdown {
                grid-template-columns: 1fr;
            }

            .quiz-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            #container {
                padding: 20px 15px;
            }

            h1 {
                font-size: 1.8rem;
            }

            .concept-card {
                padding: 20px;
            }

            canvas {
                height: 300px;
            }
        }
    </style>
</head>
<body>

<div id="container">
    <h1>交互式进程同步(PV操作)教程</h1>

    <!-- 概念分解区域 -->
    <div id="concept-breakdown">
        <div class="concept-card" data-concept="process">
            <h3>
                <span class="concept-icon" style="background: #667eea;">P</span>
                进程 (Process)
            </h3>
            <p>进程是程序的一次执行实例。每个进程都有自己的执行状态，可以是就绪、运行、阻塞或完成状态。在我们的例子中，P1、P2、P3、P4 就是四个不同的进程。</p>
        </div>

        <div class="concept-card" data-concept="semaphore">
            <h3>
                <span class="concept-icon" style="background: #00b894;">S</span>
                信号量 (Semaphore)
            </h3>
            <p>信号量是一个整数变量，用于控制对共享资源的访问。它支持两个原子操作：P操作(等待/减1)和V操作(信号/加1)。信号量值表示可用资源的数量。</p>
        </div>

        <div class="concept-card" data-concept="pv-operation">
            <h3>
                <span class="concept-icon" style="background: #fdcb6e;">PV</span>
                PV操作
            </h3>
            <p><strong>P操作</strong>：等待信号量，如果值>0则减1并继续，否则阻塞。<strong>V操作</strong>：释放信号量，将值加1，可能唤醒等待的进程。这两个操作是原子的，不可中断。</p>
        </div>

        <div class="concept-card" data-concept="synchronization">
            <h3>
                <span class="concept-icon" style="background: #e17055;">⚡</span>
                进程同步
            </h3>
            <p>进程同步确保多个进程按照正确的顺序执行，避免竞态条件。通过信号量和PV操作，我们可以实现进程间的协调和通信，保证程序的正确性。</p>
        </div>
    </div>

    <div id="main-content">
        <div id="precedence-graph">
            <h2>1. 任务说明</h2>
            <p>我们有四个进程 P1, P2, P3, P4。它们必须按下图的"前驱图"顺序执行。</p>
            <img src="https://i.imgur.com/Gjxtp5w.png" alt="进程前驱图">
            <ul>
                <li><strong>P1</strong> 必须在 <strong>P2</strong> 和 <strong>P3</strong> 开始前完成。</li>
                <li><strong>P2</strong> 和 <strong>P3</strong> 必须在 <strong>P4</strong> 开始前完成。</li>
                <li><strong>P2</strong> 和 <strong>P3</strong> 可以同时进行。</li>
            </ul>
            <p>我们需要使用 <strong>PV操作</strong> 和 <strong>信号量</strong> 来控制这个过程。下面，让我们通过动画来理解这一切是如何运作的。</p>
        </div>

        <div id="animation-area">
            <h2>2. 动画演示</h2>
            <canvas id="canvas" width="700" height="400"></canvas>
            <div id="semaphore-status">
                 <ul id="semaphore-list">
                    <li>S1: <span id="s1-val">0</span></li>
                    <li>S2: <span id="s2-val">0</span></li>
                    <li>S3: <span id="s3-val">0</span></li>
                    <li>S4: <span id="s4-val">0</span></li>
                </ul>
            </div>
        </div>
    </div>

    <div id="controls">
        <button id="start-btn">开始</button>
        <button id="next-btn" disabled>下一步</button>
        <button id="reset-btn">重置</button>
    </div>

    <div id="explanation">
        <p id="explanation-text">点击"开始"按钮，启动进程 P1。</p>
    </div>

    <div id="quiz">
        <p id="quiz-intro">3. 知识自测：您认为 a, b, c, d, e, f 处应该填什么？</p>
         <p><strong>提示:</strong> 使用 <code>P(Sx)</code> 或 <code>V(Sx)</code>, 多个操作用空格隔开, 例如: <code>V(S1) V(S2)</code>. 我们的动画只用了4个信号量，所以请用S1, S2, S3, S4作答。</p>
        <div class="quiz-container">
            <div class="quiz-item"><label for="a">a:</label><input type="text" id="a" data-answer="V(S1) V(S2)"></div>
            <div class="quiz-item"><label for="b">b:</label><input type="text" id="b" data-answer="P(S1)"></div>
            <div class="quiz-item"><label for="c">c:</label><input type="text" id="c" data-answer="V(S3)"></div>
            <div class="quiz-item"><label for="d">d:</label><input type="text" id="d" data-answer="P(S2)"></div>
            <div class="quiz-item"><label for="e">e:</label><input type="text" id="e" data-answer="V(S4)"></div>
            <div class="quiz-item"><label for="f">f:</label><input type="text" id="f" data-answer="P(S3) P(S4)"></div>
        </div>
        <button id="check-answers">检查答案</button>
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    const startBtn = document.getElementById('start-btn');
    const nextBtn = document.getElementById('next-btn');
    const resetBtn = document.getElementById('reset-btn');
    const explanationText = document.getElementById('explanation-text');

    const s1Val = document.getElementById('s1-val');
    const s2Val = document.getElementById('s2-val');
    const s3Val = document.getElementById('s3-val');
    const s4Val = document.getElementById('s4-val');

    // 增强的状态管理
    const state = {
        processes: [
            { id: 'P1', x: 100, y: 50, status: 'ready', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P2', x: 250, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P3', x: 400, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P4', x: 550, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
        ],
        semaphores: { s1: 0, s2: 0, s3: 0, s4: 0 },
        boxes: {
            a: { x: 100, y: 350, content: '', highlight: false, fadeIn: 0 },
            b: { x: 250, y: 80, content: '', highlight: false, fadeIn: 0 },
            c: { x: 250, y: 350, content: '', highlight: false, fadeIn: 0 },
            d: { x: 400, y: 80, content: '', highlight: false, fadeIn: 0 },
            e: { x: 400, y: 350, content: '', highlight: false, fadeIn: 0 },
            f: { x: 550, y: 80, content: '', highlight: false, fadeIn: 0 },
        },
        currentStep: -1,
        animationFrameId: null,
        particles: [],
        connectionLines: [],
        time: 0,
        isAnimating: false,
        conceptHighlight: null,
    };

    // 粒子系统
    class Particle {
        constructor(x, y, color = '#667eea') {
            this.x = x;
            this.y = y;
            this.vx = (Math.random() - 0.5) * 4;
            this.vy = (Math.random() - 0.5) * 4;
            this.life = 1.0;
            this.decay = 0.02;
            this.color = color;
            this.size = Math.random() * 3 + 1;
        }

        update() {
            this.x += this.vx;
            this.y += this.vy;
            this.life -= this.decay;
            this.vx *= 0.98;
            this.vy *= 0.98;
        }

        draw(ctx) {
            ctx.save();
            ctx.globalAlpha = this.life;
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }
    }

    // 连接线动画
    class ConnectionLine {
        constructor(fromX, fromY, toX, toY, color = '#667eea') {
            this.fromX = fromX;
            this.fromY = fromY;
            this.toX = toX;
            this.toY = toY;
            this.progress = 0;
            this.color = color;
            this.width = 3;
            this.glow = 0;
        }

        update() {
            if (this.progress < 1) {
                this.progress += 0.05;
            }
            this.glow = Math.sin(state.time * 0.1) * 0.5 + 0.5;
        }

        draw(ctx) {
            if (this.progress <= 0) return;

            const currentX = this.fromX + (this.toX - this.fromX) * this.progress;
            const currentY = this.fromY + (this.toY - this.fromY) * this.progress;

            ctx.save();
            ctx.strokeStyle = this.color;
            ctx.lineWidth = this.width;
            ctx.globalAlpha = 0.8;

            // 发光效果
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 10 + this.glow * 5;

            ctx.beginPath();
            ctx.moveTo(this.fromX, this.fromY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();

            // 箭头
            if (this.progress > 0.8) {
                const angle = Math.atan2(this.toY - this.fromY, this.toX - this.fromX);
                const arrowSize = 8;

                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.moveTo(currentX, currentY);
                ctx.lineTo(
                    currentX - arrowSize * Math.cos(angle - Math.PI / 6),
                    currentY - arrowSize * Math.sin(angle - Math.PI / 6)
                );
                ctx.lineTo(
                    currentX - arrowSize * Math.cos(angle + Math.PI / 6),
                    currentY - arrowSize * Math.sin(angle + Math.PI / 6)
                );
                ctx.closePath();
                ctx.fill();
            }

            ctx.restore();
        }
    }

    const timelineHeight = 250;
    const boxWidth = 100;
    const boxHeight = 30;

    const steps = [
        {
            desc: '🚀 进程P1开始执行，这是整个流程的起点。',
            concept: 'process',
            action: () => {
                state.processes[0].status = 'running';
                addParticles(state.processes[0].x, state.processes[0].y, '#00b894');
                highlightConcept('process');
            }
        },
        {
            desc: '✅ P1执行完毕，到达操作a。它需要通知P2和P3可以开始了。',
            concept: 'synchronization',
            action: () => {
                state.processes[0].status = 'finished';
                addParticles(state.processes[0].x, 50 + timelineHeight, '#74b9ff');
                highlightConcept('synchronization');
            }
        },
        {
            desc: '📢 P1执行 V(S1) 和 V(S2)。信号量S1和S2的值增加，唤醒等待它们的进程。',
            concept: 'pv-operation',
            action: () => {
                state.semaphores.s1++;
                state.semaphores.s2++;
                state.boxes.a.content = 'V(S1) V(S2)';
                state.boxes.a.highlight = true;
                state.boxes.a.fadeIn = 1;
                highlightSemaphoreUI('s1-val');
                highlightSemaphoreUI('s2-val');
                addConnectionLine(state.boxes.a.x, state.boxes.a.y, state.processes[1].x, state.processes[1].y, '#00b894');
                addConnectionLine(state.boxes.a.x, state.boxes.a.y, state.processes[2].x, state.processes[2].y, '#00b894');
                addParticles(state.boxes.a.x, state.boxes.a.y, '#00b894');
                highlightConcept('pv-operation');
            }
        },
        {
            desc: '⚡ P2和P3被唤醒，准备执行。首先看P2，它到达操作b。',
            concept: 'process',
            action: () => {
                state.processes[1].status = 'checking';
                state.boxes.a.highlight = false;
                addParticles(state.processes[1].x, state.processes[1].y, '#fdcb6e');
                highlightConcept('process');
            }
        },
        {
            desc: '🔒 P2需要P1完成的信号。执行 P(S1)，消耗信号量S1。因为S1>0，P2可以继续。',
            concept: 'semaphore',
            action: () => {
                state.semaphores.s1--;
                state.boxes.b.content = 'P(S1)';
                state.boxes.b.highlight = true;
                state.boxes.b.fadeIn = 1;
                state.processes[1].status = 'running';
                highlightSemaphoreUI('s1-val');
                addParticles(state.boxes.b.x, state.boxes.b.y, '#667eea');
                highlightConcept('semaphore');
            }
        },
        {
            desc: '⚡ 同时，P3到达操作d。',
            concept: 'process',
            action: () => {
                state.processes[2].status = 'checking';
                state.boxes.b.highlight = false;
                addParticles(state.processes[2].x, state.processes[2].y, '#fdcb6e');
                highlightConcept('process');
            }
        },
        {
            desc: '🔒 P3需要P1完成的信号。执行 P(S2)，消耗信号量S2。因为S2>0，P3也可以继续。',
            concept: 'semaphore',
            action: () => {
                state.semaphores.s2--;
                state.boxes.d.content = 'P(S2)';
                state.boxes.d.highlight = true;
                state.boxes.d.fadeIn = 1;
                state.processes[2].status = 'running';
                highlightSemaphoreUI('s2-val');
                addParticles(state.boxes.d.x, state.boxes.d.y, '#667eea');
                highlightConcept('semaphore');
            }
        },
        {
            desc: '🔄 P2和P3现在并行执行，这展示了多进程的并发特性。',
            concept: 'synchronization',
            action: () => {
                state.boxes.d.highlight = false;
                addParticles(state.processes[1].x, 50 + timelineHeight/2, '#00b894');
                addParticles(state.processes[2].x, 50 + timelineHeight/2, '#00b894');
                highlightConcept('synchronization');
            }
        },
        {
            desc: '✅ P2执行完毕，到达操作c。它需要通知P4。',
            concept: 'process',
            action: () => {
                state.processes[1].status = 'finished';
                addParticles(state.processes[1].x, 50 + timelineHeight, '#74b9ff');
                highlightConcept('process');
            }
        },
        {
            desc: '📢 P2执行 V(S3) 来通知P4。信号量S3的值增加。',
            concept: 'pv-operation',
            action: () => {
                state.semaphores.s3++;
                state.boxes.c.content = 'V(S3)';
                state.boxes.c.highlight = true;
                state.boxes.c.fadeIn = 1;
                highlightSemaphoreUI('s3-val');
                addConnectionLine(state.boxes.c.x, state.boxes.c.y, state.processes[3].x, state.processes[3].y, '#00b894');
                addParticles(state.boxes.c.x, state.boxes.c.y, '#00b894');
                highlightConcept('pv-operation');
            }
        },
        {
            desc: '✅ P3执行完毕，到达操作e。它也需要通知P4。',
            concept: 'process',
            action: () => {
                state.processes[2].status = 'finished';
                state.boxes.c.highlight = false;
                addParticles(state.processes[2].x, 50 + timelineHeight, '#74b9ff');
                highlightConcept('process');
            }
        },
        {
            desc: '📢 P3执行 V(S4) 来通知P4。信号量S4的值增加。',
            concept: 'pv-operation',
            action: () => {
                state.semaphores.s4++;
                state.boxes.e.content = 'V(S4)';
                state.boxes.e.highlight = true;
                state.boxes.e.fadeIn = 1;
                highlightSemaphoreUI('s4-val');
                addConnectionLine(state.boxes.e.x, state.boxes.e.y, state.processes[3].x, state.processes[3].y, '#00b894');
                addParticles(state.boxes.e.x, state.boxes.e.y, '#00b894');
                highlightConcept('pv-operation');
            }
        },
        {
            desc: '⚡ P4被唤醒，到达操作f。它需要等待P2和P3都完成。',
            concept: 'synchronization',
            action: () => {
                state.processes[3].status = 'checking';
                state.boxes.e.highlight = false;
                addParticles(state.processes[3].x, state.processes[3].y, '#fdcb6e');
                highlightConcept('synchronization');
            }
        },
        {
            desc: '🔒 P4执行 P(S3) 和 P(S4)，消耗这两个信号量。现在所有前置条件都满足了！',
            concept: 'semaphore',
            action: () => {
                state.semaphores.s3--;
                state.semaphores.s4--;
                state.boxes.f.content = 'P(S3) P(S4)';
                state.boxes.f.highlight = true;
                state.boxes.f.fadeIn = 1;
                state.processes[3].status = 'running';
                highlightSemaphoreUI('s3-val');
                highlightSemaphoreUI('s4-val');
                addParticles(state.boxes.f.x, state.boxes.f.y, '#667eea');
                highlightConcept('semaphore');
            }
        },
        {
            desc: '🚀 P4开始执行，这是最后一个进程。',
            concept: 'process',
            action: () => {
                state.boxes.f.highlight = false;
                addParticles(state.processes[3].x, 50 + timelineHeight/2, '#00b894');
                highlightConcept('process');
            }
        },
        {
            desc: '🎉 P4执行完毕。所有进程都已按正确的顺序完成！',
            concept: 'synchronization',
            action: () => {
                state.processes[3].status = 'finished';
                addCelebrationParticles();
                highlightConcept('synchronization');
            }
        },
        {
            desc: '✨ 演示结束！您已经学会了进程同步的基本原理。现在可以尝试下方的自测题来巩固知识。',
            concept: null,
            action: () => {
                nextBtn.disabled = true;
                clearConceptHighlight();
            }
        },
    ];

    function draw() {
        // 清除画布并设置背景渐变
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#fafbfc');
        gradient.addColorStop(1, '#f8f9fa');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 更新时间和动画状态
        state.time += 0.1;

        // 绘制网格背景
        drawGrid();

        // 绘制时间线
        drawTimelines();

        // 绘制连接线
        state.connectionLines.forEach(line => {
            line.update();
            line.draw(ctx);
        });

        // 绘制操作框
        drawOperationBoxes();

        // 绘制进程圆圈
        drawProcesses();

        // 绘制粒子效果
        updateAndDrawParticles();

        // 绘制进度指示器
        drawProgressIndicator();
    }

    function drawGrid() {
        ctx.save();
        ctx.strokeStyle = 'rgba(0,0,0,0.03)';
        ctx.lineWidth = 1;

        // 垂直线
        for (let x = 0; x < canvas.width; x += 50) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
        }

        // 水平线
        for (let y = 0; y < canvas.height; y += 50) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
        }

        ctx.restore();
    }

    function drawTimelines() {
        state.processes.forEach((p, index) => {
            const { x, id } = p;

            // 时间线渐变
            const gradient = ctx.createLinearGradient(x, 50, x, 50 + timelineHeight);
            gradient.addColorStop(0, '#e8ecf0');
            gradient.addColorStop(1, '#bdc3c7');

            ctx.fillStyle = gradient;
            ctx.fillRect(x - 3, 50, 6, timelineHeight);

            // 进程标签
            ctx.save();
            ctx.font = 'bold 18px sans-serif';
            ctx.fillStyle = '#2c3e50';
            ctx.textAlign = 'center';
            ctx.fillText(id, x, 35);

            // 添加发光效果
            if (p.status === 'running' || p.status === 'checking') {
                ctx.shadowColor = p.status === 'running' ? '#00b894' : '#fdcb6e';
                ctx.shadowBlur = 15;
                ctx.fillText(id, x, 35);
            }

            ctx.restore();
        });
    }

    function drawOperationBoxes() {
        Object.entries(state.boxes).forEach(([key, box]) => {
            const { x, y, content, highlight, fadeIn } = box;

            ctx.save();

            // 盒子阴影
            ctx.shadowColor = 'rgba(0,0,0,0.1)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetY = 4;

            // 盒子背景
            if (highlight) {
                const gradient = ctx.createLinearGradient(x - boxWidth/2, y - boxHeight/2, x + boxWidth/2, y + boxHeight/2);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
            } else {
                ctx.fillStyle = 'white';
            }

            ctx.fillRect(x - boxWidth / 2, y - boxHeight / 2, boxWidth, boxHeight);

            // 盒子边框
            ctx.strokeStyle = highlight ? '#667eea' : '#e8ecf0';
            ctx.lineWidth = highlight ? 3 : 2;
            ctx.strokeRect(x - boxWidth / 2, y - boxHeight / 2, boxWidth, boxHeight);

            // 内容文字
            if (content) {
                ctx.fillStyle = highlight ? 'white' : '#2c3e50';
                ctx.font = 'bold 12px sans-serif';
                ctx.textAlign = 'center';
                ctx.globalAlpha = Math.min(fadeIn, 1);
                ctx.fillText(content, x, y + 4);
            }

            // 标签
            ctx.fillStyle = '#95a5a6';
            ctx.font = 'bold 14px sans-serif';
            ctx.globalAlpha = 1;
            ctx.fillText(key, x, y - boxHeight/2 - 10);

            ctx.restore();
        });
    }

    function drawProcesses() {
        state.processes.forEach((p, index) => {
            const { x, status, progress, pulsePhase, glowIntensity } = p;
            let y;

            // 计算位置
            if (status === 'running') {
                y = 50 + progress * timelineHeight;
            } else if (status === 'finished') {
                y = 50 + timelineHeight;
            } else {
                y = p.y;
            }

            // 更新动画属性
            p.pulsePhase += 0.1;

            ctx.save();

            // 状态颜色
            let color, shadowColor;
            switch(status) {
                case 'running':
                    color = '#00b894';
                    shadowColor = '#00b894';
                    p.glowIntensity = Math.sin(p.pulsePhase) * 0.3 + 0.7;
                    break;
                case 'blocked':
                    color = '#e17055';
                    shadowColor = '#e17055';
                    p.glowIntensity = 0.3;
                    break;
                case 'finished':
                    color = '#74b9ff';
                    shadowColor = '#74b9ff';
                    p.glowIntensity = 0.5;
                    break;
                case 'checking':
                    color = '#fdcb6e';
                    shadowColor = '#fdcb6e';
                    p.glowIntensity = Math.sin(p.pulsePhase * 2) * 0.4 + 0.6;
                    break;
                default:
                    color = '#667eea';
                    shadowColor = '#667eea';
                    p.glowIntensity = 0.4;
            }

            // 发光效果
            ctx.shadowColor = shadowColor;
            ctx.shadowBlur = 15 * p.glowIntensity;

            // 外圈
            ctx.fillStyle = color;
            ctx.globalAlpha = 0.3;
            ctx.beginPath();
            ctx.arc(x, y, 15 + Math.sin(p.pulsePhase) * 2, 0, 2 * Math.PI);
            ctx.fill();

            // 主圆圈
            ctx.globalAlpha = 1;
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 12, 0, 2 * Math.PI);
            ctx.fill();

            // 内圈高光
            ctx.fillStyle = 'rgba(255,255,255,0.3)';
            ctx.beginPath();
            ctx.arc(x - 3, y - 3, 4, 0, 2 * Math.PI);
            ctx.fill();

            ctx.restore();
        });
    }

    function updateAndDrawParticles() {
        // 更新粒子
        state.particles = state.particles.filter(particle => {
            particle.update();
            particle.draw(ctx);
            return particle.life > 0;
        });
    }

    function drawProgressIndicator() {
        if (state.currentStep >= 0) {
            const progress = (state.currentStep + 1) / steps.length;
            const barWidth = canvas.width - 40;
            const barHeight = 6;
            const x = 20;
            const y = canvas.height - 20;

            ctx.save();

            // 背景条
            ctx.fillStyle = 'rgba(0,0,0,0.1)';
            ctx.fillRect(x, y, barWidth, barHeight);

            // 进度条
            const gradient = ctx.createLinearGradient(x, y, x + barWidth * progress, y);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, barWidth * progress, barHeight);

            // 进度文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(`步骤 ${state.currentStep + 1} / ${steps.length}`, canvas.width / 2, y - 8);

            ctx.restore();
        }
    }

    // 辅助函数
    function addParticles(x, y, color = '#667eea', count = 8) {
        for (let i = 0; i < count; i++) {
            state.particles.push(new Particle(x, y, color));
        }
    }

    function addConnectionLine(fromX, fromY, toX, toY, color = '#667eea') {
        state.connectionLines.push(new ConnectionLine(fromX, fromY, toX, toY, color));
    }

    function addCelebrationParticles() {
        const colors = ['#00b894', '#74b9ff', '#fdcb6e', '#e17055', '#667eea'];
        for (let i = 0; i < 30; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const color = colors[Math.floor(Math.random() * colors.length)];
            state.particles.push(new Particle(x, y, color));
        }
    }

    function highlightConcept(conceptType) {
        // 清除之前的高亮
        clearConceptHighlight();

        // 高亮对应的概念卡片
        const conceptCard = document.querySelector(`[data-concept="${conceptType}"]`);
        if (conceptCard) {
            conceptCard.style.transform = 'translateY(-8px) scale(1.02)';
            conceptCard.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.2)';
            conceptCard.style.borderColor = '#667eea';
            state.conceptHighlight = conceptCard;

            // 3秒后恢复
            setTimeout(() => {
                if (state.conceptHighlight === conceptCard) {
                    clearConceptHighlight();
                }
            }, 3000);
        }
    }

    function clearConceptHighlight() {
        if (state.conceptHighlight) {
            state.conceptHighlight.style.transform = '';
            state.conceptHighlight.style.boxShadow = '';
            state.conceptHighlight.style.borderColor = '';
            state.conceptHighlight = null;
        }
    }

    function updateUI() {
        // 更新信号量显示，添加动画效果
        updateSemaphoreValue('s1-val', state.semaphores.s1);
        updateSemaphoreValue('s2-val', state.semaphores.s2);
        updateSemaphoreValue('s3-val', state.semaphores.s3);
        updateSemaphoreValue('s4-val', state.semaphores.s4);

        if (state.currentStep >= 0 && state.currentStep < steps.length) {
            const step = steps[state.currentStep];
            explanationText.innerHTML = `<strong>步骤 ${state.currentStep + 1}:</strong> ${step.desc}`;

            // 添加打字机效果
            if (!explanationText.classList.contains('typing')) {
                explanationText.classList.add('typing');
                setTimeout(() => explanationText.classList.remove('typing'), 1000);
            }
        }
    }

    function updateSemaphoreValue(elementId, newValue) {
        const element = document.getElementById(elementId);
        const currentValue = parseInt(element.textContent);

        if (currentValue !== newValue) {
            // 添加变化动画
            element.style.transform = 'scale(1.3)';
            element.style.color = newValue > currentValue ? '#00b894' : '#e17055';

            setTimeout(() => {
                element.textContent = newValue;
                element.style.transform = 'scale(1)';
                element.style.color = '';
            }, 200);
        }
    }

    function highlightSemaphoreUI(semId) {
        const el = document.getElementById(semId).parentElement;
        el.classList.add('highlight-sem');
        setTimeout(() => el.classList.remove('highlight-sem'), 800);
    }

    function animate() {
        let allFinished = true;
        let hasRunningProcess = false;

        // 更新进程进度
        state.processes.forEach(p => {
            if (p.status === 'running' && p.progress < 1) {
                p.progress += 0.008; // 稍微慢一点，让用户能看清楚
                allFinished = false;
                hasRunningProcess = true;
            }
        });

        // 更新盒子淡入效果
        Object.values(state.boxes).forEach(box => {
            if (box.content && box.fadeIn < 1) {
                box.fadeIn += 0.05;
            }
        });

        draw();

        // 持续动画循环
        if (!allFinished || state.particles.length > 0 || state.connectionLines.length > 0 || hasRunningProcess) {
            state.animationFrameId = requestAnimationFrame(animate);
        }
    }

    function reset() {
        // 停止所有动画
        if (state.animationFrameId) {
            cancelAnimationFrame(state.animationFrameId);
            state.animationFrameId = null;
        }

        // 重置状态
        state.currentStep = -1;
        state.time = 0;
        state.isAnimating = false;

        // 重置进程
        state.processes = [
            { id: 'P1', x: 100, y: 50, status: 'ready', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P2', x: 250, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P3', x: 400, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
            { id: 'P4', x: 550, y: 50, status: 'blocked', progress: 0, pulsePhase: 0, glowIntensity: 0 },
        ];

        // 重置信号量
        state.semaphores = { s1: 0, s2: 0, s3: 0, s4: 0 };

        // 重置操作框
        Object.keys(state.boxes).forEach(key => {
            state.boxes[key].content = '';
            state.boxes[key].highlight = false;
            state.boxes[key].fadeIn = 0;
        });

        // 清除粒子和连接线
        state.particles = [];
        state.connectionLines = [];

        // 重置UI
        startBtn.disabled = false;
        nextBtn.disabled = true;
        explanationText.innerHTML = '🎯 点击"开始"按钮，启动进程 P1，开始学习进程同步的奇妙之旅！';

        // 清除概念高亮
        clearConceptHighlight();

        // 清除信号量高亮
        document.querySelectorAll('.highlight-sem').forEach(el => el.classList.remove('highlight-sem'));

        updateUI();
        draw();

        // 启动基础动画循环
        animate();
    }
    
    function handleNextStep() {
        if (state.currentStep < steps.length - 1) {
            state.currentStep++;

            // 清除之前的高亮效果
            document.querySelectorAll('.highlight-sem').forEach(el => el.classList.remove('highlight-sem'));

            // 执行当前步骤
            const currentStep = steps[state.currentStep];
            currentStep.action();

            // 更新UI
            updateUI();

            // 如果是最后一步，禁用下一步按钮
            if (state.currentStep === steps.length - 1) {
                nextBtn.disabled = true;
                nextBtn.textContent = '演示完成';
            }

            // 启动动画
            if (!state.animationFrameId) {
                animate();
            }
        }
    }

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && !nextBtn.disabled) {
            e.preventDefault();
            handleNextStep();
        } else if (e.code === 'KeyR') {
            e.preventDefault();
            reset();
        }
    });

    // 鼠标悬停效果
    canvas.addEventListener('mousemove', (e) => {
        const rect = canvas.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // 检查是否悬停在进程上
        state.processes.forEach(p => {
            const distance = Math.sqrt((mouseX - p.x) ** 2 + (mouseY - (p.status === 'running' ? 50 + p.progress * timelineHeight : p.y)) ** 2);
            if (distance < 15) {
                canvas.style.cursor = 'pointer';
                canvas.title = `进程 ${p.id} - 状态: ${getStatusText(p.status)}`;
                return;
            }
        });

        // 检查是否悬停在操作框上
        Object.entries(state.boxes).forEach(([key, box]) => {
            if (mouseX >= box.x - boxWidth/2 && mouseX <= box.x + boxWidth/2 &&
                mouseY >= box.y - boxHeight/2 && mouseY <= box.y + boxHeight/2) {
                canvas.style.cursor = 'pointer';
                canvas.title = `操作 ${key}: ${box.content || '等待执行'}`;
                return;
            }
        });
    });

    canvas.addEventListener('mouseleave', () => {
        canvas.style.cursor = 'default';
        canvas.title = '';
    });

    function getStatusText(status) {
        const statusMap = {
            'ready': '就绪',
            'running': '运行中',
            'blocked': '阻塞',
            'finished': '完成',
            'checking': '检查条件'
        };
        return statusMap[status] || status;
    }

    // 事件监听器
    startBtn.addEventListener('click', () => {
        startBtn.disabled = true;
        nextBtn.disabled = false;
        nextBtn.textContent = '下一步';
        handleNextStep();
    });

    nextBtn.addEventListener('click', handleNextStep);
    resetBtn.addEventListener('click', reset);

    // 增强的测验逻辑
    const checkAnswersBtn = document.getElementById('check-answers');
    checkAnswersBtn.addEventListener('click', () => {
        const inputs = document.querySelectorAll('.quiz-container input');
        let correctCount = 0;

        inputs.forEach(input => {
            const userAnswer = input.value.trim().replace(/\s+/g, ' ').toUpperCase();
            const correctAnswer = input.getAttribute('data-answer').toUpperCase();

            if (userAnswer === correctAnswer) {
                input.classList.remove('incorrect');
                input.classList.add('correct');
                correctCount++;
            } else {
                input.classList.remove('correct');
                input.classList.add('incorrect');
            }
        });

        // 显示结果反馈
        const resultText = correctCount === inputs.length
            ? '🎉 恭喜！全部答对了！您已经掌握了进程同步的基本概念。'
            : `✨ 答对了 ${correctCount}/${inputs.length} 题。继续加油！`;

        // 创建临时反馈元素
        let feedback = document.getElementById('quiz-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.id = 'quiz-feedback';
            feedback.style.cssText = `
                margin-top: 15px;
                padding: 15px;
                border-radius: 12px;
                text-align: center;
                font-weight: 500;
                transition: all 0.3s ease;
            `;
            checkAnswersBtn.parentNode.appendChild(feedback);
        }

        feedback.textContent = resultText;
        feedback.style.background = correctCount === inputs.length
            ? 'linear-gradient(135deg, #00b894, #00cec9)'
            : 'linear-gradient(135deg, #fdcb6e, #e17055)';
        feedback.style.color = 'white';
    });

    // 初始化
    reset();

    // 添加加载完成提示
    setTimeout(() => {
        console.log('🎯 进程同步教程已加载完成！');
        console.log('💡 提示：使用空格键进行下一步，R键重置');
    }, 100);
});
</script>

</body>
</html> 