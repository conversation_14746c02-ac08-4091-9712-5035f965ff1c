<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件质量属性 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            color: #2c3e50;
            line-height: 1.6;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .canvas-container {
            position: relative;
            margin: 30px 0;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            display: block;
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #f0f2f5, #e8ecf0);
        }

        .strategy-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .strategy-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .strategy-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .strategy-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 12px;
        }

        .card-description {
            font-size: 0.9rem;
            text-align: center;
            opacity: 0.8;
            line-height: 1.4;
        }

        .explanation-panel {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation-panel.show {
            opacity: 1;
            transform: translateY(0);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .quiz-section {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .quiz-question {
            font-size: 1.2rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: white;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .quiz-option.correct {
            border-color: #27ae60;
            background: #d5f4e6;
            color: #27ae60;
        }

        .quiz-option.incorrect {
            border-color: #e74c3c;
            background: #fdeaea;
            color: #e74c3c;
        }

        .quiz-result {
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            margin-top: 20px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .quiz-result.show {
            opacity: 1;
            transform: translateY(0);
        }

        .quiz-result.correct {
            background: #d5f4e6;
            color: #27ae60;
        }

        .quiz-result.incorrect {
            background: #fdeaea;
            color: #e74c3c;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .progress-text {
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件质量属性学习</h1>
            <p class="subtitle">通过互动动画理解设计策略与质量属性的关系</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    软件质量属性通常需要采用特定的设计策略实现。例如，<strong>（ ）</strong>设计策略能提高该系统的<span style="color: #e74c3c;">可用性</span>，<strong>（ ）</strong>设计策略能够提高该系统的<span style="color: #f39c12;">性能</span>，<strong>（ ）</strong>设计策略能够提高该系统的<span style="color: #27ae60;">安全性</span>。
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>

            <div class="strategy-cards">
                <div class="strategy-card" data-strategy="heartbeat">
                    <div class="card-icon">💓</div>
                    <div class="card-title">心跳机制</div>
                    <div class="card-description">定期检测系统组件状态，及时发现故障</div>
                </div>
                <div class="strategy-card" data-strategy="priority">
                    <div class="card-icon">⚡</div>
                    <div class="card-title">优先级队列</div>
                    <div class="card-description">按重要性处理任务，提升系统响应速度</div>
                </div>
                <div class="strategy-card" data-strategy="access">
                    <div class="card-icon">🔒</div>
                    <div class="card-title">限制访问</div>
                    <div class="card-description">控制用户权限，保护系统安全</div>
                </div>
                <div class="strategy-card" data-strategy="separation">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">关注点分离</div>
                    <div class="card-description">将不同功能模块化，提高可维护性</div>
                </div>
            </div>

            <div class="explanation-panel" id="explanationPanel">
                <h3 id="explanationTitle"></h3>
                <p id="explanationContent"></p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn-primary" onclick="startAnimation()">开始学习动画</button>
                <button class="btn-primary" onclick="startQuiz()" style="margin-left: 20px;">开始答题</button>
                <button class="btn-primary" onclick="resetAnimation()" style="margin-left: 20px;">重置</button>
            </div>

            <div class="quiz-section" id="quizSection" style="display: none; margin-top: 30px;">
                <h3 style="text-align: center; margin-bottom: 20px; color: #2c3e50;">知识测验</h3>
                <div class="quiz-question" id="quizQuestion"></div>
                <div class="quiz-options" id="quizOptions"></div>
                <div class="quiz-result" id="quizResult"></div>
            </div>

            <div class="progress-section" style="margin-top: 30px;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">学习进度</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">0% 完成</div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let animationTime = 0;
        let particles = [];
        let systems = [];

        // 创建系统组件
        function createSystems() {
            systems = [
                { x: 100, y: 200, type: 'server', health: 100, color: '#e74c3c' },
                { x: 300, y: 150, type: 'database', health: 100, color: '#f39c12' },
                { x: 500, y: 200, type: 'client', health: 100, color: '#27ae60' }
            ];
        }

        // 绘制系统组件
        function drawSystems() {
            systems.forEach(system => {
                ctx.save();
                ctx.fillStyle = system.color;
                ctx.globalAlpha = system.health / 100;
                
                // 绘制组件
                ctx.beginPath();
                ctx.roundRect(system.x - 40, system.y - 30, 80, 60, 10);
                ctx.fill();
                
                // 绘制健康状态
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(system.type, system.x, system.y + 5);
                
                ctx.restore();
            });
        }

        // 心跳动画
        function animateHeartbeat() {
            const heartbeat = Math.sin(animationTime * 0.1) * 0.5 + 0.5;
            
            systems.forEach((system, index) => {
                // 心跳效果
                const scale = 1 + heartbeat * 0.2;
                ctx.save();
                ctx.translate(system.x, system.y);
                ctx.scale(scale, scale);
                ctx.translate(-system.x, -system.y);
                
                ctx.fillStyle = system.color;
                ctx.globalAlpha = 0.8;
                ctx.beginPath();
                ctx.roundRect(system.x - 40, system.y - 30, 80, 60, 10);
                ctx.fill();
                
                // 心跳信号
                if (animationTime % 60 === index * 20) {
                    particles.push({
                        x: system.x,
                        y: system.y,
                        type: 'heartbeat',
                        life: 60,
                        color: system.color
                    });
                }
                
                ctx.restore();
            });
        }

        // 优先级队列动画
        function animatePriorityQueue() {
            // 绘制队列
            const queueX = 150;
            const queueY = 100;
            
            for (let i = 0; i < 5; i++) {
                const priority = 5 - i;
                const y = queueY + i * 40;
                const alpha = (animationTime % 120) > i * 20 ? 1 : 0.3;
                
                ctx.fillStyle = `hsla(${priority * 60}, 70%, 60%, ${alpha})`;
                ctx.beginPath();
                ctx.roundRect(queueX, y, 200, 30, 5);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`优先级 ${priority}`, queueX + 100, y + 20);
            }
        }

        // 访问控制动画
        function animateAccessControl() {
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = 200;
            
            // 绘制安全屏障
            ctx.strokeStyle = '#27ae60';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.beginPath();
            ctx.arc(centerX, centerY, 80, 0, Math.PI * 2);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制用户尝试访问
            const angle = animationTime * 0.05;
            const userX = centerX + Math.cos(angle) * 120;
            const userY = centerY + Math.sin(angle) * 120;
            
            ctx.fillStyle = Math.cos(angle) > 0 ? '#e74c3c' : '#27ae60';
            ctx.beginPath();
            ctx.arc(userX, userY, 15, 0, Math.PI * 2);
            ctx.fill();
        }

        // 更新粒子
        function updateParticles() {
            particles = particles.filter(particle => {
                particle.life--;
                
                if (particle.type === 'heartbeat') {
                    particle.y -= 2;
                    particle.x += Math.sin(particle.life * 0.2) * 2;
                }
                
                return particle.life > 0;
            });
        }

        // 绘制粒子
        function drawParticles() {
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.life / 60;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 5, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
        }

        // 绘制连接线
        function drawConnections() {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            for (let i = 0; i < systems.length - 1; i++) {
                ctx.beginPath();
                ctx.moveTo(systems[i].x, systems[i].y);
                ctx.lineTo(systems[i + 1].x, systems[i + 1].y);
                ctx.stroke();
            }
            ctx.setLineDash([]);
        }

        // 绘制质量属性指示器
        function drawQualityIndicators() {
            const indicators = [
                { name: '可用性', value: animationState === 'heartbeat' ? 95 : 70, color: '#e74c3c', x: 50 },
                { name: '性能', value: animationState === 'priority' ? 90 : 65, color: '#f39c12', x: 200 },
                { name: '安全性', value: animationState === 'access' ? 88 : 60, color: '#27ae60', x: 350 }
            ];

            indicators.forEach(indicator => {
                // 背景条
                ctx.fillStyle = 'rgba(0,0,0,0.1)';
                ctx.fillRect(indicator.x, 50, 120, 20);

                // 进度条
                ctx.fillStyle = indicator.color;
                ctx.fillRect(indicator.x, 50, (indicator.value / 100) * 120, 20);

                // 标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(indicator.name, indicator.x, 45);
                ctx.fillText(`${indicator.value}%`, indicator.x + 125, 65);
            });
        }

        // 主动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            drawGrid();

            // 绘制质量指示器
            drawQualityIndicators();

            if (animationState === 'heartbeat') {
                drawConnections();
                animateHeartbeat();
            } else if (animationState === 'priority') {
                animatePriorityQueue();
            } else if (animationState === 'access') {
                animateAccessControl();
            } else {
                drawConnections();
                drawSystems();
            }

            updateParticles();
            drawParticles();

            animationTime++;
            requestAnimationFrame(animate);
        }

        // 绘制背景网格
        function drawGrid() {
            ctx.strokeStyle = 'rgba(0,0,0,0.05)';
            ctx.lineWidth = 1;

            const gridSize = 30;
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            for (let x = 0; x <= width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y <= height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
        }

        // 策略卡片点击事件
        document.querySelectorAll('.strategy-card').forEach(card => {
            card.addEventListener('click', function() {
                // 移除其他选中状态
                document.querySelectorAll('.strategy-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                
                const strategy = this.dataset.strategy;
                showExplanation(strategy);
                animationState = strategy;
            });
        });

        // 显示解释
        function showExplanation(strategy) {
            const panel = document.getElementById('explanationPanel');
            const title = document.getElementById('explanationTitle');
            const content = document.getElementById('explanationContent');
            
            const explanations = {
                heartbeat: {
                    title: '心跳机制 → 提高可用性',
                    content: '心跳机制通过定期发送检测信号来监控系统组件的健康状态。当某个组件停止响应心跳信号时，系统能够及时发现故障并采取相应措施，如重启服务或切换到备用组件，从而保证系统的持续可用性。'
                },
                priority: {
                    title: '优先级队列 → 提高性能',
                    content: '优先级队列按照任务的重要性和紧急程度来安排处理顺序。重要的任务会被优先处理，这样可以确保关键操作得到及时响应，整体提升系统的性能表现和用户体验。'
                },
                access: {
                    title: '限制访问 → 提高安全性',
                    content: '限制访问策略通过身份验证、权限控制等机制来限制用户对系统资源的访问。只有经过授权的用户才能访问相应的功能和数据，有效防止未授权访问和潜在的安全威胁。'
                },
                separation: {
                    title: '关注点分离 → 提高可维护性',
                    content: '关注点分离将系统的不同功能模块化，每个模块专注于特定的职责。这样的设计使得代码更加清晰、易于理解和维护，但主要影响的是可维护性而非题目中提到的三个质量属性。'
                }
            };
            
            const explanation = explanations[strategy];
            title.textContent = explanation.title;
            content.textContent = explanation.content;
            
            panel.classList.add('show');
        }

        // 开始动画
        function startAnimation() {
            createSystems();
            animationState = 'heartbeat';
            document.querySelector('[data-strategy="heartbeat"]').click();
        }

        // 测验相关
        let currentQuiz = 0;
        let score = 0;
        let progress = 0;

        const quizData = [
            {
                question: "哪种设计策略主要用于提高系统的可用性？",
                options: ["心跳机制", "优先级队列", "限制访问", "关注点分离"],
                correct: 0,
                explanation: "心跳机制通过定期检测组件状态，能够及时发现故障并采取措施，从而提高系统可用性。"
            },
            {
                question: "优先级队列策略主要影响系统的哪个质量属性？",
                options: ["安全性", "可用性", "性能", "可维护性"],
                correct: 2,
                explanation: "优先级队列通过合理安排任务处理顺序，优先处理重要任务，从而提升系统性能。"
            },
            {
                question: "限制访问策略主要用于提高系统的什么特性？",
                options: ["性能", "安全性", "可用性", "可扩展性"],
                correct: 1,
                explanation: "限制访问通过身份验证和权限控制，防止未授权访问，主要提高系统安全性。"
            }
        ];

        function startQuiz() {
            document.getElementById('quizSection').style.display = 'block';
            currentQuiz = 0;
            score = 0;
            showQuizQuestion();
        }

        function showQuizQuestion() {
            if (currentQuiz >= quizData.length) {
                showQuizComplete();
                return;
            }

            const quiz = quizData[currentQuiz];
            document.getElementById('quizQuestion').textContent = quiz.question;

            const optionsContainer = document.getElementById('quizOptions');
            optionsContainer.innerHTML = '';

            quiz.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'quiz-option';
                optionDiv.textContent = option;
                optionDiv.onclick = () => selectQuizOption(index);
                optionsContainer.appendChild(optionDiv);
            });

            document.getElementById('quizResult').classList.remove('show');
        }

        function selectQuizOption(selectedIndex) {
            const quiz = quizData[currentQuiz];
            const options = document.querySelectorAll('.quiz-option');
            const resultDiv = document.getElementById('quizResult');

            options.forEach((option, index) => {
                option.onclick = null; // 禁用点击
                if (index === quiz.correct) {
                    option.classList.add('correct');
                } else if (index === selectedIndex) {
                    option.classList.add('incorrect');
                }
            });

            if (selectedIndex === quiz.correct) {
                score++;
                resultDiv.textContent = `正确！${quiz.explanation}`;
                resultDiv.className = 'quiz-result correct show';
            } else {
                resultDiv.textContent = `错误。正确答案是：${quiz.options[quiz.correct]}。${quiz.explanation}`;
                resultDiv.className = 'quiz-result incorrect show';
            }

            setTimeout(() => {
                currentQuiz++;
                updateProgress();
                setTimeout(showQuizQuestion, 1000);
            }, 3000);
        }

        function showQuizComplete() {
            const resultDiv = document.getElementById('quizResult');
            const percentage = Math.round((score / quizData.length) * 100);
            resultDiv.textContent = `测验完成！您的得分：${score}/${quizData.length} (${percentage}%)`;
            resultDiv.className = `quiz-result ${percentage >= 70 ? 'correct' : 'incorrect'} show`;

            document.getElementById('quizQuestion').textContent = "恭喜完成学习！";
            document.getElementById('quizOptions').innerHTML = '';
        }

        function updateProgress() {
            progress = Math.min(100, ((currentQuiz + (score > 0 ? 1 : 0)) / (quizData.length + 1)) * 100);
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = Math.round(progress) + '% 完成';
        }

        function resetAnimation() {
            animationState = 'idle';
            animationTime = 0;
            particles = [];
            currentQuiz = 0;
            score = 0;
            progress = 0;
            updateProgress();

            document.getElementById('quizSection').style.display = 'none';
            document.querySelectorAll('.strategy-card').forEach(c => c.classList.remove('selected'));
            document.getElementById('explanationPanel').classList.remove('show');
        }

        // 初始化
        createSystems();
        animate();
        updateProgress();
    </script>
</body>
</html>
