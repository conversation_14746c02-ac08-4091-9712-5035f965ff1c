<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式交互式学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background-color: #fff;
            padding: 30px 40px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 900px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .interactive-section {
            margin-bottom: 30px;
            border-bottom: 1px dashed #ccc;
            padding-bottom: 20px;
        }
        .question-section:last-of-type, .explanation-section:last-of-type, .interactive-section:last-of-type {
            border-bottom: none;
        }
        .question-text {
            font-size: 1.2em;
            margin-bottom: 20px;
            background-color: #e8f0fe;
            padding: 15px;
            border-left: 5px solid #4a90e2;
            border-radius: 5px;
        }
        .options-container button {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background-color: #f9f9f9;
            font-size: 1.1em;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .options-container button:hover {
            background-color: #e9e9e9;
            border-color: #a0a0a0;
        }
        .options-container button.selected {
            background-color: #d1e7dd;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        .options-container button.correct {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
            animation: pulseCorrect 0.5s forwards;
        }
        .options-container button.incorrect {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
            animation: shakeIncorrect 0.5s forwards;
        }
        @keyframes pulseCorrect {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        @keyframes shakeIncorrect {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-5px); }
            40%, 80% { transform: translateX(5px); }
        }

        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            display: none; /* Hidden by default */
        }
        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls button {
            padding: 12px 25px;
            font-size: 1.1em;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #4a90e2;
            color: white;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #357bd8;
        }
        .controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        canvas {
            border: 1px solid #ddd;
            background-color: #fdfdfd;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .explanation-text {
            background-color: #f0f8ff;
            padding: 15px;
            border-left: 5px solid #8ecae6;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设计模式学习乐园</h1>

        <div class="question-section">
            <h2>题目回顾</h2>
            <div class="question-text">
                <p>按照设计模式的目的进行划分，现有设计模式可以分为三类。</p>
                <p>其中**创建型模式**通过采用抽象类所定义的接口，封装了系统中对象如何创建、组合等信息，其代表有（请作答此空）模式等；</p>
                <p>**结构型模式**主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等；</p>
                <p>**行为型模式**主要用于对象之间的职责及其提供服务的分配方式，其代表有 Visitor 模式等。</p>
            </div>

            <div class="options-container">
                <button data-answer="A">A Decorator (装饰器模式)</button>
                <button data-answer="B">B Flyweight (享元模式)</button>
                <button data-answer="C">C Command (命令模式)</button>
                <button data-answer="D">D Singleton (单例模式)</button>
            </div>
            <div id="quizFeedback" class="feedback"></div>
            <div class="controls">
                <button id="submitAnswerBtn">提交答案</button>
            </div>
        </div>

        <div class="explanation-section">
            <h2>知识点解析与动画演示</h2>
            <p class="explanation-text">
                设计模式是软件工程中经过实践总结的、针对常见问题的通用解决方案。它们可以帮助我们编写出更灵活、可维护和可重用的代码。
            </p>
            <div class="controls">
                <button id="startExplanationBtn">开始解析</button>
                <button id="nextStepBtn" style="display: none;">下一步</button>
            </div>
            <canvas id="designPatternCanvas" width="800" height="450"></canvas>
            <p id="canvasExplanation" class="explanation-text" style="display: none;"></p>
        </div>

        <div class="interactive-section">
            <h2>单例模式小游戏：我是唯一的！</h2>
            <p class="explanation-text">
                现在，让我们通过一个小游戏来深入理解单例模式的精髓。
                单例模式确保一个类只有一个实例，并提供一个全局访问点。
            </p>
            <div class="controls">
                <button id="startGameBtn">开始游戏</button>
                <button id="createInstanceBtn" style="display: none;">创建新对象</button>
                <button id="resetGameBtn" style="display: none;">重置游戏</button>
            </div>
            <canvas id="singletonGameCanvas" width="800" height="400"></canvas>
            <p id="singletonGameInfo" class="explanation-text" style="display: none;"></p>
        </div>

    </div>

    <script>
        const quizOptions = document.querySelectorAll('.options-container button');
        const submitAnswerBtn = document.getElementById('submitAnswerBtn');
        const quizFeedback = document.getElementById('quizFeedback');
        const correctAnswer = 'D'; // 正确答案是D

        let selectedAnswer = null;

        quizOptions.forEach(button => {
            button.addEventListener('click', () => {
                quizOptions.forEach(btn => btn.classList.remove('selected'));
                button.classList.add('selected');
                selectedAnswer = button.dataset.answer;
                // Clear feedback when a new option is selected
                quizFeedback.style.display = 'none';
                quizFeedback.classList.remove('correct', 'incorrect');
            });
        });

        submitAnswerBtn.addEventListener('click', () => {
            if (selectedAnswer) {
                // Remove previous feedback classes from all options
                quizOptions.forEach(btn => {
                    btn.classList.remove('correct', 'incorrect');
                    btn.style.animation = 'none'; // Reset animation
                    btn.offsetHeight; // Trigger reflow to restart animation
                });

                if (selectedAnswer === correctAnswer) {
                    document.querySelector(`[data-answer="${selectedAnswer}"]`).classList.add('correct');
                    quizFeedback.textContent = '恭喜你，回答正确！单例模式就是创建型模式的一种。';
                    quizFeedback.classList.add('correct');
                } else {
                    document.querySelector(`[data-answer="${selectedAnswer}"]`).classList.add('incorrect');
                    quizFeedback.textContent = '很遗憾，回答错误。再想想创建对象的方法哦！';
                    quizFeedback.classList.add('incorrect');
                    // Also show the correct answer after a delay for learning
                    setTimeout(() => {
                        quizOptions.forEach(btn => {
                            if (btn.dataset.answer === correctAnswer) {
                                btn.classList.add('correct'); // Highlight correct answer
                            }
                        });
                    }, 1000); // Highlight correct after 1 second
                }
                quizFeedback.style.display = 'block';
                // Disable options after submission
                quizOptions.forEach(btn => btn.disabled = true);
                submitAnswerBtn.disabled = true;
            } else {
                quizFeedback.textContent = '请选择一个答案。';
                quizFeedback.classList.add('incorrect');
                quizFeedback.style.display = 'block';
            }
        });

        // --- Canvas Explanation Logic ---
        const startExplanationBtn = document.getElementById('startExplanationBtn');
        const nextStepBtn = document.getElementById('nextStepBtn');
        const canvas = document.getElementById('designPatternCanvas');
        const ctx = canvas.getContext('2d');
        const canvasExplanation = document.getElementById('canvasExplanation');

        let explanationStep = 0;
        const explanationContent = [
            { text: '设计模式主要分为三类：创建型、结构型和行为型。它们解决的问题侧重点不同。', draw: drawCategories },
            { text: '首先是**创建型模式**。它关注对象的创建机制，在创建对象时提供更大的灵活性。比如：单例模式、工厂模式等。', draw: drawCreational },
            { text: '接着是**结构型模式**。它关注如何将类和对象组合成更大的结构，以形成更复杂的功能。比如：适配器模式、装饰器模式等。', draw: drawStructural },
            { text: '最后是**行为型模式**。它关注对象之间的职责分配和协作方式，从而实现复杂的行为。比如：访问者模式、命令模式等。', draw: drawBehavioral },
            { text: '现在，你对这三类模式有基本了解了吗？准备好进入单例模式的小游戏了吗？', draw: clearCanvas }
        ];

        startExplanationBtn.addEventListener('click', () => {
            explanationStep = 0;
            startExplanationBtn.style.display = 'none';
            nextStepBtn.style.display = 'inline-block';
            executeExplanationStep();
        });

        nextStepBtn.addEventListener('click', () => {
            explanationStep++;
            if (explanationStep < explanationContent.length) {
                executeExplanationStep();
            } else {
                nextStepBtn.style.display = 'none';
                canvasExplanation.textContent = '解析完成！请尝试下方的单例模式小游戏。';
                canvasExplanation.style.display = 'block';
            }
        });

        function executeExplanationStep() {
            const step = explanationContent[explanationStep];
            canvasExplanation.textContent = step.text;
            canvasExplanation.style.display = 'block';
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas before drawing new step
            step.draw();
        }

        function drawCategories() {
            // Draw three main categories
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#34495e';

            drawBox(ctx, canvas.width / 4, canvas.height / 2, '创建型模式', '#f39c12');
            drawBox(ctx, canvas.width / 2, canvas.height / 2, '结构型模式', '#27ae60');
            drawBox(ctx, canvas.width * 3 / 4, canvas.height / 2, '行为型模式', '#e74c3c');
        }

        function drawBox(context, x, y, text, color) {
            context.fillStyle = color;
            context.fillRect(x - 80, y - 40, 160, 80);
            context.strokeStyle = '#333';
            context.lineWidth = 2;
            context.strokeRect(x - 80, y - 40, 160, 80);

            context.fillStyle = 'white';
            context.fillText(text, x, y + 5);
        }

        function drawCreational() {
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#34495e';

            // Main category box
            drawBox(ctx, canvas.width / 2, 80, '创建型模式', '#f39c12');

            // Examples
            drawExample(ctx, canvas.width / 4, 250, '单例模式', '一个实例');
            drawExample(ctx, canvas.width * 3 / 4, 250, '工厂模式', '创建对象');

            // Arrows
            drawArrow(ctx, canvas.width / 2, 120, canvas.width / 4, 210, '#333');
            drawArrow(ctx, canvas.width / 2, 120, canvas.width * 3 / 4, 210, '#333');
        }

        function drawStructural() {
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#34495e';

            // Main category box
            drawBox(ctx, canvas.width / 2, 80, '结构型模式', '#27ae60');

            // Examples
            drawExample(ctx, canvas.width / 4, 250, '适配器模式', '接口转换');
            drawExample(ctx, canvas.width * 3 / 4, 250, '装饰器模式', '增强功能');

            // Arrows
            drawArrow(ctx, canvas.width / 2, 120, canvas.width / 4, 210, '#333');
            drawArrow(ctx, canvas.width / 2, 120, canvas.width * 3 / 4, 210, '#333');
        }

        function drawBehavioral() {
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#34495e';

            // Main category box
            drawBox(ctx, canvas.width / 2, 80, '行为型模式', '#e74c3c');

            // Examples
            drawExample(ctx, canvas.width / 4, 250, '访问者模式', '数据与操作分离');
            drawExample(ctx, canvas.width * 3 / 4, 250, '命令模式', '请求封装');

            // Arrows
            drawArrow(ctx, canvas.width / 2, 120, canvas.width / 4, 210, '#333');
            drawArrow(ctx, canvas.width / 2, 120, canvas.width * 3 / 4, 210, '#333');
        }

        function drawExample(context, x, y, text1, text2) {
            context.fillStyle = '#ecf0f1';
            context.fillRect(x - 60, y - 30, 120, 60);
            context.strokeStyle = '#333';
            context.lineWidth = 1;
            context.strokeRect(x - 60, y - 30, 120, 60);

            context.fillStyle = '#333';
            context.font = '16px Arial';
            context.fillText(text1, x, y - 8);
            context.font = '14px Arial';
            context.fillText(text2, x, y + 15);
        }

        function drawArrow(context, fromX, fromY, toX, toY, color) {
            context.strokeStyle = color;
            context.lineWidth = 2;
            context.beginPath();
            context.moveTo(fromX, fromY);
            context.lineTo(toX, toY);
            context.stroke();

            // Arrowhead
            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(toY - fromY, toX - fromX);
            context.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            context.moveTo(toX, toY);
            context.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            context.stroke();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // --- Singleton Game Logic ---
        const startGameBtn = document.getElementById('startGameBtn');
        const createInstanceBtn = document.getElementById('createInstanceBtn');
        const resetGameBtn = document.getElementById('resetGameBtn');
        const singletonCanvas = document.getElementById('singletonGameCanvas');
        const singletonCtx = singletonCanvas.getContext('2d');
        const singletonGameInfo = document.getElementById('singletonGameInfo');

        let singletonInstance = null;
        let instanceCount = 0;
        let animationFrameId;

        class SingletonObject {
            constructor(id) {
                this.id = id;
                this.x = 0;
                this.y = singletonCanvas.height / 2;
                this.color = `hsl(${Math.random() * 360}, 70%, 50%)`;
                this.size = 30;
                this.targetX = singletonCanvas.width / 2;
                this.speed = 5;
            }

            draw() {
                singletonCtx.fillStyle = this.color;
                singletonCtx.beginPath();
                singletonCtx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                singletonCtx.fill();
                singletonCtx.strokeStyle = '#333';
                singletonCtx.lineWidth = 2;
                singletonCtx.stroke();

                singletonCtx.fillStyle = 'white';
                singletonCtx.font = '16px Arial';
                singletonCtx.textAlign = 'center';
                singletonCtx.fillText(`对象 ${this.id}`, this.x, this.y + 5);
            }

            update() {
                if (Math.abs(this.targetX - this.x) > this.speed) {
                    this.x += (this.targetX > this.x ? 1 : -1) * this.speed;
                    return true; // Still animating
                } else {
                    this.x = this.targetX;
                    return false; // Animation finished
                }
            }
        }

        function getSingletonInstance() {
            if (!singletonInstance) {
                instanceCount++;
                singletonInstance = new SingletonObject(instanceCount);
                singletonGameInfo.textContent = `成功创建了新的单例对象 (ID: ${singletonInstance.id})！`;
                singletonGameInfo.style.color = 'green';
            } else {
                singletonGameInfo.textContent = `单例对象已存在 (ID: ${singletonInstance.id})！没有创建新对象。`;
                singletonGameInfo.style.color = 'blue';
            }
            singletonGameInfo.style.display = 'block';
            return singletonInstance;
        }

        function animateSingletonGame() {
            singletonCtx.clearRect(0, 0, singletonCanvas.width, singletonCanvas.height);

            if (singletonInstance) {
                const animating = singletonInstance.update();
                singletonInstance.draw();
                if (!animating) {
                    createInstanceBtn.disabled = false; // Enable button once animation stops
                }
            }

            animationFrameId = requestAnimationFrame(animateSingletonGame);
        }

        startGameBtn.addEventListener('click', () => {
            startGameBtn.style.display = 'none';
            createInstanceBtn.style.display = 'inline-block';
            resetGameBtn.style.display = 'inline-block';
            singletonGameInfo.style.display = 'block';
            singletonGameInfo.textContent = '点击“创建新对象”按钮，看看会发生什么。';
            if (!animationFrameId) {
                animationFrameId = requestAnimationFrame(animateSingletonGame);
            }
        });

        createInstanceBtn.addEventListener('click', () => {
            createInstanceBtn.disabled = true; // Disable button during animation
            getSingletonInstance();
            if (singletonInstance.x !== singletonInstance.targetX) {
                 // If object needs to animate, restart animation from current position
                 singletonInstance.x = 0; // Start from left for visual effect
            }
        });

        resetGameBtn.addEventListener('click', () => {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
            singletonInstance = null;
            instanceCount = 0;
            singletonCtx.clearRect(0, 0, singletonCanvas.width, singletonCanvas.height);
            singletonGameInfo.textContent = '游戏已重置。再次点击“开始游戏”按钮。';
            createInstanceBtn.style.display = 'none';
            resetGameBtn.style.display = 'none';
            startGameBtn.style.display = 'inline-block';
            createInstanceBtn.disabled = false; // Re-enable for next game
        });

        // Initial setup for the quiz buttons
        quizOptions.forEach(button => button.disabled = false);
        submitAnswerBtn.disabled = false;
    </script>
</body>
</html> 