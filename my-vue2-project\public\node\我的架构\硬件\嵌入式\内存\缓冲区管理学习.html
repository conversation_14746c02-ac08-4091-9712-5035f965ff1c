<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓冲区管理 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .concept-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .simulation-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transform: scale(1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-card h4 {
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
        }

        .explanation {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        .formula {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            text-align: center;
            font-size: 1.1em;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 缓冲区管理学习</h1>
            <p>通过动画和交互理解单缓冲区与双缓冲区的工作原理</p>
        </div>

        <div class="section">
            <h2>📚 基础概念</h2>
            
            <div class="concept-card">
                <h3>🔍 什么是缓冲区？</h3>
                <p>缓冲区是内存中的一块临时存储区域，用于在磁盘和用户程序之间传输数据。就像一个中转站，帮助协调不同速度的设备之间的数据传输。</p>
            </div>

            <div class="concept-card">
                <h3>⏱️ 时间参数说明</h3>
                <ul style="margin-top: 15px; line-height: 1.8;">
                    <li><strong>磁盘读取时间：</strong>16μs - 从磁盘读取一个块到缓冲区</li>
                    <li><strong>数据传输时间：</strong>5μs - 从缓冲区传输到用户区</li>
                    <li><strong>处理时间：</strong>1μs - 用户区处理数据的时间</li>
                    <li><strong>文件大小：</strong>10个磁盘块</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎮 交互式模拟</h2>
            
            <div class="control-panel">
                <button class="btn" onclick="startSingleBuffer()">🔄 单缓冲区演示</button>
                <button class="btn" onclick="startDoubleBuffer()">⚡ 双缓冲区演示</button>
                <button class="btn" onclick="resetSimulation()">🔄 重置</button>
                <button class="btn" onclick="compareMode()">📊 对比模式</button>
            </div>

            <div class="simulation-area">
                <div class="canvas-container">
                    <canvas id="simulationCanvas" width="1000" height="400"></canvas>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div id="currentStatus" style="text-align: center; margin: 20px 0; font-size: 1.2em; font-weight: bold;"></div>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <h4>单缓冲区总时间</h4>
                    <div class="value" id="singleBufferTime">0μs</div>
                </div>
                <div class="stat-card">
                    <h4>双缓冲区总时间</h4>
                    <div class="value" id="doubleBufferTime">0μs</div>
                </div>
                <div class="stat-card">
                    <h4>效率提升</h4>
                    <div class="value" id="efficiency">0%</div>
                </div>
                <div class="stat-card">
                    <h4>当前进度</h4>
                    <div class="value" id="currentProgress">0/10</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📐 计算公式详解</h2>
            
            <div class="explanation">
                <h3>🔢 单缓冲区计算方法</h3>
                <p>单缓冲区中，读取和传输不能同时进行，必须串行执行：</p>
                <div class="formula">
                    总时间 = 第一块处理时间 + (剩余块数 × 每块处理时间)<br>
                    = (16 + 5 + 1) + (10-1) × (16 + 5)<br>
                    = 22 + 9 × 21 = 22 + 189 = 211μs
                </div>
            </div>

            <div class="explanation">
                <h3>⚡ 双缓冲区计算方法</h3>
                <p>双缓冲区可以实现流水线操作，读取和传输可以并行：</p>
                <div class="formula">
                    总时间 = 第一块处理时间 + (剩余块数 × 最长操作时间)<br>
                    = (16 + 5 + 1) + (10-1) × max(16, 5+1)<br>
                    = 22 + 9 × 16 = 22 + 144 = 166μs
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 效率对比</h3>
                <p>双缓冲区相比单缓冲区的效率提升：</p>
                <div class="formula">
                    效率提升 = (211 - 166) / 211 × 100% ≈ 21.3%
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('simulationCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationId;
        let currentStep = 0;
        let isAnimating = false;
        let mode = 'single'; // 'single', 'double', 'compare'
        
        // 动画状态
        let diskBlocks = [];
        let buffers = [];
        let userArea = null;
        let currentTime = 0;
        let totalBlocks = 10;
        
        // 初始化
        function init() {
            // 创建磁盘块
            diskBlocks = [];
            for (let i = 0; i < totalBlocks; i++) {
                diskBlocks.push({
                    id: i + 1,
                    x: 50 + (i % 5) * 80,
                    y: 50 + Math.floor(i / 5) * 60,
                    processed: false,
                    reading: false,
                    color: '#e3f2fd'
                });
            }

            // 创建缓冲区
            buffers = [
                { x: 400, y: 200, active: false, hasData: false, blockId: null, color: '#f5f5f5' }
            ];

            // 用户区
            userArea = { x: 700, y: 200, processing: false, blockId: null, color: '#f5f5f5' };

            currentStep = 0;
            currentTime = 0;
            updateDisplay();
        }

        // 绘制函数
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('磁盘区', 50, 30);
            ctx.fillText('缓冲区', 400, 30);
            ctx.fillText('用户区', 700, 30);

            // 绘制磁盘块
            diskBlocks.forEach(block => {
                ctx.fillStyle = block.color;
                ctx.fillRect(block.x, block.y, 60, 40);
                ctx.strokeStyle = block.reading ? '#ff4444' : '#ddd';
                ctx.lineWidth = block.reading ? 3 : 1;
                ctx.strokeRect(block.x, block.y, 60, 40);

                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`块${block.id}`, block.x + 30, block.y + 25);
            });

            // 绘制缓冲区
            buffers.forEach((buffer, index) => {
                ctx.fillStyle = buffer.color;
                ctx.fillRect(buffer.x, buffer.y, 80, 60);
                ctx.strokeStyle = buffer.active ? '#4caf50' : '#ddd';
                ctx.lineWidth = buffer.active ? 3 : 1;
                ctx.strokeRect(buffer.x, buffer.y, 80, 60);

                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`缓冲区${index + 1}`, buffer.x + 40, buffer.y - 10);

                if (buffer.hasData) {
                    ctx.fillText(`块${buffer.blockId}`, buffer.x + 40, buffer.y + 35);
                }
            });

            // 绘制用户区
            ctx.fillStyle = userArea.color;
            ctx.fillRect(userArea.x, userArea.y, 80, 60);
            ctx.strokeStyle = userArea.processing ? '#ff9800' : '#ddd';
            ctx.lineWidth = userArea.processing ? 3 : 1;
            ctx.strokeRect(userArea.x, userArea.y, 80, 60);

            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('用户区', userArea.x + 40, userArea.y - 10);

            if (userArea.processing) {
                ctx.fillText(`处理块${userArea.blockId}`, userArea.x + 40, userArea.y + 35);
            }

            // 绘制连接线和数据流动画
            drawConnections();

            // 绘制时间轴
            drawTimeline();
        }

        function drawConnections() {
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;

            // 磁盘到缓冲区的连接线
            ctx.beginPath();
            ctx.moveTo(350, 100);
            ctx.lineTo(400, 200);
            ctx.stroke();

            // 缓冲区到用户区的连接线
            ctx.beginPath();
            ctx.moveTo(480, 230);
            ctx.lineTo(700, 230);
            ctx.stroke();
        }

        function drawTimeline() {
            const timelineY = 350;
            const timelineWidth = 800;

            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(`当前时间: ${currentTime}μs`, 50, timelineY);

            // 绘制时间进度条
            const progress = Math.min(currentTime / (mode === 'single' ? 211 : 166), 1);
            ctx.fillStyle = '#e0e0e0';
            ctx.fillRect(200, timelineY - 10, timelineWidth, 20);
            ctx.fillStyle = '#4caf50';
            ctx.fillRect(200, timelineY - 10, timelineWidth * progress, 20);
        }

        // 更新显示
        function updateDisplay() {
            draw();

            // 更新统计信息
            document.getElementById('singleBufferTime').textContent = '211μs';
            document.getElementById('doubleBufferTime').textContent = '166μs';
            document.getElementById('efficiency').textContent = '21.3%';
            document.getElementById('currentProgress').textContent = `${currentStep}/${totalBlocks}`;

            // 更新进度条
            const progress = (currentStep / totalBlocks) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 单缓冲区动画
        function startSingleBuffer() {
            if (isAnimating) return;

            mode = 'single';
            resetSimulation();
            isAnimating = true;

            // 只使用一个缓冲区
            buffers = [
                { x: 400, y: 200, active: false, hasData: false, blockId: null, color: '#f5f5f5' }
            ];

            document.getElementById('currentStatus').textContent = '🔄 单缓冲区模式 - 串行处理';

            animateSingleBuffer(0);
        }

        function animateSingleBuffer(blockIndex) {
            if (blockIndex >= totalBlocks) {
                isAnimating = false;
                document.getElementById('currentStatus').textContent = '✅ 单缓冲区处理完成！总时间: 211μs';
                return;
            }

            const block = diskBlocks[blockIndex];
            const buffer = buffers[0];

            // 阶段1: 读取磁盘块到缓冲区 (16μs)
            block.reading = true;
            block.color = '#ffeb3b';
            buffer.active = true;
            buffer.color = '#fff3e0';

            document.getElementById('currentStatus').textContent = `📖 正在读取块${block.id}到缓冲区... (16μs)`;

            setTimeout(() => {
                // 阶段2: 从缓冲区传输到用户区 (5μs)
                block.reading = false;
                block.color = '#c8e6c9';
                buffer.hasData = true;
                buffer.blockId = block.id;
                buffer.color = '#e8f5e8';
                userArea.processing = true;
                userArea.blockId = block.id;
                userArea.color = '#fff3e0';

                document.getElementById('currentStatus').textContent = `📤 传输块${block.id}到用户区... (5μs)`;
                currentTime += 16;

                setTimeout(() => {
                    // 阶段3: 用户区处理 (1μs)
                    document.getElementById('currentStatus').textContent = `⚙️ 用户区处理块${block.id}... (1μs)`;
                    currentTime += 5;

                    setTimeout(() => {
                        // 完成当前块
                        block.processed = true;
                        block.color = '#4caf50';
                        buffer.active = false;
                        buffer.hasData = false;
                        buffer.blockId = null;
                        buffer.color = '#f5f5f5';
                        userArea.processing = false;
                        userArea.blockId = null;
                        userArea.color = '#f5f5f5';

                        currentTime += 1;
                        currentStep = blockIndex + 1;
                        updateDisplay();

                        // 继续下一个块
                        setTimeout(() => animateSingleBuffer(blockIndex + 1), 500);
                    }, 200);
                }, 1000);
            }, 1600);
        }

        // 双缓冲区动画
        function startDoubleBuffer() {
            if (isAnimating) return;

            mode = 'double';
            resetSimulation();
            isAnimating = true;

            // 使用两个缓冲区
            buffers = [
                { x: 350, y: 200, active: false, hasData: false, blockId: null, color: '#f5f5f5' },
                { x: 450, y: 200, active: false, hasData: false, blockId: null, color: '#f5f5f5' }
            ];

            document.getElementById('currentStatus').textContent = '⚡ 双缓冲区模式 - 流水线处理';

            animateDoubleBuffer(0);
        }

        function animateDoubleBuffer(blockIndex) {
            if (blockIndex >= totalBlocks) {
                isAnimating = false;
                document.getElementById('currentStatus').textContent = '✅ 双缓冲区处理完成！总时间: 166μs';
                return;
            }

            const block = diskBlocks[blockIndex];
            const buffer1 = buffers[0];
            const buffer2 = buffers[1];

            // 使用交替缓冲区
            const currentBuffer = blockIndex % 2 === 0 ? buffer1 : buffer2;

            // 开始读取
            block.reading = true;
            block.color = '#ffeb3b';
            currentBuffer.active = true;
            currentBuffer.color = '#fff3e0';

            document.getElementById('currentStatus').textContent = `📖 读取块${block.id}到缓冲区${(blockIndex % 2) + 1}... (并行处理)`;

            setTimeout(() => {
                block.reading = false;
                block.color = '#c8e6c9';
                currentBuffer.hasData = true;
                currentBuffer.blockId = block.id;
                currentBuffer.color = '#e8f5e8';

                // 立即开始处理
                userArea.processing = true;
                userArea.blockId = block.id;
                userArea.color = '#fff3e0';

                setTimeout(() => {
                    block.processed = true;
                    block.color = '#4caf50';
                    currentBuffer.active = false;
                    currentBuffer.hasData = false;
                    currentBuffer.blockId = null;
                    currentBuffer.color = '#f5f5f5';
                    userArea.processing = false;
                    userArea.blockId = null;
                    userArea.color = '#f5f5f5';

                    currentTime += 16; // 双缓冲区每个周期16μs
                    currentStep = blockIndex + 1;
                    updateDisplay();

                    // 继续下一个块
                    setTimeout(() => animateDoubleBuffer(blockIndex + 1), 300);
                }, 600);
            }, 1600);
        }

        // 重置模拟
        function resetSimulation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            isAnimating = false;
            currentStep = 0;
            currentTime = 0;

            // 重置所有状态
            diskBlocks.forEach(block => {
                block.processed = false;
                block.reading = false;
                block.color = '#e3f2fd';
            });

            buffers.forEach(buffer => {
                buffer.active = false;
                buffer.hasData = false;
                buffer.blockId = null;
                buffer.color = '#f5f5f5';
            });

            userArea.processing = false;
            userArea.blockId = null;
            userArea.color = '#f5f5f5';

            document.getElementById('currentStatus').textContent = '🎮 选择一种缓冲区模式开始演示';
            updateDisplay();
        }

        // 对比模式
        function compareMode() {
            resetSimulation();

            // 创建对比视图
            canvas.height = 600;

            // 显示对比信息
            document.getElementById('currentStatus').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center;">
                    <div style="background: #ffebee; padding: 15px; border-radius: 10px;">
                        <h4>🔄 单缓冲区</h4>
                        <p>串行处理：读取→传输→处理</p>
                        <p><strong>总时间：211μs</strong></p>
                        <p>公式：22 + 9×21 = 211μs</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px;">
                        <h4>⚡ 双缓冲区</h4>
                        <p>并行处理：流水线操作</p>
                        <p><strong>总时间：166μs</strong></p>
                        <p>公式：22 + 9×16 = 166μs</p>
                    </div>
                </div>
            `;

            drawComparison();
        }

        function drawComparison() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制对比图表
            const chartY = 100;
            const barHeight = 40;
            const maxTime = 211;
            const scale = 600 / maxTime;

            // 单缓冲区条形图
            ctx.fillStyle = '#f44336';
            ctx.fillRect(100, chartY, 211 * scale, barHeight);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('单缓冲区: 211μs', 100 + (211 * scale) / 2, chartY + 25);

            // 双缓冲区条形图
            ctx.fillStyle = '#4caf50';
            ctx.fillRect(100, chartY + 80, 166 * scale, barHeight);
            ctx.fillStyle = 'white';
            ctx.fillText('双缓冲区: 166μs', 100 + (166 * scale) / 2, chartY + 105);

            // 绘制时间轴
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'left';
            for (let i = 0; i <= maxTime; i += 50) {
                const x = 100 + i * scale;
                ctx.fillText(i + 'μs', x, chartY - 10);
                ctx.beginPath();
                ctx.moveTo(x, chartY - 5);
                ctx.lineTo(x, chartY + 140);
                ctx.strokeStyle = '#ddd';
                ctx.stroke();
            }

            // 绘制效率提升箭头
            const improvement = (211 - 166) / 211 * 100;
            ctx.fillStyle = '#ff9800';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`效率提升: ${improvement.toFixed(1)}%`, canvas.width / 2, chartY + 180);

            // 绘制流水线示意图
            drawPipelineDiagram();
        }

        function drawPipelineDiagram() {
            const pipelineY = 350;
            const blockWidth = 60;
            const blockHeight = 30;

            ctx.fillStyle = '#333';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('流水线对比:', 50, pipelineY - 20);

            // 单缓冲区时间线
            ctx.fillText('单缓冲区:', 50, pipelineY + 20);
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = i === 0 ? '#ffeb3b' : i === 1 ? '#ff9800' : '#4caf50';
                ctx.fillRect(150 + i * 70, pipelineY, blockWidth, blockHeight);
                ctx.fillStyle = '#333';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                const labels = ['读取16μs', '传输5μs', '处理1μs'];
                ctx.fillText(labels[i], 150 + i * 70 + blockWidth/2, pipelineY + 20);
            }

            // 双缓冲区时间线
            ctx.fillStyle = '#333';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('双缓冲区:', 50, pipelineY + 80);

            // 显示并行操作
            ctx.fillStyle = '#4caf50';
            ctx.fillRect(150, pipelineY + 60, blockWidth, blockHeight);
            ctx.fillRect(220, pipelineY + 60, blockWidth, blockHeight);

            ctx.fillStyle = '#333';
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('读取', 150 + blockWidth/2, pipelineY + 80);
            ctx.fillText('传输+处理', 220 + blockWidth/2, pipelineY + 80);

            // 绘制并行箭头
            ctx.strokeStyle = '#4caf50';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(150, pipelineY + 100);
            ctx.lineTo(280, pipelineY + 100);
            ctx.stroke();

            ctx.fillStyle = '#4caf50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('并行执行', 215, pipelineY + 115);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            init();
            updateDisplay();
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    startSingleBuffer();
                    break;
                case '2':
                    startDoubleBuffer();
                    break;
                case 'r':
                case 'R':
                    resetSimulation();
                    break;
                case 'c':
                case 'C':
                    compareMode();
                    break;
            }
        });
    </script>
</body>
</html>
