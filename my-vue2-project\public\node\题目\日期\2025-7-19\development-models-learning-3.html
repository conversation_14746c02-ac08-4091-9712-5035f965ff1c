<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发模型学习 - RAD模型专题</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .models-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .models-demo {
            text-align: center;
            margin: 30px 0;
        }

        #modelsCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .model-controls {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .model-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .fountain-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .spiral-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .rad-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .rup-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .model-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .model-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .model-comparison {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-card.highlight {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .quiz-question {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-rad {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-rad {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            animation: floatRAD 18s infinite ease-in-out;
        }

        .rad1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .rad2 {
            top: 70%;
            right: 15%;
            animation-delay: 6s;
        }

        .rad3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 12s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatRAD {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-30px) rotate(90deg); }
            50% { transform: translateY(15px) rotate(180deg); }
            75% { transform: translateY(-15px) rotate(270deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .model-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .model-comparison {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-rad rad1"></div>
        <div class="floating-rad rad2"></div>
        <div class="floating-rad rad3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🚀 RAD模型专题学习</h1>
            <p>深度理解RAD模型：需要用户参与的快速应用开发</p>
        </div>

        <div class="main-grid">
            <div class="models-section">
                <h2 class="section-title">🛠️ 开发模型对比演示</h2>
                
                <div class="models-demo">
                    <canvas id="modelsCanvas" width="700" height="400"></canvas>
                </div>

                <div class="model-controls">
                    <button class="model-btn fountain-btn" onclick="demonstrateModel('fountain')">
                        喷泉模型<br><small>复用好、无间隙</small>
                    </button>
                    <button class="model-btn spiral-btn" onclick="demonstrateModel('spiral')">
                        螺旋模型<br><small>瀑布+原型结合</small>
                    </button>
                    <button class="model-btn rad-btn" onclick="demonstrateModel('rad')">
                        RAD模型<br><small>用户参与、模块化</small>
                    </button>
                    <button class="model-btn rup-btn" onclick="demonstrateModel('rup')">
                        RUP模型<br><small>用例驱动、架构中心</small>
                    </button>
                </div>

                <div class="model-comparison">
                    <div class="comparison-card">
                        <h3>⛲ 喷泉模型特征1</h3>
                        <p><strong>第一空答案</strong><br>• 复用好<br>• 开发过程无间隙<br>• 节省时间<br>• 面向对象开发</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🌀 螺旋模型特征</h3>
                        <p><strong>第二空答案</strong><br>• 瀑布与原型结合体<br>• 适用于复杂项目<br>• 风险驱动开发<br>• 迭代螺旋上升</p>
                    </div>
                    <div class="comparison-card highlight">
                        <h3>🚀 RAD模型特征</h3>
                        <p><strong>第三空答案</strong><br>• 需要用户参与<br>• 模块化要求高<br>• 不适用新技术<br>• 快速应用开发</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🎯 RUP模型特征</h3>
                        <p><strong>第四空答案</strong><br>• 用例驱动<br>• 架构为中心<br>• 迭代开发<br>• 增量交付</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 对于开发模型来说，（　　）复用好、开发过程无间隙、节省时间。（　　）是瀑布与原型（演化）模型结合体，适用于复杂项目。<strong>（请作答此空）需要用户参与，模块化要求高，不适用新技术</strong>。（　　）是用例驱动、架构为中心、迭代、增量。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        A. RAD模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 螺旋模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. RUP模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 喷泉模型
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：A. RAD模型</strong></p>
                    <p>根据题目四个空的特征描述：</p>
                    <ul>
                        <li><strong>第一空</strong>：喷泉模型 - 复用好、开发过程无间隙、节省时间</li>
                        <li><strong>第二空</strong>：螺旋模型 - 瀑布与原型（演化）模型结合体，适用于复杂项目</li>
                        <li><strong>第三空</strong>：<span class="highlight-rad">RAD模型</span> - 需要用户参与，模块化要求高，不适用新技术</li>
                        <li><strong>第四空</strong>：RUP模型 - 用例驱动、架构为中心、迭代、增量</li>
                    </ul>
                    <p><strong>RAD模型的核心特点</strong>：</p>
                    <ul>
                        <li><strong>用户深度参与</strong>：整个开发过程需要用户持续参与和反馈</li>
                        <li><strong>模块化要求高</strong>：系统必须能够分解为独立的功能模块</li>
                        <li><strong>不适用新技术</strong>：依赖成熟的技术和工具，避免技术风险</li>
                        <li><strong>快速应用开发</strong>：通过复用和并行开发缩短开发周期</li>
                        <li><strong>原型驱动</strong>：通过快速原型获得用户反馈</li>
                    </ul>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了RAD模型的核心特征！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('modelsCanvas');
        const ctx = canvas.getContext('2d');
        let currentModel = 'rad';
        let animationId = null;

        // 演示不同开发模型
        function demonstrateModel(modelType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentModel = modelType;
            
            // 更新按钮状态
            document.querySelectorAll('.model-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${modelType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(modelType) {
                case 'fountain':
                    drawFountainModel();
                    break;
                case 'spiral':
                    drawSpiralModel();
                    break;
                case 'rad':
                    drawRADModel();
                    break;
                case 'rup':
                    drawRUPModel();
                    break;
            }
        }

        // 绘制喷泉模型（第一空特征）
        function drawFountainModel() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('喷泉模型 - 第一空特征', 350, 40);

            // 绘制复用组件
            const components = ['组件A', '组件B', '组件C', '组件D'];
            components.forEach((comp, index) => {
                const x = 150 + index * 120;
                const y = 100;
                
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(x, y, 80, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, 80, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(comp, x + 40, y + 35);
            });

            // 无间隙开发流程
            ctx.fillStyle = '#e17055';
            ctx.fillRect(200, 200, 300, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 200, 300, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('无间隙并行开发', 350, 225);

            // 特点标注
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 复用好', 100, 300);
            ctx.fillText('✓ 开发过程无间隙', 250, 300);
            ctx.fillText('✓ 节省时间', 450, 300);
        }

        // 绘制螺旋模型（第二空特征）
        function drawSpiralModel() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('螺旋模型 - 第二空特征', 350, 40);

            // 绘制螺旋
            const centerX = 350;
            const centerY = 200;
            let radius = 30;
            let angle = 0;
            
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 5;
            ctx.beginPath();
            
            for (let i = 0; i < 150; i++) {
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
                
                angle += 0.15;
                radius += 0.8;
            }
            ctx.stroke();

            // 象限标注
            const quadrants = ['计划', '风险分析', '实施', '评估'];
            const positions = [
                {x: 450, y: 120},
                {x: 450, y: 280},
                {x: 250, y: 280},
                {x: 250, y: 120}
            ];
            
            quadrants.forEach((quad, index) => {
                ctx.fillStyle = '#e84393';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(quad, positions[index].x, positions[index].y);
            });

            // 特点
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('瀑布与原型结合 • 适用复杂项目', 350, 350);
        }

        // 绘制RAD模型（第三空特征）
        function drawRADModel() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RAD模型 - 快速应用开发', 350, 40);

            // 用户参与中心
            ctx.fillStyle = '#00a085';
            ctx.beginPath();
            ctx.arc(350, 150, 50, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('用户', 350, 160);

            // 周围的模块
            const modules = [
                {name: '模块A', angle: 0},
                {name: '模块B', angle: Math.PI / 2},
                {name: '模块C', angle: Math.PI},
                {name: '模块D', angle: 3 * Math.PI / 2}
            ];
            
            modules.forEach(module => {
                const x = 350 + Math.cos(module.angle) * 120;
                const y = 150 + Math.sin(module.angle) * 120;
                
                ctx.fillStyle = '#00b894';
                ctx.fillRect(x - 40, y - 20, 80, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 40, y - 20, 80, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(module.name, x, y + 5);
                
                // 连接线
                ctx.strokeStyle = '#00a085';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(350 + Math.cos(module.angle) * 50, 150 + Math.sin(module.angle) * 50);
                ctx.lineTo(350 + Math.cos(module.angle) * 80, 150 + Math.sin(module.angle) * 80);
                ctx.stroke();
            });

            // 成熟技术标注
            ctx.fillStyle = '#e17055';
            ctx.fillRect(100, 300, 120, 30);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(100, 300, 120, 30);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('成熟技术', 160, 320);

            ctx.fillStyle = '#e17055';
            ctx.fillRect(480, 300, 120, 30);
            ctx.strokeRect(480, 300, 120, 30);
            ctx.fillText('避免新技术', 540, 320);

            // 特点标注
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 需要用户参与', 50, 280);
            ctx.fillText('✓ 模块化要求高', 250, 280);
            ctx.fillText('✗ 不适用新技术', 450, 280);
        }

        // 绘制RUP模型（第四空特征）
        function drawRUPModel() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RUP模型 - 第四空特征', 350, 40);

            // 用例驱动
            ctx.fillStyle = '#e17055';
            ctx.fillRect(100, 100, 100, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(100, 100, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用例驱动', 150, 125);

            // 架构为中心
            ctx.fillStyle = '#e17055';
            ctx.fillRect(300, 100, 100, 40);
            ctx.strokeRect(300, 100, 100, 40);
            ctx.fillText('架构中心', 350, 125);

            // 迭代增量
            const iterations = ['迭代1', '迭代2', '迭代3'];
            iterations.forEach((iter, index) => {
                const x = 150 + index * 120;
                const y = 200;
                
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(x, y, 80, 50);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 80, 50);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = '12px Arial';
                ctx.fillText(iter, x + 40, y + 30);
                
                // 增量箭头
                if (index < iterations.length - 1) {
                    ctx.strokeStyle = '#e17055';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + 80, y + 25);
                    ctx.lineTo(x + 120, y + 25);
                    ctx.stroke();
                }
            });

            // 特点
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用例驱动 • 架构为中心 • 迭代增量', 350, 320);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('A. RAD模型')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateModel('rad');
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateModel('rad');
            
            // 自动演示序列
            setTimeout(() => demonstrateModel('fountain'), 4000);
            setTimeout(() => demonstrateModel('spiral'), 8000);
            setTimeout(() => demonstrateModel('rad'), 12000);
            setTimeout(() => demonstrateModel('rup'), 16000);
            setTimeout(() => demonstrateModel('rad'), 20000);
        };
    </script>
</body>
</html>
