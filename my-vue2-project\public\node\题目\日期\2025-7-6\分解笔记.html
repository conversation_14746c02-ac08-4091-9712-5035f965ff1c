<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识串联笔记系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .module-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex: 1;
        }
        h2 {
            color: #0056b3;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .note-input-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        textarea {
            width: calc(100% - 20px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 100px;
        }
        button {
            background-color: #28a745;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #218838;
        }
        .note-item {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            word-wrap: break-word;
            opacity: 0; /* Initial state for animation */
            transform: translateY(20px); /* Initial state for animation */
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }
        .note-item.fade-in {
            opacity: 1;
            transform: translateY(0);
        }
        .storage-format-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        pre {
            background-color: #eee;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>知识串联笔记系统</h1>

    <div class="note-input-section">
        <h2>添加新笔记</h2>
        <textarea id="noteContent" placeholder="输入您的笔记内容..."></textarea>
        <select id="noteCategory">
            <option value="frontend">前端</option>
            <option value="javascript">JavaScript</option>
            <option value="database">数据库表</option>
        </select>
        <button onclick="addNote()">添加笔记并处理</button>
    </div>

    <div class="container">
        <div class="module-section">
            <h2>前端笔记</h2>
            <div id="frontendNotes"></div>
        </div>
        <div class="module-section">
            <h2>JavaScript笔记</h2>
            <div id="javascriptNotes"></div>
        </div>
        <div class="module-section">
            <h2>数据库表笔记</h2>
            <div id="databaseNotes"></div>
        </div>
    </div>

    <div class="storage-format-section">
        <h2>模拟入库格式</h2>
        <pre id="storageFormatDisplay">
            {
                "category": "",
                "content": "",
                "timestamp": ""
            }
        </pre>
    </div>

    <script>
        function addNote() {
            const noteContent = document.getElementById('noteContent').value;
            const noteCategory = document.getElementById('noteCategory').value;

            if (!noteContent.trim()) {
                alert('笔记内容不能为空！');
                return;
            }

            // JS动画模拟：展示数据处理过程
            simulateJsAnimation(noteContent, noteCategory);

            // 将笔记添加到相应的模块
            const noteItem = document.createElement('div');
            noteItem.className = 'note-item';
            noteItem.textContent = noteContent;

            let targetDiv;
            switch (noteCategory) {
                case 'frontend':
                    targetDiv = document.getElementById('frontendNotes');
                    break;
                case 'javascript':
                    targetDiv = document.getElementById('javascriptNotes');
                    break;
                case 'database':
                    targetDiv = document.getElementById('databaseNotes');
                    break;
                default:
                    targetDiv = document.getElementById('frontendNotes'); // Default to frontend
            }
            targetDiv.prepend(noteItem); // Add to top

            // 触发动画
            setTimeout(() => {
                noteItem.classList.add('fade-in');
            }, 10);

            // 生成模拟入库格式
            generateStorageFormat(noteContent, noteCategory);

            // 清空输入框
            document.getElementById('noteContent').value = '';
        }

        function simulateJsAnimation(content, category) {
            // 可以在这里添加更复杂的动画效果，例如：
            // 1. 改变背景颜色短暂提示
            // 2. 文本内容暂时飞向目标区域
            // 3. 简单的加载条等

            console.log(`JS动画模拟：正在处理 "${content}" 到 "${category}" 类别...`);
            // 实际演示中，可以给用户一个短暂的视觉反馈
            const button = document.querySelector('button');
            button.textContent = '处理中...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = '添加笔记并处理';
                button.disabled = false;
                alert('笔记处理完成！'); // 简单的弹窗模拟动画结束
            }, 1000); // 1秒动画
        }

        function generateStorageFormat(content, category) {
            const timestamp = new Date().toISOString();
            const storageData = {
                category: category,
                content: content,
                timestamp: timestamp
            };
            document.getElementById('storageFormatDisplay').textContent = JSON.stringify(storageData, null, 2);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始显示一个空的JSON结构
            generateStorageFormat('', '');
        });
    </script>
</body>
</html> 