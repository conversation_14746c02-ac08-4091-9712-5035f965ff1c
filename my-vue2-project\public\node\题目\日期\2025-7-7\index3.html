<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库风格 - 知识点解析与交互演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 900px;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        .question-section, .explanation-section, .interactive-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .question-section p {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #34495e;
        }
        .options div {
            background-color: #ecf0f1;
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            display: flex;
            align-items: center;
            font-size: 1.1em;
        }
        .options div:hover {
            background-color: #dbe2e3;
            transform: translateY(-2px);
        }
        .options div.selected {
            background-color: #3498db;
            color: #fff;
            border: 2px solid #2980b9;
        }
        .options div.correct {
            background-color: #27ae60;
            color: #fff;
            border: 2px solid #219354;
        }
        .options div.incorrect {
            background-color: #e74c3c;
            color: #fff;
            border: 2px solid #c0392b;
        }
        .option-label {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 50%;
            background-color: #fff;
            color: #34495e;
            margin-right: 15px;
            font-weight: bold;
            border: 1px solid #ccc;
        }
        .options div.selected .option-label,
        .options div.correct .option-label,
        .options div.incorrect .option-label {
            background-color: #fff; /* Keep label white */
            color: inherit; /* Inherit color from parent div */
        }

        #feedback {
            margin-top: 20px;
            font-size: 1.1em;
            text-align: center;
            font-weight: bold;
        }
        #explanation {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-left: 5px solid #3498db;
            border-radius: 5px;
            font-size: 1.05em;
        }
        #explanation h3 {
            text-align: left;
            color: #3498db;
            margin-top: 0;
            margin-bottom: 15px;
        }
        #explanation p {
            margin-bottom: 10px;
        }

        canvas {
            border: 1px solid #ccc;
            background-color: #ecf0f1;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls button {
            background-color: #2ecc71;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #27ae60;
        }
        .controls button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构风格：仓库风格解析</h1>

        <div class="question-section">
            <h2>问题回顾</h2>
            <p>在仓库风格中，有两种不同的构件，其中，（）说明当前状态，（）在中央数据存储上执行。</p>
            <div class="options">
                <div id="optionA" onclick="selectOption('A')"><span class="option-label">A</span>注册表</div>
                <div id="optionB" onclick="selectOption('B')"><span class="option-label">B</span>中央数据结构</div>
                <div id="optionC" onclick="selectOption('C')"><span class="option-label">C</span>事件</div>
                <div id="optionD" onclick="selectOption('D')"><span class="option-label">D</span>数据库</div>
            </div>
            <div id="feedback"></div>
        </div>

        <div class="explanation-section">
            <h2>知识点解释</h2>
            <div id="explanation">
                <h3>什么是仓库风格？</h3>
                <p>仓库风格是一种常见的软件架构风格，它将数据存储（"仓库"）与数据处理（"独立构件"）分离。这种风格的核心思想是，所有参与者（或称"独立构件"）通过一个中央共享的数据存储来交互，而不是直接相互通信。</p>
                <h3>仓库风格的两个主要构件：</h3>
                <p>1.  <b>中央数据结构（Central Data Structure）</b>：它就是题目中第一个括号所指的构件。它负责存储和管理系统当前的<b>状态</b>。想象它是一个大型的共享黑板或一个图书馆，所有的信息都集中在这里。独立构件通过读写这个中央数据结构来获取信息或更新系统状态。</p>
                <p>2.  <b>独立构件（Independent Components）</b>：它们是题目中第二个括号所指的构件，指的是执行计算任务的独立程序或模块。它们不直接相互通信，而是通过访问和修改"中央数据结构"来完成各自的功能。它们就像是这个图书馆里的读者和管理员，所有人都通过图书馆（中央数据结构）来获取或存放书籍（数据）。</p>
                <p>所以，正确答案是 <b>B. 中央数据结构</b>，因为它既描述了当前状态，也是独立构件执行（操作数据）的地方。</p>
            </div>
        </div>

        <div class="interactive-section">
            <h2>交互演示：仓库风格的工作原理</h2>
            <p>通过下面的动画，我们将直观地理解仓库风格的运作方式。点击按钮开始演示！</p>
            <canvas id="architectureCanvas" width="800" height="450"></canvas>
            <div class="controls">
                <button id="startButton">开始演示</button>
                <button id="resetButton" disabled>重置</button>
            </div>
        </div>
    </div>

    <script>
        const correctAnswer = 'B';
        let selectedOption = null;

        function selectOption(optionId) {
            if (selectedOption) {
                document.getElementById('option' + selectedOption).classList.remove('selected');
            }
            selectedOption = optionId;
            document.getElementById('option' + optionId).classList.add('selected');

            // Optionally, show feedback immediately or add a "Submit" button
            // For this example, let's show feedback immediately on selection
            checkAnswer();
        }

        function checkAnswer() {
            const feedbackDiv = document.getElementById('feedback');
            const optionElements = document.querySelectorAll('.options div');

            optionElements.forEach(opt => {
                opt.classList.remove('correct', 'incorrect', 'selected'); // Clear previous states
                opt.onclick = null; // Disable further clicks
            });

            if (selectedOption === correctAnswer) {
                document.getElementById('option' + selectedOption).classList.add('correct');
                feedbackDiv.innerHTML = '<span style="color: #27ae60;">恭喜你，回答正确！</span>';
            } else {
                document.getElementById('option' + selectedOption).classList.add('incorrect');
                document.getElementById('option' + correctAnswer).classList.add('correct'); // Highlight correct answer
                feedbackDiv.innerHTML = '<span style="color: #e74c3c;">很遗憾，回答错误。正确答案是B。请仔细阅读下方的知识点解释。</span>';
            }
        }

        // Canvas animation logic
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');

        const startButton = document.getElementById('startButton');
        const resetButton = document.getElementById('resetButton');

        let animationFrameId;
        let animationStep = 0;
        let lastTimestamp = 0;
        const animationSpeed = 0.05; // Adjust for slower/faster animation
        let progress = 0;

        const colors = {
            component: '#3498db',
            dataStructure: '#e67e22',
            arrow: '#2c3e50',
            text: '#2c3e50',
            highlight: '#f1c40f'
        };

        const sizes = {
            componentWidth: 100,
            componentHeight: 60,
            dataStructureWidth: 200,
            dataStructureHeight: 120,
            padding: 40,
            arrowSize: 10
        };

        function drawRoundedRect(x, y, width, height, radius, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        function drawArrow(fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - sizes.arrowSize * Math.cos(angle - Math.PI / 6), toY - sizes.arrowSize * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - sizes.arrowSize * Math.cos(angle + Math.PI / 6), toY - sizes.arrowSize * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        function drawText(text, x, y, color = colors.text, font = '16px Arial', align = 'center') {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawArchitecture(highlightElement = null, arrowProgress = 0, message = '') {
            clearCanvas();

            // Central Data Structure
            const dsX = (canvas.width - sizes.dataStructureWidth) / 2;
            const dsY = (canvas.height - sizes.dataStructureHeight) / 2;
            drawRoundedRect(dsX, dsY, sizes.dataStructureWidth, sizes.dataStructureHeight, 15, highlightElement === 'dataStructure' ? colors.highlight : colors.dataStructure);
            drawText('中央数据结构 (状态)', dsX + sizes.dataStructureWidth / 2, dsY + sizes.dataStructureHeight / 2 + 5);

            // Independent Components
            const comp1X = dsX - sizes.componentWidth - sizes.padding * 2;
            const comp1Y = (canvas.height - sizes.componentHeight) / 2;
            drawRoundedRect(comp1X, comp1Y, sizes.componentWidth, sizes.componentHeight, 10, highlightElement === 'comp1' ? colors.highlight : colors.component);
            drawText('独立构件 A', comp1X + sizes.componentWidth / 2, comp1Y + sizes.componentHeight / 2 + 5);

            const comp2X = dsX + sizes.dataStructureWidth + sizes.padding * 2;
            const comp2Y = (canvas.height - sizes.componentHeight) / 2;
            drawRoundedRect(comp2X, comp2Y, sizes.componentWidth, sizes.componentHeight, 10, highlightElement === 'comp2' ? colors.highlight : colors.component);
            drawText('独立构件 B', comp2X + sizes.componentWidth / 2, comp2Y + sizes.componentHeight / 2 + 5);

            // Arrows (Interaction)
            // Comp1 to DS
            const comp1_to_ds_startX = comp1X + sizes.componentWidth;
            const comp1_to_ds_startY = comp1Y + sizes.componentHeight / 2;
            const comp1_to_ds_endX = dsX;
            const comp1_to_ds_endY = dsY + sizes.dataStructureHeight / 2;
            if (arrowProgress > 0) {
                const currentX = comp1_to_ds_startX + (comp1_to_ds_endX - comp1_to_ds_startX) * arrowProgress;
                const currentY = comp1_to_ds_startY + (comp1_to_ds_endY - comp1_to_ds_startY) * arrowProgress;
                drawArrow(comp1_to_ds_startX, comp1_to_ds_startY, currentX, currentY, colors.arrow);
            }
            if (arrowProgress === 1) { // Draw full arrow if progress is complete
                drawArrow(comp1_to_ds_startX, comp1_to_ds_startY, comp1_to_ds_endX, comp1_to_ds_endY, colors.arrow);
            }

            // DS to Comp2
            const ds_to_comp2_startX = dsX + sizes.dataStructureWidth;
            const ds_to_comp2_startY = dsY + sizes.dataStructureHeight / 2;
            const ds_to_comp2_endX = comp2X;
            const ds_to_comp2_endY = comp2Y + sizes.componentHeight / 2;
            if (arrowProgress > 0) {
                const currentX = ds_to_comp2_startX + (ds_to_comp2_endX - ds_to_comp2_startX) * arrowProgress;
                const currentY = ds_to_comp2_startY + (ds_to_comp2_endY - ds_to_comp2_startY) * arrowProgress;
                drawArrow(ds_to_comp2_startX, ds_to_comp2_startY, currentX, currentY, colors.arrow);
            }
            if (arrowProgress === 1) { // Draw full arrow if progress is complete
                drawArrow(ds_to_comp2_startX, ds_to_comp2_startY, ds_to_comp2_endX, ds_to_comp2_endY, colors.arrow);
            }


            // Message display
            if (message) {
                ctx.fillStyle = '#333';
                ctx.font = '20px "Segoe UI"';
                ctx.textAlign = 'center';
                ctx.fillText(message, canvas.width / 2, dsY - 30);
            }
        }

        function animate(timestamp) {
            if (!lastTimestamp) lastTimestamp = timestamp;
            const deltaTime = (timestamp - lastTimestamp) / 1000; // seconds
            lastTimestamp = timestamp;

            progress += deltaTime * animationSpeed;

            // Define animation steps
            switch (animationStep) {
                case 0: // Initial state
                    drawArchitecture();
                    startButton.disabled = false;
                    resetButton.disabled = true;
                    break;
                case 1: // Highlight Central Data Structure and explain state
                    if (progress < 1) {
                        drawArchitecture('dataStructure', 0, '1. 中央数据结构存储系统当前"状态"');
                    } else {
                        progress = 0;
                        animationStep++;
                    }
                    break;
                case 2: // Highlight Independent Components and explain execution
                    if (progress < 1) {
                        drawArchitecture('comp1', 0, '2. 独立构件A通过中央数据结构交互');
                    } else {
                        progress = 0;
                        animationStep++;
                    }
                    break;
                case 3:
                     if (progress < 1) {
                        drawArchitecture('comp2', 0, '3. 独立构件B也通过中央数据结构交互');
                    } else {
                        progress = 0;
                        animationStep++;
                    }
                    break;
                case 4: // Show data flow from Comp1 to DS (write)
                    if (progress < 1) {
                        drawArchitecture(null, progress, '4. 构件A写入数据到中央数据结构');
                    } else {
                        progress = 0;
                        animationStep++;
                    }
                    break;
                case 5: // Show data flow from DS to Comp2 (read)
                    if (progress < 1) {
                        drawArchitecture(null, progress, '5. 构件B从中央数据结构读取数据');
                    } else {
                        progress = 0;
                        animationStep++;
                    }
                    break;
                case 6: // Cycle through
                     if (progress < 1) {
                        drawArchitecture(null, progress, '6. 它们不直接通信，只通过"仓库"');
                    } else {
                        progress = 0;
                        animationStep = 4; // Loop back to data flow example
                    }
                    break;
            }

            animationFrameId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animationStep = 1;
            progress = 0;
            lastTimestamp = 0;
            startButton.disabled = true;
            resetButton.disabled = false;
            animationFrameId = requestAnimationFrame(animate);
        }

        function resetAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animationStep = 0;
            progress = 0;
            lastTimestamp = 0;
            drawArchitecture(); // Draw initial state immediately
            startButton.disabled = false;
            resetButton.disabled = true;
        }

        startButton.addEventListener('click', startAnimation);
        resetButton.addEventListener('click', resetAnimation);

        // Initial draw when page loads
        drawArchitecture();

        // Ensure canvas redraws on window resize (optional, for responsive design)
        window.addEventListener('resize', () => {
            // Recalculate positions based on new canvas size if necessary
            // For simplicity, we'll just redraw the current state.
            drawArchitecture();
        });

    </script>
</body>
</html> 