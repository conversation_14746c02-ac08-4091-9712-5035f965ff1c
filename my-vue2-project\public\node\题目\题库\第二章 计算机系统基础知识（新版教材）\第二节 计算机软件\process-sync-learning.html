<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程同步与PV操作 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .learning-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f8fafc;
            cursor: pointer;
            transition: box-shadow 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
            transform: translateY(-2px);
        }

        .explanation {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .process-box {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-title {
            font-size: 1.5em;
            color: #8b4513;
            margin-bottom: 20px;
            text-align: center;
        }

        .option {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            transform: translateX(10px);
            border-color: #667eea;
        }

        .option.selected {
            background: #667eea;
            color: white;
        }

        .option.correct {
            background: #48bb78;
            color: white;
        }

        .option.wrong {
            background: #f56565;
            color: white;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header fade-in">
            <h1>🔄 进程同步与PV操作</h1>
            <p>让我们用动画来理解进程间的协调与同步</p>
        </div>

        <!-- 基础概念介绍 -->
        <div class="learning-section fade-in">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="explanation">
                <p><strong>什么是进程同步？</strong></p>
                <p>想象你和朋友们一起做菜：</p>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>🥕 有人负责切菜（进程P1）</li>
                    <li>🍳 有人负责炒菜（进程P2、P3）</li>
                    <li>🍽️ 有人负责装盘（进程P4）</li>
                    <li>🧹 有人负责收拾（进程P5）</li>
                </ul>
                <p>炒菜的人必须等切菜完成，装盘的人必须等炒菜完成。这就是<span class="highlight">进程同步</span>！</p>
            </div>
        </div>

        <!-- PV操作介绍 -->
        <div class="learning-section fade-in">
            <h2 class="section-title">🔧 PV操作详解</h2>
            <div class="explanation">
                <p><strong>P操作（等待操作）：</strong></p>
                <p>🚦 就像红绿灯，如果信号量为0，进程就要等待（阻塞）</p>
                <p><strong>V操作（信号操作）：</strong></p>
                <p>✅ 完成工作后，发出信号告诉其他进程可以继续了</p>
            </div>

            <div class="canvas-container">
                <canvas id="pvCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startPVDemo()">开始PV操作演示</button>
                <button class="btn btn-secondary" onclick="resetPVDemo()">重置</button>
            </div>
        </div>

        <!-- 前趋图解析 -->
        <div class="learning-section fade-in">
            <h2 class="section-title">📊 前趋图分析</h2>
            <div class="explanation">
                <p>根据题目的前趋图：</p>
                <div style="text-align: center; margin: 20px 0;">
                    <span class="process-box" style="background: #4299e1;">P1</span>
                    <span style="font-size: 1.5em;">→</span>
                    <span class="process-box" style="background: #48bb78;">P2</span>
                    <span style="font-size: 1.5em;">→</span>
                    <span class="process-box" style="background: #ed8936;">P4</span>
                    <span style="font-size: 1.5em;">→</span>
                    <span class="process-box" style="background: #9f7aea;">P5</span>
                </div>
                <div style="text-align: center; margin: 20px 0;">
                    <span class="process-box" style="background: #4299e1;">P1</span>
                    <span style="font-size: 1.5em;">→</span>
                    <span class="process-box" style="background: #f56565;">P3</span>
                    <span style="font-size: 1.5em;">→</span>
                    <span class="process-box" style="background: #ed8936;">P4</span>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="precedenceCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startPrecedenceDemo()">演示执行顺序</button>
                <button class="btn btn-secondary" onclick="resetPrecedenceDemo()">重置</button>
            </div>
        </div>

        <!-- 信号量分析 -->
        <div class="learning-section fade-in">
            <h2 class="section-title">🎯 信号量分析</h2>
            <div class="explanation">
                <p><strong>信号量设置规则：</strong></p>
                <ul style="margin: 10px 0 10px 20px;">
                    <li>S1：P1完成后通知P2</li>
                    <li>S2：P1完成后通知P3</li>
                    <li>S3：P2完成后通知P4</li>
                    <li>S4：P3完成后通知P4</li>
                    <li>S5：P4完成后通知P5</li>
                </ul>
            </div>

            <div class="canvas-container">
                <canvas id="semaphoreCanvas" width="900" height="600"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startSemaphoreDemo()">演示信号量操作</button>
                <button class="btn btn-secondary" onclick="stepBySemaphore()">单步执行</button>
                <button class="btn btn-secondary" onclick="resetSemaphoreDemo()">重置</button>
            </div>
        </div>

        <!-- 答案解析 -->
        <div class="learning-section fade-in">
            <h2 class="section-title">💡 答案解析</h2>
            <div class="explanation">
                <p><strong>分析过程：</strong></p>
                <p>🔍 <strong>a处（P2开始前）：</strong>P2必须等待P1完成，所以需要<span class="highlight">P(S1)</span></p>
                <p>🔍 <strong>b处（P2结束后）：</strong>P2完成后要通知P4，所以需要<span class="highlight">V(S3)</span></p>
                <p>🔍 <strong>c处（P3开始前）：</strong>P3必须等待P1完成，所以需要<span class="highlight">P(S2)</span></p>
                <p>🔍 <strong>d处（P3结束后）：</strong>P3完成后要通知P4，所以需要<span class="highlight">V(S4)</span></p>
                <p><strong>正确答案：D</strong> - P(S1)和V(S3)、P(S2)和V(S4)</p>
            </div>
        </div>

        <!-- 互动测试 -->
        <div class="quiz-section fade-in">
            <h2 class="quiz-title">🎯 测试你的理解</h2>
            <div id="quiz-question">
                <p><strong>根据前趋图，P2进程在开始执行前需要什么操作？</strong></p>
                <div class="option" onclick="selectOption(this, false)">A. V(S1) - 发送信号</div>
                <div class="option" onclick="selectOption(this, true)">B. P(S1) - 等待P1完成</div>
                <div class="option" onclick="selectOption(this, false)">C. P(S3) - 等待P4完成</div>
                <div class="option" onclick="selectOption(this, false)">D. V(S3) - 通知P4</div>
            </div>
            <div id="quiz-result" style="margin-top: 20px; text-align: center; font-weight: bold;"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationStep = 0;
        let isAnimating = false;
        let semaphoreStep = 0;

        // PV操作演示
        function startPVDemo() {
            const canvas = document.getElementById('pvCanvas');
            const ctx = canvas.getContext('2d');

            if (isAnimating) return;
            isAnimating = true;

            let step = 0;
            const maxSteps = 8;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = '20px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('PV操作原理演示', canvas.width/2, 30);

                // 绘制信号量
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#2d3748';
                ctx.fillText('信号量 S = ' + Math.max(0, step - 4), 150, 80);

                // 绘制P操作
                if (step >= 1) {
                    ctx.fillStyle = '#e53e3e';
                    ctx.fillRect(50, 120, 200, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('P操作：等待信号', 150, 155);
                }

                // 绘制V操作
                if (step >= 5) {
                    ctx.fillStyle = '#38a169';
                    ctx.fillRect(550, 120, 200, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('V操作：发送信号', 650, 155);
                }

                // 绘制进程状态
                if (step >= 2 && step < 5) {
                    ctx.fillStyle = '#fbb6ce';
                    ctx.fillRect(50, 220, 200, 40);
                    ctx.fillStyle = '#2d3748';
                    ctx.textAlign = 'center';
                    ctx.fillText('进程阻塞等待...', 150, 245);
                }

                if (step >= 6) {
                    ctx.fillStyle = '#9ae6b4';
                    ctx.fillRect(50, 220, 200, 40);
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText('进程继续执行！', 150, 245);
                }

                // 绘制箭头和连接线
                if (step >= 3) {
                    drawArrow(ctx, 250, 150, 350, 150, '#4299e1');
                    ctx.fillStyle = '#4299e1';
                    ctx.textAlign = 'center';
                    ctx.fillText('等待信号', 300, 140);
                }

                if (step >= 6) {
                    drawArrow(ctx, 550, 150, 450, 150, '#48bb78');
                    ctx.fillStyle = '#48bb78';
                    ctx.fillText('信号到达', 500, 140);
                }

                step++;
                if (step <= maxSteps) {
                    setTimeout(animate, 1000);
                } else {
                    isAnimating = false;
                }
            }

            animate();
        }

        function resetPVDemo() {
            const canvas = document.getElementById('pvCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            isAnimating = false;
        }

        // 前趋图演示
        function startPrecedenceDemo() {
            const canvas = document.getElementById('precedenceCanvas');
            const ctx = canvas.getContext('2d');

            if (isAnimating) return;
            isAnimating = true;

            let step = 0;
            const processes = [
                {name: 'P1', x: 100, y: 200, color: '#4299e1', active: false},
                {name: 'P2', x: 300, y: 150, color: '#48bb78', active: false},
                {name: 'P3', x: 300, y: 250, color: '#f56565', active: false},
                {name: 'P4', x: 500, y: 200, color: '#ed8936', active: false},
                {name: 'P5', x: 700, y: 200, color: '#9f7aea', active: false}
            ];

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = '20px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('进程执行顺序演示', canvas.width/2, 30);

                // 绘制连接线
                ctx.strokeStyle = '#cbd5e0';
                ctx.lineWidth = 2;

                // P1 -> P2
                drawArrow(ctx, 150, 180, 250, 160, '#cbd5e0');
                // P1 -> P3
                drawArrow(ctx, 150, 220, 250, 240, '#cbd5e0');
                // P2 -> P4
                drawArrow(ctx, 350, 160, 450, 180, '#cbd5e0');
                // P3 -> P4
                drawArrow(ctx, 350, 240, 450, 220, '#cbd5e0');
                // P4 -> P5
                drawArrow(ctx, 550, 200, 650, 200, '#cbd5e0');

                // 激活对应的进程
                if (step >= 1) processes[0].active = true; // P1
                if (step >= 2) { processes[1].active = true; processes[2].active = true; } // P2, P3
                if (step >= 3) processes[3].active = true; // P4
                if (step >= 4) processes[4].active = true; // P5

                // 绘制进程
                processes.forEach(proc => {
                    ctx.fillStyle = proc.active ? proc.color : '#e2e8f0';
                    ctx.beginPath();
                    ctx.arc(proc.x, proc.y, 30, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = proc.active ? 'white' : '#a0aec0';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(proc.name, proc.x, proc.y + 5);

                    if (proc.active && step <= 4) {
                        // 添加脉冲效果
                        ctx.strokeStyle = proc.color;
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.arc(proc.x, proc.y, 35, 0, 2 * Math.PI);
                        ctx.stroke();
                    }
                });

                // 显示当前步骤说明
                ctx.fillStyle = '#2d3748';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                const stepTexts = [
                    '开始执行...',
                    'P1 执行完成',
                    'P2 和 P3 可以并行执行',
                    'P2 和 P3 都完成，P4 开始执行',
                    'P4 完成，P5 开始执行',
                    '所有进程执行完成！'
                ];
                ctx.fillText(stepTexts[step] || '', canvas.width/2, canvas.height - 30);

                step++;
                if (step <= 5) {
                    setTimeout(animate, 1500);
                } else {
                    isAnimating = false;
                }
            }

            animate();
        }

        function resetPrecedenceDemo() {
            const canvas = document.getElementById('precedenceCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            isAnimating = false;
        }

        // 信号量演示
        function startSemaphoreDemo() {
            semaphoreStep = 0;
            stepBySemaphore();

            const interval = setInterval(() => {
                stepBySemaphore();
                if (semaphoreStep >= 10) {
                    clearInterval(interval);
                }
            }, 2000);
        }

        function stepBySemaphore() {
            const canvas = document.getElementById('semaphoreCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = '20px Microsoft YaHei';
            ctx.fillStyle = '#4a5568';
            ctx.textAlign = 'center';
            ctx.fillText('信号量操作详细演示', canvas.width/2, 30);

            // 绘制进程和操作
            const processes = [
                {name: 'P1', x: 100, y: 100, operations: ['开始', 'V(S1)', 'V(S2)']},
                {name: 'P2', x: 300, y: 80, operations: ['P(S1)', '执行', 'V(S3)']},
                {name: 'P3', x: 300, y: 180, operations: ['P(S2)', '执行', 'V(S4)']},
                {name: 'P4', x: 500, y: 130, operations: ['P(S3)', 'P(S4)', '执行', 'V(S5)']},
                {name: 'P5', x: 700, y: 130, operations: ['P(S5)', '执行', '结束']}
            ];

            // 信号量状态
            const semaphores = {
                S1: semaphoreStep >= 2 ? 1 : 0,
                S2: semaphoreStep >= 3 ? 1 : 0,
                S3: semaphoreStep >= 5 ? 1 : 0,
                S4: semaphoreStep >= 7 ? 1 : 0,
                S5: semaphoreStep >= 9 ? 1 : 0
            };

            // 绘制信号量状态
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#2d3748';
            let yPos = 60;
            Object.entries(semaphores).forEach(([name, value]) => {
                ctx.fillText(`${name} = ${value}`, 50, yPos);
                yPos += 20;
            });

            // 绘制进程
            processes.forEach((proc, index) => {
                // 进程圆圈
                ctx.fillStyle = semaphoreStep > index * 2 ? '#4299e1' : '#e2e8f0';
                ctx.beginPath();
                ctx.arc(proc.x, proc.y, 25, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = semaphoreStep > index * 2 ? 'white' : '#a0aec0';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(proc.name, proc.x, proc.y + 4);

                // 操作列表
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Microsoft YaHei';
                proc.operations.forEach((op, opIndex) => {
                    const isActive = semaphoreStep === index * 2 + opIndex + 1;
                    ctx.fillStyle = isActive ? '#e53e3e' : '#718096';
                    ctx.fillText(op, proc.x, proc.y + 50 + opIndex * 15);
                });
            });

            // 绘制连接线和信号传递
            if (semaphoreStep >= 2) {
                drawArrow(ctx, 125, 100, 275, 85, '#48bb78');
                ctx.fillStyle = '#48bb78';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('S1信号', 200, 85);
            }

            if (semaphoreStep >= 3) {
                drawArrow(ctx, 125, 100, 275, 175, '#48bb78');
                ctx.fillStyle = '#48bb78';
                ctx.fillText('S2信号', 200, 175);
            }

            // 显示当前步骤说明
            const stepDescriptions = [
                '初始状态：所有信号量为0',
                'P1开始执行',
                'P1执行V(S1)，通知P2可以开始',
                'P1执行V(S2)，通知P3可以开始',
                'P2执行P(S1)成功，开始执行',
                'P2执行V(S3)，通知P4',
                'P3执行P(S2)成功，开始执行',
                'P3执行V(S4)，通知P4',
                'P4等待P2和P3都完成',
                'P4执行V(S5)，通知P5',
                'P5开始执行，所有进程完成'
            ];

            ctx.fillStyle = '#2d3748';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(stepDescriptions[semaphoreStep] || '', canvas.width/2, canvas.height - 30);

            semaphoreStep++;
        }

        function resetSemaphoreDemo() {
            const canvas = document.getElementById('semaphoreCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            semaphoreStep = 0;
        }

        // 绘制箭头函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;

            // 绘制线条
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 绘制箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 10;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI/6),
                      toY - arrowLength * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI/6),
                      toY - arrowLength * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }

        // 测试题功能
        function selectOption(element, isCorrect) {
            // 移除所有选项的选中状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'wrong');
            });

            // 标记选中的选项
            element.classList.add('selected');

            setTimeout(() => {
                // 显示正确答案
                document.querySelectorAll('.option').forEach((opt, index) => {
                    if (index === 1) { // 正确答案是B
                        opt.classList.add('correct');
                    } else if (opt.classList.contains('selected') && !isCorrect) {
                        opt.classList.add('wrong');
                    }
                });

                // 显示结果
                const resultDiv = document.getElementById('quiz-result');
                if (isCorrect) {
                    resultDiv.innerHTML = '🎉 正确！P2需要等待P1完成，所以要执行P(S1)操作';
                    resultDiv.style.color = '#48bb78';
                } else {
                    resultDiv.innerHTML = '❌ 不对哦！P2需要等待P1的信号，应该是P(S1)';
                    resultDiv.style.color = '#f56565';
                }
            }, 500);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加淡入动画
            const sections = document.querySelectorAll('.fade-in');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
