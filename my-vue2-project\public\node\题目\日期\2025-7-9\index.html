<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能机器人系统架构质量属性学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 30px auto;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            padding: 40px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            background-color: #e8f5e9; /* Light green for sections */
            border: 1px solid #c8e6c9;
        }
        .question-section p, .explanation-section p {
            margin-bottom: 15px;
        }
        .options {
            list-style: none;
            padding: 0;
            margin-top: 20px;
        }
        .options li {
            background-color: #fff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            margin-bottom: 10px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        .options li:hover {
            background-color: #e0f2f7; /* Light blue on hover */
            border-color: #a7d9f0;
        }
        .options li.correct {
            background-color: #d4edda; /* Green for correct */
            border-color: #28a745;
            font-weight: bold;
        }
        .options li.wrong {
            background-color: #f8d7da; /* Red for wrong */
            border-color: #dc3545;
            font-weight: bold;
        }
        .option-label {
            display: inline-block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            border: 1px solid #ccc;
            border-radius: 50%;
            margin-right: 15px;
            background-color: #f0f0f0;
            color: #555;
        }
        .explanation-content {
            margin-top: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dcdcdc;
        }
        .explanation-content h3 {
            color: #3f51b5; /* Deeper blue for explanation titles */
            text-align: left;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .interactive-area {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background-color: #e3f2fd; /* Light blue for interactive area */
            border-radius: 10px;
            border: 1px solid #bbdefb;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 2px solid #a7d9f0;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        button {
            padding: 12px 25px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .game-controls {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能机器人系统架构质量属性学习</h1>

        <div class="question-section">
            <h2>问题1：单选题</h2>
            <p>某公司欲开发一个智能机器人系统，在架构设计阶段，公司的架构师识别出3个核心质量属性场景。其中：</p>
            <ul>
                <li>“机器人系统主电源断电后，能够在10秒内自动启动备用电源并进行切换，恢复正常运行” 主要与（）质量属性相关，通常可采用（）架构策略实现该属性；</li>
                <li>“机器人在正常运动过程中如果发现前方2米内有人或者障碍物，应在1秒内停止并在2秒内选择一条新的运行路径” 主要与（）质量属性相关，通常可采用（）架构策略实现该属性；</li>
                <li>“对机器人的远程控制命令应该进行加密，从而能够抵挡恶意的入侵破坏行为，并对攻击进行报警和记录” 主要与（）质量属性相关，通常可采用（）架构策略实现该属性。</li>
            </ul>
            <p>第一个场景描述主要与以下哪个质量属性相关？</p>
            <ul class="options" id="options">
                <li data-value="可用性"><span class="option-label">A</span>可用性</li>
                <li data-value="性能"><span class="option-label">B</span>性能</li>
                <li data-value="易用性"><span class="option-label">C</span>易用性</li>
                <li data-value="可修改性"><span class="option-label">D</span>可修改性</li>
            </ul>
        </div>

        <div class="explanation-section">
            <h2>知识点解释与互动</h2>
            <div class="explanation-content">
                <div id="availability-explanation">
                    <h3>可用性 (Availability)</h3>
                    <p>“可用性”指的是系统在面对故障时，能够持续提供服务的能力。它关注的是系统在给定时间段内正常运行的比例。当系统发生故障（例如电源断电）时，如果能够快速恢复并继续运行，那么它的可用性就很高。常见的实现策略包括冗余备份、故障转移、心跳检测等。</p>
                    <div class="interactive-area">
                        <h4>互动演示：可用性 - 机器人电源故障恢复</h4>
                        <canvas id="availabilityCanvas" width="500" height="200"></canvas>
                        <button id="powerOutageBtn">模拟断电</button>
                    </div>
                </div>

                <div id="performance-explanation" style="display: none;">
                    <h3>性能 (Performance)</h3>
                    <p>“性能”指的是系统在特定负载下，及时响应并完成操作的能力。它关注的是系统的响应时间、吞吐量、资源利用率等。例如，机器人需要快速识别障碍物并及时调整路径，这都属于性能的范畴。常见的实现策略包括负载均衡、缓存、资源调度、并发处理等。</p>
                    <div class="interactive-area">
                        <h4>互动演示：性能 - 机器人避障寻路游戏</h4>
                        <canvas id="performanceCanvas" width="500" height="250"></canvas>
                        <div class="game-controls">
                            <button id="startRobotGame">开始避障</button>
                            <button id="resetRobotGame">重置</button>
                        </div>
                    </div>
                </div>

                <div id="security-explanation" style="display: none;">
                    <h3>安全性 (Security)</h3>
                    <p>“安全性”指的是系统保护数据和资源免受未经授权的访问、使用、泄露、破坏或修改的能力。它关注的是系统的保密性、完整性、可用性、不可否认性等方面。例如，对远程控制命令进行加密，防止恶意入侵，都属于安全性的范畴。常见的实现策略包括加密、认证、授权、审计日志、防火墙等。</p>
                    <div class="interactive-area">
                        <h4>互动演示：安全性 - 命令加密传输</h4>
                        <canvas id="securityCanvas" width="500" height="200"></canvas>
                        <button id="sendSecureCommand">发送加密命令</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const optionsList = document.getElementById('options');
        const availabilityExplanation = document.getElementById('availability-explanation');
        const performanceExplanation = document.getElementById('performance-explanation');
        const securityExplanation = document.getElementById('security-explanation');

        const availabilityCanvas = document.getElementById('availabilityCanvas');
        const availCtx = availabilityCanvas.getContext('2d');
        const powerOutageBtn = document.getElementById('powerOutageBtn');

        const performanceCanvas = document.getElementById('performanceCanvas');
        const perfCtx = performanceCanvas.getContext('2d');
        const startRobotGameBtn = document.getElementById('startRobotGame');
        const resetRobotGameBtn = document.getElementById('resetRobotGame');

        const securityCanvas = document.getElementById('securityCanvas');
        const secCtx = securityCanvas.getContext('2d');
        const sendSecureCommandBtn = document.getElementById('sendSecureCommand');

        let isOptionSelected = false; // 标记是否已选择选项

        // 答案和解释映射
        const correctOption = '可用性';
        const explanations = {
            '可用性': availabilityExplanation,
            '性能': performanceExplanation,
            '安全性': securityExplanation
            // 易用性和可修改性没有专门的解释块，因为题目主要涉及前三者
        };

        // 初始化显示第一个解释
        function initializeExplanations() {
            for (let key in explanations) {
                if (explanations[key]) {
                    explanations[key].style.display = 'none';
                }
            }
            if (availabilityExplanation) {
                availabilityExplanation.style.display = 'block'; // 默认显示可用性解释
            }
        }
        initializeExplanations(); // 调用初始化函数

        // 选项点击事件处理
        optionsList.addEventListener('click', (event) => {
            if (isOptionSelected) return; // 如果已经选择过，则不响应点击

            const targetLi = event.target.closest('li');
            if (targetLi && targetLi.dataset.value) {
                const selectedValue = targetLi.dataset.value;

                // 移除之前的高亮
                Array.from(optionsList.children).forEach(li => {
                    li.classList.remove('correct', 'wrong');
                });

                if (selectedValue === correctOption) {
                    targetLi.classList.add('correct');
                    alert('恭喜你，选择正确！');
                    showExplanation(correctOption); // 显示正确答案的解释
                } else {
                    targetLi.classList.add('wrong');
                    alert('很遗憾，选择错误。正确答案是【可用性】。请查看下方解释。');
                    showExplanation(correctOption); // 错误也显示正确答案的解释
                }
                isOptionSelected = true; // 标记已选择
            }
        });

        // 显示对应解释的函数
        function showExplanation(type) {
            for (let key in explanations) {
                if (explanations[key]) {
                    explanations[key].style.display = 'none'; // 隐藏所有解释
                }
            }
            if (explanations[type]) {
                explanations[type].style.display = 'block'; // 显示目标解释
            }
        }

        // --- 可用性动画 ---
        let robotX = 100;
        let powerStatus = 'main'; // 'main', 'backup', 'off'
        let recoveryTimer = null;
        let animationFrameId = null;

        function drawAvailability() {
            availCtx.clearRect(0, 0, availabilityCanvas.width, availabilityCanvas.height);

            // 绘制主电源
            availCtx.fillStyle = powerStatus === 'main' ? '#4CAF50' : '#ccc'; // 绿色表示开启，灰色表示关闭
            availCtx.fillRect(50, 50, 40, 80);
            availCtx.fillStyle = 'white';
            availCtx.fillText('主电源', 55, 95);

            // 绘制备用电源
            availCtx.fillStyle = powerStatus === 'backup' ? '#2196F3' : '#ccc'; // 蓝色表示开启，灰色表示关闭
            availCtx.fillRect(150, 50, 40, 80);
            availCtx.fillStyle = 'white';
            availCtx.fillText('备用电源', 155, 95);

            // 绘制机器人
            availCtx.fillStyle = '#FF9800'; // 橙色机器人
            availCtx.beginPath();
            availCtx.arc(robotX, 150, 20, 0, Math.PI * 2);
            availCtx.fill();
            availCtx.fillStyle = 'white';
            availCtx.font = '14px Arial';
            availCtx.fillText('机器人', robotX - 18, 155);

            // 机器人运行状态
            if (powerStatus === 'main' || powerStatus === 'backup') {
                availCtx.fillStyle = '#4CAF50';
                availCtx.fillText('运行中...', robotX + 30, 150);
                robotX = (robotX + 1) % (availabilityCanvas.width - 50); // 简单移动
                if (robotX < 100) robotX = 100; // 限制在画布内
            } else {
                availCtx.fillStyle = '#F44336';
                availCtx.fillText('已停止', robotX + 30, 150);
            }

            animationFrameId = requestAnimationFrame(drawAvailability);
        }

        powerOutageBtn.addEventListener('click', () => {
            if (powerStatus === 'off') return; // 避免重复点击

            powerStatus = 'off';
            cancelAnimationFrame(animationFrameId); // 停止当前动画
            drawAvailability(); // 立即更新状态

            // 模拟10秒后启动备用电源
            clearTimeout(recoveryTimer);
            recoveryTimer = setTimeout(() => {
                powerStatus = 'backup';
                alert('备用电源已启动，系统恢复正常运行！');
                animationFrameId = requestAnimationFrame(drawAvailability); // 恢复动画
            }, 10000); // 10秒
        });

        // 初始绘制可用性动画
        drawAvailability();

        // --- 性能游戏 ---
        let robotPerfX = 50;
        let robotPerfY = 125;
        let robotPerfSpeed = 2;
        let obstacleX = 350;
        let obstacleY = 100;
        let obstacleWidth = 30;
        let obstacleHeight = 80;
        let isRobotRunning = false;
        let pathFound = false;
        let stopTime = 0;
        let newPathTime = 0;
        let performanceAnimationFrameId = null;

        function drawPerformance() {
            perfCtx.clearRect(0, 0, performanceCanvas.width, performanceCanvas.height);

            // 绘制障碍物
            perfCtx.fillStyle = '#F44336'; // 红色障碍物
            perfCtx.fillRect(obstacleX, obstacleY, obstacleWidth, obstacleHeight);

            // 绘制机器人
            perfCtx.fillStyle = '#FFC107'; // 黄色机器人
            perfCtx.beginPath();
            perfCtx.arc(robotPerfX, robotPerfY, 15, 0, Math.PI * 2);
            perfCtx.fill();
            perfCtx.fillStyle = 'black';
            perfCtx.font = '12px Arial';
            perfCtx.fillText('机器人', robotPerfX - 15, robotPerfY + 30);

            if (isRobotRunning) {
                // 机器人移动
                robotPerfX += robotPerfSpeed;

                // 检查是否接近障碍物 (2米内)
                if (robotPerfX + 15 > obstacleX - 40 && robotPerfX + 15 < obstacleX + obstacleWidth && !pathFound) {
                    if (stopTime === 0) {
                        stopTime = performance.now(); // 记录停止时间
                        robotPerfSpeed = 0; // 停止移动
                        perfCtx.fillStyle = '#F44336';
                        perfCtx.fillText('检测到障碍！', robotPerfX + 20, robotPerfY - 10);
                    }
                    const stopDuration = performance.now() - stopTime;
                    if (stopDuration > 1000) { // 超过1秒未停止
                        perfCtx.fillStyle = '#F44336';
                        perfCtx.fillText('停止超时！', performanceCanvas.width / 2 - 40, 20);
                    } else {
                        perfCtx.fillStyle = '#4CAF50';
                        perfCtx.fillText(`停止时间: ${(stopDuration / 1000).toFixed(2)}s`, performanceCanvas.width / 2 - 60, 20);
                    }

                    // 模拟选择新路径
                    if (stopTime !== 0 && newPathTime === 0) {
                        newPathTime = performance.now();
                        setTimeout(() => {
                            if (robotPerfY === 125) {
                                robotPerfY = 50; // 向上移动
                            } else {
                                robotPerfY = 125; // 向下移动
                            }
                            pathFound = true;
                            robotPerfSpeed = 2; // 继续移动
                            const pathDuration = performance.now() - newPathTime;
                            if (pathDuration > 2000) { // 超过2秒未找到新路径
                                perfCtx.fillStyle = '#F44336';
                                perfCtx.fillText('寻路超时！', performanceCanvas.width / 2 - 40, 40);
                            } else {
                                perfCtx.fillStyle = '#4CAF50';
                                perfCtx.fillText(`寻路时间: ${(pathDuration / 1000).toFixed(2)}s`, performanceCanvas.width / 2 - 60, 40);
                            }
                        }, 1000); // 模拟1秒后选择新路径 (用于测试2秒寻路)
                    }
                }

                // 机器人到达终点或离开障碍物
                if (robotPerfX > performanceCanvas.width - 50 || (pathFound && robotPerfX > obstacleX + obstacleWidth + 30)) {
                    isRobotRunning = false;
                    alert('机器人成功避开障碍！');
                    cancelAnimationFrame(performanceAnimationFrameId);
                }

                // 循环动画
                performanceAnimationFrameId = requestAnimationFrame(drawPerformance);
            }
        }

        startRobotGameBtn.addEventListener('click', () => {
            if (!isRobotRunning) {
                isRobotRunning = true;
                stopTime = 0;
                newPathTime = 0;
                pathFound = false;
                robotPerfX = 50;
                robotPerfY = 125;
                robotPerfSpeed = 2;
                performanceAnimationFrameId = requestAnimationFrame(drawPerformance);
            }
        });

        resetRobotGameBtn.addEventListener('click', () => {
            cancelAnimationFrame(performanceAnimationFrameId);
            isRobotRunning = false;
            robotPerfX = 50;
            robotPerfY = 125;
            stopTime = 0;
            newPathTime = 0;
            pathFound = false;
            drawPerformance(); // 绘制初始状态
        });

        // 初始绘制性能游戏场景
        drawPerformance();

        // --- 安全性动画 ---
        let commandStatus = 'idle'; // 'idle', 'sending', 'encrypting', 'decrypting', 'received'
        let commandX = 50;
        let commandText = '原始命令';
        let securityAnimationFrameId = null;

        function drawSecurity() {
            secCtx.clearRect(0, 0, securityCanvas.width, securityCanvas.height);

            // 绘制发送端
            secCtx.fillStyle = '#607D8B';
            secCtx.fillRect(20, 50, 80, 100);
            secCtx.fillStyle = 'white';
            secCtx.fillText('发送端', 35, 95);

            // 绘制接收端
            secCtx.fillStyle = '#607D8B';
            secCtx.fillRect(securityCanvas.width - 100, 50, 80, 100);
            secCtx.fillStyle = 'white';
            secCtx.fillText('接收端', securityCanvas.width - 85, 95);

            // 绘制命令
            secCtx.fillStyle = '#FF5722';
            secCtx.beginPath();
            secCtx.arc(commandX, 100, 15, 0, Math.PI * 2);
            secCtx.fill();
            secCtx.fillStyle = 'black';
            secCtx.font = '14px Arial';
            secCtx.fillText(commandText, commandX - 25, 130);

            switch (commandStatus) {
                case 'sending':
                    commandX += 2;
                    if (commandX >= 150) {
                        commandStatus = 'encrypting';
                        commandText = '加密中...';
                    }
                    break;
                case 'encrypting':
                    // 模拟加密过程
                    secCtx.fillStyle = '#4CAF50'; // 绿色表示加密
                    secCtx.fillText('加密中...', 180, 40);
                    setTimeout(() => {
                        commandText = '********'; // 加密后的命令
                        commandStatus = 'transferring';
                    }, 1000); // 1秒加密时间
                    break;
                case 'transferring':
                    commandX += 3;
                    if (commandX >= securityCanvas.width - 150) {
                        commandStatus = 'decrypting';
                        commandText = '解密中...';
                    }
                    break;
                case 'decrypting':
                    // 模拟解密过程
                    secCtx.fillStyle = '#2196F3'; // 蓝色表示解密
                    secCtx.fillText('解密中...', securityCanvas.width - 250, 40);
                    setTimeout(() => {
                        commandText = '执行命令'; // 解密后的命令
                        commandStatus = 'received';
                    }, 1000); // 1秒解密时间
                    break;
                case 'received':
                    secCtx.fillStyle = '#4CAF50';
                    secCtx.fillText('命令已执行！', securityCanvas.width - 120, 130);
                    break;
                default:
                    break;
            }

            securityAnimationFrameId = requestAnimationFrame(drawSecurity);
        }

        sendSecureCommandBtn.addEventListener('click', () => {
            if (commandStatus === 'idle' || commandStatus === 'received') {
                commandX = 50;
                commandText = '原始命令';
                commandStatus = 'sending';
                cancelAnimationFrame(securityAnimationFrameId);
                securityAnimationFrameId = requestAnimationFrame(drawSecurity);
            }
        });

        // 初始绘制安全性动画
        drawSecurity();

        // 隐藏除可用性之外的解释区域，确保首次加载时只有可用性是可见的
        // 这一行在前面的步骤中已经存在，无需重复添加，保留检查即可
        // performanceExplanation.style.display = 'none';
        // securityExplanation.style.display = 'none';
    </script>
</body>
</html> 