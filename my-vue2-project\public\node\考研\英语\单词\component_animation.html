<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Component</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #FF6F00; /* Engineering Orange */
            --secondary-color: #455A64; /* Blueprint Blue-Grey */
            --glow-color: #FFAB40;
            --light-bg: #ECEFF1;
            --panel-bg: #ffffff;
            --text-color: #263238;
            --canvas-bg: #37474F; 
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #CFD8DC;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
            border-right: 2px solid #B0BEC5;
        }

        .word-panel h1 {
            font-family: 'Roboto Mono', monospace;
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--primary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding: 10px 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            background: #CFD8DC;
            border-radius: 5px;
            color: #37474F;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #CFD8DC;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            font-family: 'Roboto Mono', monospace;
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--secondary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--secondary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--secondary-color);
            color: white;
            box-shadow: 0 0 10px var(--glow-color);
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 20px var(--glow-color);
            z-index: 10;
        }
        .control-button:hover { background-color: #E65100; }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>component</h1>
            <p class="pronunciation">[kəmˈpəʊnənt]</p>
            <div class="details">
                <p><strong>词性：</strong> 名词 (n.) / 形容词 (adj.)</p>
                <p><strong>含义：</strong> 成分, 零部件 / 组成的</p>
                <div class="example">
                    <p><strong>例句：</strong> Trust is a vital component of a healthy relationship.</p>
                    <p><strong>翻译：</strong> 信任是健康关系的重要组成部分。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="com-game">com- (together, 共同)</button>
                    <button class="morpheme-btn" data-activity="pon-game">-pon- (to put, 放置)</button>
                    <button class="morpheme-btn" data-activity="ent-game">-ent (赋予词性)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：总装时刻</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Assemble!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let elements = [];

        function drawGrid() {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            for(let x=0; x<canvas.width; x+=20) {
                ctx.beginPath(); ctx.moveTo(x, 0); ctx.lineTo(x, canvas.height); ctx.stroke();
            }
            for(let y=0; y<canvas.height; y+=20) {
                ctx.beginPath(); ctx.moveTo(0, y); ctx.lineTo(canvas.width, y); ctx.stroke();
            }
        }

        function welcomeScreen() {
            ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--canvas-bg');
            ctx.fillRect(0,0,canvas.width,canvas.height);
            drawGrid();
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 32px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('🏆 10词挑战完成！🏆', canvas.width / 2, canvas.height / 2 - 30);
            ctx.font = '20px Roboto';
            ctx.fillText('这是最后一个单词，开始我们的"总装"吧！', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Morpheme Games ---
        class Particle {
            constructor(x, y, vx, vy, color, size) { this.x=x; this.y=y; this.vx=vx; this.vy=vy; this.color=color; this.size=size; }
            update() { this.x += this.vx; this.y += this.vy; }
            draw() { ctx.fillStyle=this.color; ctx.beginPath(); ctx.arc(this.x, this.y, this.size, 0, Math.PI*2); ctx.fill(); }
        }

        function initComGame() {
            elements = [];
            for(let i=0; i<50; i++) elements.push(new Particle(Math.random()*canvas.width, Math.random()*canvas.height, 0, 0, '#FFAB40', 3));
            controlBtn.textContent = '汇集 (com-)';
            controlBtn.classList.remove('hidden');
            let isConverging = false;
            controlBtn.onclick = () => { isConverging = true; };
            
            function animateComGame() {
                ctx.fillStyle = 'rgba(55, 71, 79, 0.2)'; ctx.fillRect(0,0,canvas.width,canvas.height); drawGrid();
                if(isConverging) {
                    elements.forEach(p => {
                        p.vx += (canvas.width/2 - p.x) * 0.001;
                        p.vy += (canvas.height/2 - p.y) * 0.001;
                    });
                }
                elements.forEach(p => p.update());
                elements.forEach(p => p.draw());
                animationFrameId = requestAnimationFrame(animateComGame);
            }
            animateComGame();
        }
        
        class PuzzlePiece {
            constructor(x, y, tx, ty, w, h, color) { this.x=x; this.y=y; this.tx=tx; this.ty=ty; this.w=w; this.h=h; this.color=color; this.placed=false;}
            update() {
                if(!this.placed) {
                    this.x += (this.tx - this.x) * 0.1;
                    this.y += (this.ty - this.y) * 0.1;
                    if(Math.abs(this.x-this.tx)<1 && Math.abs(this.y-this.ty)<1) this.placed = true;
                }
            }
            draw() { ctx.fillStyle=this.placed ? '#FF6F00' : this.color; ctx.fillRect(this.x,this.y,this.w,this.h); }
            drawTarget() { ctx.strokeStyle = '#B0BEC5'; ctx.lineWidth=2; ctx.strokeRect(this.tx,this.ty,this.w,this.h); }
        }

        function initPonGame() {
             elements = [
                new PuzzlePiece(100, 100, canvas.width/2 - 50, canvas.height/2 - 50, 50, 50, '#78909C'),
                new PuzzlePiece(100, 200, canvas.width/2, canvas.height/2 - 50, 50, 100, '#78909C'),
                new PuzzlePiece(100, 350, canvas.width/2 - 50, canvas.height/2, 50, 50, '#78909C'),
             ];
             controlBtn.textContent = '放置 (pon-)';
             controlBtn.classList.remove('hidden');
             let hasClicked = false;
             controlBtn.onclick = () => { hasClicked = true; };

             function animatePonGame() {
                ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--canvas-bg'); ctx.fillRect(0,0,canvas.width,canvas.height); drawGrid();
                elements.forEach(p => p.drawTarget());
                if(hasClicked) elements.forEach(p => p.update());
                elements.forEach(p => p.draw());
                animationFrameId = requestAnimationFrame(animatePonGame);
             }
             animatePonGame();
        }
        
        function initEntGame() {
            const shape = {
                x: canvas.width / 2,
                y: canvas.height / 2,
                w: 120,
                h: 120,
                color: '#90A4AE',
                draw() {
                    ctx.fillStyle = this.color;
                    ctx.fillRect(this.x - this.w / 2, this.y - this.h / 2, this.w, this.h);
                    ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--canvas-bg');
                    ctx.font = 'italic 20px Roboto';
                    ctx.textAlign = 'center';
                    ctx.fillText("... an object ...", this.x, this.y);
                }
            };

            const label = {
                x: canvas.width / 2,
                y: canvas.height - 60, // Start from bottom
                tx: canvas.width / 2,
                ty: canvas.height / 2 + 85, // Target below the shape
                text: '[ Noun / Adjective ]',
                state: 'idle',
                update() {
                    if (this.state === 'moving') {
                        this.x += (this.tx - this.x) * 0.05;
                        this.y += (this.ty - this.y) * 0.05;
                        if (Math.abs(this.x - this.tx) < 1 && Math.abs(this.y - this.ty) < 1) {
                            this.state = 'attached';
                            shape.color = '#455A64'; // Change shape color on attachment
                        }
                    }
                },
                draw() {
                    ctx.fillStyle = this.state === 'attached' ? '#FF6F00' : '#FFAB40';
                    ctx.font = 'bold 24px Roboto Mono';
                    ctx.textAlign = 'center';
                    ctx.fillText(this.text, this.x, this.y);
                }
            };

            elements = [shape, label];

            controlBtn.textContent = '赋予词性 (-ent)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                if (label.state === 'idle') label.state = 'moving';
            };

            function animateEntGame() {
                ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--canvas-bg');
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                drawGrid();

                elements.forEach(el => {
                    if (el.update) el.update();
                    el.draw();
                });

                animationFrameId = requestAnimationFrame(animateEntGame);
            }
            animateEntGame();
        }

        // --- Full Animation ---
        function initFullAnimation() {
            elements = [
                { x: 50, y: 50, tx: canvas.width/2, ty: canvas.height/2, w: 40, h:40, state: 'idle'}, // Gear 1
                { x: canvas.width-100, y: 50, tx: canvas.width/2-50, ty: canvas.height/2-50, w: 20, h:100, state: 'idle'}, // Block 1
                { x: 50, y: canvas.height-100, tx: canvas.width/2+50, ty: canvas.height/2-50, w: 20, h:100, state: 'idle'}, // Block 2
                { x: canvas.width-100, y: canvas.height-100, tx: canvas.width/2, ty: canvas.height/2-80, w: 100, h:20, state: 'idle'}, // Top
            ];
            controlBtn.textContent = 'Assemble!';
            controlBtn.classList.remove('hidden');
            
            controlBtn.onclick = () => { elements.forEach(e => e.state = 'moving'); };
            let rotation = 0;

            function animateFull() {
                ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--canvas-bg'); ctx.fillRect(0,0,canvas.width,canvas.height); drawGrid();
                
                let allAssembled = true;
                elements.forEach(p => {
                    if (p.state === 'moving') {
                        allAssembled = false;
                        p.x += (p.tx - p.x) * 0.05;
                        p.y += (p.ty - p.y) * 0.05;
                        if(Math.abs(p.x - p.tx) < 1 && Math.abs(p.y - p.ty) < 1) p.state = 'assembled';
                    }
                    ctx.fillStyle = p.state === 'assembled' ? '#FF6F00' : '#90A4AE';
                    ctx.save();
                    ctx.translate(p.x, p.y);
                    // only rotate the gear
                    if(elements.indexOf(p) === 0 && allAssembled) ctx.rotate((rotation += 0.01));
                    ctx.fillRect(-p.w/2, -p.h/2, p.w, p.h);
                    ctx.restore();
                });

                animationFrameId = requestAnimationFrame(animateFull);
            }
            animateFull();
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                if (activity === 'com-game') initComGame();
                else if (activity === 'pon-game') initPonGame();
                else if (activity === 'ent-game') initEntGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html> 