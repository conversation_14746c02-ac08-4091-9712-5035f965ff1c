<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>hashCode() 和 equals() 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #1a73e8;
            border-bottom: 2px solid #e8e8e8;
            padding-bottom: 10px;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            height: auto;
        }
        .controls, .object-controls {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s, transform 0.1s;
        }
        button:hover {
            background-color: #155ab6;
        }
        button:active {
            transform: scale(0.98);
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #explanation {
            margin-top: 15px;
            padding: 15px;
            background-color: #e8f0fe;
            border: 1px solid #d2e3fc;
            border-radius: 4px;
            min-height: 50px;
            font-size: 1.1em;
            transition: background-color 0.5s;
        }
        .object-info {
            font-family: 'Courier New', Courier, monospace;
            background: #eee;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .highlight {
            background-color: #fffbe6;
            border-color: #ffe58f;
        }
    </style>
</head>
<body>

    <h1>Java hashCode() 和 equals() 探秘</h1>

    <div class="container">
        <h2>第一步：什么是 hashCode()?</h2>
        <p>`hashCode()` 的作用是获取一个独一无二的整数，我们称之为"哈希码"。这个哈希码主要用来决定对象在"哈希表"这种数据结构中的存储位置，就像是储物柜的编号。</p>
        <div class="controls">
            <input type="text" id="hashInput" value="Hello" placeholder="输入任意文本">
            <button id="hashCodeBtn">1. 计算哈希码</button>
            <span id="hashCodeOutput" class="object-info"></span>
        </div>
        <p><strong>关键点：</strong> <code>hashCode()</code> 是一个对象的"身份证号"，但不同对象也可能有相同的身份证号（这被称为"哈希冲突"）。</p>
    </div>

    <div class="container">
        <h2>第二步：在 HashSet 中如何工作？</h2>
        <p>让我们通过向 <code>HashSet</code>（一种不允许重复元素的集合）中添加对象，来观察 <code>hashCode()</code> 和 <code>equals()</code> 如何协同工作。下面的动画将模拟这个过程。</p>
        <canvas id="hashSetCanvas" width="850" height="300"></canvas>
        <div id="explanation">
            点击下面的按钮，尝试添加一个对象到哈希表中。
        </div>
        <div class="object-controls">
            <button class="addObjectBtn" data-obj-index="0">添加 User("John", 101)</button>
            <button class="addObjectBtn" data-obj-index="1">添加 User("Jane", 102)</button>
            <button class="addObjectBtn" data-obj-index="2">添加 User("Mike", 103)</button>
            <button class="addObjectBtn" data-obj-index="3">添加 User("John", 101) <small>(重复)</small></button>
            <button class="addObjectBtn" data-obj-index="4">添加 User("Kate", 201) <small>(哈希冲突)</small></button>
            <button id="resetBtn">重置</button>
        </div>
        <h4>💡 规则说明:</h4>
        <ul>
            <li><code>User("John", 101)</code> 和 <code>User("Kate", 201)</code> 这两个对象，我们特意设计了它们的 <code>hashCode()</code> 返回相同的值，用以演示"哈希冲突"。</li>
            <li>当你尝试添加一个完全重复的对象时（例如，再次添加 <code>User("John", 101)"</code>），<code>hashCode()</code> 会相同，并且 <code>equals()</code> 方法检查后会发现它们是相等的，于是添加操作会被拒绝。</li>
        </ul>
    </div>

<script>
// --- Part 1: Simple hashCode calculation ---
const hashInput = document.getElementById('hashInput');
const hashCodeBtn = document.getElementById('hashCodeBtn');
const hashCodeOutput = document.getElementById('hashCodeOutput');

function simpleStringHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash |= 0; // Convert to 32bit integer
    }
    return hash;
}

hashCodeBtn.addEventListener('click', () => {
    const text = hashInput.value || "Hello";
    const hashCode = simpleStringHash(text);
    hashCodeOutput.textContent = `"${text}".hashCode() -> ${hashCode}`;
});
// Trigger on load
hashCodeBtn.click();


// --- Part 2: HashSet Simulation ---
const canvas = document.getElementById('hashSetCanvas');
const ctx = canvas.getContext('2d');
const explanationDiv = document.getElementById('explanation');
const addObjectBtns = document.querySelectorAll('.addObjectBtn');
const resetBtn = document.getElementById('resetBtn');

const NUM_BUCKETS = 7;
const BUCKET_WIDTH = 100;
const BUCKET_HEIGHT = 200;
const BUCKET_MARGIN = 20;

let buckets = Array(NUM_BUCKETS).fill(null).map(() => []);

const userObjects = [
    { name: "John", id: 101, color: "#3498db" }, // hash -> 1
    { name: "Jane", id: 102, color: "#e74c3c" }, // hash -> 2
    { name: "Mike", id: 103, color: "#2ecc71" }, // hash -> 3
    { name: "John", id: 101, color: "#3498db" }, // hash -> 1, duplicate
    { name: "Kate", id: 201, color: "#f1c40f" }, // hash -> 1, collision
];

// Custom hash code function for our objects
function getObjectHashCode(obj) {
    // A simple hash function for demonstration.
    // "John" and "Kate" will have a collision.
    if (obj.name === "Kate") return simpleStringHash("John") + obj.id - 101;
    return simpleStringHash(obj.name) + obj.id;
}

// Custom equals function
function areObjectsEqual(obj1, obj2) {
    return obj1.name === obj2.name && obj1.id === obj2.id;
}

function drawBuckets() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < NUM_BUCKETS; i++) {
        const x = BUCKET_MARGIN + i * (BUCKET_WIDTH + BUCKET_MARGIN);
        ctx.strokeStyle = '#aaa';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, 50, BUCKET_WIDTH, BUCKET_HEIGHT);
        ctx.fillStyle = '#666';
        ctx.textAlign = 'center';
        ctx.font = '14px sans-serif';
        ctx.fillText(`索引 ${i}`, x + BUCKET_WIDTH / 2, 40);
    }
}

function drawObjectsInBuckets() {
    buckets.forEach((bucket, index) => {
        const bucketX = BUCKET_MARGIN + index * (BUCKET_WIDTH + BUCKET_MARGIN);
        bucket.forEach((obj, pos) => {
            const x = bucketX + 15;
            const y = 65 + pos * 35;
            drawObject(x, y, obj, 'static');
        });
    });
}

function drawObject(x, y, obj, mode = 'static') {
    const text = `${obj.name}, ${obj.id}`;
    const textWidth = ctx.measureText(text).width;
    
    ctx.fillStyle = obj.color;
    ctx.globalAlpha = (mode === 'moving' || mode === 'rejected') ? 0.7 : 1.0;
    
    ctx.fillRect(x, y, BUCKET_WIDTH - 30, 30);
    
    if (mode === 'rejected') {
        ctx.strokeStyle = 'red';
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, BUCKET_WIDTH - 30, 30);
        // Draw a cross
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + BUCKET_WIDTH - 30, y + 30);
        ctx.moveTo(x + BUCKET_WIDTH - 30, y);
        ctx.lineTo(x, y + 30);
        ctx.stroke();
    }
    
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.font = '12px sans-serif';
    ctx.fillText(text, x + (BUCKET_WIDTH - 30) / 2, y + 19);
    ctx.globalAlpha = 1.0;
}

function setExplanation(text, highlight = false) {
    explanationDiv.innerHTML = text;
    if (highlight) {
        explanationDiv.classList.add('highlight');
    } else {
        explanationDiv.classList.remove('highlight');
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function setButtonsDisabled(disabled) {
    addObjectBtns.forEach(b => b.disabled = disabled);
    resetBtn.disabled = disabled;
}

async function animateAddObject(obj) {
    setButtonsDisabled(true);
    let currentObject = JSON.parse(JSON.stringify(obj));
    let x = canvas.width / 2 - 50;
    let y = -50;
    
    const hashCode = getObjectHashCode(currentObject);
    const bucketIndex = Math.abs(hashCode % NUM_BUCKETS);
    const targetX = BUCKET_MARGIN + bucketIndex * (BUCKET_WIDTH + BUCKET_MARGIN) + 15;
    const existingBucket = buckets[bucketIndex];

    // 1. Calculate hashCode
    setExplanation(`1. 准备添加对象 <span class="object-info">${currentObject.name}, ${currentObject.id}</span>。首先计算它的哈希码...`);
    await sleep(1500);

    setExplanation(`1. 对象 <span class="object-info">${currentObject.name}, ${currentObject.id}</span> 的哈希码是 <b>${hashCode}</b>。`, true);
    await sleep(1500);

    setExplanation(`2. 根据哈希码定位到存储桶。计算索引: <code>${hashCode} % ${NUM_BUCKETS} = ${bucketIndex}</code>。`);
    
    // 2. Animate move to bucket
    const startY = y;
    const endY = 60;
    const duration = 1000;
    let startTime = performance.now();

    function animate(time) {
        let elapsed = time - startTime;
        let progress = Math.min(elapsed / duration, 1);
        
        let currentY = startY + (endY - startY) * progress;
        
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawBuckets();
        drawObjectsInBuckets();
        drawObject(targetX, currentY, currentObject, 'moving');

        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            onAnimationEnd();
        }
    }
    requestAnimationFrame(animate);

    async function onAnimationEnd() {
        await sleep(500);
        // 3. Check bucket
        if (existingBucket.length === 0) {
            setExplanation(`3. 索引 ${bucketIndex} 的桶是空的。对象直接存入。`, true);
            buckets[bucketIndex].push(currentObject);
        } else {
            setExplanation(`3. 索引 ${bucketIndex} 的桶里有对象！需要进一步检查。`);
            await sleep(1500);
            
            setExplanation(`4. 逐个比较桶内对象。首先比较哈希码...`);
            await sleep(1500);
            
            let duplicateFound = false;
            for (const existingObj of existingBucket) {
                const existingHashCode = getObjectHashCode(existingObj);
                if (hashCode === existingHashCode) {
                    setExplanation(`5. 发现一个哈希码相同的对象！现在调用 <code>equals()</code> 方法进行最终确认...`, true);
                    await sleep(2000);
                    
                    if (areObjectsEqual(currentObject, existingObj)) {
                        setExplanation(`6. <code>equals()</code> 返回 <b>true</b>。这是一个重复对象，添加失败！`, true);
                        drawObject(targetX, 60, currentObject, 'rejected');
                        await sleep(1500);
                        duplicateFound = true;
                        break;
                    } else {
                        setExplanation(`6. <code>equals()</code> 返回 <b>false</b>。这不是重复对象（只是哈希冲突）。继续检查下一个...`);
                        await sleep(2000);
                    }
                }
            }

            if (!duplicateFound) {
                 setExplanation(`5. 桶内所有对象都已比较完毕。没有发现重复。`, true);
                 await sleep(1500);
                 setExplanation(`6. 这是一次哈希冲突，对象被添加到桶的末尾。`, true);
                 buckets[bucketIndex].push(currentObject);
            }
        }
        
        // Final state
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawBuckets();
        drawObjectsInBuckets();
        setButtonsDisabled(false);
        await sleep(2000);
        setExplanation('操作完成。请选择下一个要添加的对象，或重置。');
    }
}

function reset() {
    buckets = Array(NUM_BUCKETS).fill(null).map(() => []);
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawBuckets();
    drawObjectsInBuckets();
    setExplanation('哈希表已重置。请添加一个新对象。');
    setButtonsDisabled(false);
}

// Event Listeners
addObjectBtns.forEach(btn => {
    btn.addEventListener('click', (e) => {
        const index = e.target.getAttribute('data-obj-index');
        const objectToAdd = userObjects[index];
        animateAddObject(objectToAdd);
    });
});

resetBtn.addEventListener('click', reset);

// Initial Draw
drawBuckets();

</script>
</body>
</html> 