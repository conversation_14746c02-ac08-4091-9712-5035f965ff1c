<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机流水线互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .pipeline-container {
            position: relative;
            height: 400px;
            margin: 40px 0;
            background: #f8fafc;
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid #e2e8f0;
        }

        .stage {
            position: absolute;
            width: 180px;
            height: 80px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stage:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .stage-1 { background: linear-gradient(135deg, #ff6b6b, #ee5a52); top: 50px; left: 50px; }
        .stage-2 { background: linear-gradient(135deg, #4ecdc4, #44a08d); top: 50px; left: 250px; }
        .stage-3 { background: linear-gradient(135deg, #45b7d1, #96c93d); top: 50px; left: 450px; }
        .stage-4 { background: linear-gradient(135deg, #f9ca24, #f0932b); top: 50px; left: 650px; }
        .stage-5 { background: linear-gradient(135deg, #6c5ce7, #a29bfe); top: 50px; left: 850px; }

        .instruction {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.5s ease;
            top: 200px;
            left: 20px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .formula-box {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #2d3436;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .quiz-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #00b894;
            color: white;
            border-color: #00b894;
        }

        .quiz-option.wrong {
            background: #e17055;
            color: white;
            border-color: #e17055;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        .time-display {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        /* 公式分解动画样式 */
        .formula-breakdown {
            background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
            border: 2px solid #667eea;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .formula-breakdown::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        .formula-step {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(20px);
            opacity: 0;
            transition: all 0.6s ease;
            position: relative;
            border-left: 5px solid #667eea;
        }

        .formula-step.active {
            transform: translateY(0);
            opacity: 1;
        }

        .formula-step.highlight {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .formula-component {
            display: inline-block;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .formula-component:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        .formula-component.active {
            background: linear-gradient(135deg, #00b894, #55a3ff);
            animation: glow 1.5s infinite alternate;
        }

        .interactive-formula {
            font-size: 1.4rem;
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            font-family: 'Courier New', monospace;
            line-height: 1.8;
        }

        .explanation-box {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            transform: translateX(-100%);
            opacity: 0;
            transition: all 0.8s ease;
        }

        .explanation-box.show {
            transform: translateX(0);
            opacity: 1;
        }

        .visual-timeline {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 15px;
            overflow-x: auto;
        }

        .timeline-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 15px;
            min-width: 80px;
        }

        .timeline-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ddd, #bbb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 10px;
            transition: all 0.5s ease;
            position: relative;
        }

        .timeline-circle.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scale(1.2);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .timeline-circle::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: #ddd;
            transform: translateY(-50%);
        }

        .timeline-circle:last-child::after {
            display: none;
        }

        .timeline-circle.active::after {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .timeline-label {
            font-size: 0.9rem;
            text-align: center;
            color: #666;
            font-weight: bold;
        }

        .calculation-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .step-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transform: translateY(20px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .step-card.show {
            transform: translateY(0);
            opacity: 1;
        }

        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 15px;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes glow {
            0% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            100% { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8), 0 0 40px rgba(102, 126, 234, 0.6); }
        }

        @keyframes countUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .number-animation {
            animation: countUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 计算机流水线互动学习</h1>
            <p class="subtitle">通过动画和游戏理解流水线的工作原理</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是流水线？</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; text-align: center; margin-bottom: 30px;">
                想象一下汽车生产线：每个工人负责一个步骤，多辆汽车可以同时在不同阶段进行组装。<br>
                计算机流水线也是如此，将指令执行分成多个阶段，提高处理效率！
            </p>
            
            <div class="pipeline-container" id="pipelineContainer">
                <div class="time-display" id="timeDisplay">时间: 0Δt</div>
                
                <div class="stage stage-1" data-stage="1" data-time="2">
                    取指令<br>(2Δt)
                </div>
                <div class="stage stage-2" data-stage="2" data-time="1">
                    分析指令<br>(1Δt)
                </div>
                <div class="stage stage-3" data-stage="3" data-time="3">
                    取操作数<br>(3Δt)
                </div>
                <div class="stage stage-4" data-stage="4" data-time="1">
                    运算<br>(1Δt)
                </div>
                <div class="stage stage-5" data-stage="5" data-time="2">
                    写回结果<br>(2Δt)
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startSingleInstruction()">🎯 执行单条指令</button>
                <button class="btn" onclick="startPipeline()">⚡ 启动流水线</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📊 关键概念与公式</h2>

            <div class="formula-box">
                <h3>🔧 重要公式：</h3>
                <p><strong>流水线执行周期</strong> = 最慢阶段的执行时间 = 3Δt</p>
                <p><strong>流水线执行时间</strong> = 首条指令执行时间 + (指令总数-1) × 流水线执行周期</p>
                <p><strong>流水线吞吐率</strong> = 任务数 ÷ 完成时间</p>
                <p><strong>流水线加速比</strong> = 不采用流水线的执行时间 ÷ 采用流水线的执行时间</p>
            </div>

            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-value" id="cycleTime">3Δt</div>
                    <div>执行周期</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="throughput">1/3Δt</div>
                    <div>最大吞吐率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="speedup">5:2</div>
                    <div>10条指令加速比</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="efficiency">50%</div>
                    <div>流水线效率</div>
                </div>
            </div>
        </div>

        <!-- 新增：流水线执行时间公式分解讲解区域 -->
        <div class="section">
            <h2 class="section-title">🧮 流水线执行时间公式深度解析</h2>

            <div class="formula-breakdown">
                <div class="interactive-formula" id="mainFormula">
                    <div style="margin-bottom: 20px; font-size: 1.6rem; color: #667eea;">
                        <strong>流水线执行时间</strong> = 首条指令执行时间 + (指令总数-1) × 流水线执行周期
                    </div>

                    <div style="font-size: 1.2rem; color: #666;">
                        <span class="formula-component" data-component="first-instruction" onclick="highlightComponent('first-instruction')">
                            首条指令执行时间
                        </span>
                        <span style="margin: 0 10px;">+</span>
                        <span class="formula-component" data-component="instruction-count" onclick="highlightComponent('instruction-count')">
                            (指令总数-1)
                        </span>
                        <span style="margin: 0 10px;">×</span>
                        <span class="formula-component" data-component="cycle-time" onclick="highlightComponent('cycle-time')">
                            流水线执行周期
                        </span>
                    </div>
                </div>

                <div class="controls" style="margin: 30px 0;">
                    <button class="btn" onclick="startFormulaBreakdown()">🎯 开始公式分解</button>
                    <button class="btn" onclick="showVisualTimeline()">⏱️ 显示时间轴</button>
                    <button class="btn" onclick="calculateExample()">🧮 计算示例</button>
                    <button class="btn" onclick="resetFormulaDemo()">🔄 重置演示</button>
                </div>

                <div id="formulaSteps" style="display: none;">
                    <div class="formula-step" data-step="1">
                        <div class="step-number">1</div>
                        <h4>理解首条指令执行时间</h4>
                        <p>首条指令需要走完所有5个阶段才能完成：</p>
                        <p><strong>2Δt + 1Δt + 3Δt + 1Δt + 2Δt = 9Δt</strong></p>
                        <div class="explanation-box">
                            💡 这是第一条指令从开始到完全执行完毕所需的总时间
                        </div>
                    </div>

                    <div class="formula-step" data-step="2">
                        <div class="step-number">2</div>
                        <h4>理解流水线执行周期</h4>
                        <p>流水线的执行周期由最慢的阶段决定：</p>
                        <p><strong>max(2Δt, 1Δt, 3Δt, 1Δt, 2Δt) = 3Δt</strong></p>
                        <div class="explanation-box">
                            💡 每隔3Δt就能完成一条新指令（除了第一条）
                        </div>
                    </div>

                    <div class="formula-step" data-step="3">
                        <div class="step-number">3</div>
                        <h4>理解(指令总数-1)的含义</h4>
                        <p>除了第一条指令外，其余指令都按周期间隔完成：</p>
                        <p><strong>10条指令 → 需要额外等待 (10-1) = 9个周期</strong></p>
                        <div class="explanation-box">
                            💡 第一条指令用了9Δt，后面9条指令每隔3Δt完成一条
                        </div>
                    </div>

                    <div class="formula-step" data-step="4">
                        <div class="step-number">4</div>
                        <h4>最终计算</h4>
                        <p>将所有部分组合起来：</p>
                        <p><strong>总时间 = 9Δt + 9 × 3Δt = 9Δt + 27Δt = 36Δt</strong></p>
                        <div class="explanation-box">
                            💡 相比非流水线的90Δt，节省了54Δt，效率提升2.5倍！
                        </div>
                    </div>
                </div>

                <div id="visualTimeline" style="display: none;">
                    <h4 style="text-align: center; margin-bottom: 20px;">📊 流水线执行时间轴</h4>
                    <div class="visual-timeline">
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="0">0</div>
                            <div class="timeline-label">开始</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="9">9</div>
                            <div class="timeline-label">首条指令<br>完成</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="12">12</div>
                            <div class="timeline-label">第2条<br>完成</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="15">15</div>
                            <div class="timeline-label">第3条<br>完成</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="18">18</div>
                            <div class="timeline-label">第4条<br>完成</div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-circle" data-time="36">36</div>
                            <div class="timeline-label">全部<br>完成</div>
                        </div>
                    </div>
                </div>

                <div id="calculationExample" style="display: none;">
                    <h4 style="text-align: center; margin-bottom: 20px;">🧮 实际计算演示</h4>
                    <div class="calculation-steps">
                        <div class="step-card" data-calc-step="1">
                            <h5><span class="step-number">1</span>确定基本参数</h5>
                            <p>• 指令总数：<span class="number-animation">10</span>条</p>
                            <p>• 首条指令时间：<span class="number-animation">9</span>Δt</p>
                            <p>• 流水线周期：<span class="number-animation">3</span>Δt</p>
                        </div>

                        <div class="step-card" data-calc-step="2">
                            <h5><span class="step-number">2</span>应用公式</h5>
                            <p>流水线执行时间 = 9 + (10-1) × 3</p>
                            <p>= 9 + 9 × 3</p>
                            <p>= 9 + 27</p>
                        </div>

                        <div class="step-card" data-calc-step="3">
                            <h5><span class="step-number">3</span>得出结果</h5>
                            <p style="font-size: 1.4rem; color: #667eea; font-weight: bold;">
                                总执行时间 = <span class="number-animation">36</span>Δt
                            </p>
                            <p>相比非流水线节省：90 - 36 = 54Δt</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧮 详细计算过程</h2>
            
            <div class="formula-box">
                <h3>📝 题目分析：</h3>
                <p><strong>给定条件：</strong></p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li>取指令：2Δt</li>
                    <li>分析指令：1Δt</li>
                    <li>取操作数：3Δt（最慢阶段）</li>
                    <li>运算：1Δt</li>
                    <li>写回结果：2Δt</li>
                </ul>
                
                <p><strong>计算步骤：</strong></p>
                <p>1️⃣ 流水线执行周期 = max(2,1,3,1,2) = 3Δt</p>
                <p>2️⃣ 10条指令不用流水线时间 = (2+1+3+1+2) × 10 = 90Δt</p>
                <p>3️⃣ 10条指令使用流水线时间 = (2+1+3+1+2) + (10-1) × 3 = 9 + 27 = 36Δt</p>
                <p>4️⃣ 加速比 = 90Δt ÷ 36Δt = 5:2</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">🎮 互动测试</h2>
            <div class="quiz-question">
                某计算机系统采用5级流水线结构执行指令，若连续向流水线输入10条指令，该流水线的加速比为？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 1:10</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">B. 2:1</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">C. 5:2</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 3:1</div>
            </div>
            <div id="explanation" style="margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.9); border-radius: 10px; display: none;">
                <h3>💡 答案解析：</h3>
                <p>正确答案是 <strong>C. 5:2</strong></p>
                <p>计算过程：</p>
                <ul>
                    <li>不使用流水线：10 × 9Δt = 90Δt</li>
                    <li>使用流水线：9Δt + 9 × 3Δt = 36Δt</li>
                    <li>加速比：90Δt ÷ 36Δt = 5:2</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 进阶练习</h2>
            <div class="formula-box">
                <h3>🧠 思考题：</h3>
                <p><strong>1.</strong> 如果流水线有冲突，会发生什么？</p>
                <p><strong>2.</strong> 为什么流水线的执行周期由最慢的阶段决定？</p>
                <p><strong>3.</strong> 如何提高流水线的效率？</p>

                <div style="margin-top: 20px;">
                    <button class="btn" onclick="showAdvancedConcepts()">🔍 查看高级概念</button>
                </div>
            </div>

            <div id="advancedConcepts" style="display: none; margin-top: 20px;">
                <div class="formula-box" style="background: linear-gradient(135deg, #a8edea, #fed6e3);">
                    <h3>🚀 高级概念：</h3>
                    <p><strong>流水线冲突：</strong></p>
                    <ul>
                        <li><strong>结构冲突：</strong>多条指令争用同一硬件资源</li>
                        <li><strong>数据冲突：</strong>指令间存在数据依赖关系</li>
                        <li><strong>控制冲突：</strong>分支指令改变程序执行流程</li>
                    </ul>

                    <p><strong>解决方案：</strong></p>
                    <ul>
                        <li><strong>流水线停顿：</strong>暂停流水线直到冲突解决</li>
                        <li><strong>数据前推：</strong>将结果直接传递给需要的指令</li>
                        <li><strong>分支预测：</strong>预测分支方向，减少控制冲突</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎨 可视化对比</h2>
            <div class="controls">
                <button class="btn" onclick="showComparison()">📊 显示性能对比</button>
                <button class="btn" onclick="showEfficiencyChart()">📈 显示效率图表</button>
            </div>

            <canvas id="comparisonChart" width="800" height="400" style="display: none; margin: 20px auto; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);"></canvas>
        </div>
    </div>

    <script>
        let currentTime = 0;
        let animationId = null;
        let instructions = [];
        let isAnimating = false;

        // 创建指令对象
        function createInstruction(id) {
            return {
                id: id,
                stage: 0,
                timeInStage: 0,
                element: null,
                completed: false
            };
        }

        // 创建指令DOM元素
        function createInstructionElement(instruction) {
            const element = document.createElement('div');
            element.className = 'instruction';
            element.textContent = instruction.id;
            element.style.left = '20px';
            element.style.top = '200px';
            document.getElementById('pipelineContainer').appendChild(element);
            return element;
        }

        // 单条指令执行动画
        function startSingleInstruction() {
            if (isAnimating) return;
            resetAnimation();
            
            const instruction = createInstruction('I1');
            instruction.element = createInstructionElement(instruction);
            instructions = [instruction];
            isAnimating = true;
            
            animateSingleInstruction();
        }

        function animateSingleInstruction() {
            const instruction = instructions[0];
            const stages = [
                { time: 2, pos: 130 },  // 取指令
                { time: 1, pos: 330 },  // 分析指令
                { time: 3, pos: 530 },  // 取操作数
                { time: 1, pos: 730 },  // 运算
                { time: 2, pos: 930 }   // 写回结果
            ];
            
            if (instruction.stage < stages.length) {
                const currentStage = stages[instruction.stage];
                
                // 高亮当前阶段
                document.querySelectorAll('.stage').forEach(s => s.classList.remove('highlight'));
                document.querySelector(`.stage-${instruction.stage + 1}`).classList.add('highlight');
                
                // 移动指令到当前阶段
                instruction.element.style.left = currentStage.pos + 'px';
                instruction.timeInStage++;
                currentTime++;
                
                document.getElementById('timeDisplay').textContent = `时间: ${currentTime}Δt`;
                
                if (instruction.timeInStage >= currentStage.time) {
                    instruction.stage++;
                    instruction.timeInStage = 0;
                }
                
                if (instruction.stage < stages.length) {
                    setTimeout(animateSingleInstruction, 800);
                } else {
                    instruction.element.style.opacity = '0.5';
                    document.querySelectorAll('.stage').forEach(s => s.classList.remove('highlight'));
                    isAnimating = false;
                }
            }
        }

        // 流水线执行动画
        function startPipeline() {
            if (isAnimating) return;
            resetAnimation();
            
            // 创建10条指令
            for (let i = 1; i <= 10; i++) {
                const instruction = createInstruction(`I${i}`);
                instructions.push(instruction);
            }
            
            isAnimating = true;
            animatePipeline();
        }

        function animatePipeline() {
            const stages = [
                { time: 2, pos: 130 },
                { time: 1, pos: 330 },
                { time: 3, pos: 530 },
                { time: 1, pos: 730 },
                { time: 2, pos: 930 }
            ];
            
            currentTime++;
            document.getElementById('timeDisplay').textContent = `时间: ${currentTime}Δt`;
            
            // 更新所有指令的状态
            instructions.forEach((instruction, index) => {
                if (instruction.completed) return;
                
                // 检查是否可以进入流水线
                if (!instruction.element && currentTime >= index * 3 + 1) {
                    instruction.element = createInstructionElement(instruction);
                }
                
                if (instruction.element) {
                    instruction.timeInStage++;
                    
                    if (instruction.stage < stages.length) {
                        const currentStage = stages[instruction.stage];
                        instruction.element.style.left = currentStage.pos + 'px';
                        
                        if (instruction.timeInStage >= currentStage.time) {
                            instruction.stage++;
                            instruction.timeInStage = 0;
                            
                            if (instruction.stage >= stages.length) {
                                instruction.completed = true;
                                instruction.element.style.opacity = '0.3';
                            }
                        }
                    }
                }
            });
            
            // 检查是否所有指令都完成
            const allCompleted = instructions.every(inst => inst.completed);
            if (!allCompleted && currentTime < 50) {
                setTimeout(animatePipeline, 600);
            } else {
                isAnimating = false;
                updateStats();
            }
        }

        // 重置动画
        function resetAnimation() {
            if (animationId) {
                clearTimeout(animationId);
            }
            
            currentTime = 0;
            instructions = [];
            isAnimating = false;
            
            document.getElementById('timeDisplay').textContent = '时间: 0Δt';
            document.querySelectorAll('.instruction').forEach(el => el.remove());
            document.querySelectorAll('.stage').forEach(s => s.classList.remove('highlight'));
        }

        // 更新统计信息
        function updateStats() {
            const totalInstructions = 10;
            const pipelineTime = 36;
            const sequentialTime = 90;
            const speedup = sequentialTime / pipelineTime;
            
            document.getElementById('speedup').textContent = `${speedup.toFixed(1)}:1`;
            
            // 添加动画效果
            document.querySelectorAll('.stat-card').forEach(card => {
                card.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    card.style.animation = '';
                }, 500);
            });
        }

        // 测试答案选择
        function selectAnswer(element, isCorrect) {
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('5:2')) {
                    option.classList.add('correct');
                }
            });

            // 显示解析
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('explanation').style.animation = 'fadeInUp 0.5s ease-out';

            setTimeout(() => {
                document.querySelectorAll('.quiz-option').forEach(option => {
                    option.style.pointerEvents = 'auto';
                    option.classList.remove('correct', 'wrong');
                });
                document.getElementById('explanation').style.display = 'none';
            }, 5000);
        }

        // 显示高级概念
        function showAdvancedConcepts() {
            const concepts = document.getElementById('advancedConcepts');
            if (concepts.style.display === 'none') {
                concepts.style.display = 'block';
                concepts.style.animation = 'fadeInUp 0.5s ease-out';
            } else {
                concepts.style.display = 'none';
            }
        }

        // 显示性能对比
        function showComparison() {
            const canvas = document.getElementById('comparisonChart');
            canvas.style.display = 'block';

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制对比图
            drawPerformanceComparison(ctx);
        }

        // 显示效率图表
        function showEfficiencyChart() {
            const canvas = document.getElementById('comparisonChart');
            canvas.style.display = 'block';

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制效率图表
            drawEfficiencyChart(ctx);
        }

        // 绘制性能对比图
        function drawPerformanceComparison(ctx) {
            const width = ctx.canvas.width;
            const height = ctx.canvas.height;

            // 设置背景
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, width, height);

            // 标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('流水线 vs 非流水线性能对比', width/2, 40);

            // 绘制柱状图
            const barWidth = 100;
            const barSpacing = 150;
            const startX = width/2 - barSpacing;
            const maxHeight = 250;

            // 非流水线
            const sequentialTime = 90;
            const sequentialHeight = (sequentialTime / 90) * maxHeight;

            ctx.fillStyle = '#e17055';
            ctx.fillRect(startX - barWidth/2, height - 80 - sequentialHeight, barWidth, sequentialHeight);

            ctx.fillStyle = '#2d3436';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('非流水线', startX, height - 50);
            ctx.fillText('90Δt', startX, height - 30);

            // 流水线
            const pipelineTime = 36;
            const pipelineHeight = (pipelineTime / 90) * maxHeight;

            ctx.fillStyle = '#00b894';
            ctx.fillRect(startX + barSpacing - barWidth/2, height - 80 - pipelineHeight, barWidth, pipelineHeight);

            ctx.fillText('流水线', startX + barSpacing, height - 50);
            ctx.fillText('36Δt', startX + barSpacing, height - 30);

            // 加速比标注
            ctx.fillStyle = '#6c5ce7';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillText('加速比: 5:2 = 2.5倍', width/2, height - 10);
        }

        // 绘制效率图表
        function drawEfficiencyChart(ctx) {
            const width = ctx.canvas.width;
            const height = ctx.canvas.height;

            // 设置背景
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, width, height);

            // 标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('流水线效率随指令数量变化', width/2, 40);

            // 绘制坐标轴
            ctx.strokeStyle = '#2d3436';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(80, height - 80);
            ctx.lineTo(width - 40, height - 80);
            ctx.moveTo(80, height - 80);
            ctx.lineTo(80, 80);
            ctx.stroke();

            // 绘制效率曲线
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let n = 1; n <= 20; n++) {
                const x = 80 + (n / 20) * (width - 120);
                const efficiency = (n * 9) / (9 + (n - 1) * 3);
                const y = height - 80 - (efficiency / 3) * (height - 160);

                if (n === 1) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // 标注关键点
            ctx.fillStyle = '#e17055';
            ctx.beginPath();
            ctx.arc(80 + (10 / 20) * (width - 120), height - 80 - (2.5 / 3) * (height - 160), 5, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#2d3436';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('10条指令: 2.5倍加速', 80 + (10 / 20) * (width - 120) + 10, height - 80 - (2.5 / 3) * (height - 160) - 10);
        }

        // 阶段点击事件
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageNum = this.dataset.stage;
                const time = this.dataset.time;
                
                // 显示阶段信息
                alert(`第${stageNum}阶段：${this.textContent}\n执行时间：${time}Δt`);
            });
        });

        // 公式分解相关函数
        let currentFormulaStep = 0;
        let formulaAnimationId = null;

        // 开始公式分解动画
        function startFormulaBreakdown() {
            const stepsContainer = document.getElementById('formulaSteps');
            stepsContainer.style.display = 'block';

            // 隐藏其他演示
            document.getElementById('visualTimeline').style.display = 'none';
            document.getElementById('calculationExample').style.display = 'none';

            // 重置所有步骤
            document.querySelectorAll('.formula-step').forEach(step => {
                step.classList.remove('active', 'highlight');
            });

            currentFormulaStep = 0;
            animateFormulaStep();
        }

        // 动画显示公式步骤
        function animateFormulaStep() {
            const steps = document.querySelectorAll('.formula-step');

            if (currentFormulaStep < steps.length) {
                const currentStep = steps[currentFormulaStep];

                // 显示当前步骤
                setTimeout(() => {
                    currentStep.classList.add('active');

                    // 高亮当前步骤
                    setTimeout(() => {
                        currentStep.classList.add('highlight');

                        // 显示解释框
                        const explanationBox = currentStep.querySelector('.explanation-box');
                        if (explanationBox) {
                            explanationBox.classList.add('show');
                        }

                        // 移除高亮，准备下一步
                        setTimeout(() => {
                            currentStep.classList.remove('highlight');
                            currentFormulaStep++;
                            animateFormulaStep();
                        }, 2000);
                    }, 500);
                }, 800);
            }
        }

        // 高亮公式组件
        function highlightComponent(componentType) {
            // 移除所有高亮
            document.querySelectorAll('.formula-component').forEach(comp => {
                comp.classList.remove('active');
            });

            // 高亮选中的组件
            const component = document.querySelector(`[data-component="${componentType}"]`);
            component.classList.add('active');

            // 显示对应的解释
            showComponentExplanation(componentType);

            // 3秒后移除高亮
            setTimeout(() => {
                component.classList.remove('active');
            }, 3000);
        }

        // 显示组件解释
        function showComponentExplanation(componentType) {
            const explanations = {
                'first-instruction': '首条指令需要完整走完所有5个阶段：2+1+3+1+2=9Δt',
                'instruction-count': '除第一条外的指令数量，每条都在流水线中按周期完成',
                'cycle-time': '流水线执行周期，由最慢阶段决定：max(2,1,3,1,2)=3Δt'
            };

            // 创建临时提示框
            const tooltip = document.createElement('div');
            tooltip.className = 'explanation-box show';
            tooltip.innerHTML = `💡 ${explanations[componentType]}`;
            tooltip.style.position = 'fixed';
            tooltip.style.top = '50%';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translate(-50%, -50%)';
            tooltip.style.zIndex = '1000';
            tooltip.style.maxWidth = '400px';

            document.body.appendChild(tooltip);

            // 3秒后移除
            setTimeout(() => {
                tooltip.remove();
            }, 3000);
        }

        // 显示可视化时间轴
        function showVisualTimeline() {
            const timeline = document.getElementById('visualTimeline');
            timeline.style.display = 'block';

            // 隐藏其他演示
            document.getElementById('formulaSteps').style.display = 'none';
            document.getElementById('calculationExample').style.display = 'none';

            // 动画显示时间轴节点
            const circles = document.querySelectorAll('.timeline-circle');
            circles.forEach((circle, index) => {
                setTimeout(() => {
                    circle.classList.add('active');

                    // 添加数字动画
                    const time = circle.dataset.time;
                    let currentValue = 0;
                    const targetValue = parseInt(time);
                    const increment = targetValue / 20;

                    const countAnimation = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= targetValue) {
                            currentValue = targetValue;
                            clearInterval(countAnimation);
                        }
                        circle.textContent = Math.floor(currentValue);
                    }, 50);

                }, index * 500);
            });
        }

        // 计算示例演示
        function calculateExample() {
            const example = document.getElementById('calculationExample');
            example.style.display = 'block';

            // 隐藏其他演示
            document.getElementById('formulaSteps').style.display = 'none';
            document.getElementById('visualTimeline').style.display = 'none';

            // 逐步显示计算卡片
            const cards = document.querySelectorAll('.step-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('show');

                    // 为数字添加动画效果
                    const numbers = card.querySelectorAll('.number-animation');
                    numbers.forEach(num => {
                        num.style.animation = 'countUp 0.8s ease-out';
                    });
                }, index * 800);
            });
        }

        // 重置公式演示
        function resetFormulaDemo() {
            // 隐藏所有演示区域
            document.getElementById('formulaSteps').style.display = 'none';
            document.getElementById('visualTimeline').style.display = 'none';
            document.getElementById('calculationExample').style.display = 'none';

            // 重置所有状态
            document.querySelectorAll('.formula-step').forEach(step => {
                step.classList.remove('active', 'highlight');
                const explanationBox = step.querySelector('.explanation-box');
                if (explanationBox) {
                    explanationBox.classList.remove('show');
                }
            });

            document.querySelectorAll('.timeline-circle').forEach(circle => {
                circle.classList.remove('active');
            });

            document.querySelectorAll('.step-card').forEach(card => {
                card.classList.remove('show');
            });

            document.querySelectorAll('.formula-component').forEach(comp => {
                comp.classList.remove('active');
            });

            currentFormulaStep = 0;

            // 移除任何临时提示框
            document.querySelectorAll('.explanation-box[style*="position: fixed"]').forEach(tooltip => {
                tooltip.remove();
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();

            // 为公式组件添加悬停效果
            document.querySelectorAll('.formula-component').forEach(component => {
                component.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });

                component.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = '';
                    }
                });
            });
        });
    </script>
</body>
</html>
