<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词 Ameliorate 学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        header {
            background-color: #4CAF50;
            color: white;
            padding: 20px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .container {
            background-color: #fff;
            margin: 20px;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            box-sizing: border-box;
        }
        h1, h2 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 25px;
        }
        .word-display {
            font-size: 3em;
            font-weight: bold;
            color: #2196F3;
            text-align: center;
            margin: 30px 0;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .word-display:hover {
            transform: translateY(-5px);
        }
        .translation, .explanation {
            font-size: 1.2em;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: justify;
        }
        .explanation strong {
            color: #FF5722;
        }
        .affix-section {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        .affix-item {
            margin-bottom: 20px;
        }
        .affix-item h3 {
            color: #673AB7;
            font-size: 1.5em;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .affix-item h3:hover {
            text-decoration: underline;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #fcfcfc;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
        }
        .interactive-section {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
            text-align: center;
        }
        .interactive-section button {
            background-color: #00BCD4;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin: 10px;
        }
        .interactive-section button:hover {
            background-color: #0097A7;
            transform: translateY(-2px);
        }
        .example-sentences {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        .example-sentences h2 {
            color: #4CAF50;
        }
        .example-sentences ul {
            list-style: none;
            padding: 0;
        }
        .example-sentences li {
            background-color: #e8f5e9;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 5px solid #8BC34A;
            border-radius: 5px;
        }
        footer {
            margin-top: 40px;
            padding: 20px;
            text-align: center;
            color: #777;
            font-size: 0.9em;
            width: 100%;
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <header>
        <h1>考研单词学习：Ameliorate</h1>
    </header>

    <div class="container">
        <h2 class="word-display">Ameliorate</h2>
        <p class="translation">翻译：v. 改善，改良；（使）变好</p>
        
        <div class="explanation">
            <h2>词义解释</h2>
            <p><strong>Ameliorate (v.) 改善，改良；（使）变好：</strong> 指使某事变得更好、更可接受或更令人满意。它通常用于描述减轻困难、痛苦或不愉快的情况，使其得到改进。</p>
            <p>这个词强调的是通过努力或干预，使事物从一个不那么好的状态转变为一个更好的状态。</p>
        </div>

        <div class="affix-section">
            <h2>词缀故事与动画演示</h2>
            <div class="affix-item">
                <h3>前缀：a- (ad-)</h3>
                <div class="canvas-container">
                    <canvas id="aCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：</strong> 想象一个静止的画面，突然有一个小箭头“a-”出现了，它指引着方向“向着”某个目标前进，代表着“改变”的开始，或者强调“去做”某个动作。在“ameliorate”中，“a-”就像是启动了“让事情变得更好”的进程，强调了动作的方向性。
                </p>
                <div class="interactive-section">
                    <button onclick="animateA()">开始演示 'a-'</button>
                </div>
            </div>

            <div class="affix-item">
                <h3>词根：melior-</h3>
                <div class="canvas-container">
                    <canvas id="meliorCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：</strong> 沿着“a-”箭头的方向，我们来到了一个花园。这个花园原来很普通，但是通过辛勤的耕耘，它变得越来越“melior-”，也就是越来越“好”。这个词根就像一个魔法师，能让事物变得更好，是“好”的核心意义所在。
                </p>
                <div class="interactive-section">
                    <button onclick="animateMelior()">开始演示 'melior-'</button>
                </div>
            </div>

            <div class="affix-item">
                <h3>后缀：-ate</h3>
                <div class="canvas-container">
                    <canvas id="ateCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：</strong> 当“a-”的指引和“melior-”的魔法力量结合在一起时，就产生了一个结果——“-ate”。它把“变得更好”这个过程变成了实际的“行动”，表示“使……变好”或“形成……的状态”。“-ate”作为一个后缀，在这里将“变好”的含义动词化，表示一个主动的改善行为。
                </p>
                <div class="interactive-section">
                    <button onclick="animateAte()">开始演示 '-ate'</button>
                </div>
            </div>
        </div>

        <div class="example-sentences">
            <h2>例句：加深理解</h2>
            <ul>
                <li>Steps have been taken to **ameliorate** the living conditions of the poor. (已经采取措施改善穷人的生活条件。)</li>
                <li>Foreign aid is badly needed to **ameliorate** the effects of the drought. (急需外援来缓解旱灾的影响。)</li>
                <li>The new policies aim to **ameliorate** the economic disparity in the region. (新政策旨在改善该地区的经济差距。)</li>
                <li>There is little hope that the situation will **ameliorate** quickly. (情况很快会好转的希望很小。)</li>
            </ul>
        </div>
    </div>

    <footer>
        <p>&copy; 2024 考研单词专家. 保留所有权利.</p>
    </footer>

    <script>
        // Canvas动画函数 - a- (ad-)
        function animateA() {
            const canvas = document.getElementById('aCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let arrowX = 50;
            let arrowY = height / 2;
            let targetX = width - 50;
            let speed = 2;
            let animationFrameId;

            function drawArrow(x, y) {
                ctx.fillStyle = '#2196F3';
                ctx.beginPath();
                ctx.moveTo(x, y - 10);
                ctx.lineTo(x + 30, y);
                ctx.lineTo(x, y + 10);
                ctx.fill();
                ctx.fillRect(x - 20, y - 2, 50, 4);

                ctx.font = '20px Arial';
                ctx.fillStyle = '#333';
                ctx.fillText('方向/强调', x + 40, y + 5);
            }

            function animate() {
                ctx.clearRect(0, 0, width, height);
                drawArrow(arrowX, arrowY);
                
                arrowX += speed;
                if (arrowX > targetX) {
                    arrowX = 50; // Reset for continuous animation
                }

                animationFrameId = requestAnimationFrame(animate);
            }
            
            // Stop any existing animation before starting a new one
            if (window.aAnimation) {
                cancelAnimationFrame(window.aAnimation);
            }
            window.aAnimation = animationFrameId;
            animate();
        }

        // Canvas动画函数 - melior-
        function animateMelior() {
            const canvas = document.getElementById('meliorCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let circleRadius = 20;
            let growthSpeed = 0.5;
            let currentRadius = 10;
            let animationFrameId;

            function draw() {
                ctx.clearRect(0, 0, width, height);

                // Draw initial state (smaller circle)
                ctx.fillStyle = '#FF5722';
                ctx.beginPath();
                ctx.arc(width / 2, height / 2, currentRadius, 0, Math.PI * 2);
                ctx.fill();

                ctx.font = '20px Arial';
                ctx.fillStyle = '#333';
                ctx.fillText('变好', width / 2 - 20, height / 2 + currentRadius + 20);

                currentRadius += growthSpeed;
                if (currentRadius > 50) { // Max size
                    currentRadius = 10; // Reset for continuous growth
                }
                
                animationFrameId = requestAnimationFrame(draw);
            }

            // Stop any existing animation before starting a new one
            if (window.meliorAnimation) {
                cancelAnimationFrame(window.meliorAnimation);
            }
            window.meliorAnimation = animationFrameId;
            draw();
        }

        // Canvas动画函数 - ate
        function animateAte() {
            const canvas = document.getElementById('ateCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let texts = ['Ameliorate', '改善', '改良', '变好'];
            let currentTextIndex = 0;
            let fadeIn = true;
            let alpha = 0;
            let animationFrameId;

            function draw() {
                ctx.clearRect(0, 0, width, height);

                ctx.font = 'bold 40px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = `rgba(103, 58, 183, ${alpha})`;
                ctx.fillText(texts[currentTextIndex], width / 2, height / 2);

                if (fadeIn) {
                    alpha += 0.02;
                    if (alpha >= 1) {
                        fadeIn = false;
                        setTimeout(() => {
                            // Small delay before fading out
                        }, 1000); 
                    }
                } else {
                    alpha -= 0.02;
                    if (alpha <= 0) {
                        fadeIn = true;
                        currentTextIndex = (currentTextIndex + 1) % texts.length;
                    }
                }
                animationFrameId = requestAnimationFrame(draw);
            }

            // Stop any existing animation before starting a new one
            if (window.ateAnimation) {
                cancelAnimationFrame(window.ateAnimation);
            }
            window.ateAnimation = animationFrameId;
            draw();
        }
    </script>
</body>
</html>