<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：over-（过度、超过）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
        }

        #powerCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .training-ground {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .power-station {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .power-station::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 154, 158, 0.2), transparent);
            transition: left 0.6s;
        }

        .power-station:hover::before {
            left: 100%;
        }

        .power-station:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .power-station.activated {
            opacity: 1;
            transform: translateY(0);
        }

        .power-transformation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .normal-ability {
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .normal-ability::after {
            content: '普通';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #218838;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .power-amplifier {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: powerPulse 2s ease-in-out infinite;
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.4);
        }

        .power-amplifier::before {
            content: '⚡';
            font-size: 1.8rem;
            color: white;
        }

        .power-amplifier::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            border: 3px solid rgba(255, 107, 107, 0.3);
            border-radius: 50%;
            animation: energyWave 1.5s infinite;
        }

        .super-ability {
            background: #dc3545;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
            animation: superGlow 2s ease-in-out infinite;
        }

        .super-ability::after {
            content: '超能';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c82333;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .power-explanation {
            background: rgba(255, 107, 107, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .training-example {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .power-level {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .power-level.charging {
            background: #ffc107;
            animation: charge 1s infinite;
        }

        .power-level.supercharged {
            background: #dc3545;
            animation: supercharge 1s infinite;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes powerPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes energyWave {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }

        @keyframes superGlow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
            }
            50% {
                box-shadow: 0 0 25px rgba(220, 53, 69, 0.8);
            }
        }

        @keyframes charge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes supercharge {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
                box-shadow: 0 0 5px #dc3545;
            }
            50% {
                opacity: 0.7;
                transform: scale(1.5);
                box-shadow: 0 0 15px #dc3545;
            }
        }

        @keyframes lightning {
            0%, 100% {
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
        }

        .interactive-hint {
            text-align: center;
            color: #ff6b6b;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .energy-particles {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #ff6b6b;
            border-radius: 50%;
            pointer-events: none;
            animation: lightning 1s infinite;
        }

        .power-meter {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .meter-segment {
            width: 30px;
            height: 8px;
            background: #dee2e6;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .meter-segment.active {
            background: linear-gradient(90deg, #28a745, #ffc107);
        }

        .meter-segment.super {
            background: linear-gradient(90deg, #ffc107, #dc3545);
            animation: supercharge 0.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>超能前缀：over-</h1>
            <p>在超能力训练营中学会"超越极限"的力量</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>⚡ 超能力训练营的故事</h2>
                <p>在一个神秘的超能力训练营里，普通人可以通过特殊训练获得超能力。训练营的秘密武器是"over-"能量放大器，它能让任何普通的能力变得"过度"或"超过"正常水平。当学员们的普通动词通过这个放大器时，就会获得"over-"前缀，变成拥有超强力量的超能力词汇！</p>
            </div>

            <div class="canvas-container">
                <canvas id="powerCanvas"></canvas>
                <div class="power-meter" id="powerMeter">
                    <div class="meter-segment"></div>
                    <div class="meter-segment"></div>
                    <div class="meter-segment"></div>
                    <div class="meter-segment"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择超能力训练营的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"超能力训练营"的比喻，是因为"over-"前缀的核心含义就是"超过"、"过度"，这与超能力"超越常人"的概念完美契合。能量放大器的视觉效果帮助学生理解"放大"、"增强"的概念，而训练营的设定强调了从普通到超能的转变过程。通过"普通→超能"的对比展示，让抽象的"过度"概念变得生动有趣，帮助学生建立清晰的程度对比概念。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startTraining()">启动训练</button>
                <button class="btn" onclick="showPowers()">显示超能力</button>
                <button class="btn" onclick="resetCamp()">重置训练营</button>
            </div>

            <div class="interactive-hint">
                ⚡ 点击"启动训练"观看超能力觉醒过程，点击训练站查看详细说明
            </div>
        </div>

        <div class="training-ground" id="trainingGround">
            <div class="power-station">
                <div class="power-level"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Work → Overwork</h3>
                <div class="power-transformation">
                    <div class="normal-ability">work</div>
                    <div class="power-amplifier"></div>
                    <div class="super-ability"><span class="prefix-highlight">over</span>work</div>
                </div>
                <div class="power-explanation">
                    工作 → <span class="prefix-highlight">过度</span>工作
                </div>
                <div class="training-example">
                    <strong>超能力觉醒：</strong><br>
                    <strong>普通：</strong>I work 8 hours a day. (我每天工作8小时。)<br>
                    <strong>超能：</strong>I overwork every day. (我每天都过度工作。)<br>
                    <strong>解析：</strong>"work"表示工作，加上"over-"变成"overwork"，表示过度工作、工作过量。超越了正常的工作强度。
                </div>
            </div>

            <div class="power-station">
                <div class="power-level"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Sleep → Oversleep</h3>
                <div class="power-transformation">
                    <div class="normal-ability">sleep</div>
                    <div class="power-amplifier"></div>
                    <div class="super-ability"><span class="prefix-highlight">over</span>sleep</div>
                </div>
                <div class="power-explanation">
                    睡觉 → <span class="prefix-highlight">过度</span>睡觉
                </div>
                <div class="training-example">
                    <strong>超能力觉醒：</strong><br>
                    <strong>普通：</strong>I sleep 8 hours. (我睡8小时。)<br>
                    <strong>超能：</strong>I overslept this morning. (我今天早上睡过头了。)<br>
                    <strong>解析：</strong>"sleep"表示睡觉，加上"over-"变成"oversleep"，表示睡过头、睡得过久。超过了正常的睡眠时间。
                </div>
            </div>

            <div class="power-station">
                <div class="power-level"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Heat → Overheat</h3>
                <div class="power-transformation">
                    <div class="normal-ability">heat</div>
                    <div class="power-amplifier"></div>
                    <div class="super-ability"><span class="prefix-highlight">over</span>heat</div>
                </div>
                <div class="power-explanation">
                    加热 → <span class="prefix-highlight">过度</span>加热
                </div>
                <div class="training-example">
                    <strong>超能力觉醒：</strong><br>
                    <strong>普通：</strong>The engine heats up. (引擎加热了。)<br>
                    <strong>超能：</strong>The engine overheated. (引擎过热了。)<br>
                    <strong>解析：</strong>"heat"表示加热，加上"over-"变成"overheat"，表示过热、过度加热。超过了正常的温度范围。
                </div>
            </div>

            <div class="power-station">
                <div class="power-level"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Look → Overlook</h3>
                <div class="power-transformation">
                    <div class="normal-ability">look</div>
                    <div class="power-amplifier"></div>
                    <div class="super-ability"><span class="prefix-highlight">over</span>look</div>
                </div>
                <div class="power-explanation">
                    看 → <span class="prefix-highlight">俯视</span>/忽视
                </div>
                <div class="training-example">
                    <strong>超能力觉醒：</strong><br>
                    <strong>普通：</strong>Look at the details. (看细节。)<br>
                    <strong>超能：</strong>Don't overlook the details. (不要忽视细节。)<br>
                    <strong>解析：</strong>"look"表示看，加上"over-"变成"overlook"，表示俯视、忽视。从高处往下看，或者看过头而忽略。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"over-"前缀表示超过正常程度、过度、在...之上。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"over-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"over-"后的动词基本含义</li>
                <li><strong>应用程度概念：</strong>在词根意思前加上"过度"、"超过"、"过分"</li>
                <li><strong>语境判断：</strong>根据句子语境选择最合适的中文表达</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象超能力训练营的能量放大器，"over-"就像超能力，让普通动作变得超强！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('powerCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentTraining = 0;
        let energyParticles = [];
        let lightningBolts = [];
        let powerLevel = 0;
        
        const trainings = [
            { normal: 'work', super: 'overwork', x: 150, y: 200 },
            { normal: 'sleep', super: 'oversleep', x: 350, y: 250 },
            { normal: 'heat', super: 'overheat', x: 550, y: 200 },
            { normal: 'look', super: 'overlook', x: 750, y: 250 }
        ];

        class EnergyParticle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
                this.color = `hsl(${Math.random() * 60}, 70%, 60%)`;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class LightningBolt {
            constructor(startX, startY, endX, endY) {
                this.startX = startX;
                this.startY = startY;
                this.endX = endX;
                this.endY = endY;
                this.life = 1;
                this.decay = 0.05;
                this.segments = this.generateSegments();
            }

            generateSegments() {
                const segments = [];
                const steps = 10;
                for (let i = 0; i <= steps; i++) {
                    const t = i / steps;
                    const x = this.startX + (this.endX - this.startX) * t + (Math.random() - 0.5) * 20;
                    const y = this.startY + (this.endY - this.startY) * t + (Math.random() - 0.5) * 20;
                    segments.push({ x, y });
                }
                return segments;
            }

            update() {
                this.life -= this.decay;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.strokeStyle = '#ffff00';
                ctx.lineWidth = 3;
                ctx.beginPath();
                for (let i = 0; i < this.segments.length - 1; i++) {
                    const current = this.segments[i];
                    const next = this.segments[i + 1];
                    ctx.moveTo(current.x, current.y);
                    ctx.lineTo(next.x, next.y);
                }
                ctx.stroke();
                ctx.restore();
            }
        }

        function drawPowerAmplifier() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 主机器体
            ctx.fillStyle = '#34495e';
            ctx.fillRect(centerX - 80, centerY - 60, 160, 100);
            
            // 能量核心
            const coreSize = 30 + Math.sin(Date.now() * 0.01) * 10;
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, coreSize);
            gradient.addColorStop(0, '#ffff00');
            gradient.addColorStop(0.5, '#ff6b6b');
            gradient.addColorStop(1, '#ee5a24');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, coreSize, 0, Math.PI * 2);
            ctx.fill();
            
            // 能量环
            if (animationState === 'training') {
                for (let i = 0; i < 3; i++) {
                    const radius = 50 + i * 20 + (Date.now() * 0.005) % 20;
                    ctx.strokeStyle = `rgba(255, 107, 107, ${0.5 - i * 0.15})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                    ctx.stroke();
                }
            }
            
            // 控制面板
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(centerX - 60, centerY + 50, 120, 30);
            
            // 指示灯
            const lightColors = ['#e74c3c', '#f39c12', '#2ecc71'];
            lightColors.forEach((color, index) => {
                ctx.fillStyle = animationState === 'training' ? color : '#95a5a6';
                ctx.beginPath();
                ctx.arc(centerX - 30 + index * 30, centerY + 65, 6, 0, Math.PI * 2);
                ctx.fill();
            });
            
            // 机器标签
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('OVER- 放大器', centerX, centerY + 100);
        }

        function drawPowerTransformation() {
            if (currentTraining < trainings.length && animationState === 'training') {
                const training = trainings[currentTraining];
                const centerX = canvas.width / 2;
                
                // 普通能力（左侧）
                ctx.fillStyle = '#28a745';
                ctx.fillRect(centerX - 250, training.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(training.normal, centerX - 200, training.y + 25);
                
                // 能量流
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(centerX - 130, training.y + 20);
                ctx.lineTo(centerX + 130, training.y + 20);
                ctx.stroke();
                
                // 超能力（右侧）
                ctx.fillStyle = '#dc3545';
                ctx.fillRect(centerX + 150, training.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮over-前缀
                ctx.fillStyle = '#ffc107';
                ctx.fillText('over', centerX + 185, training.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(training.normal, centerX + 225, training.y + 25);
            }
        }

        function createEnergyEffect(x, y) {
            for (let i = 0; i < 15; i++) {
                energyParticles.push(new EnergyParticle(x, y));
            }
        }

        function createLightning(startX, startY, endX, endY) {
            lightningBolts.push(new LightningBolt(startX, startY, endX, endY));
        }

        function updateEffects() {
            // 更新能量粒子
            energyParticles = energyParticles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            // 更新闪电
            lightningBolts = lightningBolts.filter(bolt => {
                bolt.update();
                bolt.draw();
                return bolt.life > 0;
            });
        }

        function updatePowerMeter() {
            const segments = document.querySelectorAll('.meter-segment');
            segments.forEach((segment, index) => {
                segment.classList.remove('active', 'super');
                if (index < currentTraining) {
                    segment.classList.add('active');
                } else if (index === currentTraining && animationState === 'training') {
                    segment.classList.add('super');
                }
            });
        }

        function updateStationStatus() {
            const stations = document.querySelectorAll('.power-station');
            const levels = document.querySelectorAll('.power-level');
            
            stations.forEach((station, index) => {
                const level = levels[index];
                if (index < currentTraining) {
                    level.classList.remove('charging');
                    level.classList.add('supercharged');
                } else if (index === currentTraining && animationState === 'training') {
                    level.classList.add('charging');
                    level.classList.remove('supercharged');
                } else {
                    level.classList.remove('charging', 'supercharged');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            bgGradient.addColorStop(0, '#ff9a9e');
            bgGradient.addColorStop(0.5, '#fecfef');
            bgGradient.addColorStop(1, '#fecfef');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制能量放大器
            drawPowerAmplifier();
            
            // 绘制能力转换过程
            drawPowerTransformation();
            
            // 更新特效
            updateEffects();
            
            // 更新界面状态
            updatePowerMeter();
            updateStationStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'training' && currentTraining < trainings.length) {
                // 创建能量特效
                if (Math.random() < 0.2) {
                    createEnergyEffect(canvas.width / 2 + (Math.random() - 0.5) * 160, 
                                     canvas.height / 2 + (Math.random() - 0.5) * 120);
                }
                
                // 创建闪电效果
                if (Math.random() < 0.1) {
                    createLightning(canvas.width / 2 - 100, canvas.height / 2,
                                  canvas.width / 2 + 100, canvas.height / 2);
                }
                
                // 自动切换到下一个训练
                setTimeout(() => {
                    currentTraining++;
                    if (currentTraining >= trainings.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function startTraining() {
            animationState = 'training';
            currentTraining = 0;
            energyParticles = [];
            lightningBolts = [];
        }

        function showPowers() {
            const stations = document.querySelectorAll('.power-station');
            stations.forEach((station, index) => {
                setTimeout(() => {
                    station.classList.add('activated');
                }, index * 400);
            });
        }

        function resetCamp() {
            animationState = 'idle';
            currentTraining = 0;
            energyParticles = [];
            lightningBolts = [];
            
            const stations = document.querySelectorAll('.power-station');
            stations.forEach(station => station.classList.remove('activated'));
            
            const levels = document.querySelectorAll('.power-level');
            levels.forEach(level => {
                level.classList.remove('charging', 'supercharged');
            });
        }

        // 初始化
        animate();

        // 点击训练站的交互
        document.querySelectorAll('.power-station').forEach(station => {
            station.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
