<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单画中画测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .controls {
            margin: 30px 0;
        }
        
        button {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .primary {
            background: #409EFF !important;
            border-color: #409EFF !important;
        }
        
        .primary:hover {
            background: #66b1ff !important;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .info { background: rgba(64, 158, 255, 0.2); border: 1px solid rgba(64, 158, 255, 0.5); }
        .success { background: rgba(103, 194, 58, 0.2); border: 1px solid rgba(103, 194, 58, 0.5); }
        .warning { background: rgba(230, 162, 60, 0.2); border: 1px solid rgba(230, 162, 60, 0.5); }
        .error { background: rgba(245, 108, 108, 0.2); border: 1px solid rgba(245, 108, 108, 0.5); }
        
        .content-area {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .pip-active {
            border: 3px solid #409EFF !important;
            box-shadow: 0 0 20px rgba(64, 158, 255, 0.5) !important;
        }
        
        video {
            max-width: 100%;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .demo-content {
            text-align: left;
            line-height: 1.8;
        }
        
        .highlight {
            background: rgba(255, 235, 59, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 简单画中画演示</h1>
        
        <div class="controls">
            <button onclick="checkSupport()">🔍 检查支持</button>
            <button id="startBtn" onclick="startPip()" class="primary" disabled>📺 开启画中画</button>
            <button id="stopBtn" onclick="stopPip()" disabled>⏹️ 停止画中画</button>
        </div>
        
        <div id="status" class="status info">点击"检查支持"开始测试</div>
        
        <div class="content-area" id="contentArea">
            <h2>📄 演示内容</h2>
            <div class="demo-content">
                <p>这是一个<span class="highlight">画中画功能</span>的简单演示。</p>
                <p>当您点击"开启画中画"按钮后，这个内容将在一个小窗口中显示。</p>
                <p>您可以：</p>
                <ul>
                    <li>🔄 继续浏览其他网页</li>
                    <li>📱 调整画中画窗口大小</li>
                    <li>🎯 移动窗口位置</li>
                    <li>⚡ 查看实时更新</li>
                </ul>
                <p id="timeDisplay">当前时间: <span id="currentTime"></span></p>
                <p id="counterDisplay">更新计数: <span id="updateCounter">0</span></p>
            </div>
        </div>
        
        <video id="pipVideo" style="display: none;"></video>
    </div>

    <script>
        let video = null;
        let canvas = null;
        let ctx = null;
        let updateInterval = null;
        let updateCounter = 0;

        // 更新时间显示
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString();
            updateCounter++;
            document.getElementById('updateCounter').textContent = updateCounter;
        }

        // 检查浏览器支持
        function checkSupport() {
            const supported = 'pictureInPictureEnabled' in document && 
                            document.pictureInPictureEnabled &&
                            'requestPictureInPicture' in HTMLVideoElement.prototype;

            if (supported) {
                updateStatus('✅ 您的浏览器支持画中画功能！', 'success');
                document.getElementById('startBtn').disabled = false;
            } else {
                updateStatus('❌ 您的浏览器不支持画中画功能', 'error');
            }

            console.log('画中画支持检测:', {
                pictureInPictureEnabled: 'pictureInPictureEnabled' in document,
                documentEnabled: document.pictureInPictureEnabled,
                videoSupport: 'requestPictureInPicture' in HTMLVideoElement.prototype,
                finalResult: supported
            });
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        async function startPip() {
            try {
                updateStatus('🚀 正在启动画中画...', 'warning');

                // 创建canvas
                canvas = document.createElement('canvas');
                ctx = canvas.getContext('2d');
                canvas.width = 640;
                canvas.height = 480;

                // 获取video元素
                video = document.getElementById('pipVideo');
                video.style.display = 'block';
                video.muted = true;
                video.autoplay = true;

                // 开始绘制内容
                drawContent();

                // 创建视频流
                const stream = canvas.captureStream(30);
                video.srcObject = stream;

                // 等待video准备就绪
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('视频加载超时'));
                    }, 5000);

                    video.addEventListener('loadedmetadata', () => {
                        clearTimeout(timeout);
                        resolve();
                    }, { once: true });

                    video.addEventListener('error', (e) => {
                        clearTimeout(timeout);
                        reject(new Error('视频加载失败'));
                    }, { once: true });
                });

                // 启动画中画
                await video.requestPictureInPicture();

                // 更新UI状态
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('contentArea').classList.add('pip-active');

                // 开始内容更新
                startContentUpdate();

                // 监听退出事件
                video.addEventListener('leavepictureinpicture', stopPip, { once: true });

                updateStatus('🎉 画中画启动成功！您现在可以切换到其他窗口', 'success');

            } catch (error) {
                console.error('画中画启动失败:', error);
                updateStatus('❌ 画中画启动失败: ' + error.message, 'error');
                cleanup();
            }
        }

        function stopPip() {
            try {
                if (document.pictureInPictureElement) {
                    document.exitPictureInPicture();
                }
            } catch (error) {
                console.error('退出画中画失败:', error);
            }

            cleanup();
            updateStatus('画中画已停止', 'info');
        }

        function cleanup() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }

            if (video) {
                video.style.display = 'none';
                video.srcObject = null;
            }

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('contentArea').classList.remove('pip-active');
        }

        function startContentUpdate() {
            updateInterval = setInterval(drawContent, 100); // 10fps
        }

        function drawContent() {
            if (!ctx || !canvas) return;

            // 清空canvas
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('📺 画中画演示', canvas.width / 2, 50);

            // 绘制时间
            const now = new Date();
            ctx.font = '20px Microsoft YaHei';
            ctx.fillText(now.toLocaleTimeString(), canvas.width / 2, 90);

            // 绘制内容
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('这是画中画窗口中的内容', 50, 140);
            ctx.fillText('您可以继续浏览其他页面', 50, 170);
            ctx.fillText('窗口会实时更新内容', 50, 200);

            // 绘制动画圆圈
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2 + 50;
            const time = Date.now() * 0.005;
            const radius = 30 + Math.sin(time) * 10;

            ctx.fillStyle = `hsl(${(time * 50) % 360}, 70%, 60%)`;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制计数器
            ctx.fillStyle = '#ffffff';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`更新次数: ${updateCounter}`, canvas.width / 2, canvas.height - 50);

            updateCounter++;
        }

        // 页面加载时开始更新时间
        window.addEventListener('load', () => {
            checkSupport();
            setInterval(updateTime, 1000);
            updateTime();
        });
    </script>
</body>
</html>
