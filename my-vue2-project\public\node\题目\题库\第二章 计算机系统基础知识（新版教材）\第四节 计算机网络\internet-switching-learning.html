<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Internet网络交换方式互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 60px 40px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            animation: fadeInDown 1.2s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            font-weight: 300;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 24px;
            padding: 60px;
            margin-bottom: 60px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
            backdrop-filter: blur(10px);
        }

        .section-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            font-weight: 300;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 50px 0;
            position: relative;
        }

        #networkCanvas {
            border: none;
            border-radius: 20px;
            background: #f8f9fa;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 50px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 18px 36px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.25);
        }

        .explanation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px;
            border-radius: 20px;
            margin: 50px 0;
            line-height: 2;
            font-size: 1.2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .comparison-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .comparison-card.highlight {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        }

        .quiz-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 60px;
            border-radius: 24px;
            margin: 60px 0;
            box-shadow: 0 30px 60px rgba(0,0,0,0.1);
        }

        .quiz-question {
            font-size: 1.8rem;
            margin-bottom: 40px;
            text-align: center;
            font-weight: 400;
            line-height: 1.5;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.15);
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.25);
            transform: scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.9);
            border-color: #4CAF50;
            transform: scale(1.1);
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.9);
            border-color: #f44336;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            gap: 25px;
        }

        .step {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            transition: all 0.4s ease;
            border: 3px solid transparent;
        }

        .step.active {
            background: #4CAF50;
            transform: scale(1.3);
            border-color: white;
            box-shadow: 0 10px 25px rgba(76, 175, 80, 0.4);
        }

        .thinking-process {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 50px;
            border-radius: 20px;
            margin: 50px 0;
            line-height: 2;
            font-size: 1.2rem;
        }

        .highlight-box {
            background: rgba(255, 255, 255, 0.1);
            border-left: 5px solid #FFD700;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 Internet网络交换方式</h1>
            <p class="subtitle">零基础轻松理解网络核心技术</p>
        </div>

        <div class="section">
            <h2 class="section-title">🤔 题目分析</h2>
            <div class="quiz-section">
                <div class="quiz-question">
                    Internet 网络核心采取的交换方式为（   ）
                </div>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer('A', true)">
                        <strong>A. 分组交换</strong><br>
                        <small>数据分成小包传输</small>
                    </div>
                    <div class="quiz-option" onclick="selectAnswer('B', false)">
                        <strong>B. 电路交换</strong><br>
                        <small>建立专用通道</small>
                    </div>
                    <div class="quiz-option" onclick="selectAnswer('C', false)">
                        <strong>C. 虚电路交换</strong><br>
                        <small>虚拟专用路径</small>
                    </div>
                    <div class="quiz-option" onclick="selectAnswer('D', false)">
                        <strong>D. 消息交换</strong><br>
                        <small>整个消息传输</small>
                    </div>
                </div>
                <div id="answerFeedback" style="margin-top: 30px; text-align: center; font-size: 1.3rem; font-weight: bold;"></div>
            </div>
        </div>

        <!-- 详细解释部分 -->
        <div id="explanationSection" class="section" style="display: none;">
            <h2 class="section-title">📚 知识详解</h2>

            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>

            <div class="canvas-container">
                <canvas id="networkCanvas" width="1000" height="600"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="demonstratePacketSwitching()">🔄 演示分组交换</button>
                <button class="btn btn-secondary" onclick="demonstrateCircuitSwitching()">📞 演示电路交换</button>
                <button class="btn btn-primary" onclick="compareAll()">⚖️ 对比所有方式</button>
                <button class="btn btn-secondary" onclick="resetDemo()">🔄 重新开始</button>
            </div>

            <div id="currentExplanation" class="explanation">
                <h3>🌟 什么是分组交换？</h3>
                <div class="highlight-box">
                    <strong>简单理解：</strong>就像寄快递一样，把一个大包裹分成很多小包裹，每个小包裹都有地址标签，可以走不同的路线，最后在目的地重新组装。
                </div>
                <p>点击上面的按钮开始动画演示！</p>
            </div>
        </div>

        <!-- 思路分析部分 -->
        <div class="section">
            <h2 class="section-title">🧠 做题思路</h2>
            <div class="thinking-process">
                <h3>💡 如何快速判断？</h3>
                <div class="highlight-box">
                    <strong>关键词识别：</strong>看到"Internet网络核心"，立即想到分组交换！
                </div>

                <h4>🔍 排除法分析：</h4>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li><strong>电路交换：</strong>传统电话网络使用，需要建立专用通道，Internet不用</li>
                    <li><strong>虚电路交换：</strong>ATM网络使用，Internet不是基于这种方式</li>
                    <li><strong>消息交换：</strong>早期技术，现在基本不用了</li>
                    <li><strong>分组交换：</strong>✅ Internet的核心技术！</li>
                </ul>

                <div class="highlight-box">
                    <strong>记忆口诀：</strong>"Internet分组走，电话电路通，ATM虚电路，消息已过时"
                </div>
            </div>
        </div>

        <!-- 对比分析部分 -->
        <div class="section">
            <h2 class="section-title">📊 四种交换方式对比</h2>
            <div class="comparison-grid">
                <div class="comparison-card highlight">
                    <h3>🎯 分组交换 (Internet)</h3>
                    <p><strong>特点：</strong>数据分成小包，独立传输</p>
                    <p><strong>优点：</strong>灵活、高效、容错性强</p>
                    <p><strong>应用：</strong>Internet、局域网</p>
                    <p><strong>比喻：</strong>快递分包配送</p>
                </div>

                <div class="comparison-card">
                    <h3>📞 电路交换</h3>
                    <p><strong>特点：</strong>建立专用通信路径</p>
                    <p><strong>优点：</strong>传输质量稳定</p>
                    <p><strong>应用：</strong>传统电话网</p>
                    <p><strong>比喻：</strong>专用高速公路</p>
                </div>

                <div class="comparison-card">
                    <h3>🔗 虚电路交换</h3>
                    <p><strong>特点：</strong>虚拟的专用路径</p>
                    <p><strong>优点：</strong>结合两者优点</p>
                    <p><strong>应用：</strong>ATM网络</p>
                    <p><strong>比喻：</strong>预定路线的公交</p>
                </div>

                <div class="comparison-card">
                    <h3>📨 消息交换</h3>
                    <p><strong>特点：</strong>整个消息存储转发</p>
                    <p><strong>缺点：</strong>延迟大、效率低</p>
                    <p><strong>应用：</strong>早期网络（已淘汰）</p>
                    <p><strong>比喻：</strong>传统邮政系统</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let canvas, ctx;
        let animationId;
        let currentStep = 1;

        // 初始化画布
        function initCanvas() {
            canvas = document.getElementById('networkCanvas');
            ctx = canvas.getContext('2d');
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
        }

        // 测试答案选择
        function selectAnswer(option, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const feedback = document.getElementById('answerFeedback');

            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.textContent.includes(option)) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                }
            });

            if (isCorrect) {
                feedback.innerHTML = '🎉 恭喜答对了！<br>Internet确实采用分组交换方式！';
                feedback.style.color = '#4CAF50';
            } else {
                feedback.innerHTML = '❌ 答案不对哦！<br>正确答案是 A. 分组交换<br>让我们通过动画来理解为什么！';
                feedback.style.color = '#f44336';

                // 显示正确答案
                setTimeout(() => {
                    options.forEach(opt => {
                        if (opt.textContent.includes('A.')) {
                            opt.classList.add('correct');
                        }
                    });
                }, 1000);
            }

            // 延迟显示详细解释
            setTimeout(() => {
                showDetailedExplanation();
            }, 2000);
        }

        function showDetailedExplanation() {
            document.getElementById('explanationSection').style.display = 'block';
            document.getElementById('explanationSection').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            initCanvas();
            drawInitialNetwork();
        }

        // 绘制初始网络图
        function drawInitialNetwork() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制网络节点
            const nodes = [
                {x: 100, y: 300, label: '发送方'},
                {x: 300, y: 150, label: '路由器A'},
                {x: 300, y: 450, label: '路由器B'},
                {x: 500, y: 200, label: '路由器C'},
                {x: 500, y: 400, label: '路由器D'},
                {x: 700, y: 150, label: '路由器E'},
                {x: 700, y: 450, label: '路由器F'},
                {x: 900, y: 300, label: '接收方'}
            ];

            // 绘制连接线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 3;
            const connections = [
                [0, 1], [0, 2], [1, 3], [2, 4], [3, 5], [4, 6], [5, 7], [6, 7], [1, 4], [3, 6]
            ];

            connections.forEach(([from, to]) => {
                ctx.beginPath();
                ctx.moveTo(nodes[from].x, nodes[from].y);
                ctx.lineTo(nodes[to].x, nodes[to].y);
                ctx.stroke();
            });

            // 绘制节点
            nodes.forEach(node => {
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(node.x, node.y, 25, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.fillText(node.label, node.x, node.y + 5);
            });
        }

        // 演示分组交换
        function demonstratePacketSwitching() {
            updateStep(2);
            updateExplanation('分组交换演示', '数据被分成多个小包，每个包独立选择最佳路径传输');

            drawInitialNetwork();

            // 创建数据包
            const packets = [
                {x: 100, y: 300, color: '#ff6b6b', label: '包1', path: [0, 1, 3, 5, 7]},
                {x: 100, y: 300, color: '#4ecdc4', label: '包2', path: [0, 2, 4, 6, 7]},
                {x: 100, y: 300, color: '#45b7d1', label: '包3', path: [0, 1, 4, 6, 7]}
            ];

            animatePackets(packets);
        }

        // 演示电路交换
        function demonstrateCircuitSwitching() {
            updateStep(3);
            updateExplanation('电路交换演示', '建立专用通道，所有数据走同一条路径');

            drawInitialNetwork();

            // 高亮专用路径
            const path = [0, 1, 3, 5, 7];
            highlightPath(path, '#ff6b6b');

            // 单一数据流
            const data = {x: 100, y: 300, color: '#ff6b6b', label: '数据流', path: path};
            animateSingleData(data);
        }

        // 对比所有方式
        function compareAll() {
            updateStep(4);
            updateExplanation('综合对比', 'Internet选择分组交换是因为它更灵活、高效，能够充分利用网络资源');

            // 这里可以添加更复杂的对比动画
            drawInitialNetwork();
        }

        // 重置演示
        function resetDemo() {
            updateStep(1);
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            drawInitialNetwork();
            updateExplanation('准备开始', '点击按钮开始学习不同的交换方式');
        }

        // 更新步骤指示器
        function updateStep(step) {
            document.querySelectorAll('.step').forEach((s, i) => {
                s.classList.toggle('active', i + 1 === step);
            });
            currentStep = step;
        }

        // 更新解释文本
        function updateExplanation(title, content) {
            document.getElementById('currentExplanation').innerHTML = `
                <h3>🌟 ${title}</h3>
                <div class="highlight-box">
                    <strong>${content}</strong>
                </div>
            `;
        }

        // 动画函数
        function animatePackets(packets) {
            const nodes = [
                {x: 100, y: 300}, {x: 300, y: 150}, {x: 300, y: 450},
                {x: 500, y: 200}, {x: 500, y: 400}, {x: 700, y: 150},
                {x: 700, y: 450}, {x: 900, y: 300}
            ];

            let frame = 0;

            function animate() {
                drawInitialNetwork();

                packets.forEach((packet, index) => {
                    const delay = index * 30;
                    const adjustedFrame = Math.max(0, frame - delay);
                    const progress = (adjustedFrame % 200) / 200;
                    const pathIndex = Math.floor(progress * (packet.path.length - 1));
                    const segmentProgress = (progress * (packet.path.length - 1)) % 1;

                    if (pathIndex < packet.path.length - 1) {
                        const from = nodes[packet.path[pathIndex]];
                        const to = nodes[packet.path[pathIndex + 1]];

                        packet.x = from.x + (to.x - from.x) * segmentProgress;
                        packet.y = from.y + (to.y - from.y) * segmentProgress;

                        // 绘制数据包
                        ctx.fillStyle = packet.color;
                        ctx.beginPath();
                        ctx.arc(packet.x, packet.y, 15, 0, 2 * Math.PI);
                        ctx.fill();

                        ctx.fillStyle = 'white';
                        ctx.font = '12px Microsoft YaHei';
                        ctx.fillText(packet.label, packet.x, packet.y + 4);
                    }
                });

                frame++;
                if (frame < 400) {
                    animationId = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function animateSingleData(data) {
            const nodes = [
                {x: 100, y: 300}, {x: 300, y: 150}, {x: 300, y: 450},
                {x: 500, y: 200}, {x: 500, y: 400}, {x: 700, y: 150},
                {x: 700, y: 450}, {x: 900, y: 300}
            ];

            let frame = 0;

            function animate() {
                drawInitialNetwork();
                highlightPath(data.path, '#ff6b6b');

                const progress = (frame % 200) / 200;
                const pathIndex = Math.floor(progress * (data.path.length - 1));
                const segmentProgress = (progress * (data.path.length - 1)) % 1;

                if (pathIndex < data.path.length - 1) {
                    const from = nodes[data.path[pathIndex]];
                    const to = nodes[data.path[pathIndex + 1]];

                    data.x = from.x + (to.x - from.x) * segmentProgress;
                    data.y = from.y + (to.y - from.y) * segmentProgress;

                    // 绘制数据流
                    ctx.fillStyle = data.color;
                    ctx.beginPath();
                    ctx.arc(data.x, data.y, 20, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText(data.label, data.x, data.y + 4);
                }

                frame++;
                if (frame < 300) {
                    animationId = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function highlightPath(path, color) {
            const nodes = [
                {x: 100, y: 300}, {x: 300, y: 150}, {x: 300, y: 450},
                {x: 500, y: 200}, {x: 500, y: 400}, {x: 700, y: 150},
                {x: 700, y: 450}, {x: 900, y: 300}
            ];

            ctx.strokeStyle = color;
            ctx.lineWidth = 6;

            for (let i = 0; i < path.length - 1; i++) {
                const from = nodes[path[i]];
                const to = nodes[path[i + 1]];

                ctx.beginPath();
                ctx.moveTo(from.x, from.y);
                ctx.lineTo(to.x, to.y);
                ctx.stroke();
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            // 添加一些入场动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${index * 0.2}s`;
            });
        });
    </script>
</body>
</html>
