<!DOCTYPE html>
<html>
<head>
<title>Expel Animation</title>
<meta charset="UTF-8">
<style>
    body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #f0e68c; /* Khaki */
    }
    #canvas {
        border: 1px solid #000;
        background-color: #ffffff;
    }
    #explanation {
        width: 800px;
        text-align: left;
        margin-top: 20px;
    }
    #controls {
        margin-top: 10px;
    }
    button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
    }
</style>
</head>
<body>

<h1>Word Animation: Expel (驱逐)</h1>
<canvas id="canvas" width="800" height="400"></canvas>
<div id="controls">
    <button id="playBtn">Play Animation</button>
</div>
<div id="explanation">
    <h2>Expel (驱逐) = ex- (向外) + pel (推)</h2>
    <p><b>故事:</b> 想象一个城堡，里面有一个不守规矩的人。国王决定必须将他驱逐出去。两个卫兵把他从城堡大门里"推"了"出去"。这个动作就是 "expel"。</p>
    <p><b>ex- (向外):</b> 这个人被卫兵推向城堡的<b>外面</b>。动画中，他会从门内移动到门外。</p>
    <p><b>pel (推):</b> 卫兵们用力地<b>推</b>他，这是驱逐的核心动作。</p>
    <p><b>交互:</b> 点击 "Play Animation" 按钮，观看这个人被驱逐出城堡的过程。</p>
</div>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let animationId;
let progress = 0; // 0 to 1

const gate = { x: 350, y: 100, width: 100, height: 200 };
const person = { startX: 400, endX: 600, y: 250 };
const guard1 = { x: 370, y: 250 };
const guard2 = { x: 430, y: 250 };

function drawPerson(x, y, color = 'black') {
    ctx.save();
    ctx.translate(x,y);
    // Body
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(0, -40);
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.stroke();

    // Head
    ctx.beginPath();
    ctx.arc(0, -50, 10, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
    
    // Arms
    ctx.beginPath();
    ctx.moveTo(-15, -30);
    ctx.lineTo(15, -30);
    ctx.stroke();
    ctx.restore();
}

function drawCastleGate() {
    // Wall
    ctx.fillStyle = '#808080'; // Gray
    ctx.fillRect(0, 100, canvas.width, 200);

    // Gate opening
    ctx.clearRect(gate.x, gate.y, gate.width, gate.height);

    // Gate Arch
    ctx.beginPath();
    ctx.moveTo(gate.x, gate.y + gate.height);
    ctx.lineTo(gate.x, gate.y);
    ctx.quadraticCurveTo(gate.x + gate.width / 2, gate.y - 40, gate.x + gate.width, gate.y);
    ctx.lineTo(gate.x + gate.width, gate.y + gate.height);
    ctx.strokeStyle = '#663300';
    ctx.lineWidth = 10;
    ctx.stroke();

    ctx.fillStyle = '#000';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('城堡内部 (Inside)', gate.x - 60, gate.y + 100);
    ctx.fillText('城堡外部 (Outside)', gate.x + gate.width + 60, gate.y + 100);
}

function drawPushArrow(fromX, toX, y, text) {
    const midX = (fromX + toX) / 2;
    ctx.beginPath();
    ctx.moveTo(fromX, y);
    ctx.lineTo(toX, y);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 3;
    ctx.stroke();

    const headlen = 10;
    const angle = Math.atan2(0, toX - fromX);
    ctx.beginPath();
    ctx.moveTo(toX, y);
    ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), y - headlen * Math.sin(angle - Math.PI / 6));
    ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), y - headlen * Math.sin(angle + Math.PI / 6));
    ctx.closePath();
    ctx.fillStyle = 'red';
    ctx.fill();

    ctx.fillStyle = 'red';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(text, midX, y - 20);
}


function animate() {
    progress += 0.01;
    if (progress > 1) {
        progress = 1;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    drawCastleGate();

    const personX = person.startX + (person.endX - person.startX) * progress;
    
    drawPerson(personX, person.y, 'blue'); // The person being expelled
    
    // Guards are "pushing", so they move with the person until they are out of the gate
    if (personX < gate.x + gate.width + 30) {
        drawPerson(personX - 30, guard1.y, 'black'); // Guard 1
        drawPerson(personX + 30, guard2.y, 'black'); // Guard 2
    } else {
        // Guards stay at the gate
        drawPerson(gate.x + gate.width, guard1.y, 'black');
        drawPerson(gate.x + gate.width + 60, guard2.y, 'black');
    }


    // Annotations
    const pushArrowY = person.y - 80;
    if (progress > 0.1 && progress < 0.9) {
        // Arrow showing the push action
        drawPushArrow(personX - 50, personX + 50, pushArrowY, 'pel (推)');

        // Arrow showing the direction 'out'
        if (personX > gate.x + gate.width / 2) {
            ctx.fillStyle = 'purple';
            ctx.font = '24px Arial';
            ctx.fillText('ex- (向外)', personX, 50);
        }
    }
    
    if (progress >= 1) {
        cancelAnimationFrame(animationId);
        playBtn.disabled = false;
        playBtn.textContent = "Play Again";
        ctx.fillStyle = 'green';
        ctx.font = '24px Arial';
        ctx.fillText('已被驱逐!', person.endX, person.y - 120);
    } else {
        animationId = requestAnimationFrame(animate);
    }
}

function startAnimation() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    progress = 0;
    playBtn.disabled = true;
    playBtn.textContent = "Animating...";
    animate();
}

playBtn.addEventListener('click', startAnimation);

// Initial draw
ctx.clearRect(0, 0, canvas.width, canvas.height);
drawCastleGate();
drawPerson(person.startX, person.y, 'blue');
drawPerson(guard1.x, guard1.y, 'black');
drawPerson(guard2.x, guard2.y, 'black');

</script>

</body>
</html> 