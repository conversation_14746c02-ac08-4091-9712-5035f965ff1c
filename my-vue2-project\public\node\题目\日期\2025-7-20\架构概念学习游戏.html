<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏗️ 架构概念互动学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 2;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: slideDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 15px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 4px 8px rgba(0,0,0,0.3); }
            to { text-shadow: 0 4px 20px rgba(255,255,255,0.5); }
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .game-board {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .question-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInLeft 1s ease-out;
        }

        .canvas-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInRight 1s ease-out;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .options-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .option:hover::before {
            left: 100%;
        }

        .option:hover {
            transform: translateY(-5px) scale(1.02);
            border-color: #667eea;
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
        }

        .option.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            transform: translateY(-5px) scale(1.05);
        }

        .option.correct {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            border-color: #4caf50;
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
            border-color: #f44336;
            animation: wrongShake 0.6s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            cursor: pointer;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .explanation-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin-top: 30px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .explanation-section.show {
            opacity: 1;
            transform: translateY(0);
        }

        .concept-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .concept-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.6s ease;
        }

        .concept-card:hover::before {
            transform: scale(1);
        }

        .concept-card:hover {
            transform: translateY(-10px) rotateY(5deg);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .concept-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .concept-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .concept-desc {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .score-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            color: #333;
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @media (max-width: 768px) {
            .game-board {
                grid-template-columns: 1fr;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    
    <div class="score-display">
        🎯 得分: <span id="score">0</span>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 架构概念学习游戏</h1>
            <p class="subtitle">通过互动游戏掌握ANSI/IEEE 1471-2000标准核心概念</p>
        </div>

        <div class="game-board">
            <div class="question-panel">
                <div class="question-text">
                    <strong>🎯 挑战题目：</strong><br><br>
                    ANSI/IEEE 1471-2000是对软件密集型系统的架构进行描述的标准。在该标准中，（ ）这一概念主要用于描述软件架构模型。在此基础上，通常采用（ ）描述某个利益相关人（Stakeholder）所关注架构模型的某一方面。（ ）则是对所有利益相关人关注点的响应和回答。
                </div>
                
                <div class="options-grid">
                    <div class="option" data-value="A">
                        <div>🏛️</div>
                        <div>A. 架构</div>
                    </div>
                    <div class="option" data-value="B">
                        <div>⚙️</div>
                        <div>B. 系统</div>
                    </div>
                    <div class="option" data-value="C">
                        <div>📊</div>
                        <div>C. 模型</div>
                    </div>
                    <div class="option" data-value="D">
                        <div>🎯</div>
                        <div>D. 使命</div>
                    </div>
                </div>
                
                <div class="controls">
                    <button class="btn" onclick="checkAnswer()">🚀 提交答案</button>
                    <button class="btn" onclick="showHint()">💡 获取提示</button>
                    <button class="btn" onclick="showExplanation()">📚 查看解析</button>
                </div>
            </div>

            <div class="canvas-panel">
                <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎮 架构概念可视化游戏</h3>
                <canvas id="gameCanvas"></canvas>
                <div class="controls">
                    <button class="btn" onclick="startVisualization()">▶️ 开始演示</button>
                    <button class="btn" onclick="playGame()">🎲 互动游戏</button>
                    <button class="btn" onclick="resetCanvas()">🔄 重置</button>
                </div>
            </div>
        </div>

        <div class="explanation-section" id="explanationSection">
            <h3 style="color: #333; margin-bottom: 20px; text-align: center;">📖 深度解析：架构的核心作用</h3>
            <p style="line-height: 1.8; color: #555; margin-bottom: 30px; font-size: 1.1rem;">
                在ANSI/IEEE 1471-2000标准中，<strong style="color: #667eea;">架构（Architecture）</strong>是对所有利益相关人关注点的响应和回答。它不仅仅是一个技术概念，更是连接需求与实现的桥梁。
            </p>
            
            <div class="concept-cards">
                <div class="concept-card" onclick="highlightConcept('architecture')">
                    <span class="concept-icon">🏛️</span>
                    <div class="concept-title">架构 (Architecture)</div>
                    <div class="concept-desc">对所有利益相关人关注点的响应和回答，是系统的基础结构</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('system')">
                    <span class="concept-icon">⚙️</span>
                    <div class="concept-title">系统 (System)</div>
                    <div class="concept-desc">为达成特定使命而在环境中构建的整体</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('model')">
                    <span class="concept-icon">📊</span>
                    <div class="concept-title">模型 (Model)</div>
                    <div class="concept-desc">架构的具体表现形式，便于可视化和分析</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('mission')">
                    <span class="concept-icon">🎯</span>
                    <div class="concept-title">使命 (Mission)</div>
                    <div class="concept-desc">系统要达成的目标和目的</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedOption = null;
        let score = 0;
        let canvas, ctx;
        let animationFrame;
        let gameState = 'idle';
        let particles = [];

        // 初始化
        window.onload = function() {
            createStars();
            initCanvas();
            drawWelcomeScreen();
        };

        // 创建星空背景
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 100; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                starsContainer.appendChild(star);
            }
        }

        // 初始化Canvas
        function initCanvas() {
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }

        // 绘制欢迎界面
        function drawWelcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 渐变背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#bbdefb');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 欢迎文字
            ctx.fillStyle = '#333';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 架构概念可视化', canvas.width/2, canvas.height/2 - 40);
            
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('点击按钮开始学习之旅', canvas.width/2, canvas.height/2 + 20);
        }

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedOption = this.dataset.value;
                
                // 添加选择音效（视觉反馈）
                this.style.transform = 'translateY(-5px) scale(1.05)';
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedOption) {
                alert('🤔 请先选择一个答案哦！');
                return;
            }
            
            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.value === 'A') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.value !== 'A') {
                    option.classList.add('wrong');
                }
            });
            
            if (selectedOption === 'A') {
                score += 100;
                updateScore();
                setTimeout(() => {
                    alert('🎉 恭喜答对了！架构确实是对所有利益相关人关注点的响应和回答！');
                    createCelebrationEffect();
                }, 500);
            } else {
                setTimeout(() => {
                    alert('❌ 答案错误。正确答案是A：架构。让我们通过可视化来理解这个概念！');
                }, 500);
            }
        }

        // 显示提示
        function showHint() {
            alert('💡 提示：想想什么概念是"对所有利益相关人关注点的响应和回答"？这个概念描述了系统的整体结构和设计原则。');
        }

        // 显示解析
        function showExplanation() {
            const section = document.getElementById('explanationSection');
            section.classList.add('show');
            section.scrollIntoView({ behavior: 'smooth' });
            score += 50;
            updateScore();
        }

        // 开始可视化演示
        function startVisualization() {
            gameState = 'visualizing';
            animateArchitectureConcepts();
        }

        // 架构概念动画
        function animateArchitectureConcepts() {
            let step = 0;
            const maxSteps = 4;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e3f2fd');
                gradient.addColorStop(1, '#bbdefb');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                switch(step) {
                    case 0:
                        drawConcept(canvas.width/2, 100, '🎯 使命', '#ff9800', 'Mission');
                        drawText('系统的目标和目的', canvas.width/2, 350);
                        break;
                    case 1:
                        drawConcept(canvas.width/2, 100, '🎯 使命', '#ff9800', 'Mission');
                        drawConcept(canvas.width/2, 200, '⚙️ 系统', '#2196f3', 'System');
                        drawArrow(canvas.width/2, 130, canvas.width/2, 170);
                        drawText('为达成使命而构建的系统', canvas.width/2, 350);
                        break;
                    case 2:
                        drawConcept(canvas.width/2, 100, '🎯 使命', '#ff9800', 'Mission');
                        drawConcept(canvas.width/2, 200, '⚙️ 系统', '#2196f3', 'System');
                        drawConcept(canvas.width/2, 300, '🏛️ 架构', '#4caf50', 'Architecture');
                        drawArrow(canvas.width/2, 130, canvas.width/2, 170);
                        drawArrow(canvas.width/2, 230, canvas.width/2, 270);
                        drawText('架构响应所有关注点', canvas.width/2, 350);
                        break;
                    case 3:
                        drawConcept(canvas.width/2, 150, '🏛️ 架构', '#4caf50', 'Architecture');
                        drawConcept(150, 250, '👥 利益相关人', '#9c27b0', 'Stakeholder');
                        drawConcept(canvas.width-150, 250, '📊 模型', '#e91e63', 'Model');
                        drawArrow(200, 270, canvas.width/2-50, 180);
                        drawArrow(canvas.width-200, 270, canvas.width/2+50, 180);
                        drawText('架构连接利益相关人和模型', canvas.width/2, 350);
                        break;
                }
                
                step = (step + 1) % maxSteps;
                
                if (gameState === 'visualizing') {
                    setTimeout(() => requestAnimationFrame(animate), 2000);
                }
            }
            
            animate();
        }

        // 绘制概念节点
        function drawConcept(x, y, text, color, label) {
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制图标
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text.split(' ')[0], x, y + 8);
            
            // 绘制标签
            ctx.fillStyle = '#333';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillText(label, x, y + 65);
        }

        // 绘制箭头
        function drawArrow(x1, y1, x2, y2) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 15 * Math.cos(angle - Math.PI/6), y2 - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(x2 - 15 * Math.cos(angle + Math.PI/6), y2 - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = '#333';
            ctx.fill();
        }

        // 绘制文字
        function drawText(text, x, y) {
            ctx.fillStyle = '#333';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        // 互动游戏
        function playGame() {
            gameState = 'playing';
            initParticleGame();
        }

        // 粒子游戏
        function initParticleGame() {
            particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    color: ['#667eea', '#764ba2', '#4caf50', '#ff9800'][Math.floor(Math.random() * 4)],
                    size: Math.random() * 10 + 5
                });
            }
            animateParticles();
        }

        // 粒子动画
        function animateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#bbdefb');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制粒子
            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, 2 * Math.PI);
                ctx.fillStyle = particle.color;
                ctx.fill();
                
                // 更新位置
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                // 边界反弹
                if (particle.x <= 0 || particle.x >= canvas.width) particle.vx *= -1;
                if (particle.y <= 0 || particle.y >= canvas.height) particle.vy *= -1;
            });
            
            if (gameState === 'playing') {
                requestAnimationFrame(animateParticles);
            }
        }

        // 重置画布
        function resetCanvas() {
            gameState = 'idle';
            drawWelcomeScreen();
        }

        // 高亮概念
        function highlightConcept(concept) {
            score += 25;
            updateScore();
            
            // 添加视觉反馈
            event.target.style.transform = 'translateY(-10px) rotateY(5deg) scale(1.05)';
            setTimeout(() => {
                event.target.style.transform = 'translateY(-10px) rotateY(5deg)';
            }, 200);
        }

        // 更新分数
        function updateScore() {
            document.getElementById('score').textContent = score;
        }

        // 庆祝效果
        function createCelebrationEffect() {
            // 这里可以添加更多庆祝动画
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    const celebration = document.createElement('div');
                    celebration.innerHTML = '🎉';
                    celebration.style.position = 'fixed';
                    celebration.style.left = Math.random() * window.innerWidth + 'px';
                    celebration.style.top = Math.random() * window.innerHeight + 'px';
                    celebration.style.fontSize = '2rem';
                    celebration.style.zIndex = '1000';
                    celebration.style.pointerEvents = 'none';
                    celebration.style.animation = 'fadeOut 2s ease-out forwards';
                    document.body.appendChild(celebration);
                    
                    setTimeout(() => {
                        document.body.removeChild(celebration);
                    }, 2000);
                }, i * 100);
            }
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            if (gameState === 'idle') {
                drawWelcomeScreen();
            }
        });

        // 添加淡出动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-50px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
