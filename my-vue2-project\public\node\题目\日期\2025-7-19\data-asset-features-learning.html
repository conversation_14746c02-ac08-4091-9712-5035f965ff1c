<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据资产特征学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .features-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .features-demo {
            text-align: center;
            margin: 30px 0;
        }

        #featuresCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .feature-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .feature-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .value-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .share-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .control-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .quantify-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .test-btn {
            background: linear-gradient(45deg, #a29bfe, #6c5ce7);
        }

        .maintain-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .feature-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .feature-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .feature-card.correct {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .feature-card.incorrect {
            border-color: #e17055;
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
        }

        .feature-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .feature-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .highlight-incorrect {
            color: #d63031;
            font-weight: bold;
            background: rgba(214,48,49,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-data {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: floatData 12s infinite ease-in-out;
        }

        .data1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .data2 {
            top: 70%;
            right: 15%;
            animation-delay: 4s;
        }

        .data3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 8s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatData {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .feature-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .feature-cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-data data1"></div>
        <div class="floating-data data2"></div>
        <div class="floating-data data3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>📊 数据资产特征学习</h1>
            <p>深度理解数据资产的核心特征与价值属性</p>
        </div>

        <div class="main-grid">
            <div class="features-section">
                <h2 class="section-title">💎 数据资产特征演示</h2>
                
                <div class="features-demo">
                    <canvas id="featuresCanvas" width="700" height="350"></canvas>
                </div>

                <div class="feature-controls">
                    <button class="feature-btn value-btn" onclick="demonstrateFeature('value')">
                        可增值<br><small>Value Creation</small>
                    </button>
                    <button class="feature-btn share-btn" onclick="demonstrateFeature('share')">
                        可共享<br><small>Shareable</small>
                    </button>
                    <button class="feature-btn control-btn" onclick="demonstrateFeature('control')">
                        可控制<br><small>Controllable</small>
                    </button>
                    <button class="feature-btn quantify-btn" onclick="demonstrateFeature('quantify')">
                        可量化<br><small>Quantifiable</small>
                    </button>
                    <button class="feature-btn test-btn" onclick="demonstrateFeature('test')">
                        可测试<br><small>Testable</small>
                    </button>
                    <button class="feature-btn maintain-btn" onclick="demonstrateFeature('maintain')">
                        可维护<br><small>Maintainable</small>
                    </button>
                </div>

                <div class="feature-cards">
                    <div class="feature-card correct">
                        <h3>✅ 可增值</h3>
                        <p>数据通过分析处理可以产生新的价值，支持决策和创新</p>
                    </div>
                    <div class="feature-card correct">
                        <h3>✅ 可共享</h3>
                        <p>数据可以被多个用户同时使用，不会因使用而消耗</p>
                    </div>
                    <div class="feature-card correct">
                        <h3>✅ 可控制</h3>
                        <p>可以对数据的访问、使用和分发进行有效控制</p>
                    </div>
                    <div class="feature-card correct">
                        <h3>✅ 可量化</h3>
                        <p>数据的价值、质量和规模都可以通过指标进行量化评估</p>
                    </div>
                    <div class="feature-card incorrect">
                        <h3>❌ 可测试</h3>
                        <p>测试主要针对软件功能，不是数据资产的核心特征</p>
                    </div>
                    <div class="feature-card incorrect">
                        <h3>❌ 可维护</h3>
                        <p>维护更多指软件系统，数据资产更强调管理和治理</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 数据资产的特征包括（　　）<br><br>
                    ①可增值 ②可测试 ③可共享 ④可维护 ⑤可控制 ⑥可量化
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. ①②③④
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. ①②③⑤
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. ①②④⑤
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        D. ①③⑤⑥
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：D. ①③⑤⑥</strong></p>
                    <p>数据资产的核心特征包括：</p>
                    <ul>
                        <li><span class="highlight-correct">①可增值</span>：数据通过分析、挖掘、处理可以产生新的价值和洞察</li>
                        <li><span class="highlight-correct">③可共享</span>：数据具有非排他性，可以被多个用户同时使用而不会消耗</li>
                        <li><span class="highlight-correct">⑤可控制</span>：可以对数据的访问权限、使用范围、分发渠道进行有效控制</li>
                        <li><span class="highlight-correct">⑥可量化</span>：数据的价值、质量、规模等都可以通过具体指标进行量化评估</li>
                    </ul>
                    <p><strong>不属于数据资产特征的选项：</strong></p>
                    <ul>
                        <li><span class="highlight-incorrect">②可测试</span>：测试主要针对软件系统的功能验证，不是数据资产的核心特征</li>
                        <li><span class="highlight-incorrect">④可维护</span>：维护更多指软件系统的维护，数据资产更强调数据治理和管理</li>
                    </ul>
                    <p><strong>数据资产的其他重要特性</strong>：虚拟性、时效性、安全性、交换性、规模性等。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了数据资产的核心特征！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('featuresCanvas');
        const ctx = canvas.getContext('2d');
        let currentFeature = 'value';
        let animationId = null;

        // 演示不同特征
        function demonstrateFeature(featureType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentFeature = featureType;
            
            // 更新按钮状态
            document.querySelectorAll('.feature-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${featureType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(featureType) {
                case 'value':
                    drawValueFeature();
                    break;
                case 'share':
                    drawShareFeature();
                    break;
                case 'control':
                    drawControlFeature();
                    break;
                case 'quantify':
                    drawQuantifyFeature();
                    break;
                case 'test':
                    drawTestFeature();
                    break;
                case 'maintain':
                    drawMaintainFeature();
                    break;
            }
        }

        // 绘制可增值特征
        function drawValueFeature() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可增值 - 数据价值创造', 350, 40);

            // 原始数据
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(50, 100, 120, 80);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 100, 120, 80);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('原始数据', 110, 145);

            // 处理过程
            const processes = ['清洗', '分析', '挖掘'];
            processes.forEach((process, index) => {
                const x = 220 + index * 80;
                const y = 120;
                
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(x, y, 60, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 60, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(process, x + 30, y + 25);
                
                // 箭头
                if (index === 0) {
                    ctx.strokeStyle = '#0984e3';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(170, 140);
                    ctx.lineTo(220, 140);
                    ctx.stroke();
                }
                if (index < processes.length - 1) {
                    ctx.beginPath();
                    ctx.moveTo(x + 60, y + 20);
                    ctx.lineTo(x + 80, y + 20);
                    ctx.stroke();
                }
            });

            // 增值结果
            ctx.fillStyle = '#00b894';
            ctx.fillRect(530, 100, 120, 80);
            ctx.strokeStyle = '#00a085';
            ctx.lineWidth = 3;
            ctx.strokeRect(530, 100, 120, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('增值数据', 590, 145);

            // 最后的箭头
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(460, 140);
            ctx.lineTo(530, 140);
            ctx.stroke();

            // 价值指标
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(200, 220, 300, 60);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 220, 300, 60);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('价值提升：洞察 + 决策支持 + 创新', 350, 255);

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 通过分析处理产生新价值', 50, 320);
            ctx.fillText('✓ 支持业务决策和创新', 350, 320);
        }

        // 绘制可共享特征
        function drawShareFeature() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可共享 - 数据非排他性', 350, 40);

            // 中心数据
            ctx.fillStyle = '#fd79a8';
            ctx.beginPath();
            ctx.arc(350, 150, 50, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('数据', 350, 160);

            // 多个用户
            const users = [
                {x: 200, y: 100, name: '用户A'},
                {x: 500, y: 100, name: '用户B'},
                {x: 200, y: 200, name: '用户C'},
                {x: 500, y: 200, name: '用户D'}
            ];

            users.forEach(user => {
                ctx.fillStyle = '#e84393';
                ctx.beginPath();
                ctx.arc(user.x, user.y, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(user.name, user.x, user.y + 4);
                
                // 连接线
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(user.x, user.y);
                ctx.lineTo(350, 150);
                ctx.stroke();
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('同一数据可被多个用户同时使用', 350, 280);
            
            ctx.font = 'bold 14px Arial';
            ctx.fillText('数据不会因使用而消耗或减少', 350, 310);
        }

        // 绘制可控制特征
        function drawControlFeature() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可控制 - 数据访问管理', 350, 40);

            // 数据中心
            ctx.fillStyle = '#00b894';
            ctx.fillRect(300, 100, 100, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(300, 100, 100, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('数据资产', 350, 145);

            // 控制层
            ctx.fillStyle = 'rgba(0, 184, 148, 0.3)';
            ctx.fillRect(250, 80, 200, 120);
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(250, 80, 200, 120);
            ctx.setLineDash([]);

            // 访问控制
            const controls = [
                {x: 100, y: 120, name: '权限控制', allowed: true},
                {x: 100, y: 160, name: '用户认证', allowed: true},
                {x: 550, y: 120, name: '未授权访问', allowed: false},
                {x: 550, y: 160, name: '非法使用', allowed: false}
            ];

            controls.forEach(control => {
                const color = control.allowed ? '#00b894' : '#e17055';
                const symbol = control.allowed ? '✓' : '✗';
                
                ctx.fillStyle = color;
                ctx.fillRect(control.x - 40, control.y - 15, 80, 30);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(control.x - 40, control.y - 15, 80, 30);
                
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(control.name, control.x, control.y);
                
                ctx.font = '20px Arial';
                ctx.fillText(symbol, control.x, control.y + 40);
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('通过权限管理控制数据访问和使用', 350, 280);
        }

        // 绘制可量化特征
        function drawQuantifyFeature() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可量化 - 数据价值评估', 350, 40);

            // 量化指标
            const metrics = [
                {name: '数据量', value: '10TB', x: 100, y: 100},
                {name: '质量分', value: '85%', x: 300, y: 100},
                {name: '价值评估', value: '500万', x: 500, y: 100},
                {name: '使用频次', value: '1000次/日', x: 200, y: 200},
                {name: '准确率', value: '99.5%', x: 400, y: 200}
            ];

            metrics.forEach(metric => {
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(metric.x - 50, metric.y, 100, 60);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.strokeRect(metric.x - 50, metric.y, 100, 60);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(metric.name, metric.x, metric.y + 20);
                
                ctx.fillStyle = '#e17055';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(metric.value, metric.x, metric.y + 45);
            });

            // 图表
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(100, 300);
            ctx.lineTo(150, 280);
            ctx.lineTo(200, 290);
            ctx.lineTo(250, 270);
            ctx.lineTo(300, 260);
            ctx.lineTo(350, 250);
            ctx.stroke();

            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据价值可通过多维度指标进行量化评估', 350, 330);
        }

        // 绘制可测试特征（错误选项）
        function drawTestFeature() {
            ctx.fillStyle = '#a29bfe';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可测试 - 非数据资产特征', 350, 40);

            // 测试场景
            ctx.fillStyle = '#e17055';
            ctx.fillRect(200, 100, 300, 100);
            ctx.strokeStyle = '#d63031';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 300, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('❌ 错误特征', 350, 140);
            
            ctx.font = '14px Arial';
            ctx.fillText('测试主要针对软件功能', 350, 170);
            ctx.fillText('不是数据资产的核心特征', 350, 190);

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('测试更多用于软件系统的功能验证', 350, 250);
            ctx.fillText('数据资产更强调价值、共享、控制等特性', 350, 280);
        }

        // 绘制可维护特征（错误选项）
        function drawMaintainFeature() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可维护 - 非数据资产特征', 350, 40);

            // 维护场景
            ctx.fillStyle = '#e17055';
            ctx.fillRect(200, 100, 300, 100);
            ctx.strokeStyle = '#d63031';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 300, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('❌ 错误特征', 350, 140);
            
            ctx.font = '14px Arial';
            ctx.fillText('维护主要针对软件系统', 350, 170);
            ctx.fillText('数据资产更强调治理和管理', 350, 190);

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('维护更多指软件系统的维护更新', 350, 250);
            ctx.fillText('数据资产更注重数据治理、质量管理等', 350, 280);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('D. ①③⑤⑥')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画序列
                    demonstrateFeature('value');
                    setTimeout(() => demonstrateFeature('share'), 2000);
                    setTimeout(() => demonstrateFeature('control'), 4000);
                    setTimeout(() => demonstrateFeature('quantify'), 6000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateFeature('value');
            
            // 自动演示序列
            setTimeout(() => demonstrateFeature('share'), 3000);
            setTimeout(() => demonstrateFeature('control'), 6000);
            setTimeout(() => demonstrateFeature('quantify'), 9000);
            setTimeout(() => demonstrateFeature('value'), 12000);
        };
    </script>
</body>
</html>
