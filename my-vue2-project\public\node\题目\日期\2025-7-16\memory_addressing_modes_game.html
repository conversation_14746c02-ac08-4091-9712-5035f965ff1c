<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储器寻址方式探索游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .quiz-title {
            font-size: 1.8rem;
            color: #667eea;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #444;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: #f8f9ff;
            border: 2px solid #e1e5f2;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .option-card.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .option-card.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
            animation: correctPulse 0.6s ease-out;
        }

        .option-card.incorrect {
            background: #f44336;
            color: white;
            border-color: #f44336;
            animation: shake 0.5s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .feedback {
            text-align: center;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
            font-size: 1.1rem;
            font-weight: 500;
            line-height: 1.6;
        }

        .feedback.correct {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            color: #2e7d32;
            border: 2px solid #4CAF50;
        }

        .feedback.incorrect {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            color: #c62828;
            border: 2px solid #f44336;
        }

        .game-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .game-title {
            font-size: 2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .game-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .canvas-container {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid #e1e5f2;
        }

        #memoryCanvas {
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .memory-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .info-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .info-card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .info-card p {
            line-height: 1.6;
            color: #555;
        }

        .explanation-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .explanation-title {
            font-size: 2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 40px;
            font-weight: 600;
        }

        .knowledge-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .knowledge-card:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .knowledge-card h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .knowledge-card p {
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .demo-status {
            text-align: center;
            margin: 20px 0;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 500;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9ff;
            border-radius: 10px;
            padding: 20px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }

        .glow {
            animation: glow 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .quiz-section, .game-section, .explanation-section {
                padding: 25px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
            
            .memory-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>💾 存储器寻址方式探索</h1>
            <p>通过互动游戏深入理解不同的存储器寻址方式，掌握计算机存储系统的核心概念</p>
        </div>

        <!-- 题目测试区 -->
        <div class="quiz-section">
            <div class="quiz-title">🎯 知识检测</div>
            <div class="question-text">
                <span class="highlight">（____）</span>不属于按寻址方式划分的一类存储器。
            </div>
            <div class="options-grid">
                <div class="option-card" data-option="A">A. 随机存储器</div>
                <div class="option-card" data-option="B">B. 顺序存储器</div>
                <div class="option-card" data-option="C">C. 相联存储器</div>
                <div class="option-card" data-option="D">D. 直接存储器</div>
            </div>
            <div id="quiz-feedback" class="feedback"></div>
        </div>

        <!-- 游戏互动区 -->
        <div class="game-section">
            <div class="game-title">🎮 存储器寻址方式体验馆</div>
            <div class="game-subtitle">点击不同的寻址方式，观看动画演示，理解它们的工作原理</div>

            <div class="canvas-container">
                <canvas id="memoryCanvas" width="1000" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn" id="random-btn">🎲 随机寻址</button>
                <button class="btn" id="sequential-btn">📜 顺序寻址</button>
                <button class="btn" id="direct-btn">🎯 直接寻址</button>
                <button class="btn secondary" id="content-btn">🔍 内容寻址(相联)</button>
                <button class="btn secondary" id="reset-btn">🔄 重置演示</button>
            </div>

            <div class="demo-status" id="demo-status">
                选择一种寻址方式，观看动画演示！点击"内容寻址"看看为什么它不属于按寻址方式分类。
            </div>

            <div class="memory-info">
                <div class="info-card">
                    <h3>🎲 随机寻址</h3>
                    <p>可以直接访问任意地址的数据，访问时间相同。如RAM内存，想访问哪个地址就访问哪个。</p>
                </div>
                <div class="info-card">
                    <h3>📜 顺序寻址</h3>
                    <p>必须按顺序访问数据，如磁带。要访问后面的数据，必须先经过前面的数据。</p>
                </div>
                <div class="info-card">
                    <h3>🎯 直接寻址</h3>
                    <p>通过地址直接定位数据位置，如硬盘。知道地址就能直接"跳"到那个位置。</p>
                </div>
                <div class="info-card">
                    <h3>🔍 内容寻址</h3>
                    <p>不是按地址找数据，而是按内容找数据！这是按存储内容分类的，不是按寻址方式分类的。</p>
                </div>
            </div>
        </div>

        <!-- 知识解释区 -->
        <div class="explanation-section">
            <div class="explanation-title">📚 深度解析</div>
            
            <div class="knowledge-card">
                <h3>🤔 什么是寻址方式？</h3>
                <p><span class="highlight">寻址方式</span>就是计算机找到数据的方法，就像你在图书馆找书的不同方式。</p>
                <p>想象一下：你要在一个巨大的图书馆里找一本书，你可以：</p>
                <p>• <strong>随机找：</strong>任意选一个书架直接去看（随机寻址）</p>
                <p>• <strong>按顺序找：</strong>从第一排开始，一排一排地找（顺序寻址）</p>
                <p>• <strong>按编号找：</strong>知道书的编号，直接去对应位置（直接寻址）</p>
            </div>

            <div class="knowledge-card">
                <h3>🎲 随机寻址 - 想去哪就去哪</h3>
                <p><strong>特点：</strong>可以直接访问任意地址，访问时间都一样</p>
                <p><strong>典型代表：</strong>RAM（内存）</p>
                <p><strong>生活比喻：</strong>就像电梯，想去几楼直接按几楼，不用一层层爬</p>
                <p><strong>优点：</strong>访问速度快，效率高</p>
                <p><strong>缺点：</strong>成本相对较高</p>
            </div>

            <div class="knowledge-card">
                <h3>📜 顺序寻址 - 排队等候</h3>
                <p><strong>特点：</strong>必须按顺序访问，要访问后面的数据必须经过前面的</p>
                <p><strong>典型代表：</strong>磁带存储器</p>
                <p><strong>生活比喻：</strong>就像看录像带，想看后面的内容必须快进，不能直接跳到那里</p>
                <p><strong>优点：</strong>成本低，适合大容量存储</p>
                <p><strong>缺点：</strong>访问速度慢，特别是访问靠后的数据</p>
            </div>

            <div class="knowledge-card">
                <h3>🎯 直接寻址 - 精准定位</h3>
                <p><strong>特点：</strong>通过地址直接定位到数据位置</p>
                <p><strong>典型代表：</strong>硬盘存储器</p>
                <p><strong>生活比喻：</strong>就像GPS导航，知道地址就能直接"跳"到目标位置</p>
                <p><strong>优点：</strong>访问速度比顺序寻址快</p>
                <p><strong>缺点：</strong>访问时间可能因位置不同而略有差异</p>
            </div>

            <div class="knowledge-card">
                <h3>🔍 相联存储器 - 特殊的存在</h3>
                <p><strong>为什么不属于按寻址方式分类？</strong></p>
                <p>相联存储器（内容寻址存储器）是按<span class="highlight">存储内容</span>来分类的，不是按<span class="highlight">寻址方式</span>分类的！</p>
                <p><strong>工作原理：</strong>不是通过地址找数据，而是通过数据内容找数据</p>
                <p><strong>生活比喻：</strong>就像在图书馆里说"我要找关于恐龙的书"，而不是说"我要第3排第5本书"</p>
                <p><strong>应用场景：</strong>缓存系统、数据库索引等需要快速内容匹配的场合</p>
            </div>

            <div class="knowledge-card">
                <h3>🎯 记忆口诀</h3>
                <p><strong>按寻址方式分类的三兄弟：</strong></p>
                <p>• <strong>随机哥：</strong>"想去哪就去哪，时间都一样"</p>
                <p>• <strong>顺序弟：</strong>"排队等候，一个一个来"</p>
                <p>• <strong>直接妹：</strong>"知道地址，直接到达"</p>
                <p><strong>相联存储器：</strong>"我不是按地址找的，我是按内容找的，所以我不在这个分类里！"</p>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentDemo: null,
            animationId: null,
            particles: []
        };

        // Canvas相关
        const canvas = document.getElementById('memoryCanvas');
        const ctx = canvas.getContext('2d');

        // 题目测试逻辑
        const quizOptions = document.querySelectorAll('.option-card');
        const quizFeedback = document.getElementById('quiz-feedback');
        const correctAnswer = 'C';

        // 按钮元素
        const buttons = {
            random: document.getElementById('random-btn'),
            sequential: document.getElementById('sequential-btn'),
            direct: document.getElementById('direct-btn'),
            content: document.getElementById('content-btn'),
            reset: document.getElementById('reset-btn')
        };

        const demoStatus = document.getElementById('demo-status');

        // 题目测试事件
        quizOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 清除之前的选择
                quizOptions.forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'incorrect');
                });

                // 标记当前选择
                option.classList.add('selected');
                
                const selectedOption = option.dataset.option;
                
                setTimeout(() => {
                    if (selectedOption === correctAnswer) {
                        option.classList.add('correct');
                        showFeedback('🎉 正确！相联存储器确实不属于按寻址方式划分的存储器。它是按存储内容来分类的，通过内容而不是地址来访问数据。现在让我们通过游戏来深入体验各种寻址方式的区别！', 'correct');
                    } else {
                        option.classList.add('incorrect');
                        // 显示正确答案
                        quizOptions.forEach(opt => {
                            if (opt.dataset.option === correctAnswer) {
                                opt.classList.add('correct');
                            }
                        });
                        showFeedback('💡 不对哦！正确答案是C。相联存储器是按存储内容分类的，不是按寻址方式分类的。随机、顺序、直接寻址都是按照访问数据的方式来分类的，而相联存储器是按内容匹配来工作的。', 'incorrect');
                    }
                }, 300);
            });
        });

        function showFeedback(message, type) {
            quizFeedback.innerHTML = message;
            quizFeedback.className = `feedback ${type}`;
            quizFeedback.style.display = 'block';
        }

        // 游戏控制逻辑
        buttons.random.addEventListener('click', () => startDemo('random'));
        buttons.sequential.addEventListener('click', () => startDemo('sequential'));
        buttons.direct.addEventListener('click', () => startDemo('direct'));
        buttons.content.addEventListener('click', () => startDemo('content'));
        buttons.reset.addEventListener('click', resetDemo);

        function startDemo(type) {
            // 重置状态
            if (gameState.animationId) {
                cancelAnimationFrame(gameState.animationId);
            }
            
            // 更新按钮状态
            Object.values(buttons).forEach(btn => btn.classList.remove('active'));
            if (buttons[type]) {
                buttons[type].classList.add('active');
            }

            gameState.currentDemo = type;
            gameState.particles = [];

            switch(type) {
                case 'random':
                    demoRandomAccess();
                    break;
                case 'sequential':
                    demoSequentialAccess();
                    break;
                case 'direct':
                    demoDirectAccess();
                    break;
                case 'content':
                    demoContentAccess();
                    break;
            }
        }

        function resetDemo() {
            if (gameState.animationId) {
                cancelAnimationFrame(gameState.animationId);
            }
            gameState.currentDemo = null;
            gameState.particles = [];
            Object.values(buttons).forEach(btn => btn.classList.remove('active'));
            clearCanvas();
            demoStatus.innerHTML = '选择一种寻址方式，观看动画演示！点击"内容寻址"看看为什么它不属于按寻址方式分类。';
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 随机寻址演示
        function demoRandomAccess() {
            demoStatus.innerHTML = '🎲 随机寻址演示：CPU可以直接访问任意地址，就像电梯直达任意楼层！';
            
            let step = 0;
            const maxSteps = 200;
            const memoryBlocks = [];
            
            // 初始化内存块
            for (let i = 0; i < 8; i++) {
                for (let j = 0; j < 4; j++) {
                    memoryBlocks.push({
                        x: 200 + i * 80,
                        y: 150 + j * 60,
                        id: i * 4 + j,
                        accessed: false,
                        glowTime: 0
                    });
                }
            }

            function animate() {
                clearCanvas();
                
                // 绘制标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('随机寻址存储器 (RAM)', canvas.width / 2, 40);
                
                // 绘制CPU
                drawCPU(50, 250);
                
                // 绘制内存块
                memoryBlocks.forEach(block => {
                    drawMemoryBlock(block.x, block.y, block.id, block.accessed, block.glowTime);
                    if (block.glowTime > 0) block.glowTime--;
                });
                
                // 每30帧随机访问一个内存块
                if (step % 30 === 0) {
                    const randomBlock = memoryBlocks[Math.floor(Math.random() * memoryBlocks.length)];
                    randomBlock.accessed = true;
                    randomBlock.glowTime = 30;
                    
                    // 绘制访问线
                    drawAccessLine(120, 280, randomBlock.x, randomBlock.y + 20);
                    
                    // 添加粒子效果
                    addParticles(randomBlock.x + 20, randomBlock.y + 20);
                }
                
                // 绘制说明文字
                ctx.font = '16px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'left';
                ctx.fillText('• CPU可以直接访问任意地址', 50, 450);
                ctx.fillText('• 访问时间相同，效率高', 50, 470);
                
                updateParticles();
                
                step++;
                if (step < maxSteps && gameState.currentDemo === 'random') {
                    gameState.animationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        // 顺序寻址演示
        function demoSequentialAccess() {
            demoStatus.innerHTML = '📜 顺序寻址演示：必须按顺序访问，就像磁带播放，要看后面必须快进！';
            
            let step = 0;
            let currentIndex = 0;
            const maxSteps = 300;
            const tapeBlocks = [];
            
            // 初始化磁带块
            for (let i = 0; i < 12; i++) {
                tapeBlocks.push({
                    x: 150 + i * 60,
                    y: 250,
                    id: i,
                    accessed: false,
                    current: false
                });
            }

            function animate() {
                clearCanvas();
                
                // 绘制标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('顺序寻址存储器 (磁带)', canvas.width / 2, 40);
                
                // 绘制磁带读写头
                drawTapeHead(130