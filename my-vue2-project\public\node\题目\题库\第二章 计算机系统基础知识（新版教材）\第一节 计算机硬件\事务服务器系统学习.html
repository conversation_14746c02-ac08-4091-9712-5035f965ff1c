<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事务服务器系统 - 进程管理学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInLeft 1s ease-out 0.6s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            animation: slideInRight 1s ease-out 0.9s both;
        }

        #gameCanvas {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            cursor: pointer;
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            animation: fadeIn 1s ease-out 1.2s both;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .process-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .process-item {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }

        .process-item:hover {
            transform: translateX(10px);
        }

        .process-item h4 {
            color: #007bff;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .start-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .start-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-100px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1>🖥️ 事务服务器系统</h1>
            <p>通过动画和游戏学习进程管理概念</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                <strong>题目：</strong>典型的事务服务器系统包括多个在共享内存中访问数据的进程，其中（ ）监控其它进程，一旦进程失败，它将为该失败进程执行恢复动作，并重启该进程。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <h4>A. 检查点进程</h4>
                    <p>负责定期保存系统状态</p>
                </div>
                <div class="option" data-answer="B">
                    <h4>B. 数据库写进程</h4>
                    <p>负责将数据写入磁盘</p>
                </div>
                <div class="option" data-answer="C">
                    <h4>C. 进程监控进程</h4>
                    <p>监控和管理其他进程</p>
                </div>
                <div class="option" data-answer="D">
                    <h4>D. 锁管理器进程</h4>
                    <p>管理数据访问锁</p>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎮 交互式进程管理模拟器</h3>
            <button class="start-button" onclick="startSimulation()">开始模拟</button>
            <canvas id="gameCanvas"></canvas>
        </div>

        <div class="explanation">
            <h3>📚 知识解析</h3>
            <p><strong>事务服务器系统</strong>是一个复杂的多进程系统，就像一个繁忙的工厂，需要不同的"工人"（进程）协同工作。</p>
            
            <div class="process-list">
                <div class="process-item">
                    <h4>🔍 进程监控进程（正确答案）</h4>
                    <p>就像工厂的"安全主管"，时刻监视其他工人的状态，一旦发现有工人"倒下"，立即采取救援措施并安排替代人员。</p>
                </div>
                
                <div class="process-item">
                    <h4>💾 检查点进程</h4>
                    <p>像"档案管理员"，定期保存工作进度，确保意外发生时可以从最近的保存点恢复。</p>
                </div>
                
                <div class="process-item">
                    <h4>✍️ 数据库写进程</h4>
                    <p>像"记录员"，负责将内存中的数据永久保存到磁盘上。</p>
                </div>
                
                <div class="process-item">
                    <h4>🔒 锁管理器进程</h4>
                    <p>像"交通警察"，管理数据访问权限，防止多个进程同时修改同一数据造成冲突。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 选择题交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const allOptions = document.querySelectorAll('.option');
                
                allOptions.forEach(opt => {
                    opt.style.pointerEvents = 'none';
                    if (opt.dataset.answer === 'C') {
                        opt.classList.add('correct');
                    } else if (opt === this && answer !== 'C') {
                        opt.classList.add('wrong');
                    }
                });

                setTimeout(() => {
                    if (answer === 'C') {
                        showCelebration();
                    }
                }, 600);
            });
        });

        // Canvas 游戏
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let processes = [];
        let monitor = null;
        let gameStarted = false;

        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }

        function startSimulation() {
            if (gameStarted) return;
            gameStarted = true;
            resizeCanvas();
            initializeProcesses();
            animate();
        }

        function initializeProcesses() {
            processes = [];
            
            // 创建监控进程
            monitor = {
                x: canvas.width / 2,
                y: 100,
                radius: 30,
                color: '#28a745',
                name: '监控进程',
                status: 'active',
                pulsePhase: 0
            };

            // 创建其他进程
            const processTypes = [
                { name: '服务器进程1', color: '#007bff' },
                { name: '服务器进程2', color: '#007bff' },
                { name: '锁管理器', color: '#ffc107' },
                { name: '检查点进程', color: '#6f42c1' },
                { name: '写进程', color: '#fd7e14' }
            ];

            processTypes.forEach((type, index) => {
                processes.push({
                    x: 150 + (index % 3) * 200,
                    y: 250 + Math.floor(index / 3) * 150,
                    radius: 25,
                    color: type.color,
                    name: type.name,
                    status: 'active',
                    health: 100,
                    lastFailTime: 0
                });
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (monitor) {
                drawMonitor();
                drawConnections();
            }
            
            processes.forEach(process => {
                drawProcess(process);
                updateProcess(process);
            });

            animationId = requestAnimationFrame(animate);
        }

        function drawMonitor() {
            monitor.pulsePhase += 0.1;
            const pulseSize = monitor.radius + Math.sin(monitor.pulsePhase) * 5;
            
            // 绘制监控范围
            ctx.beginPath();
            ctx.arc(monitor.x, monitor.y, pulseSize + 20, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(40, 167, 69, 0.3)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制监控进程
            ctx.beginPath();
            ctx.arc(monitor.x, monitor.y, pulseSize, 0, Math.PI * 2);
            ctx.fillStyle = monitor.color;
            ctx.fill();
            
            // 绘制眼睛
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(monitor.x - 8, monitor.y - 5, 4, 0, Math.PI * 2);
            ctx.arc(monitor.x + 8, monitor.y - 5, 4, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'black';
            ctx.beginPath();
            ctx.arc(monitor.x - 8, monitor.y - 5, 2, 0, Math.PI * 2);
            ctx.arc(monitor.x + 8, monitor.y - 5, 2, 0, Math.PI * 2);
            ctx.fill();
            
            // 标签
            ctx.fillStyle = 'black';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(monitor.name, monitor.x, monitor.y + 50);
        }

        function drawConnections() {
            processes.forEach(process => {
                ctx.beginPath();
                ctx.moveTo(monitor.x, monitor.y);
                ctx.lineTo(process.x, process.y);
                ctx.strokeStyle = process.status === 'failed' ? '#dc3545' : 'rgba(40, 167, 69, 0.5)';
                ctx.lineWidth = 2;
                ctx.stroke();
            });
        }

        function drawProcess(process) {
            // 绘制进程圆圈
            ctx.beginPath();
            ctx.arc(process.x, process.y, process.radius, 0, Math.PI * 2);
            
            if (process.status === 'failed') {
                ctx.fillStyle = '#dc3545';
            } else if (process.status === 'recovering') {
                ctx.fillStyle = '#ffc107';
            } else {
                ctx.fillStyle = process.color;
            }
            ctx.fill();
            
            // 绘制健康条
            const barWidth = 40;
            const barHeight = 6;
            const barX = process.x - barWidth / 2;
            const barY = process.y + process.radius + 10;
            
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(barX, barY, barWidth, barHeight);
            
            ctx.fillStyle = process.health > 50 ? '#28a745' : process.health > 20 ? '#ffc107' : '#dc3545';
            ctx.fillRect(barX, barY, (process.health / 100) * barWidth, barHeight);
            
            // 标签
            ctx.fillStyle = 'black';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(process.name, process.x, process.y + process.radius + 30);
            
            // 状态指示
            if (process.status === 'failed') {
                ctx.fillStyle = 'red';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText('❌', process.x, process.y - 5);
            } else if (process.status === 'recovering') {
                ctx.fillStyle = 'orange';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText('🔄', process.x, process.y - 5);
            }
        }

        function updateProcess(process) {
            const currentTime = Date.now();
            
            // 随机故障
            if (process.status === 'active' && Math.random() < 0.001) {
                process.status = 'failed';
                process.health = 0;
                process.lastFailTime = currentTime;
                
                // 监控进程检测到故障，开始恢复
                setTimeout(() => {
                    if (process.status === 'failed') {
                        process.status = 'recovering';
                        
                        // 恢复过程
                        setTimeout(() => {
                            process.status = 'active';
                            process.health = 100;
                        }, 2000);
                    }
                }, 1000);
            }
            
            // 健康值恢复
            if (process.status === 'active' && process.health < 100) {
                process.health = Math.min(100, process.health + 0.5);
            }
        }

        function showCelebration() {
            // 创建庆祝动画
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createConfetti();
                }, i * 100);
            }
        }

        function createConfetti() {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '1000';
            confetti.style.animation = 'confettiFall 3s linear forwards';
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }

        // 添加confetti动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        window.addEventListener('load', () => {
            createParticles();
            resizeCanvas();
        });

        window.addEventListener('resize', resizeCanvas);

        // 点击canvas交互
        canvas.addEventListener('click', (e) => {
            if (!gameStarted) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            processes.forEach(process => {
                const distance = Math.sqrt((x - process.x) ** 2 + (y - process.y) ** 2);
                if (distance < process.radius && process.status === 'active') {
                    // 手动触发故障
                    process.status = 'failed';
                    process.health = 0;
                    
                    setTimeout(() => {
                        process.status = 'recovering';
                        setTimeout(() => {
                            process.status = 'active';
                            process.health = 100;
                        }, 2000);
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
