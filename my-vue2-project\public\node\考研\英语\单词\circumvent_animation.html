<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：Circumvent</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            flex-direction: column;
        }
        .container {
            max-width: 800px;
            width: 100%;
        }
        h1, h2 {
            color: #005a9c;
            border-bottom: 2px solid #005a9c;
            padding-bottom: 10px;
            text-align: center;
        }
        .card {
            background-color: #fff;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }
        canvas {
            display: block;
            margin: 20px auto;
            background-color: #eef5ff;
            border-radius: 8px;
            width: 100%;
            height: auto;
        }
        button {
            display: block;
            margin: 0 auto;
            padding: 12px 25px;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }
        ul {
            list-style-type: none;
            padding-left: 0;
        }
        li {
            background: #eef5ff;
            margin: 8px 0;
            padding: 12px 15px;
            border-left: 5px solid #007bff;
            border-radius: 5px;
        }
        strong {
            color: #d9534f;
            font-weight: 600;
        }
        p {
            line-height: 1.8;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            color: #888;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>单词：Circumvent <span style="font-weight:normal; font-size: 1.5rem;">/ˌsɜːkəmˈvent/</span></h1>
        
        <div class="card">
            <h2>词源拆解：一个"绕着走"的故事</h2>
            <p>
                <strong>circumvent</strong> 这个词可以拆分为两部分，就像一个迷你故事：
            </p>
            <ul>
                <li><strong>circum-</strong>: 一个拉丁前缀，意思是"<strong>环绕、围绕</strong>"。想象一下 "circle" (圆圈) 和 "circus" (马戏团，通常是圆形的场地)，它们都暗示着"圆"的概念。</li>
                <li><strong>vent</strong>: 一个拉丁词根，意思是"<strong>来</strong>"或"<strong>去</strong>"，源自拉丁动词 "venire"。我们熟悉的 "event" (事件，字面意思是"出来"的事情) 和 "prevent" (阻止，字面意思是"预先来到"面前) 都包含这个词根。</li>
            </ul>
            <p>
                把它们合在一起，<strong>circumvent</strong> 的字面意思就是"绕着走" (to come around)，是不是非常形象？
            </p>
        </div>

        <div class="card">
            <h2>含义与用法</h2>
            <p><strong>v. 规避，绕行，（巧妙地）回避（问题或困难）</strong></p>
            <p>
                这个词的核心在于"巧妙"。当你 "circumvent" 一个问题、规则或障碍时，你不是硬碰硬，而是像动画里的小人绕过障碍物一样，找到了一个聪明、甚至有时是"偷偷摸摸"的方法来绕过它。
            </p>
            <p><strong>例句:</strong></p>
            <p><em>She found a way to <strong>circumvent</strong> the bureaucracy.</em></p>
            <p><em>她找到了一个方法来规避官僚主义的繁文缛节。</em></p>
        </div>

        <div class="card">
            <h2>交互动画演示</h2>
            <p>点击下面的"播放动画"按钮，看看 "circumvent" 是如何通过故事动起来的！</p>
            <canvas id="wordAnimation" width="800" height="400"></canvas>
            <button id="playButton">播放动画</button>
        </div>
    </div>

    <div class="footer">
        <p>教育性动画演示，助您轻松记单词。</p>
    </div>

    <script>
        const canvas = document.getElementById('wordAnimation');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        let animationState = 'initial'; // initial, moving_to_obstacle, path_appearing, circumventing, finished
        let agentPos = { x: 50, y: 300 };
        const obstacle = { x: 350, width: 20, height: 150, y: 225 };
        const pathRadius = 100;
        const pathCenter = { x: obstacle.x + obstacle.width / 2, y: agentPos.y };
        let pathProgress = 0; // 0 to 1
        let agentAngle = Math.PI; // For circular motion

        function drawAgent() {
            // Simple stick figure
            ctx.fillStyle = '#007bff';
            ctx.beginPath();
            ctx.arc(agentPos.x, agentPos.y - 20, 10, 0, Math.PI * 2); // Head
            ctx.fill();
            ctx.beginPath();
            ctx.moveTo(agentPos.x, agentPos.y - 10); // Body
            ctx.lineTo(agentPos.x, agentPos.y + 10);
            ctx.lineWidth = 3;
            ctx.strokeStyle = '#007bff';
            ctx.stroke();
            ctx.beginPath(); // Legs
            ctx.moveTo(agentPos.x, agentPos.y + 10);
            ctx.lineTo(agentPos.x - 10, agentPos.y + 25);
            ctx.moveTo(agentPos.x, agentPos.y + 10);
            ctx.lineTo(agentPos.x + 10, agentPos.y + 25);
            ctx.stroke();
        }

        function drawObstacle() {
            ctx.fillStyle = '#6c757d';
            ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('问题/规则', obstacle.x + obstacle.width / 2, obstacle.y - 10);
        }

        function drawPath() {
            ctx.beginPath();
            ctx.setLineDash([5, 10]);
            ctx.arc(pathCenter.x, pathCenter.y, pathRadius, Math.PI, Math.PI * (1 + pathProgress), false);
            ctx.lineWidth = 3;
            ctx.strokeStyle = '#28a745';
            ctx.stroke();
            ctx.setLineDash([]);
        }

        function drawText(text, x, y, size = 24) {
            ctx.fillStyle = '#333';
            ctx.font = `bold ${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }
        
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawObstacle();
            
            if (animationState === 'path_appearing' || animationState === 'circumventing' || animationState === 'finished') {
                drawPath();
                drawText('circum- (环绕)', pathCenter.x, pathCenter.y - pathRadius - 20);
            }
            
            drawAgent();
            
            if (agentPos.x < obstacle.x - 20) {
                 drawText('vent (来/去)', agentPos.x, agentPos.y - 50);
            }

            if(animationState === 'finished') {
                drawText('Circumvent = 绕着走，规避', canvas.width / 2, 50, 30);
            }
        }

        function update() {
            switch (animationState) {
                case 'moving_to_obstacle':
                    if (agentPos.x < obstacle.x - 50) {
                        agentPos.x += 2;
                    } else {
                        animationState = 'path_appearing';
                    }
                    break;
                case 'path_appearing':
                    if (pathProgress < 1) {
                        pathProgress += 0.02;
                    } else {
                        pathProgress = 1;
                        animationState = 'circumventing';
                    }
                    break;
                case 'circumventing':
                    if (agentAngle > 0) {
                        agentAngle -= 0.02;
                        agentPos.x = pathCenter.x + Math.cos(agentAngle) * pathRadius;
                        agentPos.y = pathCenter.y - Math.sin(agentAngle) * pathRadius;
                    } else {
                        agentPos.y = pathCenter.y; // Snap to line
                        animationState = 'finished';
                        playButton.textContent = '重新播放';
                        playButton.disabled = false;
                    }
                    break;
            }
        }

        function animate() {
            update();
            draw();
            if (animationState !== 'finished' && animationState !== 'initial') {
                requestAnimationFrame(animate);
            }
        }

        function startAnimation() {
            playButton.textContent = '动画播放中...';
            playButton.disabled = true;

            // Reset state
            agentPos = { x: 50, y: 300 };
            pathProgress = 0;
            agentAngle = Math.PI;
            animationState = 'moving_to_obstacle';
            
            animate();
        }
        
        playButton.addEventListener('click', startAnimation);

        // Initial draw
        draw();
        drawText('点击 "播放动画" 开始', canvas.width / 2, 50, 30);

    </script>
</body>
</html> 