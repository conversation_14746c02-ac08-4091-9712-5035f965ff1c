<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多机系统耦合 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            text-align: center;
            font-weight: 500;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
        }

        .result.correct {
            background: #d4edda;
            color: #155724;
        }

        .result.wrong {
            background: #f8d7da;
            color: #721c24;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            position: absolute;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🖥️ 多机系统耦合学习</h1>
            <p class="subtitle">通过动画和交互理解紧耦合与松耦合系统</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是系统耦合？</h2>
            <div class="explanation">
                <h3>💡 基础概念</h3>
                <p><strong>耦合</strong>是指系统中各个组件之间相互依赖和连接的程度。就像人与人之间的关系一样：</p>
                <p>🤝 <strong>紧耦合</strong>：就像连体婴儿，彼此紧密相连，共享资源，一个有问题另一个也会受影响</p>
                <p>👋 <strong>松耦合</strong>：就像独立的朋友，各自独立，通过消息联系，一个有问题不会直接影响另一个</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="couplingCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="showTightCoupling()">🔗 演示紧耦合</button>
                <button class="btn" onclick="showLooseCoupling()">🔄 演示松耦合</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 SMP系统交互游戏</h2>
            <div class="explanation">
                <h3>🖥️ 对称多处理器系统（SMP）</h3>
                <p>SMP系统中，多个CPU共享内存和总线，就像多个厨师在同一个厨房里工作，共用所有的锅碗瓢盆。</p>
                <p>点击下面的CPU来看看它们如何协作处理任务！</p>
            </div>

            <div class="canvas-container">
                <canvas id="smpCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="addTask()">➕ 添加任务</button>
                <button class="btn" onclick="startProcessing()">▶️ 开始处理</button>
                <button class="btn" onclick="pauseProcessing()">⏸️ 暂停</button>
                <button class="btn" onclick="resetSMP()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📚 知识要点总结</h2>
            <div class="explanation">
                <h3>🔑 关键概念</h3>
                <p><strong>紧耦合系统特点：</strong></p>
                <ul style="margin-left: 20px; margin-bottom: 15px;">
                    <li>✅ 共享内存和总线</li>
                    <li>✅ 处理器间通信速度快</li>
                    <li>✅ 资源利用率高</li>
                    <li>❌ 扩展性有限</li>
                    <li>❌ 故障影响范围大</li>
                </ul>

                <p><strong>SMP系统优势：</strong></p>
                <ul style="margin-left: 20px;">
                    <li>🚀 任务可以并行处理</li>
                    <li>⚖️ 负载均匀分配</li>
                    <li>🔄 处理器间可以互相支援</li>
                    <li>💾 共享内存访问效率高</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧠 知识测试</h2>
            <div class="quiz-container">
                <div class="quiz-question">
                    紧耦合多机系统一般通过（ ）实现多机间的通信。对称多处理器结构（SMP）属于（ ）系统。
                </div>

                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">A. 松耦合</div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">B. 紧耦合</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">C. 混合耦合</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">D. 最低耦合</div>
                </div>

                <div id="quizResult" class="result" style="display: none;"></div>

                <button class="btn" onclick="showExplanation()" style="margin-top: 20px;">💡 查看详细解析</button>
            </div>

            <div id="detailedExplanation" class="explanation" style="display: none;">
                <h3>📖 详细解析</h3>
                <p><strong>正确答案：B. 紧耦合</strong></p>
                <p>SMP（对称多处理器结构）系统的特点：</p>
                <ul style="margin-left: 20px;">
                    <li>🔗 多个CPU通过<strong>共享内存</strong>和<strong>共享总线</strong>进行通信</li>
                    <li>⚡ 处理器间通信速度非常快，延迟极低</li>
                    <li>🤝 所有CPU平等访问系统资源</li>
                    <li>🎯 任务可以动态分配给任何空闲的处理器</li>
                </ul>
                <p>这种架构中，处理器之间的连接非常紧密，因此属于<strong>紧耦合系统</strong>。</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let totalSteps = 4;
        let animationId;
        let smpTasks = [];
        let cpuStates = [false, false, false, false]; // CPU工作状态
        let isProcessing = false;

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 耦合演示画布
        const couplingCanvas = document.getElementById('couplingCanvas');
        const couplingCtx = couplingCanvas.getContext('2d');

        // SMP演示画布
        const smpCanvas = document.getElementById('smpCanvas');
        const smpCtx = smpCanvas.getContext('2d');

        // 绘制紧耦合系统
        function drawTightCoupling() {
            couplingCtx.clearRect(0, 0, couplingCanvas.width, couplingCanvas.height);

            // 背景
            const gradient = couplingCtx.createLinearGradient(0, 0, couplingCanvas.width, couplingCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            couplingCtx.fillStyle = gradient;
            couplingCtx.fillRect(0, 0, couplingCanvas.width, couplingCanvas.height);

            // 共享内存区域
            couplingCtx.fillStyle = '#ffd700';
            couplingCtx.fillRect(300, 150, 200, 100);
            couplingCtx.fillStyle = '#333';
            couplingCtx.font = '16px Arial';
            couplingCtx.textAlign = 'center';
            couplingCtx.fillText('共享内存', 400, 205);

            // CPU们
            const cpuPositions = [
                {x: 150, y: 100},
                {x: 650, y: 100},
                {x: 150, y: 300},
                {x: 650, y: 300}
            ];

            cpuPositions.forEach((pos, index) => {
                // CPU圆形
                couplingCtx.fillStyle = '#667eea';
                couplingCtx.beginPath();
                couplingCtx.arc(pos.x, pos.y, 40, 0, Math.PI * 2);
                couplingCtx.fill();

                // CPU标签
                couplingCtx.fillStyle = 'white';
                couplingCtx.font = '14px Arial';
                couplingCtx.textAlign = 'center';
                couplingCtx.fillText(`CPU${index + 1}`, pos.x, pos.y + 5);

                // 连接线到共享内存
                couplingCtx.strokeStyle = '#28a745';
                couplingCtx.lineWidth = 3;
                couplingCtx.beginPath();
                couplingCtx.moveTo(pos.x, pos.y);
                couplingCtx.lineTo(400, 200);
                couplingCtx.stroke();
            });

            // 标题
            couplingCtx.fillStyle = '#333';
            couplingCtx.font = 'bold 20px Arial';
            couplingCtx.textAlign = 'center';
            couplingCtx.fillText('紧耦合系统 - 共享内存架构', 400, 50);
        }

        // 绘制松耦合系统
        function drawLooseCoupling() {
            couplingCtx.clearRect(0, 0, couplingCanvas.width, couplingCanvas.height);

            // 背景
            const gradient = couplingCtx.createLinearGradient(0, 0, couplingCanvas.width, couplingCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            couplingCtx.fillStyle = gradient;
            couplingCtx.fillRect(0, 0, couplingCanvas.width, couplingCanvas.height);

            // 独立的计算机节点
            const nodePositions = [
                {x: 150, y: 150},
                {x: 400, y: 150},
                {x: 650, y: 150}
            ];

            nodePositions.forEach((pos, index) => {
                // 计算机节点
                couplingCtx.fillStyle = '#764ba2';
                couplingCtx.fillRect(pos.x - 60, pos.y - 40, 120, 80);

                // 节点标签
                couplingCtx.fillStyle = 'white';
                couplingCtx.font = '14px Arial';
                couplingCtx.textAlign = 'center';
                couplingCtx.fillText(`节点${index + 1}`, pos.x, pos.y + 5);

                // 本地内存
                couplingCtx.fillStyle = '#ffc107';
                couplingCtx.fillRect(pos.x - 40, pos.y + 60, 80, 30);
                couplingCtx.fillStyle = '#333';
                couplingCtx.font = '12px Arial';
                couplingCtx.fillText('本地内存', pos.x, pos.y + 80);
            });

            // 网络连接
            couplingCtx.strokeStyle = '#dc3545';
            couplingCtx.lineWidth = 2;
            couplingCtx.setLineDash([5, 5]);

            // 连接线
            couplingCtx.beginPath();
            couplingCtx.moveTo(210, 150);
            couplingCtx.lineTo(340, 150);
            couplingCtx.stroke();

            couplingCtx.beginPath();
            couplingCtx.moveTo(460, 150);
            couplingCtx.lineTo(590, 150);
            couplingCtx.stroke();

            // 网络标签
            couplingCtx.fillStyle = '#dc3545';
            couplingCtx.font = '12px Arial';
            couplingCtx.fillText('网络通信', 275, 130);
            couplingCtx.fillText('网络通信', 525, 130);

            // 标题
            couplingCtx.fillStyle = '#333';
            couplingCtx.font = 'bold 20px Arial';
            couplingCtx.textAlign = 'center';
            couplingCtx.fillText('松耦合系统 - 分布式架构', 400, 50);

            couplingCtx.setLineDash([]);
        }

        // 显示紧耦合动画
        function showTightCoupling() {
            drawTightCoupling();
            currentStep = 1;
            updateProgress();

            // 添加数据流动画
            let time = 0;
            function animate() {
                drawTightCoupling();

                // 绘制数据流动效果
                const cpuPositions = [
                    {x: 150, y: 100},
                    {x: 650, y: 100},
                    {x: 150, y: 300},
                    {x: 650, y: 300}
                ];

                cpuPositions.forEach((pos, index) => {
                    const progress = (Math.sin(time + index) + 1) / 2;
                    const x = pos.x + (400 - pos.x) * progress;
                    const y = pos.y + (200 - pos.y) * progress;

                    couplingCtx.fillStyle = '#ff6b6b';
                    couplingCtx.beginPath();
                    couplingCtx.arc(x, y, 5, 0, Math.PI * 2);
                    couplingCtx.fill();
                });

                time += 0.1;
                animationId = requestAnimationFrame(animate);
            }
            animate();
        }

        // 显示松耦合动画
        function showLooseCoupling() {
            drawLooseCoupling();
            currentStep = 2;
            updateProgress();

            // 添加消息传递动画
            let time = 0;
            function animate() {
                drawLooseCoupling();

                // 绘制消息包
                const progress1 = (Math.sin(time) + 1) / 2;
                const progress2 = (Math.sin(time + Math.PI) + 1) / 2;

                // 消息包1
                const x1 = 210 + (340 - 210) * progress1;
                couplingCtx.fillStyle = '#ff6b6b';
                couplingCtx.fillRect(x1 - 5, 145, 10, 10);

                // 消息包2
                const x2 = 460 + (590 - 460) * progress2;
                couplingCtx.fillStyle = '#4ecdc4';
                couplingCtx.fillRect(x2 - 5, 145, 10, 10);

                time += 0.05;
                animationId = requestAnimationFrame(animate);
            }
            animate();
        }

        // 重置动画
        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            couplingCtx.clearRect(0, 0, couplingCanvas.width, couplingCanvas.height);

            // 绘制初始状态
            couplingCtx.fillStyle = '#f8f9fa';
            couplingCtx.fillRect(0, 0, couplingCanvas.width, couplingCanvas.height);
            couplingCtx.fillStyle = '#333';
            couplingCtx.font = 'bold 24px Arial';
            couplingCtx.textAlign = 'center';
            couplingCtx.fillText('点击按钮开始演示', 400, 200);

            currentStep = 0;
            updateProgress();
        }

        // SMP系统绘制
        function drawSMPSystem() {
            smpCtx.clearRect(0, 0, smpCanvas.width, smpCanvas.height);

            // 背景
            const gradient = smpCtx.createLinearGradient(0, 0, smpCanvas.width, smpCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            smpCtx.fillStyle = gradient;
            smpCtx.fillRect(0, 0, smpCanvas.width, smpCanvas.height);

            // 共享内存
            smpCtx.fillStyle = '#ffd700';
            smpCtx.fillRect(300, 50, 200, 80);
            smpCtx.fillStyle = '#333';
            smpCtx.font = '16px Arial';
            smpCtx.textAlign = 'center';
            smpCtx.fillText('共享内存', 400, 95);

            // 共享总线
            smpCtx.strokeStyle = '#28a745';
            smpCtx.lineWidth = 8;
            smpCtx.beginPath();
            smpCtx.moveTo(100, 200);
            smpCtx.lineTo(700, 200);
            smpCtx.stroke();
            smpCtx.fillStyle = '#28a745';
            smpCtx.font = '14px Arial';
            smpCtx.fillText('共享总线', 400, 190);

            // CPU们
            const cpuPositions = [
                {x: 150, y: 300},
                {x: 300, y: 300},
                {x: 500, y: 300},
                {x: 650, y: 300}
            ];

            cpuPositions.forEach((pos, index) => {
                // CPU状态颜色
                const isWorking = cpuStates[index];
                smpCtx.fillStyle = isWorking ? '#ff6b6b' : '#667eea';

                // CPU圆形
                smpCtx.beginPath();
                smpCtx.arc(pos.x, pos.y, 35, 0, Math.PI * 2);
                smpCtx.fill();

                // CPU标签
                smpCtx.fillStyle = 'white';
                smpCtx.font = '14px Arial';
                smpCtx.textAlign = 'center';
                smpCtx.fillText(`CPU${index + 1}`, pos.x, pos.y + 5);

                // 连接到总线
                smpCtx.strokeStyle = '#666';
                smpCtx.lineWidth = 2;
                smpCtx.beginPath();
                smpCtx.moveTo(pos.x, pos.y - 35);
                smpCtx.lineTo(pos.x, 200);
                smpCtx.stroke();

                // 连接到内存
                smpCtx.strokeStyle = '#ffc107';
                smpCtx.lineWidth = 1;
                smpCtx.setLineDash([2, 2]);
                smpCtx.beginPath();
                smpCtx.moveTo(pos.x, 200);
                smpCtx.lineTo(400, 130);
                smpCtx.stroke();
                smpCtx.setLineDash([]);
            });

            // 任务队列
            smpCtx.fillStyle = '#e9ecef';
            smpCtx.fillRect(50, 400, 700, 80);
            smpCtx.fillStyle = '#333';
            smpCtx.font = '16px Arial';
            smpCtx.fillText('任务队列', 400, 425);

            // 绘制任务
            smpTasks.forEach((task, index) => {
                const x = 80 + index * 60;
                const y = 440;

                smpCtx.fillStyle = task.color;
                smpCtx.fillRect(x, y, 50, 30);
                smpCtx.fillStyle = 'white';
                smpCtx.font = '12px Arial';
                smpCtx.textAlign = 'center';
                smpCtx.fillText(`T${task.id}`, x + 25, y + 20);
            });
        }

        // 添加任务
        function addTask() {
            if (smpTasks.length < 10) {
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
                smpTasks.push({
                    id: smpTasks.length + 1,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    processing: false
                });
                drawSMPSystem();
            }
        }

        // 开始处理
        function startProcessing() {
            if (!isProcessing && smpTasks.length > 0) {
                isProcessing = true;
                currentStep = 3;
                updateProgress();
                processNextTask();
            }
        }

        // 处理下一个任务
        function processNextTask() {
            if (!isProcessing || smpTasks.length === 0) return;

            // 找到空闲的CPU
            const freeCpuIndex = cpuStates.findIndex(state => !state);
            if (freeCpuIndex !== -1 && smpTasks.length > 0) {
                // 分配任务给CPU
                cpuStates[freeCpuIndex] = true;
                const task = smpTasks.shift();

                // 模拟处理时间
                setTimeout(() => {
                    cpuStates[freeCpuIndex] = false;
                    drawSMPSystem();

                    // 继续处理下一个任务
                    if (isProcessing) {
                        processNextTask();
                    }
                }, 2000 + Math.random() * 2000);

                drawSMPSystem();
            }

            // 如果还有任务，继续检查
            if (smpTasks.length > 0) {
                setTimeout(processNextTask, 500);
            }
        }

        // 暂停处理
        function pauseProcessing() {
            isProcessing = false;
        }

        // 重置SMP
        function resetSMP() {
            isProcessing = false;
            smpTasks = [];
            cpuStates = [false, false, false, false];
            drawSMPSystem();
        }

        // 测试相关函数
        function selectAnswer(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                showResult(true);
                currentStep = 4;
                updateProgress();
            } else {
                element.classList.add('wrong');
                showResult(false);
            }
        }

        function showResult(isCorrect) {
            const resultDiv = document.getElementById('quizResult');
            resultDiv.style.display = 'block';

            if (isCorrect) {
                resultDiv.className = 'result correct';
                resultDiv.innerHTML = '🎉 恭喜！答案正确！<br>SMP系统确实属于紧耦合系统。';
            } else {
                resultDiv.className = 'result wrong';
                resultDiv.innerHTML = '❌ 答案错误，请再想想。<br>提示：SMP系统中CPU共享内存和总线。';
            }
        }

        function showExplanation() {
            const explanation = document.getElementById('detailedExplanation');
            explanation.style.display = explanation.style.display === 'none' ? 'block' : 'none';
        }

        // 初始化
        window.onload = function() {
            resetAnimation();
            drawSMPSystem();
            updateProgress();

            // 添加一些示例任务
            for (let i = 0; i < 3; i++) {
                addTask();
            }
        };

        // 添加点击事件监听器
        smpCanvas.addEventListener('click', function(event) {
            const rect = smpCanvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 检查是否点击了CPU
            const cpuPositions = [
                {x: 150, y: 300},
                {x: 300, y: 300},
                {x: 500, y: 300},
                {x: 650, y: 300}
            ];

            cpuPositions.forEach((pos, index) => {
                const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
                if (distance < 35) {
                    // 切换CPU状态（仅用于演示）
                    if (!isProcessing) {
                        cpuStates[index] = !cpuStates[index];
                        drawSMPSystem();
                    }
                }
            });
        });
    </script>
</body>
</html>
