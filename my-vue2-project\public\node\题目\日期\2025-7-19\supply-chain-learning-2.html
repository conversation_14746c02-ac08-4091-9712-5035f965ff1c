<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应链信息流互动学习 - 第二题</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: slideInFromTop 1.2s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 2px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .flow-demo {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-area {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            border-radius: 2px;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        #flowCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        .flow-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 25px 0;
            flex-wrap: wrap;
        }

        .flow-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .demand-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
            color: white;
        }

        .supply-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
        }

        .flow-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #74b9ff;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            text-align: center;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #74b9ff;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(116,185,255,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-demand {
            color: #e84393;
            font-weight: bold;
            background: rgba(253,121,168,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .highlight-supply {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            animation: floatAround 15s infinite ease-in-out;
        }

        .shape1 {
            width: 80px;
            height: 80px;
            background: #74b9ff;
            border-radius: 50%;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape2 {
            width: 60px;
            height: 60px;
            background: #fd79a8;
            border-radius: 50%;
            top: 60%;
            right: 15%;
            animation-delay: 5s;
        }

        .shape3 {
            width: 100px;
            height: 100px;
            background: #00b894;
            border-radius: 50%;
            bottom: 20%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes slideInFromTop {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatAround {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .quiz-options {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-shape shape1"></div>
        <div class="floating-shape shape2"></div>
        <div class="floating-shape shape3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔄 供应链信息流学习</h1>
            <p>通过互动动画深度理解需求信息流与供应信息流</p>
        </div>

        <div class="main-content">
            <div class="flow-demo">
                <h2 class="section-title">📊 信息流动画演示</h2>
                
                <div class="canvas-container">
                    <canvas id="flowCanvas" width="500" height="300"></canvas>
                </div>

                <div class="flow-controls">
                    <button class="flow-btn demand-btn" onclick="animateFlow('demand', '客户订单')">客户订单</button>
                    <button class="flow-btn demand-btn" onclick="animateFlow('demand', '采购合同')">采购合同</button>
                    <button class="flow-btn supply-btn" onclick="animateFlow('supply', '完工报告单')">完工报告单</button>
                    <button class="flow-btn supply-btn" onclick="animateFlow('supply', '销售报告')">销售报告</button>
                </div>

                <p style="text-align: center; color: #636e72; margin-top: 20px;">
                    点击按钮查看不同信息的流动方向
                </p>
            </div>

            <div class="quiz-area">
                <h2 class="section-title">🎯 互动练习</h2>
                
                <div class="quiz-question">
                    📝 供应链中的信息流覆盖了从供应商、制造商到分销商，再到零售商等供应链中的所有环节，其信息流分为需求信息流和供应信息流，（　　）属于需求信息流，<strong>（请作答此空）属于供应信息流</strong>。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">A. 客户订单</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">B. 采购合同</div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">C. 完工报告单</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">D. 销售报告</div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：C. 完工报告单</strong></p>
                    <p>供应链信息流的两个方向：</p>
                    <ul>
                        <li><span class="highlight-demand">需求信息流</span>：从需方→供方流动
                            <br>• 客户订单、生产计划、采购合同等</li>
                        <li><span class="highlight-supply">供应信息流</span>：从供方→需方流动
                            <br>• 入库单、<strong>完工报告单</strong>、库存记录、可供销售量、提货发运单等</li>
                    </ul>
                    <p><strong>完工报告单</strong>是制造商完成生产后向下游传递的信息，告知产品已完工可供配送，属于典型的供应信息流。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了供应信息流的核心概念！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('flowCanvas');
        const ctx = canvas.getContext('2d');
        let currentAnimation = null;

        // 绘制供应链节点
        function drawSupplyChain() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const nodes = [
                { x: 80, y: 150, name: '供应商', color: '#fd79a8', icon: '🏭' },
                { x: 180, y: 150, name: '制造商', color: '#74b9ff', icon: '🔧' },
                { x: 280, y: 150, name: '分销商', color: '#00b894', icon: '📦' },
                { x: 380, y: 150, name: '零售商', color: '#6c5ce7', icon: '🏪' }
            ];

            // 绘制连接线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 3;
            for (let i = 0; i < nodes.length - 1; i++) {
                ctx.beginPath();
                ctx.moveTo(nodes[i].x + 25, nodes[i].y);
                ctx.lineTo(nodes[i + 1].x - 25, nodes[i + 1].y);
                ctx.stroke();
            }

            // 绘制节点
            nodes.forEach(node => {
                // 节点圆圈
                ctx.beginPath();
                ctx.arc(node.x, node.y, 25, 0, 2 * Math.PI);
                ctx.fillStyle = node.color;
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.stroke();

                // 节点文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(node.name, node.x, node.y + 4);

                // 图标
                ctx.font = '16px Arial';
                ctx.fillText(node.icon, node.x, node.y - 40);
            });
        }

        // 动画显示信息流
        function animateFlow(type, itemName) {
            if (currentAnimation) {
                cancelAnimationFrame(currentAnimation);
            }

            drawSupplyChain();
            
            const startX = type === 'demand' ? 380 : 80;
            const endX = type === 'demand' ? 80 : 380;
            const y = type === 'demand' ? 100 : 200;
            const color = type === 'demand' ? '#fd79a8' : '#00b894';
            const direction = type === 'demand' ? -1 : 1;
            
            let currentX = startX;
            let progress = 0;
            const totalDistance = Math.abs(endX - startX);
            
            function animate() {
                drawSupplyChain();
                
                // 绘制流动路径
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(startX, y);
                ctx.lineTo(endX, y);
                ctx.stroke();
                ctx.setLineDash([]);

                // 绘制信息包
                const radius = 15 + Math.sin(progress * 0.3) * 3; // 脉动效果
                ctx.beginPath();
                ctx.arc(currentX, y, radius, 0, 2 * Math.PI);
                ctx.fillStyle = color;
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 信息包文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 10px Arial';
                ctx.textAlign = 'center';
                const words = itemName.split('');
                if (words.length <= 4) {
                    ctx.fillText(itemName, currentX, y + 3);
                } else {
                    ctx.fillText(words.slice(0, 2).join(''), currentX, y - 2);
                    ctx.fillText(words.slice(2).join(''), currentX, y + 8);
                }

                // 标签
                ctx.fillStyle = color;
                ctx.font = 'bold 14px Arial';
                ctx.fillText(itemName, currentX, y + 35);

                // 方向箭头
                const arrowSize = 8;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.moveTo(currentX + direction * 20, y);
                ctx.lineTo(currentX + direction * 20 - direction * arrowSize, y - arrowSize/2);
                ctx.lineTo(currentX + direction * 20 - direction * arrowSize, y + arrowSize/2);
                ctx.closePath();
                ctx.fill();

                currentX += direction * 2;
                progress++;
                
                if ((direction > 0 && currentX < endX) || (direction < 0 && currentX > endX)) {
                    currentAnimation = requestAnimationFrame(animate);
                } else {
                    // 到达动画
                    ctx.beginPath();
                    ctx.arc(endX, y, 20, 0, 2 * Math.PI);
                    ctx.fillStyle = color;
                    ctx.fill();
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 成功提示
                    ctx.fillStyle = color;
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 到达!', endX, y - 35);
                }
            }
            
            animate();
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 完工报告单')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    animateFlow('supply', '完工报告单');
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            drawSupplyChain();
            
            // 自动演示
            setTimeout(() => {
                animateFlow('demand', '采购合同');
            }, 1000);
            
            setTimeout(() => {
                animateFlow('supply', '完工报告单');
            }, 4000);
        };
    </script>
</body>
</html>
