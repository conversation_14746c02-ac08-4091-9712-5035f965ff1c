<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机主存构成详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 960px;
            width: 100%;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #007bff;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section {
            margin-bottom: 30px;
        }
        .question-text {
            font-size: 1.5em;
            margin-bottom: 20px;
            text-align: center;
            color: #2c3e50;
        }
        .options {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }
        .option {
            background-color: #e0f2f7;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            width: 80%;
            max-width: 400px;
            text-align: center;
            font-size: 1.1em;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .option:hover {
            background-color: #c9e6f2;
            transform: translateY(-3px);
        }
        .option.selected {
            background-color: #007bff;
            color: white;
            box-shadow: 0 4px 10px rgba(0, 123, 255, 0.3);
        }
        .feedback {
            text-align: center;
            margin-top: 20px;
            font-weight: bold;
            font-size: 1.2em;
        }
        .feedback.correct {
            color: #28a745;
        }
        .feedback.incorrect {
            color: #dc3545;
        }

        .explanation-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .explanation-item {
            margin-bottom: 30px;
            background-color: #f9fbfb;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .explanation-item h3 {
            color: #007bff;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
            text-align: center;
        }
        .explanation-item p {
            font-size: 1em;
            text-align: justify;
            margin-bottom: 15px;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            background-color: #e8f5e9; /* Light green for canvas background */
            border-radius: 8px;
            overflow: hidden;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
        }
        canvas {
            border: 1px solid #ddd;
            display: block;
            background-color: #ffffff;
        }
        .interactive-button {
            display: block;
            margin: 15px auto 0;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .interactive-button:hover {
            background-color: #218838;
        }
        .interactive-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>计算机系统的主存构成详解</h1>
        <div class="question-section">
            <div class="question-text">计算机系统的主存主要是由 () 构成的。</div>
            <div class="options">
                <div class="option" data-answer="DRAM">A. DRAM</div>
                <div class="option" data-answer="SRAM">B. SRAM</div>
                <div class="option" data-answer="Cache">C. Cache</div>
                <div class="option" data-answer="EEPROM">D. EEPROM</div>
            </div>
            <div class="feedback" id="feedback"></div>
        </div>
    </div>

    <div class="container explanation-section">
        <h2>概念解析与动画演示</h2>

        <div class="explanation-item">
            <h3>DRAM (动态随机存取存储器)</h3>
            <p>DRAM 是电脑内存中最常见的类型，也就是我们常说的"内存条"。它之所以被称为"动态"，是因为它需要周期性地刷新（充电），否则存储的数据会很快丢失。DRAM 的优点是结构简单、存储密度高（同样大小能存更多数据）、成本低，因此非常适合作为计算机的主内存。</p>
            <div class="canvas-container">
                <canvas id="dramCanvas" width="400" height="200"></canvas>
            </div>
            <button class="interactive-button" id="dramButton">开始DRAM刷新演示</button>
        </div>

        <div class="explanation-item">
            <h3>SRAM (静态随机存取存储器)</h3>
            <p>SRAM 不需要刷新，数据可以保持不变，直到断电。它之所以被称为"静态"，正是因为这个特点。SRAM 的优点是速度非常快，比 DRAM 快得多，但缺点是结构复杂、存储密度低、成本高。因此，SRAM 通常用在需要极高速度但容量不大的地方，比如 CPU 内部的缓存（Cache）。</p>
            <div class="canvas-container">
                <canvas id="sramCanvas" width="400" height="200"></canvas>
            </div>
            <button class="interactive-button" id="sramButton">开始SRAM工作演示</button>
        </div>

        <div class="explanation-item">
            <h3>Cache (高速缓存)</h3>
            <p>Cache 是一种高速小容量的存储器，通常使用 SRAM 构成。它位于 CPU 和主内存（DRAM）之间，用于存储 CPU 经常访问的数据和指令。当 CPU 需要数据时，首先会在 Cache 中查找，如果找到了（命中），就直接从 Cache 中读取，速度非常快；如果没找到（未命中），再去主内存中读取，并将读取的数据一部分拷贝到 Cache 中，以便下次快速访问。Cache 的作用是提高 CPU 访问数据的速度，从而提升整个系统的性能。</p>
            <div class="canvas-container">
                <canvas id="cacheCanvas" width="400" height="200"></canvas>
            </div>
            <button class="interactive-button" id="cacheButton">开始Cache命中/未命中演示</button>
        </div>

        <div class="explanation-item">
            <h3>EEPROM (电可擦可编程只读存储器)</h3>
            <p>EEPROM 是一种非易失性存储器，即使断电数据也不会丢失。它可以通过电信号进行擦除和重新编程。EEPROM 通常用于存储少量需要长期保存的配置信息或固件，例如 BIOS 芯片。虽然它也是一种存储器，但它的读写速度相对较慢，不能作为计算机的主内存使用。</p>
            <div class="canvas-container">
                <canvas id="eepromCanvas" width="400" height="200"></canvas>
            </div>
            <button class="interactive-button" id="eepromButton">开始EEPROM数据写入演示</button>
        </div>
    </div>

    <script>
        const options = document.querySelectorAll('.option');
        const feedback = document.getElementById('feedback');
        let selectedOption = null;

        options.forEach(option => {
            option.addEventListener('click', () => {
                if (selectedOption) {
                    selectedOption.classList.remove('selected');
                }
                option.classList.add('selected');
                selectedOption = option;

                const answer = option.dataset.answer;
                if (answer === 'DRAM') {
                    feedback.textContent = '回答正确！';
                    feedback.classList.remove('incorrect');
                    feedback.classList.add('correct');
                } else {
                    feedback.textContent = '回答错误。正确答案是 A. DRAM。';
                    feedback.classList.remove('correct');
                    feedback.classList.add('incorrect');
                }
            });
        });

        // Canvas Animations - Placeholder for now, will add detailed logic in next steps

        // DRAM Animation
        const dramCanvas = document.getElementById('dramCanvas');
        const dramCtx = dramCanvas.getContext('2d');
        const dramButton = document.getElementById('dramButton');
        let dramAnimationInterval;

        function drawDramCell(ctx, x, y, value, isRefreshing) {
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 1;
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, 40, 40);
            ctx.strokeRect(x, y, 40, 40);

            ctx.fillStyle = '#007bff';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(value, x + 20, y + 20);

            if (isRefreshing) {
                ctx.fillStyle = 'rgba(40, 167, 69, 0.3)'; // Green tint for refreshing
                ctx.fillRect(x, y, 40, 40);
            }
        }

        function animateDRAM() {
            let refreshCycle = 0;
            const cells = Array(5).fill(0).map((_, i) => ({ value: Math.round(Math.random()), x: 50 + i * 60, y: 80 }));

            if (dramAnimationInterval) clearInterval(dramAnimationInterval); // Clear previous interval
            dramButton.disabled = true; // Disable button during animation

            dramAnimationInterval = setInterval(() => {
                dramCtx.clearRect(0, 0, dramCanvas.width, dramCanvas.height);
                dramCtx.fillStyle = '#333';
                dramCtx.font = '16px Arial';
                dramCtx.textAlign = 'left';
                dramCtx.fillText('DRAM 存储单元 (电容)', 10, 30);
                dramCtx.fillText('刷新状态: ' + (refreshCycle % 2 === 0 ? '充电/保持' : '衰减'), 10, 55);

                cells.forEach(cell => {
                    drawDramCell(dramCtx, cell.x, cell.y, cell.value, refreshCycle % 2 === 0);
                });

                refreshCycle++;
                if (refreshCycle > 10) { // Simulate a few cycles then stop
                    clearInterval(dramAnimationInterval);
                    dramButton.disabled = false; // Re-enable button
                    dramCtx.clearRect(0, 0, dramCanvas.width, dramCanvas.height);
                    dramCtx.fillText('DRAM 刷新演示完成', dramCanvas.width / 2, dramCanvas.height / 2);
                    dramCtx.textAlign = 'center';
                }
            }, 800);
        }
        dramButton.addEventListener('click', animateDRAM);

        // SRAM Animation
        const sramCanvas = document.getElementById('sramCanvas');
        const sramCtx = sramCanvas.getContext('2d');
        const sramButton = document.getElementById('sramButton');
        let sramAnimationInterval;

        function drawSramCell(ctx, x, y, value) {
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 1;
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, 40, 40);
            ctx.strokeRect(x, y, 40, 40);

            ctx.fillStyle = '#dc3545';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(value, x + 20, y + 20);

            ctx.fillStyle = '#6c757d';
            ctx.font = '12px Arial';
            ctx.fillText('锁存器', x + 20, y + 35);
        }

        function animateSRAM() {
            let step = 0;
            const sramValue = Math.round(Math.random());
            if (sramAnimationInterval) clearInterval(sramAnimationInterval);
            sramButton.disabled = true;

            sramAnimationInterval = setInterval(() => {
                sramCtx.clearRect(0, 0, sramCanvas.width, sramCanvas.height);
                sramCtx.fillStyle = '#333';
                sramCtx.font = '16px Arial';
                sramCtx.textAlign = 'left';
                sramCtx.fillText('SRAM 存储单元 (锁存器)', 10, 30);

                if (step === 0) {
                    sramCtx.fillText('数据写入: ' + sramValue, 10, 55);
                    drawSramCell(sramCtx, 180, 80, sramValue);
                } else if (step === 1) {
                    sramCtx.fillText('数据保持中...', 10, 55);
                    drawSramCell(sramCtx, 180, 80, sramValue);
                } else if (step === 2) {
                    sramCtx.fillText('数据读取: ' + sramValue, 10, 55);
                    drawSramCell(sramCtx, 180, 80, sramValue);
                }

                step++;
                if (step > 2) {
                    clearInterval(sramAnimationInterval);
                    sramButton.disabled = false;
                    sramCtx.clearRect(0, 0, sramCanvas.width, sramCanvas.height);
                    sramCtx.fillText('SRAM 工作演示完成', sramCanvas.width / 2, sramCanvas.height / 2);
                    sramCtx.textAlign = 'center';
                }
            }, 1000);
        }
        sramButton.addEventListener('click', animateSRAM);

        // Cache Animation
        const cacheCanvas = document.getElementById('cacheCanvas');
        const cacheCtx = cacheCanvas.getContext('2d');
        const cacheButton = document.getElementById('cacheButton');
        let cacheAnimationInterval;

        function drawMemoryBlock(ctx, x, y, label, data, highlightIndex = -1) {
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 150, 80);
            ctx.fillStyle = '#007bff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 75, y - 10);

            for (let i = 0; i < data.length; i++) {
                ctx.fillStyle = (i === highlightIndex) ? '#ffc107' : '#e0f2f7';
                ctx.fillRect(x + 10, y + 20 + i * 20, 130, 18);
                ctx.strokeStyle = '#bbb';
                ctx.strokeRect(x + 10, y + 20 + i * 20, 130, 18);
                ctx.fillStyle = '#333';
                ctx.fillText(data[i], x + 75, y + 30 + i * 20);
            }
        }

        function animateCache() {
            let step = 0;
            const mainMemoryData = ['A', 'B', 'C', 'D', 'E', 'F'];
            let cacheData = ['', '', ''];
            const requestedDataIndex = Math.floor(Math.random() * mainMemoryData.length);
            const requestedDataItem = mainMemoryData[requestedDataIndex];
            let cacheHit = false;

            if (cacheAnimationInterval) clearInterval(cacheAnimationInterval);
            cacheButton.disabled = true;

            cacheAnimationInterval = setInterval(() => {
                cacheCtx.clearRect(0, 0, cacheCanvas.width, cacheCanvas.height);
                cacheCtx.fillStyle = '#333';
                cacheCtx.font = '16px Arial';
                cacheCtx.textAlign = 'left';
                cacheCtx.fillText('CPU 请求数据: ' + requestedDataItem, 10, 30);

                drawMemoryBlock(cacheCtx, 20, 60, '主内存', mainMemoryData, requestedDataIndex);
                drawMemoryBlock(cacheCtx, 230, 60, 'Cache', cacheData);

                if (step === 0) {
                    cacheCtx.fillText('CPU 检查 Cache...', 10, 55);
                    // Check if data is in cache
                    const cacheIndex = cacheData.indexOf(requestedDataItem);
                    if (cacheIndex !== -1) {
                        cacheHit = true;
                        cacheCtx.fillStyle = '#28a745';
                        cacheCtx.fillText('Cache 命中！', 230, 55);
                        drawMemoryBlock(cacheCtx, 230, 60, 'Cache', cacheData, cacheIndex);
                    } else {
                        cacheHit = false;
                        cacheCtx.fillStyle = '#dc3545';
                        cacheCtx.fillText('Cache 未命中！', 230, 55);
                    }
                } else if (step === 1) {
                    if (cacheHit) {
                        cacheCtx.fillText('从 Cache 读取数据。', 10, 55);
                    } else {
                        cacheCtx.fillText('从主内存读取并存入 Cache。', 10, 55);
                        // Add to cache (simple replacement for demo)
                        cacheData.shift(); // Remove first element
                        cacheData.push(requestedDataItem); // Add new element to end
                        drawMemoryBlock(cacheCtx, 230, 60, 'Cache', cacheData); // Redraw cache with new data
                    }
                }

                step++;
                if (step > 1) {
                    clearInterval(cacheAnimationInterval);
                    cacheButton.disabled = false;
                    cacheCtx.clearRect(0, 0, cacheCanvas.width, cacheCanvas.height);
                    cacheCtx.fillText('Cache 演示完成', cacheCanvas.width / 2, cacheCanvas.height / 2);
                    cacheCtx.textAlign = 'center';
                }
            }, 1500);
        }
        cacheButton.addEventListener('click', animateCache);


        // EEPROM Animation
        const eepromCanvas = document.getElementById('eepromCanvas');
        const eepromCtx = eepromCanvas.getContext('2d');
        const eepromButton = document.getElementById('eepromButton');
        let eepromAnimationInterval;

        function drawEepromCell(ctx, x, y, value, isWriting) {
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 1;
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, 40, 40);
            ctx.strokeRect(x, y, 40, 40);

            ctx.fillStyle = '#6f42c1'; // Purple for non-volatile
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(value, x + 20, y + 20);

            if (isWriting) {
                ctx.fillStyle = 'rgba(111, 66, 193, 0.3)'; // Purple tint for writing
                ctx.fillRect(x, y, 40, 40);
            }
        }

        function animateEEPROM() {
            let step = 0;
            let eepromValue = '0';
            if (eepromAnimationInterval) clearInterval(eepromAnimationInterval);
            eepromButton.disabled = true;

            eepromAnimationInterval = setInterval(() => {
                eepromCtx.clearRect(0, 0, eepromCanvas.width, eepromCanvas.height);
                eepromCtx.fillStyle = '#333';
                eepromCtx.font = '16px Arial';
                eepromCtx.textAlign = 'left';
                eepromCtx.fillText('EEPROM 存储单元', 10, 30);

                if (step === 0) {
                    eepromCtx.fillText('擦除中...', 10, 55);
                    drawEepromCell(eepromCtx, 180, 80, '_', true); // Show erasing
                } else if (step === 1) {
                    eepromValue = Math.round(Math.random()).toString();
                    eepromCtx.fillText('写入数据: ' + eepromValue, 10, 55);
                    drawEepromCell(eepromCtx, 180, 80, eepromValue, true); // Show writing
                } else if (step === 2) {
                    eepromCtx.fillText('数据已写入并保存 (非易失)', 10, 55);
                    drawEepromCell(eepromCtx, 180, 80, eepromValue, false); // Show saved
                }

                step++;
                if (step > 2) {
                    clearInterval(eepromAnimationInterval);
                    eepromButton.disabled = false;
                    eepromCtx.clearRect(0, 0, eepromCanvas.width, eepromCanvas.height);
                    eepromCtx.fillText('EEPROM 演示完成', eepromCanvas.width / 2, eepromCanvas.height / 2);
                    eepromCtx.textAlign = 'center';
                }
            }, 1500);
        }
        eepromButton.addEventListener('click', animateEEPROM);

    </script>
</body>
</html> 