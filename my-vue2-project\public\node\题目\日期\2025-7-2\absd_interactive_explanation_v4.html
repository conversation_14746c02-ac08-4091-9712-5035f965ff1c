<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式学习：基于架构的软件设计 (ABSD)</title>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --background-color: #f8f9fa;
            --card-bg-color: #ffffff;
            --text-color: #333;
            --light-text-color: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        #main-container {
            max-width: 900px;
            width: 100%;
        }
        .card {
            background-color: var(--card-bg-color);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            padding: 25px 30px;
            transition: all 0.3s ease;
        }
        h1, h2 {
            text-align: center;
            color: var(--primary-color);
        }
        h1 {
            font-size: 2.2rem;
            margin-bottom: 30px;
        }
        h2 {
            font-size: 1.6rem;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        p, li {
            line-height: 1.8;
            font-size: 1.1rem;
        }
        .question-text {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid var(--secondary-color);
        }
        #absd-canvas {
            width: 100%;
            height: auto;
            aspect-ratio: 4 / 3;
            background-color: #fdfdfd;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }
        .explanation-point {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        .explanation-point.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .highlight-green { color: var(--success-color); font-weight: bold; }
        .highlight-red { color: var(--danger-color); font-weight: bold; }
        .highlight-blue { color: var(--primary-color); font-weight: bold; }
    </style>
</head>
<body>

<div id="main-container">
    <h1>基于架构的软件设计 (ABSD) 交互式学习</h1>

    <div class="card">
        <h2>原题回顾</h2>
        <div class="question-text">
            <p>某公司采用基于架构的软件设计 (Architecture-Based Software Design, ABSD) 方法进行软件设计与开发。ABSD方法有三个基础，分别是系统功能分解、采用 <span class="highlight-blue">[?]</span> 实现质量属性与商业需求、采用软件模板设计软件结构。ABSD方法主要包括架构需求等6个主要活动，其中 <span class="highlight-blue">[?]</span> 活动的目标是标识潜在的风险,及早发现架构设计中的缺陷和错误；<span class="highlight-blue">[?]</span> 活动针对用户的需求变化,修改应用架构,满足新的需求。</p>
        </div>
    </div>

    <div class="card">
        <h2>交互式动画演示</h2>
        <canvas id="absd-canvas"></canvas>
        <div class="controls">
            <button id="prevBtn" disabled>上一步</button>
            <button id="nextBtn">下一步</button>
        </div>
    </div>

    <div class="card">
        <h2>知识点总结</h2>
        <div id="explanation">
            <p class="explanation-point" id="exp-start">点击“下一步”开始学习ABSD的核心概念吧！</p>
            <p class="explanation-point" id="exp-foundation"><strong>第一步：ABSD的三大基础。</strong> 就像盖房子需要地基，ABSD方法建立在三个核心思想之上：<br>1. <strong>功能分解：</strong> 把一个大系统拆分成多个小功能。<br>2. <span class="highlight-green">架构风格：</span> 这是第一个空缺的答案。指通用的、可复用的解决方案，比如"客户-服务器"或"微服务"风格，用来保证软件的性能、安全等质量。<br>3. <strong>软件模板：</strong> 提供一个通用的结构框架来指导设计。</p>
            <p class="explanation-point" id="exp-review"><strong>第二步：架构复审 (Architecture Review)。</strong> 这是第二个空缺的答案。这个活动就像是"架构的质检"，它的目标是：<br>1. <span class="highlight-red">识别风险：</span> 在编码前，主动找出设计里可能出问题的地方。<br>2. <strong>发现缺陷：</strong> 提前修正设计中的错误，避免后期付出巨大修改代价。</p>
            <p class="explanation-point" id="exp-evolution"><strong>第三步：架构演化 (Architecture Evolution)。</strong> 这是第三个空缺的答案。软件不是一成不变的，它需要成长和适应。这个活动就是：<br>1. <strong>响应变化：</strong> 当用户有了新需求时。<br>2. <strong>修改架构：</strong> 对现有设计进行调整或扩展，以满足这些新需求。</p>
            <p class="explanation-point" id="exp-conclusion"><strong>核心总结：</strong> 通过刚才的演示，我们知道题目的三个空分别是 <span class="highlight-blue">架构风格</span>、<span class="highlight-blue">架构复审</span> 和 <span class="highlight-blue">架构演化</span>。题目选项虽然讨论的是"文档"，但理解了这些活动，就能更好地理解软件开发的全过程。对于选项B"随时保证文档都是最新的"，在实际工程中，我们追求文档<span class="highlight-green">保持更新</span>，但更要<span class="highlight-warning">保持稳定</span>，过于频繁的修改反而会造成混乱。但在考试中，它往往被作为"理想状态"的正确答案。</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('absd-canvas');
    const ctx = canvas.getContext('2d');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const explanations = {
        start: document.getElementById('exp-start'),
        foundation: document.getElementById('exp-foundation'),
        review: document.getElementById('exp-review'),
        evolution: document.getElementById('exp-evolution'),
        conclusion: document.getElementById('exp-conclusion')
    };

    let stage = 0;
    const totalStages = 4;

    function resizeCanvas() {
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
        draw();
    }
    window.addEventListener('resize', resizeCanvas);
    
    const colors = {
        primary: '#007bff',
        secondary: '#6c757d',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        light: '#f8f9fa',
        dark: '#333'
    };

    let animationFrameId;
    let progress = 0; // 0 to 1 for animations

    function draw() {
        if (animationFrameId) cancelAnimationFrame(animationFrameId);
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        switch (stage) {
            case 0:
                drawIntro();
                break;
            case 1:
                animateFoundation();
                break;
            case 2:
                animateReview();
                break;
            case 3:
                animateEvolution();
                break;
            case 4:
                drawConclusion();
                break;
        }
    }
    
    function startAnimation(drawFunction) {
        progress = 0;
        let startTime = null;
        const duration = 1000;

        function animationLoop(timestamp) {
            if (!startTime) startTime = timestamp;
            const elapsed = timestamp - startTime;
            progress = Math.min(elapsed / duration, 1);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawFunction(progress);

            if (progress < 1) {
                animationFrameId = requestAnimationFrame(animationLoop);
            }
        }
        animationFrameId = requestAnimationFrame(animationLoop);
    }
    
    // Stage 0: Intro
    function drawIntro() {
        drawText("ABSD 交互式动画", canvas.width / 2, canvas.height / 2 - 20, 30, colors.primary, 'bold');
        drawText("点击“下一步”开始", canvas.width / 2, canvas.height / 2 + 20, 20, colors.secondary);
    }
    
    // Stage 1: Foundation
    function drawFoundation(p) {
        drawText("ABSD 三大基础", canvas.width / 2, 50, 24, colors.primary, 'bold');
        const boxWidth = canvas.width / 4;
        const boxHeight = 80;
        const gap = 20;
        const totalWidth = 3 * boxWidth + 2 * gap;
        const startX = (canvas.width - totalWidth) / 2;
        const y = canvas.height / 2 - boxHeight / 2;

        const p1 = Math.min(1, p * 3);
        const p2 = Math.min(1, Math.max(0, p * 3 - 1));
        const p3 = Math.min(1, Math.max(0, p * 3 - 2));

        // Box 1
        drawBoxWithText(startX, y, boxWidth, boxHeight * p1, "1. 功能分解", colors.secondary);
        // Box 2
        drawBoxWithText(startX + boxWidth + gap, y, boxWidth, boxHeight * p2, "2. 架构风格", colors.success, true);
        // Box 3
        drawBoxWithText(startX + 2 * (boxWidth + gap), y, boxWidth, boxHeight * p3, "3. 软件模板", colors.secondary);
    }
    function animateFoundation() { startAnimation(drawFoundation); }

    // Stage 2: Review
    function drawReview(p) {
        drawText("活动: 架构复审", canvas.width / 2, 50, 24, colors.primary, 'bold');
        const archX = canvas.width / 2;
        const archY = canvas.height / 2 + 50;
        
        // Draw Architecture
        drawArchitecture(archX, archY, 1);

        // Draw bugs appearing
        const bugP = Math.min(1, p * 2);
        drawBug(archX - 80, archY - 40, bugP);
        drawBug(archX + 40, archY + 20, bugP);

        // Draw magnifying glass
        const glassP = Math.min(1, Math.max(0, p * 2 - 1));
        if (glassP > 0) {
            const glassX = archX - 150 + 300 * glassP;
            drawMagnifyingGlass(glassX, archY - 50, glassP);
            // Highlight bugs when glass is over them
            if (glassX > archX - 100 && glassX < archX - 60) highlightBug(archX - 80, archY - 40);
            if (glassX > archX + 20 && glassX < archX + 60) highlightBug(archX + 40, archY + 20);
        }
    }
    function animateReview() { startAnimation(drawReview); }

    // Stage 3: Evolution
    function drawEvolution(p) {
        drawText("活动: 架构演化", canvas.width / 2, 50, 24, colors.primary, 'bold');
        const archX = canvas.width / 2 - 100;
        const archY = canvas.height / 2 + 50;
        
        const appearP = Math.min(1, p * 2);
        const moveP = Math.min(1, Math.max(0, p * 2 - 1));

        // Draw initial architecture
        drawArchitecture(archX - (50 * moveP), archY, 1);
        
        // Draw "New Requirement"
        ctx.globalAlpha = appearP;
        drawBoxWithText(canvas.width - 200, canvas.height/2 - 50, 150, 60, "新需求", colors.warning);
        ctx.globalAlpha = 1;
        
        // Draw arrow
        if (appearP === 1) {
            drawArrow(canvas.width - 220, canvas.height / 2 - 20, archX + 130, archY - 20, moveP);
        }
        
        // Draw new module
        if(moveP > 0) {
            ctx.save();
            ctx.translate(archX + 80, archY + 60);
            ctx.scale(moveP, moveP);
            ctx.globalAlpha = moveP;
            drawBoxWithText(0, 0, 80, 50, "新模块", colors.success);
            ctx.restore();
        }
    }
    function animateEvolution() { startAnimation(drawEvolution); }

    // Stage 4: Conclusion
    function drawConclusion() {
        drawText("恭喜你！已完成所有学习", canvas.width / 2, canvas.height / 2 - 20, 26, colors.success, 'bold');
        drawText("请查看下方的知识点总结", canvas.width / 2, canvas.height / 2 + 20, 20, colors.secondary);
    }
    
    // --- Drawing Helpers ---
    function drawText(text, x, y, size = 16, color = '#333', weight = 'normal') {
        ctx.fillStyle = color;
        ctx.font = `${weight} ${size}px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x, y);
    }

    function drawBoxWithText(x, y, w, h, text, color, highlight = false) {
        ctx.save();
        if (h <= 0) return;
        ctx.fillStyle = color;
        ctx.strokeStyle = highlight ? colors.warning : colors.dark;
        ctx.lineWidth = highlight ? 4 : 2;
        ctx.beginPath();
        ctx.rect(x, y, w, h);
        ctx.fill();
        ctx.stroke();
        
        if (h >= 50) {
           drawText(text, x + w / 2, y + h / 2, 16, 'white', 'bold');
        }
        ctx.restore();
    }
    
    function drawArchitecture(x, y, p) {
        ctx.save();
        ctx.translate(x,y);
        ctx.scale(p,p);
        const w = 60, h = 40, gap = 20;
        // Main block
        ctx.fillStyle = colors.primary;
        ctx.fillRect(-w, -h-gap/2-h, w*2, h);
        drawText("主模块", 0, -h-gap/2-h+h/2, 14, 'white');
        // Sub modules
        ctx.fillStyle = colors.secondary;
        ctx.fillRect(-w-gap-w/2, -h/2, w, h);
        drawText("模块A", -w-gap, h/2-h/2, 14, 'white');
        ctx.fillRect(-w/2, -h/2, w, h);
        drawText("模块B", 0, h/2-h/2, 14, 'white');
        ctx.fillRect(w/2+gap, -h/2, w, h);
        drawText("模块C", w+gap-w/2, h/2-h/2, 14, 'white');
        // Lines
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(0, -h-gap/2);
        ctx.lineTo(0, -h-gap/2-h+h);
        ctx.moveTo(-w-gap, 0);
        ctx.lineTo(-w-gap, -h/2);
        ctx.moveTo(0, 0);
        ctx.lineTo(0, -h/2);
        ctx.moveTo(w+gap-w/2, 0);
        ctx.lineTo(w+gap-w/2, -h/2);
        ctx.moveTo(-w-gap,0);
        ctx.lineTo(w+gap-w/2,0);
        ctx.stroke();

        ctx.restore();
    }

    function drawBug(x, y, p) {
        if (p <= 0) return;
        ctx.save();
        ctx.translate(x, y);
        ctx.scale(p, p);
        ctx.fillStyle = colors.danger;
        ctx.beginPath();
        ctx.arc(0, 0, 8, 0, Math.PI * 2);
        ctx.fill();
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 1.5;
        // little legs
        for(let i = 0; i < 6; i++) {
            const angle = Math.PI / 3 * i;
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle)*6, Math.sin(angle)*6);
            ctx.lineTo(Math.cos(angle)*12, Math.sin(angle)*12);
            ctx.stroke();
        }
        ctx.restore();
    }
    
    function highlightBug(x, y) {
        ctx.save();
        ctx.strokeStyle = colors.warning;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.arc(x, y, 15, 0, Math.PI * 2);
        ctx.stroke();
        ctx.restore();
    }

    function drawMagnifyingGlass(x, y, p) {
        ctx.save();
        ctx.translate(x, y);
        ctx.scale(p, p);
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 4;
        // Handle
        ctx.rotate(Math.PI / 4);
        ctx.beginPath();
        ctx.roundRect(25, -5, 40, 10, 5);
        ctx.stroke();
        ctx.rotate(-Math.PI / 4);
        // Lens
        ctx.beginPath();
        ctx.arc(0, 0, 30, 0, Math.PI * 2);
        ctx.stroke();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.fill();
        ctx.restore();
    }
    
    function drawArrow(x1, y1, x2, y2, p) {
        if (p <= 0) return;
        const headlen = 10;
        const newX2 = x1 + (x2 - x1) * p;
        const newY2 = y1 + (y2 - y1) * p;
        const angle = Math.atan2(newY2 - y1, newX2 - x1);
        
        ctx.save();
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(newX2, newY2);
        if (p > 0.9) {
            ctx.lineTo(newX2 - headlen * Math.cos(angle - Math.PI / 6), newY2 - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(newX2, newY2);
            ctx.lineTo(newX2 - headlen * Math.cos(angle + Math.PI / 6), newY2 - headlen * Math.sin(angle + Math.PI / 6));
        }
        ctx.stroke();
        ctx.restore();
    }

    // --- Control Logic ---
    function updateUI() {
        // Buttons
        prevBtn.disabled = stage === 0;
        nextBtn.disabled = stage === totalStages;
        
        // Explanations
        Object.values(explanations).forEach(el => el.classList.remove('visible'));
        
        switch (stage) {
            case 0: explanations.start.classList.add('visible'); break;
            case 1: explanations.foundation.classList.add('visible'); break;
            case 2: explanations.review.classList.add('visible'); break;
            case 3: explanations.evolution.classList.add('visible'); break;
            case 4: 
                explanations.foundation.classList.add('visible');
                explanations.review.classList.add('visible');
                explanations.evolution.classList.add('visible');
                explanations.conclusion.classList.add('visible');
                break;
        }
        
        draw();
    }

    nextBtn.addEventListener('click', () => {
        if (stage < totalStages) {
            stage++;
            updateUI();
        }
    });

    prevBtn.addEventListener('click', () => {
        if (stage > 0) {
            stage--;
            updateUI();
        }
    });

    // Initial setup
    resizeCanvas();
    updateUI();
});
</script>

</body>
</html> 