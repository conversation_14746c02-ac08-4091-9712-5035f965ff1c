<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研长难句词缀故事动画 - 监管改革的阻力</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(to right top, #00c6ff, #0072ff);
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 900px;
            background-color: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 15px;
        }
        .sentence {
            font-size: 1.3em;
            margin: 25px 0;
            color: #34495e;
            font-weight: 500;
            line-height: 1.6;
        }
        .sentence strong {
            color: #0072ff;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }
        .sentence strong:hover {
            color: #ff4757;
            text-shadow: 0 0 8px rgba(255, 71, 87, 0.5);
        }
        .sentence-translation {
            font-size: 1em;
            margin-bottom: 25px;
            color: #7f8c8d;
        }
        canvas {
            background-color: #fdfdfd;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            cursor: pointer;
            display: block;
            margin: 0 auto 25px auto;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 25px;
        }
        .controls button {
            padding: 12px 25px;
            font-size: 1.1em;
            border: none;
            border-radius: 10px;
            background-color: #0072ff;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 10px rgba(0, 114, 255, 0.2);
        }
        .controls button:hover, .controls button:focus {
            background-color: #0056b3;
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 114, 255, 0.3);
            outline: none;
        }
        .controls button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
            transform: translateY(0);
            box-shadow: none;
        }
        .explanation {
            min-height: 120px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 6px solid #00c6ff;
            text-align: left;
            line-height: 1.7;
            font-size: 1.05em;
        }
        .explanation strong {
            color: #ff4757;
        }
        .footer {
            margin-top: 25px;
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>

<div class="container">
    <h1>探秘监管改革的巨大阻力</h1>
    <p class="sentence">
        The <strong>overwhelming</strong> <strong>opposition</strong> to the proposal, articulated by various stakeholders, <strong>underscores</strong> the profound <strong>aversions</strong> many people have to any form of <strong>regulatory</strong> <strong>reform</strong>.
    </p>
    <p class="sentence-translation">
        <strong>句子翻译:</strong> 由不同利益相关者阐明的、对该提案的压倒性反对，突显了许多人对任何形式的监管改革都怀有深深的厌恶。
    </p>
    <canvas id="wordCanvas" width="800" height="300"></canvas>
    <div class="controls">
        <button id="prevBtn">上一个故事</button>
        <button id="nextBtn">下一个故事</button>
    </div>
    <div class="explanation" id="explanationBox">
        <p>点击"下一个故事"按钮，让我们一起通过动画故事，拆解这个复杂的句子！你也可以直接点击句子中高亮的单词，直接查看对应的故事。</p>
    </div>
    <div class="footer">
        <p>通过生动的故事和互动，让考研英语学习不再枯燥。</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const explanationBox = document.getElementById('explanationBox');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const sentenceWords = document.querySelectorAll('.sentence strong');

    const wordsData = [
        {
            word: 'overwhelming',
            fullStory: '<strong>overwhelming (压倒性的):</strong> 想象一下，一股巨大的力量从你的<strong>头顶(over)</strong>上方，猛地<strong>压(whelm)</strong>下来，让你无法抗拒。这就是"压倒性的"。前缀 `over-` 意为"在...之上"，而 `whelm` 是一个古老的词，意为"压倒，淹没"。动画将展示一个巨大的重物从天而降，将一个小物体完全压住。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const groundY = 250;
                // Draw small object
                ctx.fillStyle = '#3498db';
                ctx.fillRect(canvas.width / 2 - 25, groundY - 50, 50, 50);

                // Draw overwhelming weight
                const weightY = -100 + (groundY - 150) * Math.min(1, p * 2);
                ctx.fillStyle = '#c0392b';
                ctx.fillRect(canvas.width / 2 - 100, weightY, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('OVER', canvas.width / 2, weightY + 60);

                // Show explanation text
                if (p > 0.5) {
                    const textProgress = (p - 0.5) / 0.5;
                    ctx.globalAlpha = textProgress;
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '28px Arial';
                    ctx.fillText('over(在...之上) + whelm(压倒) = 压倒性的', canvas.width / 2, 50);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'opposition',
            fullStory: '<strong>opposition (反对):</strong> 想象两个人，他们背对背站着，把各自的观点<strong>放(pos)</strong>在<strong>对立面(op-)</strong>。`op-` 是 `ob-` 的变体，表示"相反，对抗"，`pos` 词根意为"放置"。因此，把观点放在对立面，就是"反对"。动画将展示两个小人背对背，中间出现一条鸿沟，表示他们的立场完全对立。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const groundY = 220;
                
                const pos1 = centerX - 50 - 150 * p;
                const pos2 = centerX + 50 + 150 * p;
                
                // Draw person 1
                ctx.fillStyle = '#e67e22';
                ctx.fillRect(pos1 - 20, groundY - 80, 40, 80);
                
                // Draw person 2
                ctx.fillStyle = '#9b59b6';
                ctx.fillRect(pos2 - 20, groundY - 80, 40, 80);

                // Draw gap
                if (p > 0.3) {
                    const gapWidth = 300 * ((p - 0.3) / 0.7);
                    ctx.fillStyle = '#2c3e50';
                    ctx.fillRect(centerX - gapWidth / 2, groundY, gapWidth, 20);
                }

                if (p > 0.6) {
                    ctx.globalAlpha = (p-0.6)/0.4;
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '28px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('op(相反) + pos(放置) = 反对', centerX, 60);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'underscores',
            fullStory: '<strong>underscores (突显，强调):</strong> 这个词非常形象。想象你在看书时，为了强调重点，会用笔在重要的词语<strong>下面(under)</strong>划<strong>线(score)</strong>。`under-` 就是"在...下面"，`score` 有"划线"的意思。所以，这个词的本意就是在下面划线，引申为"强调"。动画将展示一段文字，然后一只笔在文字下方划出一条重点线。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const textY = 150;
                const text = "This is important";
                
                // Draw text
                ctx.fillStyle = '#2c3e50';
                ctx.font = '40px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(text, canvas.width / 2, textY);

                // Draw the line
                if (p > 0.2) {
                    const lineWidth = 300 * ((p-0.2)/0.8);
                    ctx.strokeStyle = '#e74c3c';
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(canvas.width / 2 - 150, textY + 20);
                    ctx.lineTo(canvas.width / 2 - 150 + lineWidth, textY + 20);
                    ctx.stroke();
                }

                 if (p > 0.7) {
                    ctx.globalAlpha = (p-0.7)/0.3;
                    ctx.fillStyle = '#c0392b';
                    ctx.fillText('under(在...下) + score(划线) = 强调', canvas.width/2, 250);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'aversions',
            fullStory: '<strong>aversions (厌恶):</strong> 当你遇到不喜欢的东西时，你的第一反应是什么？是不是想<strong>转身(vers)</strong>，<strong>离开(a-)</strong>？前缀 `a-` 是 `ab-` 的变体，表示"离开，相反"，词根 `vers` 表示"转动"。所以，转身离开，就是"厌恶"。动画将展示一个小人看到一个不喜欢的东西（比如一坨...），然后迅速转身跑开。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const groundY = 220;
                const personStartX = canvas.width / 2 - 100;
                const thingX = canvas.width / 2 + 100;
                
                // Draw "bad thing"
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.arc(thingX, groundY, 30, Math.PI, Math.PI * 2);
                ctx.fill();
                
                const turnProgress = Math.min(1, p * 3);
                const leaveProgress = Math.max(0, (p-0.33)/0.67);
                
                let personX = personStartX + (leaveProgress * -200);
                
                ctx.save();
                ctx.translate(personX, groundY - 40);
                if (turnProgress > 0.5) {
                   ctx.scale(-1, 1); // Turn around
                }
                // Draw person
                ctx.fillStyle = '#2980b9';
                ctx.fillRect(-20, 0, 40, 80);
                ctx.restore();


                if (p > 0.6) {
                    ctx.globalAlpha = (p-0.6)/0.4;
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '28px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('a(离开) + vers(转) = 厌恶', canvas.width / 2, 60);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'regulatory',
            fullStory: '<strong>regulatory (监管的):</strong> 看到这个词，你有没有想到 `regular` (规则的) 或者 `rule` (规则)？它们都来自同一个词根 `reg`，表示"统治，指导，规则"。所以，`regulatory` 就是和<strong>规则(reg)</strong>有关的，即"监管的"。动画将展示一只手正在制定和颁布规则（一个卷轴）。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const scrollY = 150;
                const scrollWidth = 200;
                const scrollHeight = 120;

                const unfoldProgress = Math.min(1, p * 1.5);

                // Draw scroll
                ctx.fillStyle = '#f1e7d0';
                ctx.fillRect(centerX - scrollWidth / 2, scrollY - (scrollHeight*unfoldProgress) / 2, scrollWidth, scrollHeight * unfoldProgress);
                
                // Draw text on scroll
                if(unfoldProgress > 0.8) {
                    ctx.fillStyle = '#5D4037';
                    ctx.font = '20px "Courier New", monospace';
                    ctx.textAlign = 'center';
                    ctx.fillText('Rule 1: ...', centerX, scrollY - 20);
                    ctx.fillText('Rule 2: ...', centerX, scrollY + 10);
                    ctx.fillText('Rule 3: ...', centerX, scrollY + 40);
                }

                if (p > 0.6) {
                    ctx.globalAlpha = (p-0.6)/0.4;
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '28px Arial';
                    ctx.fillText('reg(规则) + ulatory = 监管的', centerX, 270);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'reform',
            fullStory: '<strong>reform (改革):</strong> 这个词很简单。前缀 `re-` 表示"再一次"，`form` 表示"形式，塑造"。所以，`reform` 就是<strong>再一次(re-)</strong>去<strong>塑造(form)</strong>它的形态，也就是"改革，改造"。动画将展示一个歪歪扭扭的形状，然后一只手进来，把它重新塑造成一个规整的形状。',
            animation: (p) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const centerY = 160;

                const morphProgress = Math.min(1, p * 1.2);

                // Draw the shape
                ctx.strokeStyle = '#8e44ad';
                ctx.lineWidth = 8;
                ctx.beginPath();
                
                const initialPoints = [{x: -80, y: -50}, {x: 80, y: -30}, {x: 50, y: 70}, {x: -60, y: 40}];
                const finalPoints = [{x: -70, y: -70}, {x: 70, y: -70}, {x: 70, y: 70}, {x: -70, y: 70}];

                ctx.moveTo(centerX + initialPoints[0].x + (finalPoints[0].x - initialPoints[0].x) * morphProgress, 
                           centerY + initialPoints[0].y + (finalPoints[0].y - initialPoints[0].y) * morphProgress);

                for(let i=0; i<4; i++) {
                    const next = (i+1)%4;
                     ctx.lineTo(centerX + initialPoints[next].x + (finalPoints[next].x - initialPoints[next].x) * morphProgress, 
                               centerY + initialPoints[next].y + (finalPoints[next].y - initialPoints[next].y) * morphProgress);
                }
                ctx.closePath();
                ctx.stroke();
                
                // Draw hand
                if(p > 0.1 && p < 0.9) {
                    const handProgress = Math.sin(p * Math.PI);
                    ctx.fillStyle = '#f39c12';
                    ctx.font = '80px Arial';
                    ctx.fillText('✋', centerX + 120, centerY + 80 * handProgress);
                }

                 if (p > 0.7) {
                    ctx.globalAlpha = (p-0.7)/0.3;
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '28px Arial';
                    ctx.fillText('re(再) + form(塑造) = 改革', centerX, 280);
                    ctx.globalAlpha = 1;
                }
            }
        }
    ];

    let currentWordIndex = -1;
    let animationFrameId;
    let startTime;

    function animate(time) {
        if (!startTime) startTime = time;
        const progress = Math.min((time - startTime) / 1500, 1);
        
        if (currentWordIndex >= 0 && currentWordIndex < wordsData.length) {
            wordsData[currentWordIndex].animation(progress);
        }

        if (progress < 1) {
            animationFrameId = requestAnimationFrame(animate);
        }
    }

    function showWord(index) {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }
        
        if (index < 0 || index >= wordsData.length) {
            currentWordIndex = -1;
             ctx.clearRect(0, 0, canvas.width, canvas.height);
             explanationBox.innerHTML = `<p>所有故事都讲完啦！您可以点击"上一个故事"回顾，或刷新页面重新开始。</p>`;
             prevBtn.disabled = index >= wordsData.length;
             nextBtn.disabled = index >= wordsData.length;
            return;
        }

        currentWordIndex = index;
        
        explanationBox.innerHTML = wordsData[currentWordIndex].fullStory;
        startTime = null;
        animationFrameId = requestAnimationFrame(animate);

        // Update buttons state
        prevBtn.disabled = currentWordIndex === 0;
        nextBtn.disabled = currentWordIndex === wordsData.length - 1;

        // Update active word style
        sentenceWords.forEach((wordEl, i) => {
             const wordData = wordsData.find(d => d.word === wordEl.textContent.toLowerCase());
             if(wordData && wordData.word === wordsData[currentWordIndex].word) {
                 wordEl.style.color = '#ff4757';
                 wordEl.style.textShadow = '0 0 8px rgba(255, 71, 87, 0.5)';
             } else {
                 wordEl.style.color = '#0072ff';
                 wordEl.style.textShadow = 'none';
             }
        });
    }

    nextBtn.addEventListener('click', () => {
        if (currentWordIndex < wordsData.length) {
            showWord(currentWordIndex + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentWordIndex > 0) {
            showWord(currentWordIndex - 1);
        }
    });

    sentenceWords.forEach(wordEl => {
        wordEl.addEventListener('click', () => {
            const word = wordEl.textContent.toLowerCase();
            const index = wordsData.findIndex(d => d.word === word);
            if (index !== -1) {
                showWord(index);
            }
        });
    });

    // Initial state
    showWord(-1); // Start with no word selected
    explanationBox.innerHTML = `<p>点击"下一个故事"按钮，让我们一起通过动画故事，拆解这个复杂的句子！你也可以直接点击句子中高亮的单词，直接查看对应的故事。</p>`;
    prevBtn.disabled = true;
    nextBtn.disabled = false;


</script>
</body>
</html> 