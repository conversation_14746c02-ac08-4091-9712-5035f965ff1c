<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Predict</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Roboto:wght@300;400&display=swap');

        :root {
            --primary-color: #9b59b6; /* Amethyst Purple */
            --secondary-color: #8e44ad;
            --glow-color: #c39bd3;
            --light-bg: #4a235a;
            --panel-bg: #2c003e;
            --text-color: #f2f2f2;
            --canvas-bg: #1a0024; 
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #11001c;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border: 1px solid var(--primary-color);
            border-radius: 20px;
            box-shadow: 0 0 40px rgba(155, 89, 182, 0.4);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
            text-shadow: 0 0 10px var(--glow-color);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--glow-color);
            margin-bottom: 20px;
            font-family: 'Orbitron', sans-serif;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: rgba(0,0,0,0.2);
            border-left: 3px solid var(--primary-color);
            border-radius: 10px;
        }

        .morpheme-btn {
            font-family: 'Orbitron', sans-serif;
            padding: 8px 15px;
            border: 2px solid var(--glow-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--glow-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 0 15px var(--glow-color);
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
        }
        
        .control-button {
            font-family: 'Orbitron', sans-serif;
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 20px var(--glow-color);
            z-index: 10;
        }
        .control-button:hover { background-color: var(--secondary-color); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>predict</h1>
            <p class="pronunciation">[prɪˈdɪkt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 预言, 预测, 预示</p>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="pre-game">pre- (before, 事先)</button>
                    <button class="morpheme-btn" data-activity="dict-game">-dict- (to say, 言说)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：命运轨迹</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Predict!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let currentTicker = null;
        let elements = [];

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        function clearCanvas() {
            ctx.fillStyle = 'rgba(26, 0, 36, 0.5)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        function stopCurrentAnimation() {
            if (currentTicker) {
                gsap.ticker.remove(currentTicker);
                currentTicker = null;
            }
            gsap.killTweensOf(elements);
            elements = [];
        }

        // --- Morpheme Games ---
        function initPreGame() {
            stopCurrentAnimation();
            let process = { width: 0 };
            let result = { alpha: 0 };

            function draw() {
                clearCanvas();
                // Draw process bar
                ctx.fillStyle = '#8e44ad';
                ctx.fillRect(50, canvas.height/2, process.width, 20);
                ctx.strokeStyle = '#c39bd3';
                ctx.strokeRect(50, canvas.height/2, 300, 20);
                // Draw result
                ctx.globalAlpha = result.alpha;
                ctx.fillStyle = '#f2f2f2';
                ctx.font = '20px Orbitron';
                ctx.fillText("RESULT", 400, canvas.height/2 + 15);
                ctx.globalAlpha = 1;
            }
            gsap.ticker.add(draw);
            currentTicker = draw;

            controlBtn.textContent = '预见 (pre-)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                const tl = gsap.timeline();
                // Result appears before process is complete
                tl.to(result, { alpha: 1, duration: 0.5 }, "+=0.5")
                  .to(process, { width: 300, duration: 2, ease: 'linear' });
            };
        }

        function initDictGame() {
             stopCurrentAnimation();
             function createWave() {
                 let wave = { radius: 0, alpha: 1 };
                 elements.push(wave);
                 gsap.to(wave, {
                     radius: 200,
                     alpha: 0,
                     duration: 2.5,
                     ease: 'power1.out',
                     onComplete: () => {
                         elements = elements.filter(w => w !== wave);
                     }
                 });
             }

             function draw() {
                 clearCanvas();
                 elements.forEach(wave => {
                    ctx.strokeStyle = `rgba(195, 155, 211, ${wave.alpha})`;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(canvas.width/2, canvas.height/2, wave.radius, 0, Math.PI*2);
                    ctx.stroke();
                 });
             }
             gsap.ticker.add(draw);
             currentTicker = draw;

             controlBtn.textContent = '言说 (dict-)';
             controlBtn.classList.remove('hidden');
             controlBtn.onclick = () => {
                gsap.to({}, { duration: 1, repeat: 2, onRepeat: createWave });
                createWave();
             };
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            stopCurrentAnimation();
            const traveler = { x: 50, y: canvas.height/2, radius: 5 };
            const pathPoints = 20;
            for(let i = 0; i < pathPoints; i++) {
                elements.push({
                    x: (canvas.width / pathPoints) * (i + 0.5),
                    y: canvas.height/2 + Math.sin(i / pathPoints * Math.PI * 2) * 100,
                    radius: 2,
                    alpha: 0
                });
            }
            
            function draw() {
                clearCanvas();
                // Draw path stars
                elements.forEach(star => {
                    ctx.fillStyle = `rgba(241, 196, 15, ${star.alpha})`;
                    ctx.beginPath();
                    ctx.arc(star.x, star.y, star.radius, 0, Math.PI*2);
                    ctx.fill();
                });
                // Draw traveler
                ctx.fillStyle = '#9b59b6';
                ctx.shadowColor = '#c39bd3';
                ctx.shadowBlur = 15;
                ctx.beginPath();
                ctx.arc(traveler.x, traveler.y, traveler.radius, 0, Math.PI*2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
            gsap.ticker.add(draw);
            currentTicker = draw;
            
            controlBtn.textContent = 'Predict!';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                gsap.set(traveler, {x: 50, y: canvas.height/2}); // reset traveler
                const tl = gsap.timeline();
                // 1. Predict the path (pre-dict)
                tl.to(elements, {
                    alpha: 1,
                    stagger: 0.1,
                    ease: 'power1.in'
                });
                // 2. Traveler follows the path
                elements.forEach(point => {
                    tl.to(traveler, {
                        x: point.x,
                        y: point.y,
                        duration: 0.15,
                        ease: 'linear'
                    });
                });
            };
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const activity = btn.dataset.activity;
                if (activity === 'pre-game') initPreGame();
                else if (activity === 'dict-game') initDictGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        initFullAnimation();
    });
    </script>
</body>
</html> 