<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Disrupt</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #e67e22; /* An orange color for "disrupt" */
            --secondary-color: #2c3e50;
            --danger-color: #e74c3c;
            --success-color: #2ecc71;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .breakdown-section .insight {
            margin-top: 15px;
            font-style: italic;
            color: #555;
        }

        .animation-panel {
            flex: 2;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }
        
        .activity-wrapper {
            display: none;
            width: 100%;
            height: 100%;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .activity-wrapper.active {
            display: flex;
        }
        
        .game-container {
            width: 90%;
            max-width: 500px;
            height: 350px;
            border: 2px dashed #bdc3c7;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #ecf0f1, #ffffff);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-button {
            margin-top: 30px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(230, 126, 34, 0.4);
        }
        .control-button:hover {
            background-color: #d35400;
            transform: translateY(-2px);
        }
        .control-button:active { transform: translateY(1px); }

        #welcome-screen p { font-size: 1.2em; color: var(--text-muted); text-align: center; }

        /* --- Morpheme Game Styles for Disrupt --- */
        #dis-box1, #dis-box2 { position: absolute; width: 80px; height: 80px; background: var(--primary-color); border-radius: 10px; transition: all 1s; }
        #dis-box1 { left: calc(50% - 85px); }
        #dis-box2 { left: calc(50% + 5px); }
        .dis-game-run #dis-box1 { left: calc(50% - 160px); transform: rotate(-15deg); }
        .dis-game-run #dis-box2 { left: calc(50% + 80px); transform: rotate(15deg); }
        
        #rupt-chain-link { position: absolute; width: 200px; height: 20px; background: #bdc3c7; border-radius: 10px; }
        #rupt-hammer { position: absolute; width: 60px; height: 60px; background: var(--secondary-color); border-radius: 10px; top: -80px; left: calc(50% - 30px); transition: all 0.5s cubic-bezier(.6,-0.28,.74,1.55); }
        #rupt-crack { position: absolute; width: 10px; height: 30px; background: var(--danger-color); opacity: 0; }
        .rupt-game-run #rupt-hammer { top: calc(50% - 30px); }
        .rupt-game-run #rupt-chain-link { background: var(--danger-color); }
        .rupt-game-run #rupt-crack { opacity: 1; animation: blink 0.5s 2; }
        @keyframes blink { 50% { opacity: 0; }}

        /* --- Full Animation for Disrupt --- */
        .flow-container { width: 100%; height: 100%; position: relative; }
        .flow-block { position: absolute; width: 20px; height: 20px; background: var(--success-color); border-radius: 4px; top: 50%; transform: translateY(-50%); animation: flow 5s linear infinite; }
        @keyframes flow { 0% { left: -20px; } 100% { left: 100%; } }
        .disruptor { position: absolute; width: 50px; height: 50px; background: var(--danger-color); top: -60px; left: calc(50% - 25px); transition: all 0.5s ease-in; transform: rotate(45deg); }
        .disrupt-animation-run .disruptor { top: calc(50% - 25px); }
        .disrupt-animation-run .flow-block { animation-play-state: paused; }
        #disrupted-text { position: absolute; top: 65%; left: 50%; transform: translate(-50%, -50%) scale(0); font-size: 2em; color: var(--danger-color); transition: all 0.5s; font-weight: bold; }
        .disrupt-animation-run #disrupted-text { transform: translate(-50%, -50%) scale(1); transition-delay: 0.5s; }
        
    </style>
</head>
<body>

    <div class="container">
        <div class="word-panel">
            <h1>disrupt</h1>
            <p class="pronunciation">[dɪsˈrʌpt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 使中断，扰乱，使分裂</p>
                <div class="example">
                    <p><strong>例句：</strong> Technology can disrupt traditional business models.</p>
                    <p><strong>翻译：</strong> 科技能够颠覆传统的商业模式。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="dis-game">dis- (分开)</button>
                    <button class="morpheme-btn" data-activity="rupt-game">-rupt (打破)</button>
                </div>
                <p class="insight">点击上方词缀，体验其含义的互动游戏！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示</button>
                </div>
            </div>

        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎！</h2>

            <div id="welcome-screen" class="activity-wrapper active">
                <p>点击左侧的词缀按钮<br>开始你的交互式学习之旅！</p>
            </div>

            <!-- Morpheme Games -->
            <div id="dis-game" class="activity-wrapper">
                <div class="game-container" id="dis-container">
                    <div id="dis-box1"></div>
                    <div id="dis-box2"></div>
                </div>
                <button class="control-button" id="dis-btn">让它们"分开" (dis-)</button>
            </div>
            
            <div id="rupt-game" class="activity-wrapper">
                 <div class="game-container" id="rupt-container">
                    <div id="rupt-chain-link"></div>
                    <div id="rupt-hammer"></div>
                    <div id="rupt-crack"></div>
                </div>
                <button class="control-button" id="rupt-btn">"打破"它 (-rupt)</button>
            </div>

            <!-- Full Word Activities -->
            <div id="full-animation" class="activity-wrapper">
                <div class="game-container flow-container" id="disrupt-container">
                    <!-- Flowing blocks will be added by JS -->
                    <div class="disruptor"></div>
                    <div id="disrupted-text">Disrupted!</div>
                </div>
                <button class="control-button" id="disrupt-btn">Disrupt the Flow!</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        // --- Activity Switching Logic ---
        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetId = btn.dataset.activity;
                const targetWrapper = document.getElementById(targetId);
                
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                activityWrappers.forEach(w => w.classList.remove('active'));
                targetWrapper.classList.add('active');
                
                activityTitle.textContent = btn.textContent;
            });
        });

        // --- Morpheme Games Logic ---
        function setupMorphemeGames() {
            // DIS- Game
            const disBtn = document.getElementById('dis-btn');
            const disContainer = document.getElementById('dis-container');
            disBtn.addEventListener('click', () => {
                disContainer.classList.toggle('dis-game-run');
            });

            // RUPT- Game
            const ruptBtn = document.getElementById('rupt-btn');
            const ruptContainer = document.getElementById('rupt-container');
            ruptBtn.addEventListener('click', () => {
                ruptContainer.classList.toggle('rupt-game-run');
            });
        }
        
        // --- Full Word Activities Logic ---
        function setupFullAnimation() {
            const disruptBtn = document.getElementById('disrupt-btn');
            const container = document.getElementById('disrupt-container');
            let blocks = [];

            function resetFlow() {
                container.classList.remove('disrupt-animation-run');
                // Remove old blocks
                blocks.forEach(b => b.remove());
                blocks = [];
                // Create new blocks
                for(let i=0; i < 10; i++) {
                    const block = document.createElement('div');
                    block.className = 'flow-block';
                    block.style.animationDelay = `${i * 0.5}s`;
                    container.appendChild(block);
                    blocks.push(block);
                }
            }

            disruptBtn.addEventListener('click', () => {
                resetFlow();
                setTimeout(() => {
                    container.classList.add('disrupt-animation-run');
                }, 1500); // Let the flow establish before disrupting
            });

            // Initial setup
            resetFlow();
        }

        setupMorphemeGames();
        setupFullAnimation();
    });
    </script>

</body>
</html> 