<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航班预订系统演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .canvas-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        canvas {
            border: 1px solid #ddd;
            max-width: 100%;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .step-description {
            margin-top: 15px;
            padding: 10px;
            background-color: #eaf2f8;
            border-radius: 4px;
            min-height: 60px;
        }
        
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>航班预订系统演示</h1>
        
        <div class="explanation">
            <h2>题目解析</h2>
            <p>航空公司机票销售系统有 n 个售票点，每个售票点创建一个进程 P(i=1, 2, …, n) 管理机票销售。</p>
            <p>假设 T(j=1, 2, …, m) 单元存放某日某航班的机票剩余数量。</p>
            <p>系统组成部分:</p>
            <ul>
                <li><strong>Temp</strong>: 为进程的临时工作单元</li>
                <li><strong>x</strong>: 为要用的订票张数</li>
                <li><strong>初始化</strong>: 系统应将信息量S赋值为T(满座机空)</li>
                <li><strong>P</strong>: 进程工作流程如下图所示</li>
            </ul>
            <p>实现进程间的同步与互斥，同类中空(a)、空(b)和空(c)处应分别填入什么?</p>
        </div>
        
        <div class="canvas-container">
            <h2>交互式演示</h2>
            <canvas id="flightSystem" width="800" height="500"></canvas>
            
            <div class="controls">
                <button id="resetBtn">重置演示</button>
                <button id="nextStepBtn">下一步</button>
                <button id="autoPlayBtn">自动播放</button>
            </div>
            
            <div class="step-description" id="stepDescription">
                点击"下一步"或"自动播放"开始演示
            </div>
        </div>
    </div>

    <script>
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('flightSystem');
        const ctx = canvas.getContext('2d');
        
        // 定义颜色
        const colors = {
            background: '#f8f9fa',
            process: '#3498db',
            data: '#e74c3c',
            arrow: '#2c3e50',
            highlight: '#f1c40f',
            text: '#333333',
            success: '#2ecc71',
            failure: '#e74c3c'
        };
        
        // 定义系统状态
        let state = {
            step: 0,
            totalSteps: 8,
            autoPlay: false,
            autoPlayInterval: null,
            ticketsAvailable: 5,
            requestedTickets: 2,
            tempValue: 0,
            processState: 'idle', // idle, checking, updating, success, failure
            answers: {
                a: 'wait(mutex)',
                b: 'signal(mutex)',
                c: 'wait(empty)'
            }
        };
        
        // 绘制流程图的节点
        function drawNode(x, y, width, height, text, isHighlighted = false) {
            ctx.fillStyle = isHighlighted ? colors.highlight : colors.process;
            ctx.strokeStyle = colors.arrow;
            
            // 绘制圆角矩形
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 10);
            ctx.fill();
            ctx.stroke();
            
            // 添加文本
            ctx.fillStyle = colors.text;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 处理多行文本
            const lines = text.split('\n');
            const lineHeight = 20;
            
            for (let i = 0; i < lines.length; i++) {
                ctx.fillText(lines[i], x + width / 2, y + height / 2 - ((lines.length - 1) * lineHeight / 2) + i * lineHeight);
            }
        }
        
        // 绘制箭头
        function drawArrow(fromX, fromY, toX, toY, isHighlighted = false) {
            ctx.strokeStyle = isHighlighted ? colors.highlight : colors.arrow;
            ctx.lineWidth = isHighlighted ? 3 : 2;
            
            // 绘制线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 绘制箭头
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 10;
            
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI / 6), toY - arrowLength * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI / 6), toY - arrowLength * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = isHighlighted ? colors.highlight : colors.arrow;
            ctx.fill();
            
            ctx.lineWidth = 2;
        }
        
        // 绘制数据存储
        function drawDataStore(x, y, width, height, text, value, isHighlighted = false) {
            ctx.fillStyle = isHighlighted ? colors.highlight : colors.data;
            ctx.strokeStyle = colors.arrow;
            
            // 绘制平行四边形
            ctx.beginPath();
            ctx.moveTo(x + 20, y);
            ctx.lineTo(x + width, y);
            ctx.lineTo(x + width - 20, y + height);
            ctx.lineTo(x, y + height);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // 添加文本
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text + ': ' + value, x + width / 2, y + height / 2);
        }
        
        // 绘制整个系统
        function drawSystem() {
            // 清除画布
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('航班预订系统工作流程', canvas.width / 2, 30);
            
            // 绘制数据存储 T
            drawDataStore(600, 80, 150, 40, 'T (剩余机票)', state.ticketsAvailable, state.step === 1);
            
            // 绘制流程节点
            const startX = 150;
            const startY = 100;
            const nodeWidth = 200;
            const nodeHeight = 60;
            const verticalGap = 80;
            
            // 起始节点
            drawNode(startX, startY, nodeWidth, nodeHeight, '售票点请求购买机票\nx = ' + state.requestedTickets, state.step === 0);
            
            // 空(a)处 - wait(mutex)
            const aNodeY = startY + verticalGap;
            drawNode(startX, aNodeY, nodeWidth, nodeHeight, state.step >= 2 ? state.answers.a : '空(a)处', state.step === 2);
            
            // Temp=T
            const tempNodeY = aNodeY + verticalGap;
            drawNode(startX, tempNodeY, nodeWidth, nodeHeight, 'Temp = T', state.step === 3);
            
            // Temp-x
            const tempMinusNodeY = tempNodeY + verticalGap;
            drawNode(startX, tempMinusNodeY, nodeWidth, nodeHeight, 'Temp = Temp - x', state.step === 4);
            
            // 判断 Temp >= 0
            const checkNodeY = tempMinusNodeY + verticalGap;
            drawNode(startX, checkNodeY, nodeWidth, nodeHeight, 'Temp >= 0 ?', state.step === 5);
            
            // 空(b)处 - signal(mutex)
            const bNodeY = checkNodeY + verticalGap;
            drawNode(startX - 100, bNodeY, nodeWidth, nodeHeight, state.step >= 6 ? state.answers.b : '空(b)处', state.step === 6);
            
            // T=Temp (成功)
            const successNodeY = bNodeY + verticalGap;
            drawNode(startX - 100, successNodeY, nodeWidth, nodeHeight, 'T = Temp\n(订票成功)', state.processState === 'success');
            
            // 空(c)处 - wait(empty)
            drawNode(startX + 100, bNodeY, nodeWidth, nodeHeight, state.step >= 7 ? state.answers.c : '空(c)处', state.step === 7);
            
            // 输出"满座" (失败)
            drawNode(startX + 100, successNodeY, nodeWidth, nodeHeight, '输出"满座"\n(订票失败)', state.processState === 'failure');
            
            // 绘制箭头
            drawArrow(startX + nodeWidth/2, startY + nodeHeight, startX + nodeWidth/2, aNodeY, state.step === 1);
            drawArrow(startX + nodeWidth/2, aNodeY + nodeHeight, startX + nodeWidth/2, tempNodeY, state.step === 2);
            drawArrow(startX + nodeWidth/2, tempNodeY + nodeHeight, startX + nodeWidth/2, tempMinusNodeY, state.step === 3);
            drawArrow(startX + nodeWidth/2, tempMinusNodeY + nodeHeight, startX + nodeWidth/2, checkNodeY, state.step === 4);
            
            // 条件分支箭头
            drawArrow(startX + nodeWidth/2, checkNodeY + nodeHeight, startX - 100 + nodeWidth/2, bNodeY, 
                      state.step === 5 && state.tempValue >= 0);
            drawArrow(startX + nodeWidth/2, checkNodeY + nodeHeight, startX + 100 + nodeWidth/2, bNodeY, 
                      state.step === 5 && state.tempValue < 0);
            
            // 结果箭头
            drawArrow(startX - 100 + nodeWidth/2, bNodeY + nodeHeight, startX - 100 + nodeWidth/2, successNodeY, 
                      state.processState === 'success');
            drawArrow(startX + 100 + nodeWidth/2, bNodeY + nodeHeight, startX + 100 + nodeWidth/2, successNodeY, 
                      state.processState === 'failure');
            
            // 绘制临时变量
            if (state.step >= 3) {
                drawDataStore(400, 200, 150, 40, 'Temp', state.tempValue, state.step === 3 || state.step === 4);
            }
            
            // 绘制互斥锁状态
            ctx.fillStyle = colors.text;
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('互斥锁状态: ' + (state.step >= 2 && state.step < 6 ? '已锁定' : '未锁定'), 50, 50);
        }
        
        // 更新步骤描述
        function updateStepDescription() {
            const descriptionElement = document.getElementById('stepDescription');
            let description = '';
            
            switch(state.step) {
                case 0:
                    description = '初始状态：售票点请求购买 ' + state.requestedTickets + ' 张机票，当前剩余 ' + state.ticketsAvailable + ' 张';
                    break;
                case 1:
                    description = '检查剩余机票数量 T = ' + state.ticketsAvailable;
                    break;
                case 2:
                    description = '<span class="highlight">空(a)处应填入 wait(mutex)</span> - 获取互斥锁，确保同一时间只有一个进程访问机票数据';
                    break;
                case 3:
                    description = '将剩余机票数量复制到临时变量: Temp = T = ' + state.ticketsAvailable;
                    break;
                case 4:
                    description = '计算购票后的剩余数量: Temp = Temp - x = ' + state.tempValue;
                    break;
                case 5:
                    description = '判断是否有足够的票: Temp ' + (state.tempValue >= 0 ? '>= 0，有足够的票' : '< 0，没有足够的票');
                    break;
                case 6:
                    description = '<span class="highlight">空(b)处应填入 signal(mutex)</span> - 释放互斥锁，让其他进程可以访问机票数据';
                    break;
                case 7:
                    description = '<span class="highlight">空(c)处应填入 wait(empty)</span> - 等待有足够的票可供购买';
                    break;
                case 8:
                    if (state.processState === 'success') {
                        description = '订票成功！更新剩余机票数量 T = Temp = ' + state.ticketsAvailable;
                    } else {
                        description = '订票失败！机票数量不足，显示"满座"';
                    }
                    break;
            }
            
            descriptionElement.innerHTML = description;
        }
        
        // 进行下一步
        function nextStep() {
            if (state.step < state.totalSteps) {
                state.step++;
                
                // 根据步骤更新状态
                if (state.step === 3) {
                    state.tempValue = state.ticketsAvailable;
                } else if (state.step === 4) {
                    state.tempValue = state.tempValue - state.requestedTickets;
                } else if (state.step === 5) {
                    if (state.tempValue >= 0) {
                        state.processState = 'success';
                    } else {
                        state.processState = 'failure';
                    }
                } else if (state.step === 8) {
                    if (state.processState === 'success') {
                        state.ticketsAvailable = state.tempValue;
                    }
                }
                
                updateStepDescription();
                drawSystem();
            } else {
                stopAutoPlay();
            }
        }
        
        // 重置演示
        function resetDemo() {
            stopAutoPlay();
            state.step = 0;
            state.ticketsAvailable = 5;
            state.requestedTickets = 2;
            state.tempValue = 0;
            state.processState = 'idle';
            updateStepDescription();
            drawSystem();
        }
        
        // 自动播放
        function toggleAutoPlay() {
            if (state.autoPlay) {
                stopAutoPlay();
            } else {
                startAutoPlay();
            }
        }
        
        function startAutoPlay() {
            state.autoPlay = true;
            document.getElementById('autoPlayBtn').textContent = '停止播放';
            state.autoPlayInterval = setInterval(() => {
                if (state.step < state.totalSteps) {
                    nextStep();
                } else {
                    stopAutoPlay();
                }
            }, 1500);
        }
        
        function stopAutoPlay() {
            state.autoPlay = false;
            document.getElementById('autoPlayBtn').textContent = '自动播放';
            if (state.autoPlayInterval) {
                clearInterval(state.autoPlayInterval);
                state.autoPlayInterval = null;
            }
        }
        
        // 添加事件监听器
        document.getElementById('nextStepBtn').addEventListener('click', nextStep);
        document.getElementById('resetBtn').addEventListener('click', resetDemo);
        document.getElementById('autoPlayBtn').addEventListener('click', toggleAutoPlay);
        
        // 初始化绘制
        drawSystem();
        updateStepDescription();
    </script>
</body>
</html> 