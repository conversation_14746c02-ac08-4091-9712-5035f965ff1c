<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Innovate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #007BFF; /* Bright Blue */
            --secondary-color: #0056b3;
            --glow-color: #00BFFF;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #212529;
            --canvas-bg: #e9ecef; /* Light Grey */
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
            text-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #dee2e6;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 0 10px var(--glow-color);
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
            background-color: #fff;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 20px var(--glow-color);
            z-index: 10;
        }
        .control-button:hover { background-color: var(--secondary-color); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>innovate</h1>
            <p class="pronunciation">[ˈɪnəveɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 创新, 改革, 引入新事物</p>
                <div class="example">
                    <p><strong>例句：</strong> The company's goal is to innovate and lead the market.</p>
                    <p><strong>翻译：</strong> 公司的目标是创新并引领市场。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="in-game">in- (in, into, 进入)</button>
                    <button class="morpheme-btn" data-activity="nov-game">nov- (new, 新的)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：创造新生</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Innovate!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let elements = [];

        class Particle {
            constructor(x, y, vx, vy, color, size, life) {
                this.x = x; this.y = y; this.vx = vx; this.vy = vy;
                this.color = color; this.size = size; this.life = life;
                this.initialLife = life;
            }
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= 1;
            }
            draw() {
                ctx.globalAlpha = Math.max(0, this.life / this.initialLife);
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }

        function welcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#333';
            ctx.font = '24px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('新篇章：第九个单词！', canvas.width / 2, canvas.height / 2 - 20);
            ctx.fillText('请选择一个活动，探索"创新"的奥秘。', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Morpheme Games ---
        function initInGame() {
            elements = [];
            const container = { x: canvas.width/2 - 75, y: canvas.height/2 - 75, w: 150, h: 150 };
            controlBtn.textContent = '注入 (in-)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                for (let i = 0; i < 50; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 2;
                    elements.push(new Particle(
                        canvas.width/2 + Math.cos(angle) * 300, 
                        canvas.height/2 + Math.sin(angle) * 300,
                        (container.x + container.w/2 - (canvas.width/2 + Math.cos(angle) * 300)) / 100,
                        (container.y + container.h/2 - (canvas.height/2 + Math.sin(angle) * 300)) / 100,
                        '#007BFF', 3, 200
                    ));
                }
            };
            
            function animateInGame() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.strokeStyle = '#007BFF';
                ctx.lineWidth = 3;
                ctx.strokeRect(container.x, container.y, container.w, container.h);
                elements.forEach((p, i) => {
                    if (p.x > container.x && p.x < container.x + container.w && p.y > container.y && p.y < container.y + container.h) {
                        p.vx = 0; p.vy = 0;
                    }
                    p.update();
                    p.draw();
                });
                animationFrameId = requestAnimationFrame(animateInGame);
            }
            animateInGame();
        }

        function initNovGame() {
             let oldShape = { x: canvas.width/2, y: canvas.height/2, size: 50, color: '#6c757d', state: 'whole'};
             elements = [];
             controlBtn.textContent = '焕新 (nov-)';
             controlBtn.classList.remove('hidden');
             controlBtn.onclick = () => {
                 if (oldShape.state === 'whole') {
                    oldShape.state = 'exploding';
                    for (let i = 0; i < 100; i++) {
                        const angle = Math.random() * Math.PI * 2;
                        const speed = Math.random() * 3 + 1;
                        elements.push(new Particle(oldShape.x, oldShape.y, Math.cos(angle)*speed, Math.sin(angle)*speed, `hsl(${180 + Math.random()*40}, 100%, 50%)`, 2, 80));
                    }
                 }
             };

             function animateNovGame() {
                ctx.clearRect(0,0,canvas.width,canvas.height);
                if (oldShape.state === 'whole') {
                    ctx.fillStyle = oldShape.color;
                    ctx.fillRect(oldShape.x - oldShape.size/2, oldShape.y - oldShape.size/2, oldShape.size, oldShape.size);
                } else {
                    elements.forEach((p, i) => {
                        if (p.life <= 0) { // re-form into a new shape
                           p.vx = (canvas.width/2 - p.x) / 50;
                           p.vy = (canvas.height/2 - p.y) / 50;
                           const targetX = canvas.width/2 + Math.cos(i/100 * Math.PI*2) * 60;
                           const targetY = canvas.height/2 + Math.sin(i/100 * Math.PI*2) * 60;
                           p.vx = (targetX - p.x) / 30;
                           p.vy = (targetY - p.y) / 30;
                        }
                        p.update();
                        p.draw();
                    });
                }
                animationFrameId = requestAnimationFrame(animateNovGame);
             }
             animateNovGame();
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            elements = [];
            controlBtn.textContent = 'Innovate!';
            controlBtn.classList.remove('hidden');
            
            // Create old parts
            for (let i = 0; i < 5; i++) {
                elements.push({ type: 'part', x: Math.random()*canvas.width, y: Math.random()*canvas.height, size: 20, color: '#6c757d', targetX: 0, targetY: 0, state: 'idle'});
            }
            
            let spark = null;

            controlBtn.onclick = () => {
                if(spark) return;
                spark = new Particle(canvas.width, canvas.height/2, -10, 0, '#FFD700', 5, 500);
                
                // Assign target positions for the new shape (a simple house for example)
                elements[0].targetX = canvas.width/2; elements[0].targetY = canvas.height/2; // Base
                elements[1].targetX = canvas.width/2 - 30; elements[1].targetY = canvas.height/2 - 30; // Wall 1
                elements[2].targetX = canvas.width/2 + 30; elements[2].targetY = canvas.height/2 - 30; // Wall 2
                elements[3].targetX = canvas.width/2; elements[3].targetY = canvas.height/2 - 60; // Roof peak
                elements[4].targetX = canvas.width/2 + 60; elements[4].targetY = canvas.height/2; // door
                elements.forEach(p => p.state = 'moving');
            };

            function animateFull() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                if (spark) {
                    spark.update();
                    spark.draw();
                    if(spark.x < 0) spark = null;
                }

                elements.forEach(p => {
                    if (p.state === 'moving') {
                        p.x += (p.targetX - p.x) * 0.05;
                        p.y += (p.targetY - p.y) * 0.05;
                        if(Math.abs(p.x - p.targetX) < 1 && Math.abs(p.y - p.targetY) < 1) {
                            p.state = 'assembled';
                            p.color = '#007BFF'; // Change color on assembly
                        }
                    }
                    ctx.fillStyle = p.color;
                    ctx.fillRect(p.x - p.size/2, p.y - p.size/2, p.size, p.size);
                });
                
                // Draw lines between assembled parts
                ctx.strokeStyle = "rgba(0, 123, 255, 0.5)";
                ctx.lineWidth = 2;
                for (let i=0; i < elements.length; i++) {
                    if (elements[i].state === 'assembled') {
                        for(let j=i+1; j < elements.length; j++){
                           if (elements[j].state === 'assembled') {
                               ctx.beginPath();
                               ctx.moveTo(elements[i].x, elements[i].y);
                               ctx.lineTo(elements[j].x, elements[j].y);
                               ctx.stroke();
                           }
                        }
                    }
                }

                animationFrameId = requestAnimationFrame(animateFull);
            }
            animateFull();
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                if (activity === 'in-game') initInGame();
                else if (activity === 'nov-game') initNovGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html> 