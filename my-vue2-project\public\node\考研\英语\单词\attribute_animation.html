<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>单词动画 - Attribute</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            overflow-x: hidden;
        }
        .container {
            display: flex;
            flex-direction: row;
            gap: 20px;
            max-width: 1200px;
            width: 100%;
        }
        .canvas-container {
            flex: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        #wordCanvas {
            border: 1px solid #ddd;
            background-color: #fafafa;
            border-radius: 8px;
        }
        .info-container {
            flex: 1;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 400px;
        }
        h1, h2 {
            color: #0056b3;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 10px;
        }
        p {
            line-height: 1.8;
        }
        .highlight {
            color: #d9534f;
            font-weight: bold;
        }
        .interactive-tip {
            margin-top: 15px;
            padding: 10px;
            background-color: #e7f3fe;
            border-left: 5px solid #2196F3;
            font-style: italic;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="canvas-container">
        <h1>Attribute /əˈtrɪbjuːt/</h1>
        <canvas id="wordCanvas" width="700" height="500"></canvas>
        <p class="interactive-tip">提示：请点击右侧的物品，将其赋予给国王，以理解 "attribute" 的含义。</p>
    </div>

    <div class="info-container">
        <h2>故事化解析 🎬</h2>
        <p>你好！今天我们学习的单词是 <span class="highlight">'attribute'</span>。</p>
        <p>为了更好地理解和记忆，我为您设计了一个小故事动画：</p>
        <p>
            'attribute' 这个词可以拆解为两部分：前缀 <span class="highlight">'at-'</span> 和词根 <span class="highlight">'tribute'</span>。
        </p>
        <ul>
            <li>前缀 <span class="highlight">'at-'</span> 是 'ad-' 的变体，表示 'to' (朝向)。</li>
            <li>词根 <span class="highlight">'tribute'</span> 意为 'give' 或 'assign' (给予, 分配)。它源于拉丁语 'tribuere'。我们知道 'tribute' 本身就是一个单词，意思是"贡品"，就是古代部落(tribe)献给首领的东西。</li>
        </ul>
        <p>所以, 'attribute' 的字面意思就是"把...给予..."，引申为"把(特性、品质)归于..."或"认为是...的属性"。</p>
        
        <h2>动画故事 👑</h2>
        <p>
            我们的动画展示了一位国王。国王的身边有几样代表不同品质的物品，比如代表<span class="highlight">'智慧'</span>的书，代表<span class="highlight">'力量'</span>的剑，和代表<span class="highlight">'财富'</span>的宝箱。
        </p>
        <p>
            当您点击这些物品，它们就会移动到国王身上，这个过程就演示了将这些品质'归于'国王的过程。这个'朝向'国王的动作，就体现了前缀 <span class="highlight">'at-'</span> 的含义，而'给予'品质的动作，则体现了词根 <span class="highlight">'tribute'</span> 的含义。
        </p>
        <p>通过这个互动，我们可以直观地感受到 'attribute' 这个词的核心意义。希望能帮助您牢牢记住它！</p>
    </div>
</div>

<script>
const canvas = document.getElementById('wordCanvas');
const ctx = canvas.getContext('2d');

// 游戏对象
const king = {
    x: 300,
    y: 200,
    width: 100,
    height: 150,
    draw() {
        // 身体
        ctx.fillStyle = '#6a4c93';
        ctx.fillRect(this.x + 25, this.y + 40, 50, 80);
        // 头
        ctx.fillStyle = '#f7d6e0';
        ctx.beginPath();
        ctx.arc(this.x + 50, this.y + 20, 20, 0, Math.PI * 2);
        ctx.fill();
        // 皇冠
        ctx.fillStyle = 'gold';
        ctx.beginPath();
        ctx.moveTo(this.x + 30, this.y);
        ctx.lineTo(this.x + 35, this.y - 15);
        ctx.lineTo(this.x + 50, this.y);
        ctx.lineTo(this.x + 65, this.y - 15);
        ctx.lineTo(this.x + 70, this.y);
        ctx.closePath();
        ctx.fill();
    }
};

const items = [
    { name: 'Wisdom (智慧)', x: 580, y: 80, width: 40, height: 50, icon: '📖', targetX: king.x + 5, targetY: king.y + 50, isAnimating: false, isAttributed: false },
    { name: 'Power (力量)', x: 580, y: 220, width: 40, height: 50, icon: '⚔️', targetX: king.x + 95, targetY: king.y + 50, isAnimating: false, isAttributed: false },
    { name: 'Wealth (财富)', x: 580, y: 360, width: 40, height: 50, icon: '💎', targetX: king.x + 45, targetY: king.y + 130, isAnimating: false, isAttributed: false }
];

function drawItem(item) {
    ctx.font = '30px Arial';
    ctx.fillText(item.icon, item.x, item.y + 35);
    ctx.font = '14px "Segoe UI"';
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText(item.name, item.x + 20, item.y + 60);
}

function drawText(text, x, y, size = 20) {
    ctx.fillStyle = '#0056b3';
    ctx.font = `bold ${size}px 'Segoe UI'`;
    ctx.textAlign = 'center';
    ctx.fillText(text, x, y);
}


function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw Title
    drawText('Attribute', canvas.width / 2, 40, 30);
    
    // Draw King
    king.draw();

    // Draw prefix and root explanation
    ctx.textAlign = 'left';
    drawText("at- (to)", 10, 50, 18);
    drawText("tribute (give)", 10, 80, 18);

    items.forEach(item => {
        if (item.isAnimating) {
            const dx = item.targetX - item.x;
            const dy = item.targetY - item.y;
            const dist = Math.sqrt(dx * dx + dy * dy);

            if (dist < 2) {
                item.isAnimating = false;
                item.isAttributed = true;
                item.x = item.targetX;
                item.y = item.targetY;
            } else {
                item.x += dx * 0.05;
                item.y += dy * 0.05;
            }
        }
        drawItem(item);
    });

    // Draw the "giving" action arrow
    items.forEach(item => {
        if (item.isAnimating) {
            ctx.save();
            ctx.beginPath();
            ctx.moveTo(item.x + 50, item.y + 25);
            ctx.lineTo(king.x + king.width / 2, king.y + king.height / 2);
            ctx.strokeStyle = 'rgba(217, 83, 79, 0.5)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.restore();
            
            drawText("at- (to)", item.x + (king.x - item.x)/2, item.y + (king.y-item.y)/2, 16);
            drawText("tribute (give)", item.x + (king.x - item.x)/2, item.y + (king.y-item.y)/2 + 20, 16);
        }
    });


    if(items.every(item => item.isAttributed)) {
        drawText("The king now has all attributes.", canvas.width / 2, canvas.height - 20, 20);
    }


    requestAnimationFrame(animate);
}

canvas.addEventListener('click', (event) => {
    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    items.forEach(item => {
        if (!item.isAnimating && !item.isAttributed &&
            mouseX >= item.x && mouseX <= item.x + item.width + 40 && // Adjust click area
            mouseY >= item.y && mouseY <= item.y + item.height) {
            item.isAnimating = true;
        }
    });
});

animate();

</script>

</body>
</html> 