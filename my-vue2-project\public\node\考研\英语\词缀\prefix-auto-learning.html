<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：auto-（自动、自己）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #ecf0f1, #bdc3c7);
        }

        #factoryCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .assembly-line {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .robot-station {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .robot-station::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.6s;
        }

        .robot-station:hover::before {
            left: 100%;
        }

        .robot-station:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .robot-station.operational {
            opacity: 1;
            transform: translateY(0);
        }

        .automation-process {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .manual-mode {
            background: #e74c3c;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .manual-mode::after {
            content: '手动';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c0392b;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .automation-core {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9, #1abc9c);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: autoRotate 3s linear infinite;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.4);
        }

        .automation-core::before {
            content: '🤖';
            font-size: 2rem;
            animation: autoRotate 2s linear infinite reverse;
        }

        .automation-core::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px dashed rgba(52, 152, 219, 0.3);
            border-radius: 50%;
            animation: autoRotate 4s linear infinite;
        }

        .auto-mode {
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
            animation: autoGlow 2s ease-in-out infinite;
        }

        .auto-mode::after {
            content: '自动';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #229954;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .automation-explanation {
            background: rgba(52, 152, 219, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .production-log {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #f39c12;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #f39c12;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .automation-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #95a5a6;
            transition: all 0.3s ease;
        }

        .automation-status.activating {
            background: #f39c12;
            animation: autoActivate 1.5s infinite;
        }

        .automation-status.automated {
            background: #27ae60;
            box-shadow: 0 0 10px #27ae60;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes autoRotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes autoGlow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
            }
            50% {
                box-shadow: 0 0 25px rgba(39, 174, 96, 0.8);
            }
        }

        @keyframes autoActivate {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes conveyorMove {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        @keyframes robotArm {
            0%, 100% {
                transform: rotate(0deg);
            }
            50% {
                transform: rotate(15deg);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #3498db;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .mechanical-parts {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #3498db;
            border-radius: 50%;
            pointer-events: none;
            animation: conveyorMove 3s linear infinite;
        }

        .automation-sequence {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .sequence-step {
            width: 30px;
            height: 8px;
            background: #dee2e6;
            border-radius: 4px;
            transition: all 0.3s ease;
            position: relative;
        }

        .sequence-step.active {
            background: linear-gradient(90deg, #3498db, #2980b9);
            animation: autoRotate 1s linear infinite;
        }

        .sequence-step.completed {
            background: linear-gradient(90deg, #27ae60, #229954);
        }

        .sequence-step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #bdc3c7;
            transform: translateY(-50%);
        }

        .sequence-step:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>自动前缀：auto-</h1>
            <p>在智能机器人工厂中学会"自主运行"的智慧</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🤖 智能机器人工厂的故事</h2>
                <p>在未来世界有一座先进的智能机器人工厂，这里专门生产各种自动化设备。工厂的核心技术是"auto-"自动化系统，它能让任何需要人工操作的设备变成自动运行的智能机器。当普通的词汇通过这个自动化系统时，就会获得"自动"、"自己"、"自主"的神奇能力，从需要外部控制转变为自主运行！</p>
            </div>

            <div class="canvas-container">
                <canvas id="factoryCanvas"></canvas>
                <div class="automation-sequence" id="automationSequence">
                    <div class="sequence-step"></div>
                    <div class="sequence-step"></div>
                    <div class="sequence-step"></div>
                    <div class="sequence-step"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择智能机器人工厂的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"智能机器人工厂"的比喻，是因为"auto-"前缀的核心含义就是"自动"、"自己"，这与机器人工厂将手动操作转换为自动化的过程完美契合。自动化系统的视觉效果帮助学生理解"从手动到自动"的转换概念，而机器人工厂的设定强调了智能化和自主性。通过机械臂、传送带等工业元素，让抽象的"自动"概念变得具体可感。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAutomation()">启动自动化</button>
                <button class="btn" onclick="showRobots()">显示机器人</button>
                <button class="btn" onclick="resetFactory()">重置工厂</button>
            </div>

            <div class="interactive-hint">
                ⚙️ 点击"启动自动化"观看词汇自动化过程，点击机器人站查看生产日志
            </div>
        </div>

        <div class="assembly-line" id="assemblyLine">
            <div class="robot-station">
                <div class="automation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Mobile → Automobile</h3>
                <div class="automation-process">
                    <div class="manual-mode">mobile</div>
                    <div class="automation-core"></div>
                    <div class="auto-mode"><span class="prefix-highlight">auto</span>mobile</div>
                </div>
                <div class="automation-explanation">
                    移动的 → <span class="prefix-highlight">自动</span>移动的
                </div>
                <div class="production-log">
                    <strong>生产日志：</strong><br>
                    <strong>手动：</strong>The mobile device needs charging. (移动设备需要充电。)<br>
                    <strong>自动：</strong>The automobile runs automatically. (汽车自动运行。)<br>
                    <strong>解析：</strong>"mobile"表示移动的，加上"auto-"变成"automobile"，表示汽车、自动车。从需要外力推动的移动变成自主移动的交通工具。
                </div>
            </div>

            <div class="robot-station">
                <div class="automation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Graph → Autograph</h3>
                <div class="automation-process">
                    <div class="manual-mode">graph</div>
                    <div class="automation-core"></div>
                    <div class="auto-mode"><span class="prefix-highlight">auto</span>graph</div>
                </div>
                <div class="automation-explanation">
                    写字 → <span class="prefix-highlight">自己</span>写字
                </div>
                <div class="production-log">
                    <strong>生产日志：</strong><br>
                    <strong>手动：</strong>Draw a graph on paper. (在纸上画图表。)<br>
                    <strong>自动：</strong>Can I have your autograph? (我能要你的亲笔签名吗？)<br>
                    <strong>解析：</strong>"graph"表示写字、图表，加上"auto-"变成"autograph"，表示亲笔签名。从一般的写字变成本人亲自写的签名。
                </div>
            </div>

            <div class="robot-station">
                <div class="automation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Matic → Automatic</h3>
                <div class="automation-process">
                    <div class="manual-mode">matic</div>
                    <div class="automation-core"></div>
                    <div class="auto-mode"><span class="prefix-highlight">auto</span>matic</div>
                </div>
                <div class="automation-explanation">
                    操作的 → <span class="prefix-highlight">自动</span>操作的
                </div>
                <div class="production-log">
                    <strong>生产日志：</strong><br>
                    <strong>手动：</strong>Use the manual controls. (使用手动控制。)<br>
                    <strong>自动：</strong>The door is automatic. (这扇门是自动的。)<br>
                    <strong>解析：</strong>"matic"表示操作的，加上"auto-"变成"automatic"，表示自动的。从需要人工操作变成自动运行的系统。
                </div>
            </div>

            <div class="robot-station">
                <div class="automation-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Pilot → Autopilot</h3>
                <div class="automation-process">
                    <div class="manual-mode">pilot</div>
                    <div class="automation-core"></div>
                    <div class="auto-mode"><span class="prefix-highlight">auto</span>pilot</div>
                </div>
                <div class="automation-explanation">
                    驾驶员 → <span class="prefix-highlight">自动</span>驾驶
                </div>
                <div class="production-log">
                    <strong>生产日志：</strong><br>
                    <strong>手动：</strong>The pilot controls the plane. (飞行员控制飞机。)<br>
                    <strong>自动：</strong>The plane is on autopilot. (飞机处于自动驾驶状态。)<br>
                    <strong>解析：</strong>"pilot"表示驾驶员，加上"auto-"变成"autopilot"，表示自动驾驶。从人工驾驶变成自动驾驶系统。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"auto-"前缀表示自动、自己、自主的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"auto-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"auto-"后的词根基本含义</li>
                <li><strong>应用自动概念：</strong>在词根意思前加上"自动"、"自己"、"自主"</li>
                <li><strong>控制转换：</strong>强调从外部控制转变为自主运行</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象智能机器人工厂的自动化系统，"auto-"就像机器人，让一切变得自动化！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('factoryCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentAutomation = 0;
        let conveyorBelt = { x: 0, speed: 2 };
        let robotArms = [];
        let mechanicalParts = [];
        
        const automations = [
            { manual: 'mobile', auto: 'automobile', x: 150, y: 200 },
            { manual: 'graph', auto: 'autograph', x: 350, y: 300 },
            { manual: 'matic', auto: 'automatic', x: 550, y: 150 },
            { manual: 'pilot', auto: 'autopilot', x: 750, y: 250 }
        ];

        class RobotArm {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.angle = 0;
                this.targetAngle = 0;
                this.working = false;
            }

            update() {
                if (this.working) {
                    this.targetAngle = Math.sin(Date.now() * 0.005) * 0.3;
                }
                this.angle += (this.targetAngle - this.angle) * 0.1;
            }

            draw() {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle);
                
                // 机械臂基座
                ctx.fillStyle = '#34495e';
                ctx.fillRect(-15, -10, 30, 20);
                
                // 机械臂
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 8;
                ctx.beginPath();
                ctx.moveTo(0, 0);
                ctx.lineTo(0, -40);
                ctx.stroke();
                
                // 机械爪
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(0, -40, 6, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.restore();
            }

            startWorking() {
                this.working = true;
            }
        }

        class MechanicalPart {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = 2;
                this.size = 4;
                this.color = '#3498db';
            }

            update() {
                this.x += this.vx;
                if (this.x > canvas.width + 20) {
                    this.x = -20;
                }
            }

            draw() {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function initRobotArms() {
            robotArms = [];
            for (let i = 0; i < 3; i++) {
                robotArms.push(new RobotArm(150 + i * 200, 100));
            }
        }

        function initMechanicalParts() {
            mechanicalParts = [];
            for (let i = 0; i < 5; i++) {
                mechanicalParts.push(new MechanicalPart(i * 100, canvas.height - 50));
            }
        }

        function drawFactory() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制工厂背景
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#ecf0f1');
            gradient.addColorStop(1, '#bdc3c7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制传送带
            drawConveyorBelt();
            
            // 绘制自动化核心
            drawAutomationCore();
            
            // 绘制机械臂
            robotArms.forEach(arm => {
                arm.update();
                arm.draw();
            });
            
            // 绘制机械零件
            mechanicalParts.forEach(part => {
                part.update();
                part.draw();
            });
            
            // 绘制词汇自动化过程
            if (animationState === 'automating') {
                drawWordAutomation();
            }
        }

        function drawConveyorBelt() {
            // 传送带轨道
            ctx.fillStyle = '#34495e';
            ctx.fillRect(0, canvas.height - 60, canvas.width, 20);
            
            // 传送带移动效果
            ctx.fillStyle = '#2c3e50';
            for (let i = conveyorBelt.x; i < canvas.width + 30; i += 30) {
                ctx.fillRect(i, canvas.height - 55, 20, 10);
            }
            
            if (animationState === 'automating') {
                conveyorBelt.x -= conveyorBelt.speed;
                if (conveyorBelt.x <= -30) {
                    conveyorBelt.x = 0;
                }
            }
        }

        function drawAutomationCore() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 自动化核心
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(Date.now() * 0.001);
            
            // 外环
            const outerGradient = ctx.createRadialGradient(0, 0, 30, 0, 0, 60);
            outerGradient.addColorStop(0, 'transparent');
            outerGradient.addColorStop(0.7, 'rgba(52, 152, 219, 0.3)');
            outerGradient.addColorStop(1, 'rgba(52, 152, 219, 0.8)');
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 60, 0, Math.PI * 2);
            ctx.fill();
            
            // 内环
            const innerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 40);
            innerGradient.addColorStop(0, 'rgba(41, 128, 185, 0.8)');
            innerGradient.addColorStop(0.5, 'rgba(52, 152, 219, 0.6)');
            innerGradient.addColorStop(1, 'transparent');
            ctx.fillStyle = innerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 40, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
            
            // 核心标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('AUTO-核心', centerX, centerY + 80);
        }

        function drawWordAutomation() {
            if (currentAutomation < automations.length) {
                const automation = automations[currentAutomation];
                const centerX = canvas.width / 2;
                
                // 手动模式（左侧）
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(centerX - 250, automation.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(automation.manual, centerX - 200, automation.y + 25);
                
                // 自动化光束
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 4;
                ctx.setLineDash([8, 4]);
                ctx.beginPath();
                ctx.moveTo(centerX - 130, automation.y + 20);
                ctx.lineTo(centerX + 130, automation.y + 20);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 自动模式（右侧）
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(centerX + 150, automation.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮auto-前缀
                ctx.fillStyle = '#f39c12';
                ctx.fillText('auto', centerX + 185, automation.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(automation.manual, centerX + 225, automation.y + 25);
            }
        }

        function updateAutomationSequence() {
            const steps = document.querySelectorAll('.sequence-step');
            steps.forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index < currentAutomation) {
                    step.classList.add('completed');
                } else if (index === currentAutomation && animationState === 'automating') {
                    step.classList.add('active');
                }
            });
        }

        function updateStationStatus() {
            const stations = document.querySelectorAll('.robot-station');
            const statuses = document.querySelectorAll('.automation-status');
            
            stations.forEach((station, index) => {
                const status = statuses[index];
                if (index < currentAutomation) {
                    status.classList.remove('activating');
                    status.classList.add('automated');
                } else if (index === currentAutomation && animationState === 'automating') {
                    status.classList.add('activating');
                    status.classList.remove('automated');
                } else {
                    status.classList.remove('activating', 'automated');
                }
            });
        }

        function animate() {
            drawFactory();
            updateAutomationSequence();
            updateStationStatus();
            
            if (animationState === 'automating' && currentAutomation < automations.length) {
                // 启动机械臂
                if (robotArms[currentAutomation % robotArms.length]) {
                    robotArms[currentAutomation % robotArms.length].startWorking();
                }
                
                // 自动切换到下一个自动化
                setTimeout(() => {
                    currentAutomation++;
                    if (currentAutomation >= automations.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function startAutomation() {
            animationState = 'automating';
            currentAutomation = 0;
            conveyorBelt.x = 0;
        }

        function showRobots() {
            const stations = document.querySelectorAll('.robot-station');
            stations.forEach((station, index) => {
                setTimeout(() => {
                    station.classList.add('operational');
                }, index * 400);
            });
        }

        function resetFactory() {
            animationState = 'idle';
            currentAutomation = 0;
            conveyorBelt.x = 0;
            
            const stations = document.querySelectorAll('.robot-station');
            stations.forEach(station => station.classList.remove('operational'));
            
            const statuses = document.querySelectorAll('.automation-status');
            statuses.forEach(status => {
                status.classList.remove('activating', 'automated');
            });
            
            robotArms.forEach(arm => {
                arm.working = false;
                arm.targetAngle = 0;
            });
        }

        // 初始化
        initRobotArms();
        initMechanicalParts();
        animate();

        // 点击机器人站的交互
        document.querySelectorAll('.robot-station').forEach(station => {
            station.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
