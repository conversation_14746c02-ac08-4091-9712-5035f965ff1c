<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 面向对象特性交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            box-sizing: border-box;
        }
        h1, h2 {
            text-align: center;
            color: #1a237e;
        }
        h3 {
            color: #283593;
            border-bottom: 2px solid #e8eaf6;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        p.explanation {
            background-color: #e3f2fd;
            border-left: 5px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            line-height: 1.6;
        }
        canvas {
            display: block;
            width: 100%;
            height: auto;
            aspect-ratio: 2 / 1;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
            margin: 20px auto 0;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .controls button {
            background-color: #304ffe;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            font-weight: 500;
        }
        .controls button:hover, .controls button.active {
            background-color: #1a237e;
            transform: translateY(-2px);
        }
        .controls button:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Java面试题</h1>
        <h2>Java 语言有哪些特点?</h2>

        <h3>1. 面向对象 (Object-Oriented)</h3>
        <p class="explanation">
            <strong>核心思想：</strong>把世界万物都看作成一个个独立的"对象"，每个对象都有自己的属性（是什么）和行为（能干什么）。这就像搭乐高积木，用各种标准化的积木（对象）来搭建复杂的城堡（程序）。
            <br>
            请点击下方按钮，通过动画来理解面向对象的三个核心特性：<strong>封装、继承、多态</strong>。
        </p>

        <div class="controls">
            <button id="classObjectBtn">① 类与对象</button>
            <button id="encapsulationBtn">② 封装</button>
            <button id="inheritanceBtn">③ 继承</button>
            <button id="polymorphismBtn">④ 多态</button>
        </div>
        
        <canvas id="oopCanvas"></canvas>
    </div>

    <script>
        const canvas = document.getElementById('oopCanvas');
        const ctx = canvas.getContext('2d');
        const buttons = {
            classObject: document.getElementById('classObjectBtn'),
            encapsulation: document.getElementById('encapsulationBtn'),
            inheritance: document.getElementById('inheritanceBtn'),
            polymorphism: document.getElementById('polymorphismBtn')
        };

        let currentScene = 'classObject';
        let animationFrameId;

        // Canvas 尺寸自适应
        function resizeCanvas() {
            const container = canvas.parentElement;
            const size = Math.min(container.clientWidth, 600);
            canvas.width = size * 2; // 提高分辨率
            canvas.height = size;
            ctx.scale(2, 2); // 缩放回css大小
        }
        window.addEventListener('resize', resizeCanvas);
        

        // --- 绘图工具函数 ---
        function drawBox(x, y, width, height, color, text, textColor = '#fff', textFont = '16px Arial') {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.fillStyle = textColor;
            ctx.font = textFont;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }

        function drawText(text, x, y, color = '#333', font = '14px Arial', align = 'center') {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = align;
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }
        
        function drawArrow(fromX, fromY, toX, toY, color = '#333', lineWidth = 2) {
            ctx.strokeStyle = color;
            ctx.lineWidth = lineWidth;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // --- 动画场景 ---
        const scenes = {
            classObject: {
                title: '类 (蓝图) 与 对象 (实例)',
                percent: 0,
                draw() {
                    const w = canvas.width / 2;
                    const h = canvas.height / 2;
                    this.percent = Math.min(1, this.percent + 0.015);
                    
                    ctx.clearRect(0, 0, w, h);
                    drawText(this.title, w / 2, 20, '#1a237e', 'bold 18px Arial');

                    // 1. 画蓝图
                    const blueprintX = w * 0.2;
                    const blueprintY = h * 0.3;
                    drawBox(blueprintX, blueprintY, 100, 120, '#42a5f5', '');
                    drawText('类: Animal (动物)', blueprintX + 50, blueprintY + 20, '#fff', 'bold 14px Arial');
                    drawText('属性: name', blueprintX + 50, blueprintY + 50, '#fff', '12px Arial');
                    drawText('行为: sound()', blueprintX + 50, blueprintY + 80, '#fff', '12px Arial');
                    drawText('蓝图 (Class)', blueprintX + 50, h * 0.8, '#333');

                    if (this.percent > 0.3) {
                        // 2. 画箭头
                        const arrowStart = blueprintX + 100;
                        const arrowEnd = w * 0.7 - 70;
                        const arrowY = h * 0.45;
                        const currentArrowEnd = arrowStart + (arrowEnd - arrowStart) * Math.min(1, (this.percent - 0.3) / 0.4);
                        drawArrow(arrowStart, arrowY, currentArrowEnd, arrowY, '#ff7043');
                        drawText('根据蓝图创建', w/2, arrowY - 15, '#ff7043', '12px Arial');

                        // 3. 画实例
                        if (this.percent > 0.7) {
                             const instanceX = w * 0.7;
                             const instanceY = h * 0.3;
                             const scale = (this.percent - 0.7) / 0.3;
                             ctx.save();
                             ctx.translate(instanceX + 35, instanceY + 35);
                             ctx.scale(scale, scale);
                             drawBox(-35, -35, 70, 70, '#ef5350', 'Dog 对象');
                             ctx.restore();
                             drawText('实例 (Object)', instanceX + 35, h * 0.8, '#333');
                        }
                    }
                }
            },
            encapsulation: {
                title: '封装: 保护数据，只留接口',
                percent: 0,
                draw() {
                    const w = canvas.width / 2;
                    const h = canvas.height / 2;
                    this.percent = Math.min(1, this.percent + 0.015);

                    ctx.clearRect(0, 0, w, h);
                    drawText(this.title, w / 2, 20, '#1a237e', 'bold 18px Arial');
                    
                    const boxX = w/2 - 75;
                    const boxY = h/2 - 75;

                    // 内部细节
                    const detailsOpacity = Math.max(0, 1 - (this.percent - 0.2) / 0.3);
                    ctx.globalAlpha = detailsOpacity;
                    drawText('内部属性: age = -5 (不合理!)', boxX + 75, boxY + 40, '#d32f2f');
                    drawText('内部实现: veryComplexLogic()', boxX + 75, boxY + 60, '#757575');
                    ctx.globalAlpha = 1;

                    // 胶囊/外壳
                    const shellExpansion = Math.min(1, this.percent / 0.5);
                    ctx.strokeStyle = '#66bb6a';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(boxX, boxY, 150, 150 * shellExpansion);

                    if (shellExpansion >= 1) {
                         drawText('对象 "胶囊"', boxX + 75, boxY - 15, '#388e3c');
                         // 公开接口
                         const interfaceOpacity = Math.min(1, (this.percent - 0.5) / 0.5);
                         ctx.globalAlpha = interfaceOpacity;
                         drawBox(boxX - 80, boxY + 30, 80, 40, '#29b6f6', 'setAge()');
                         drawBox(boxX + 150, boxY + 80, 80, 40, '#29b6f6', 'getInfo()');
                         drawText('公开的接口 (方法)', boxX-40, boxY + 100, '#1e88e5');
                         drawText('外部世界', w * 0.1, h/2, '#333', '16px Arial');
                         drawArrow(w * 0.2, h/2, boxX-80, boxY+50, '#1e88e5', 2);
                         ctx.globalAlpha = 1;

                         // 阻止非法访问
                         if (this.percent > 0.8) {
                            drawText('❌ 直接访问内部', w/2, h-30, '#d32f2f', 'bold 14px Arial');
                         }
                    }
                }
            },
            inheritance: {
                title: '继承: 延续与扩展',
                percent: 0,
                draw() {
                    const w = canvas.width / 2;
                    const h = canvas.height / 2;
                    this.percent = Math.min(1, this.percent + 0.015);
                    ctx.clearRect(0, 0, w, h);
                    drawText(this.title, w / 2, 20, '#1a237e', 'bold 18px Arial');

                    // 父类
                    const parentX = w/2 - 50;
                    const parentY = 40;
                    drawBox(parentX, parentY, 100, 100, '#42a5f5', '');
                    drawText('父类: Animal', parentX + 50, parentY + 20, '#fff', 'bold 14px Arial');
                    drawText('sound()', parentX + 50, parentY + 60, '#fff');
                    
                    // 子类
                    const childY = h - 120;
                    if (this.percent > 0.2) {
                        const arrowStartY = parentY + 100;
                        const arrowEndY = childY;
                        const arrowCurrentEnd = arrowStartY + (arrowEndY - arrowStartY) * Math.min(1, (this.percent - 0.2) / 0.4);
                        drawArrow(w/2, arrowStartY, w/2, arrowCurrentEnd, '#ff7043');
                        if (this.percent > 0.4) drawText('继承', w/2 + 20, h/2, '#ff7043');
                    }
                    
                    if (this.percent > 0.6) {
                        const childX = w/2 - 60;
                        const scale = (this.percent - 0.6) / 0.4;
                        ctx.save();
                        ctx.translate(childX + 60, childY + 60);
                        ctx.scale(scale, scale);
                        ctx.globalAlpha = scale;

                        drawBox(-60, -60, 120, 120, '#ef5350', '');
                        drawText('子类: Dog', 0, -40, '#fff', 'bold 14px Arial');
                        
                        // 继承来的
                        drawBox(-50, -20, 90, 30, 'rgba(66,165,245,0.7)', 'sound()');
                        drawText('(继承自Animal)', 0, 0, '#eee', 'italic 10px Arial');

                        // 自己的
                        drawBox(-50, 20, 90, 30, '#fff', 'bark()', '#333');
                        drawText('(Dog自己的)', 0, 40, '#eee', 'italic 10px Arial');

                        ctx.restore();
                    }
                }
            },
            polymorphism: {
                 title: '多态: 一种指令, 多种表现',
                 percent: 0,
                 animalType: null, // 'dog' or 'cat'
                 speak: false,
                 draw() {
                    const w = canvas.width / 2;
                    const h = canvas.height / 2;
                    this.percent = Math.min(1, this.percent + 0.02);

                    ctx.clearRect(0, 0, w, h);
                    drawText(this.title, w / 2, 20, '#1a237e', 'bold 18px Arial');

                    // "控制器"
                    drawBox(w*0.1, h/2 - 20, 100, 40, '#7e57c2', 'animal.sound()');
                    
                    // 指针
                    const arrowStartX = w*0.1 + 100;
                    const dogY = h * 0.3;
                    const catY = h * 0.7;

                    // 对象
                    const objX = w*0.7;
                    drawBox(objX, dogY - 25, 80, 50, '#ef5350', 'Dog 对象');
                    drawBox(objX, catY - 25, 80, 50, '#ffa726', 'Cat 对象');

                    if (this.animalType) {
                        const targetY = this.animalType === 'dog' ? dogY : catY;
                        drawArrow(arrowStartX, h/2, objX, targetY, '#7e57c2');

                        if (this.speak) {
                            const speech = this.animalType === 'dog' ? '汪汪汪!' : '喵喵喵~';
                            const color = this.animalType === 'dog' ? '#ef5350' : '#ffa726';
                            drawText(speech, objX + 40, targetY - 40, color, 'bold 20px Arial');
                        }
                    } else {
                         drawText('请在下方选择一个对象', w/2, h - 20);
                    }
                 },
                 setup() {
                    // 为多态场景创建特殊按钮
                    const controls = document.querySelector('.controls');
                    let polyControls = document.getElementById('polyControls');
                    if (!polyControls) {
                        polyControls = document.createElement('div');
                        polyControls.id = 'polyControls';
                        polyControls.style.marginTop = '10px';
                        polyControls.innerHTML = `
                            <button id="pointToDog">① 指向 Dog 对象</button>
                            <button id="pointToCat">② 指向 Cat 对象</button>
                            <button id="callSound" disabled>③ 调用 sound()</button>
                        `;
                        controls.insertAdjacentElement('afterend', polyControls);

                        document.getElementById('pointToDog').onclick = () => {
                            this.animalType = 'dog';
                            this.speak = false;
                            document.getElementById('callSound').disabled = false;
                        };
                        document.getElementById('pointToCat').onclick = () => {
                            this.animalType = 'cat';
                            this.speak = false;
                            document.getElementById('callSound').disabled = false;
                        };
                         document.getElementById('callSound').onclick = () => {
                            this.speak = true;
                            setTimeout(() => { this.speak = false; }, 1500);
                        };
                    }
                    polyControls.style.display = 'flex';
                 },
                 cleanup() {
                     const polyControls = document.getElementById('polyControls');
                     if (polyControls) polyControls.style.display = 'none';
                 }
            }
        };

        function setActiveButton(activeBtn) {
            Object.values(buttons).forEach(btn => {
                btn.classList.remove('active');
            });
            if (activeBtn) activeBtn.classList.add('active');
        }

        function switchScene(sceneName) {
            if (currentScene && scenes[currentScene].cleanup) {
                scenes[currentScene].cleanup();
            }

            currentScene = sceneName;
            
            Object.values(scenes).forEach(s => s.percent = 0);
            
            if (scenes[currentScene].setup) {
                scenes[currentScene].setup();
            }

            setActiveButton(buttons[sceneName.replace('Btn','').toLowerCase()]);

            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animate();
        }

        function animate() {
            const scene = scenes[currentScene];
            if (scene) {
                scene.draw();
            }
            if(scene.percent < 1 || currentScene === 'polymorphism') { // 多态场景持续刷新
                animationFrameId = requestAnimationFrame(animate);
            }
        }
        
        // --- Event Listeners ---
        buttons.classObject.onclick = () => switchScene('classObject');
        buttons.encapsulation.onclick = () => switchScene('encapsulation');
        buttons.inheritance.onclick = () => switchScene('inheritance');
        buttons.polymorphism.onclick = () => switchScene('polymorphism');

        // --- Initial Load ---
        resizeCanvas();
        switchScene('classObject'); // 默认显示第一个场景
        buttons.classObject.classList.add('active');

    </script>
</body>
</html> 