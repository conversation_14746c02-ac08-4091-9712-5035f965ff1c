<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>效用树结构探索游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .quiz-title {
            font-size: 1.8rem;
            color: #667eea;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #444;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: #f8f9ff;
            border: 2px solid #e1e5f2;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .option-card.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .option-card.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .option-card.incorrect {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .feedback {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .feedback.correct {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4CAF50;
        }

        .feedback.incorrect {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }

        .game-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .game-title {
            font-size: 2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .game-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .canvas-container {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            border: 2px solid #e1e5f2;
        }

        #treeCanvas {
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn.secondary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .progress-container {
            margin: 30px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5f2;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .explanation-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .explanation-title {
            font-size: 2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 40px;
            font-weight: 600;
        }

        .knowledge-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }

        .knowledge-card h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .knowledge-card p {
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .step-explanation {
            text-align: center;
            margin: 20px 0;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: 500;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .quiz-section, .game-section, .explanation-section {
                padding: 25px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🌳 效用树结构探索</h1>
            <p>通过互动游戏深入理解ATAM方法中效用树的四层结构，掌握软件架构评估的核心工具</p>
        </div>

        <!-- 题目测试区 -->
        <div class="quiz-section">
            <div class="quiz-title">🎯 知识检测</div>
            <div class="question-text">
                在软件架构评估中，<span class="highlight">ATAM</span>方法采用效用树这一工具来对质量属性进行分类和优先级排序。<br><br>
                效用树的结构包括：<span class="highlight">（____）</span>
            </div>
            <div class="options-grid">
                <div class="option-card" data-option="A">A. 树根--质量属性--属性分类--质量属性场景(叶子节点)</div>
                <div class="option-card" data-option="B">B. 树根--属性分类--属性描述--质量属性场景(叶子节点)</div>
                <div class="option-card" data-option="C">C. 树根--质量属性--属性描述--质量属性场景(叶子节点)</div>
                <div class="option-card" data-option="D">D. 树根--功能需求--需求描述--质量属性场景(叶子节点)</div>
            </div>
            <div id="quiz-feedback" class="feedback"></div>
        </div>

        <!-- 游戏互动区 -->
        <div class="game-section">
            <div class="game-title">🎮 效用树构建游戏</div>
            <div class="game-subtitle">按照正确的层次结构，一步步构建完整的效用树</div>
            
            <div class="progress-container">
                <div>构建进度: <span id="progress-text">0/4</span></div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="treeCanvas" width="1000" height="600"></canvas>
            </div>

            <div class="controls">
                <button class="btn" id="step1-btn">1️⃣ 添加树根</button>
                <button class="btn" id="step2-btn" disabled>2️⃣ 添加质量属性</button>
                <button class="btn" id="step3-btn" disabled>3️⃣ 添加属性分类</button>
                <button class="btn" id="step4-btn" disabled>4️⃣ 添加质量场景</button>
                <button class="btn secondary" id="reset-btn">🔄 重新开始</button>
            </div>

            <div class="step-explanation" id="step-explanation">
                点击"添加树根"开始构建效用树，体验ATAM方法的核心工具！
            </div>
        </div>

        <!-- 知识解释区 -->
        <div class="explanation-section">
            <div class="explanation-title">📚 深度解析</div>
            
            <div class="knowledge-card">
                <h3>🎯 什么是效用树？</h3>
                <p><span class="highlight">效用树（Utility Tree）</span>是ATAM方法的核心工具，就像一个"质量属性的家族树"。它帮助我们系统地组织和理解软件系统的各种质量需求。</p>
                <p>想象你在规划一个完美的家：你需要考虑安全性、舒适性、经济性等各个方面，效用树就是帮你把这些需求分门别类、按重要性排序的工具。</p>
            </div>

            <div class="knowledge-card">
                <h3>🏗️ 效用树的四层结构详解</h3>
                <p><strong>第1层 - 树根：</strong>系统的总体质量目标，如"构建高质量的在线教育平台"</p>
                <p><strong>第2层 - 质量属性：</strong>具体的质量维度，如性能、安全性、可用性、可维护性</p>
                <p><strong>第3层 - 属性分类：</strong>对质量属性的细分，如性能可分为"响应时间"和"吞吐量"</p>
                <p><strong>第4层 - 质量场景：</strong>具体可测试的场景，如"1000用户同时在线时，页面响应时间<2秒"</p>
            </div>

            <div class="knowledge-card">
                <h3>🎪 为什么这样分层？</h3>
                <p>这种分层结构有三大好处：</p>
                <p>• <strong>从抽象到具体：</strong>帮助团队从宏观目标逐步细化到可执行的具体要求</p>
                <p>• <strong>便于优先级排序：</strong>可以在每一层进行重要性评估和排序</p>
                <p>• <strong>支持架构决策：</strong>具体的场景可以直接指导架构设计和技术选型</p>
            </div>

            <div class="knowledge-card">
                <h3>🔍 实际应用举例</h3>
                <p>以电商系统为例：</p>
                <p><strong>树根：</strong>构建高质量的电商平台</p>
                <p><strong>质量属性：</strong>性能、安全性、可用性</p>
                <p><strong>属性分类：</strong>性能→响应时间、吞吐量；安全性→数据加密、访问控制</p>
                <p><strong>质量场景：</strong>"双11期间10万用户同时下单，系统响应时间<3秒，成功率>99.9%"</p>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentStep: 0,
            maxSteps: 4,
            animationId: null
        };

        // Canvas相关
        const canvas = document.getElementById('treeCanvas');
        const ctx = canvas.getContext('2d');

        // 题目测试逻辑
        const quizOptions = document.querySelectorAll('.option-card');
        const quizFeedback = document.getElementById('quiz-feedback');
        const correctAnswer = 'A';

        // 按钮元素
        const buttons = {
            step1: document.getElementById('step1-btn'),
            step2: document.getElementById('step2-btn'),
            step3: document.getElementById('step3-btn'),
            step4: document.getElementById('step4-btn'),
            reset: document.getElementById('reset-btn')
        };

        const stepExplanation = document.getElementById('step-explanation');
        const progressText = document.getElementById('progress-text');
        const progressFill = document.getElementById('progress-fill');

        // 题目测试事件
        quizOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 清除之前的选择
                quizOptions.forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'incorrect');
                });

                // 标记当前选择
                option.classList.add('selected');
                
                const selectedOption = option.dataset.option;
                
                setTimeout(() => {
                    if (selectedOption === correctAnswer) {
                        option.classList.add('correct');
                        showFeedback('🎉 正确！效用树的结构确实是：树根→质量属性→属性分类→质量属性场景。现在让我们通过游戏来深入体验这个结构！', 'correct');
                    } else {
                        option.classList.add('incorrect');
                        // 显示正确答案
                        quizOptions.forEach(opt => {
                            if (opt.dataset.option === correctAnswer) {
                                opt.classList.add('correct');
                            }
                        });
                        showFeedback('💡 不对哦！正确答案是A。效用树的层次是：树根→质量属性→属性分类→质量属性场景。这样的结构能够从抽象逐步细化到具体可测试的场景。', 'incorrect');
                    }
                }, 300);
            });
        });

        function showFeedback(message, type) {
            quizFeedback.innerHTML = message;
            quizFeedback.className = `feedback ${type}`;
            quizFeedback.style.display = 'block';
        }

        // 游戏控制逻辑
        buttons.step1.addEventListener('click', () => executeStep(1));
        buttons.step2.addEventListener('click', () => executeStep(2));
        buttons.step3.addEventListener('click', () => executeStep(3));
        buttons.step4.addEventListener('click', () => executeStep(4));
        buttons.reset.addEventListener('click', resetGame);

        function executeStep(step) {
            if (step <= gameState.currentStep + 1) {
                gameState.currentStep = step;
                updateProgress();
                updateButtons();
                
                switch(step) {
                    case 1:
                        drawStep1();
                        stepExplanation.innerHTML = '🎯 <strong>树根已添加！</strong> 这是整个系统的质量目标，为所有质量属性提供统一的方向和愿景。';
                        break;
                    case 2:
                        drawStep2();
                        stepExplanation.innerHTML = '🏗️ <strong>质量属性已添加！</strong> 这些是系统需要关注的主要质量维度，每个都代表一个重要的评估角度。';
                        break;
                    case 3:
                        drawStep3();
                        stepExplanation.innerHTML = '🔍 <strong>属性分类已添加！</strong> 将抽象的质量属性细分为更具体的方面，便于深入分析和评估。';
                        break;
                    case 4:
                        drawStep4();
                        stepExplanation.innerHTML = '✅ <strong>效用树构建完成！</strong> 现在有了具体可测试的场景，可以进行实际的架构评估和决策了！';
                        break;
                }
            }
        }

        function updateProgress() {
            progressText.textContent = `${gameState.currentStep}/${gameState.maxSteps}`;
            progressFill.style.width = `${(gameState.currentStep / gameState.maxSteps) * 100}%`;
        }

        function updateButtons() {
            // 更新按钮状态
            buttons.step1.disabled = gameState.currentStep >= 1;
            buttons.step2.disabled = gameState.currentStep < 1 || gameState.currentStep >= 2;
            buttons.step3.disabled = gameState.currentStep < 2 || gameState.currentStep >= 3;
            buttons.step4.disabled = gameState.currentStep < 3 || gameState.currentStep >= 4;

            // 高亮下一个可点击的按钮
            Object.values(buttons).forEach(btn => btn.classList.remove('pulse'));
            if (gameState.currentStep < 4) {
                const nextButton = Object.values(buttons)[gameState.currentStep];
                if (nextButton && !nextButton.disabled) {
                    nextButton.classList.add('pulse');
                }
            }
        }

        function resetGame() {
            gameState.currentStep = 0;
            updateProgress();
            updateButtons();
            clearCanvas();
            stepExplanation.innerHTML = '点击"添加树根"开始构建效用树，体验ATAM方法的核心工具！';
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制函数
        function drawStep1() {
            clearCanvas();
            
            // 绘制树根
            const rootX = canvas.width / 2;
            const rootY = 100;
            
            // 树根背景
            drawNode(rootX, rootY, '🎯 高质量软件系统', '#667eea', '#ffffff', 200, 60);
            
            // 添加说明文字
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('第1层：树根 - 系统的总体质量目标', rootX, rootY + 120);
        }

        function drawStep2() {
            drawStep1();
            
            const rootX = canvas.width / 2;
            const rootY = 100;
            const level2Y = 250;
            
            // 质量属性节点
            const attributes = [
                { text: '🚀 性能', x: rootX - 200, color: '#4CAF50' },
                { text: '🔒 安全性', x: rootX, color: '#FF9800' },
                { text: '⚡ 可用性', x: rootX + 200, color: '#2196F3' }
            ];
            
            attributes.forEach(attr => {
                // 绘制连接线
                drawLine(rootX, rootY + 30, attr.x, level2Y - 30, '#ddd', 2);
                // 绘制节点
                drawNode(attr.x, level2Y, attr.text, attr.color, '#ffffff', 150, 50);
            });
            
            // 说明文字
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('第2层：质量属性 - 具体的质量维度', canvas.width / 2, level2Y + 100);
        }

        function drawStep3() {
            drawStep2();
            
            const rootX = canvas.width / 2;
            const level2Y = 250;
            const level3Y = 400;
            
            // 属性分类
            const classifications = [
                { text: '响应时间', x: rootX - 250, parentX: rootX - 200, color: '#4CAF50' },
                { text: '吞吐量', x: rootX - 150, parentX: rootX - 200, color: '#4CAF50' },
                { text: '数据加密', x: rootX - 50, parentX: rootX, color: '#FF9800' },
                { text: '访问控制', x: rootX + 50, parentX: rootX, color: '#FF9800' },
                { text: '故障恢复', x: rootX + 150, parentX: rootX + 200, color: '#2196F3' },
                { text: '负载均衡', x: rootX + 250, parentX: rootX + 200, color: '#2196F3' }
            ];
            
            classifications.forEach(cls => {
                // 绘制连接线
                drawLine(cls.parentX, level2Y + 25, cls.x, level3Y - 25, '#ddd', 2);
                // 绘制节点
                drawNode(cls.x, level3Y, cls.text, cls.color, '#ffffff', 120, 40);
            });
            
            // 说明文字
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('第3层：属性分类 - 质量属性的细分', canvas.width / 2, level3Y + 80);
        }

        function drawStep4() {
            drawStep3();
            
            const rootX = canvas.width / 2;
            const level3Y = 400;
            const level4Y = 530;
            
            // 质量场景（简化显示部分）
            const scenarios = [
                { text: '1000用户<2秒', x: rootX - 250, parentX: rootX - 250, color: '#81C784' },
                { text: '10000并发', x: rootX - 150, parentX: rootX - 150, color: '#81C784' },
                { text: 'SSL加密', x: rootX - 50, parentX: rootX - 50, color: '#FFB74D' },
                { text: '权限验证', x: rootX + 50, parentX: rootX + 50, color: '#FFB74D' },
                { text: '30秒恢复', x: rootX + 150, parentX: rootX + 150, color: '#64B5F6' },
                { text: '自动扩容', x: rootX + 250, parentX: rootX + 250, color: '#64B5F6' }
            ];
            
            scenarios.forEach(scenario => {
                // 绘制连接线
                drawLine(scenario.parentX, level3Y + 20, scenario.x, level4Y - 20, '#ddd', 2);
                // 绘制节点
                drawNode(scenario.x, level4Y, scenario.text, scenario.color, '#ffffff', 100, 35);
            });
            
            // 说明文字
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.fillText('第4层：质量场景 - 具体可测试的场景（叶子节点）', canvas.width / 2, level4Y + 70);
        }

        function drawNode(x, y, text, bgColor, textColor, width, height) {
            // 绘制节点背景
            ctx.fillStyle = bgColor;
            ctx.beginPath();
            ctx.roundRect(x - width/2, y - height/2, width, height, 10);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = bgColor;
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = textColor;
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        function drawLine(x1, y1, x2, y2, color, width) {
            ctx.strokeStyle = color;
            ctx.lineWidth = width;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // 初始化
        updateButtons();
        clearCanvas();
        
        // 添加 roundRect 方法支持（兼容性）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>