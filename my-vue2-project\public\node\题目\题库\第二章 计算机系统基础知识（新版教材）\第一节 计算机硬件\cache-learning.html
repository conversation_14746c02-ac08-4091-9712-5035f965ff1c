<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高速缓存（Cache）交互学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: slideInUp 1s ease-out;
        }

        .question-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: #f9f9f9;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #4CAF50;
            background: #e8f5e8;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
            animation: shake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
        }

        .controls {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            animation: fadeIn 1s ease-out;
        }

        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            opacity: 0;
            animation: slideInLeft 0.8s ease-out forwards;
        }

        .step:nth-child(2) { animation-delay: 0.2s; }
        .step:nth-child(3) { animation-delay: 0.4s; }
        .step:nth-child(4) { animation-delay: 0.6s; }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 高速缓存学习之旅</h1>
            <p>通过动画和交互，轻松掌握Cache原理</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                📝 在高速缓存（Cache）—主存储器构成的存储系统中，（ ）。
            </div>
            <div class="options">
                <div class="option" data-answer="A">
                    A. 主存地址到Cache地址的变换由硬件完成，以提高速度
                </div>
                <div class="option" data-answer="B">
                    B. 主存地址到Cache地址的变换由软件完成，以提高灵活性
                </div>
                <div class="option" data-answer="C">
                    C. Cache的命中率随其容量增大线性地提高
                </div>
                <div class="option" data-answer="D">
                    D. Cache的内容在任意时刻与主存内容完全一致
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎮 Cache工作原理动画演示</h3>
            <canvas id="cacheCanvas" width="800" height="500"></canvas>
            <div class="controls">
                <button class="btn" onclick="startDemo()">开始演示</button>
                <button class="btn" onclick="showHardwareMapping()">硬件地址映射</button>
                <button class="btn" onclick="showCacheHit()">Cache命中演示</button>
                <button class="btn" onclick="resetDemo()">重置</button>
            </div>
        </div>

        <div class="explanation">
            <h3>💡 知识点详解</h3>
            <div class="step">
                <strong>🔧 硬件地址映射：</strong>
                CPU发出的主存地址会被<span class="highlight">硬件自动转换</span>为Cache地址，这个过程无需软件参与，速度极快！
            </div>
            <div class="step">
                <strong>⚡ 为什么用硬件？</strong>
                因为地址转换需要在每次内存访问时进行，如果用软件会太慢，硬件转换可以在<span class="highlight">一个时钟周期</span>内完成。
            </div>
            <div class="step">
                <strong>🎯 Cache命中率：</strong>
                命中率不是线性增长的，而是遵循<span class="highlight">局部性原理</span>，增长会逐渐放缓。
            </div>
            <div class="step">
                <strong>🔄 数据一致性：</strong>
                Cache和主存内容<span class="highlight">不总是一致</span>的，需要通过写回、写直达等策略来维护一致性。
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('cacheCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentStep = 0;

        // 初始化选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！硬件地址映射确实是为了提高速度！');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                    setTimeout(() => {
                        alert('❌ 答案是A哦！让我们通过动画来理解为什么~');
                    }, 500);
                }
            });
        });

        function drawCPU(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#FFD700';
                ctx.shadowBlur = 20;
            }
            
            // CPU芯片
            ctx.fillStyle = highlight ? '#FFD700' : '#4CAF50';
            ctx.fillRect(x, y, 80, 60);
            
            // CPU标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', x + 40, y + 35);
            
            ctx.restore();
        }

        function drawCache(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#FF6B6B';
                ctx.shadowBlur = 20;
            }
            
            // Cache
            ctx.fillStyle = highlight ? '#FF6B6B' : '#2196F3';
            ctx.fillRect(x, y, 120, 80);
            
            // Cache标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Cache', x + 60, y + 45);
            
            ctx.restore();
        }

        function drawMainMemory(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#9C27B0';
                ctx.shadowBlur = 20;
            }
            
            // 主存
            ctx.fillStyle = highlight ? '#9C27B0' : '#FF9800';
            ctx.fillRect(x, y, 100, 120);
            
            // 主存标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主存', x + 50, y + 65);
            
            ctx.restore();
        }

        function drawArrow(fromX, fromY, toX, toY, color = '#333', animated = false) {
            ctx.save();
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            if (animated) {
                ctx.setLineDash([10, 5]);
                ctx.lineDashOffset = -Date.now() / 50;
            }
            
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
            
            ctx.restore();
        }

        function drawHardwareUnit(x, y, highlight = false) {
            ctx.save();
            if (highlight) {
                ctx.shadowColor = '#00BCD4';
                ctx.shadowBlur = 15;
            }
            
            // 硬件地址映射单元
            ctx.fillStyle = highlight ? '#00BCD4' : '#607D8B';
            ctx.fillRect(x, y, 100, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('地址映射', x + 50, y + 20);
            ctx.fillText('硬件单元', x + 50, y + 32);
            
            ctx.restore();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function startDemo() {
            currentStep = 0;
            animate();
        }

        function animate() {
            clearCanvas();
            
            // 绘制基本组件
            drawCPU(50, 200, currentStep === 1);
            drawCache(300, 150, currentStep === 3);
            drawMainMemory(600, 100, currentStep === 4);
            drawHardwareUnit(175, 250, currentStep === 2);
            
            // 绘制连接线
            if (currentStep >= 1) {
                drawArrow(130, 230, 175, 270, '#4CAF50', currentStep === 1);
            }
            if (currentStep >= 2) {
                drawArrow(275, 270, 300, 190, '#00BCD4', currentStep === 2);
            }
            if (currentStep >= 3) {
                drawArrow(420, 190, 600, 160, '#2196F3', currentStep === 3);
            }
            
            // 添加文字说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            
            if (currentStep >= 1) {
                ctx.fillText('1. CPU发出内存地址', 50, 50);
            }
            if (currentStep >= 2) {
                ctx.fillText('2. 硬件自动进行地址映射', 50, 80);
            }
            if (currentStep >= 3) {
                ctx.fillText('3. 检查Cache是否命中', 50, 110);
            }
            if (currentStep >= 4) {
                ctx.fillText('4. 未命中则访问主存', 50, 140);
            }
            
            currentStep++;
            if (currentStep <= 4) {
                setTimeout(() => {
                    animationId = requestAnimationFrame(animate);
                }, 1500);
            }
        }

        function showHardwareMapping() {
            clearCanvas();
            
            // 绘制详细的硬件映射过程
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('硬件地址映射过程', 400, 30);
            
            // 主存地址
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(100, 100, 200, 50);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('主存地址: 0x1A2B3C4D', 200, 130);
            
            // 映射硬件
            drawHardwareUnit(350, 200, true);
            
            // Cache地址
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(500, 300, 200, 50);
            ctx.fillStyle = 'white';
            ctx.fillText('Cache地址: 0x4D', 600, 330);
            
            // 箭头
            drawArrow(300, 125, 350, 220, '#FF9800', true);
            drawArrow(450, 220, 500, 325, '#2196F3', true);
            
            // 说明文字
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('硬件自动提取地址的低位', 400, 180);
            ctx.fillText('作为Cache索引，速度极快！', 400, 195);
        }

        function showCacheHit() {
            clearCanvas();
            
            // 模拟Cache命中过程
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Cache命中演示', 400, 30);
            
            // Cache块
            for (let i = 0; i < 4; i++) {
                const y = 80 + i * 60;
                const isHit = i === 2;
                
                ctx.fillStyle = isHit ? '#4CAF50' : '#E0E0E0';
                ctx.fillRect(300, y, 200, 50);
                
                ctx.fillStyle = isHit ? 'white' : '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`Cache块 ${i}`, 400, y + 20);
                ctx.fillText(isHit ? '命中！' : '空闲', 400, y + 35);
                
                if (isHit) {
                    // 添加闪烁效果
                    ctx.save();
                    ctx.shadowColor = '#4CAF50';
                    ctx.shadowBlur = 20;
                    ctx.strokeStyle = '#4CAF50';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(295, y - 5, 210, 60);
                    ctx.restore();
                }
            }
            
            // 访问箭头
            drawArrow(200, 200, 300, 200, '#FF6B6B', true);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('CPU请求数据', 50, 200);
            ctx.fillText('硬件快速定位到Cache块2', 50, 400);
            ctx.fillText('数据命中，直接返回！', 50, 420);
        }

        function resetDemo() {
            clearCanvas();
            currentStep = 0;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            // 绘制初始状态
            drawCPU(50, 200);
            drawCache(300, 150);
            drawMainMemory(600, 100);
            drawHardwareUnit(175, 250);
            
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习Cache工作原理', 400, 450);
        }

        // 初始化画布
        resetDemo();
    </script>
</body>
</html>
