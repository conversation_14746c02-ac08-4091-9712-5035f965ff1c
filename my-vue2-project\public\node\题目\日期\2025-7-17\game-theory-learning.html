<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博弈论 - 网站定价策略学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .title {
            color: white;
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0;
            transform: translateY(-40px);
            animation: slideDown 1.2s ease-out forwards;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.3rem;
            opacity: 0;
            animation: fadeIn 1.5s ease-out 0.5s forwards;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateY(40px);
            animation: slideUp 1.2s ease-out 1s forwards;
        }

        .question-section {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border-radius: 20px;
            padding: 35px;
            margin-bottom: 40px;
            border: 3px solid #667eea;
            position: relative;
            overflow: hidden;
        }

        .question-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: shimmer 4s infinite;
        }

        .question-text {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #2d3436;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .highlight {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: bold;
        }

        .game-board {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 40px;
            margin: 40px 0;
        }

        .controls-panel {
            background: #f8f9ff;
            border-radius: 20px;
            padding: 30px;
            border: 2px solid #ddd;
        }

        .controls-title {
            font-size: 1.5rem;
            color: #2d3436;
            margin-bottom: 25px;
            text-align: center;
            font-weight: bold;
        }

        .website-control {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .website-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3436;
            text-align: center;
        }

        .strategy-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .strategy-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .strategy-btn.high-price {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .strategy-btn.low-price {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .strategy-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .strategy-btn.selected {
            transform: scale(1.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        #gameCanvas {
            border-radius: 15px;
            border: 2px solid #eee;
        }

        .options-section {
            margin: 40px 0;
        }

        .options-title {
            font-size: 1.8rem;
            color: #2d3436;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .option-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 3px solid transparent;
            transform: translateY(20px);
            opacity: 0;
            animation: cardSlideIn 0.8s ease-out forwards;
        }

        .option-card:nth-child(1) { animation-delay: 1.5s; }
        .option-card:nth-child(2) { animation-delay: 1.7s; }
        .option-card:nth-child(3) { animation-delay: 1.9s; }
        .option-card:nth-child(4) { animation-delay: 2.1s; }

        .option-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.8s ease;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease;
        }

        .option-letter {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            line-height: 40px;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .option-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 10px;
        }

        .result-display {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .result-display.show {
            opacity: 1;
            transform: translateY(0);
        }

        .result-title {
            font-size: 1.5rem;
            color: #0984e3;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .result-text {
            font-size: 1.1rem;
            color: #2d3436;
            line-height: 1.6;
        }

        .reset-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 30px auto;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .reset-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }

        @keyframes slideDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes cardSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .floating-icons {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-icon {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-icons">
        <div class="floating-icon" style="top: 15%; left: 10%; font-size: 2rem;">💰</div>
        <div class="floating-icon" style="top: 25%; right: 15%; font-size: 1.8rem;">📊</div>
        <div class="floating-icon" style="bottom: 30%; left: 20%; font-size: 2.2rem;">🎯</div>
        <div class="floating-icon" style="bottom: 20%; right: 10%; font-size: 2rem;">⚖️</div>
        <div class="floating-icon" style="top: 50%; left: 5%; font-size: 1.5rem;">🏢</div>
        <div class="floating-icon" style="top: 60%; right: 8%; font-size: 1.8rem;">💡</div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🎮 博弈论学习</h1>
            <p class="subtitle">网站定价策略的博弈分析 - 囚徒困境</p>
        </div>

        <div class="main-content">
            <div class="question-section">
                <div class="question-text">
                    甲、乙两个独立的网站都主要靠广告收入来支撑发展，目前都采用较高的价格销售广告。这两个网站都想通过降价争夺更多的客户和更丰厚的利润。假设这两个网站在现有策略下各可以获得<span class="highlight">1000万元</span>的利润。如果一方单独降价，就能扩大市场份额，可以获得<span class="highlight">1500万元</span>利润，此时，另一方的市场份额就会缩小，利润将下降到<span class="highlight">200万元</span>。如果这两个网站同时降价，则他们都将只能得到<span class="highlight">700万元</span>利润。这两个网站的主管各自经过独立的理性分析后决定，（ ）。
                </div>
            </div>

            <div class="game-board">
                <div class="controls-panel">
                    <h3 class="controls-title">🎯 策略选择</h3>
                    
                    <div class="website-control">
                        <div class="website-name">🏢 甲网站</div>
                        <div class="strategy-buttons">
                            <button class="strategy-btn high-price" data-website="A" data-strategy="high">高价策略</button>
                            <button class="strategy-btn low-price" data-website="A" data-strategy="low">低价策略</button>
                        </div>
                    </div>

                    <div class="website-control">
                        <div class="website-name">🏢 乙网站</div>
                        <div class="strategy-buttons">
                            <button class="strategy-btn high-price" data-website="B" data-strategy="high">高价策略</button>
                            <button class="strategy-btn low-price" data-website="B" data-strategy="low">低价策略</button>
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gameCanvas" width="600" height="400"></canvas>
                </div>
            </div>

            <div class="result-display" id="resultDisplay">
                <div class="result-title">📊 博弈结果</div>
                <div class="result-text" id="resultText">请选择两个网站的策略来查看博弈结果</div>
            </div>

            <div class="options-section">
                <h3 class="options-title">根据博弈论分析，最终结果是：</h3>
                <div class="options-grid">
                    <div class="option-card" data-option="A">
                        <div class="option-letter">A</div>
                        <div class="option-text">甲采取高价策略，乙采取低价策略</div>
                    </div>
                    
                    <div class="option-card" data-option="B">
                        <div class="option-letter">B</div>
                        <div class="option-text">甲采取高价策略，乙采取高价策略</div>
                    </div>
                    
                    <div class="option-card" data-option="C">
                        <div class="option-letter">C</div>
                        <div class="option-text">甲采取低价策略，乙采取低价策略</div>
                    </div>
                    
                    <div class="option-card" data-option="D">
                        <div class="option-letter">D</div>
                        <div class="option-text">甲采取低价策略，乙采取高价策略</div>
                    </div>
                </div>
            </div>

            <button class="reset-button" onclick="resetGame()">🔄 重新开始分析</button>
        </div>

        <div class="explanation-panel" id="explanationPanel" style="background: linear-gradient(135deg, #e8f4fd 0%, #f8f9ff 100%); border-radius: 20px; padding: 40px; margin-top: 40px; opacity: 0; transform: translateY(30px); transition: all 0.6s ease;">
            <h2 style="color: #0984e3; font-size: 2rem; text-align: center; margin-bottom: 30px; font-weight: bold;">🎓 博弈论深度解析</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px;">
                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #667eea;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🎯 囚徒困境</h3>
                    <p style="color: #636e72; line-height: 1.7;">这是经典的囚徒困境博弈。每个参与者都有合作（高价）和背叛（低价）两种选择，但理性分析导致双方都选择背叛，结果对双方都不利。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #fd79a8;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🧠 理性分析</h3>
                    <p style="color: #636e72; line-height: 1.7;">无论对方选择什么策略，自己选择低价策略总是更优：对方高价时自己低价得1500万>1000万；对方低价时自己低价得700万>200万。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #00b894;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">⚖️ 纳什均衡</h3>
                    <p style="color: #636e72; line-height: 1.7;">双方都选择低价策略是纳什均衡点。在这个状态下，任何一方单方面改变策略都不会获得更好的结果。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #fdcb6e;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">💡 现实意义</h3>
                    <p style="color: #636e72; line-height: 1.7;">这解释了为什么价格战经常发生，即使对所有参与者都不利。缺乏信任和沟通机制导致了次优结果。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            websiteA: null, // 'high' or 'low'
            websiteB: null, // 'high' or 'low'
            gameCompleted: false
        };

        // 收益矩阵
        const payoffMatrix = {
            'high-high': { A: 1000, B: 1000 },
            'high-low': { A: 200, B: 1500 },
            'low-high': { A: 1500, B: 200 },
            'low-low': { A: 700, B: 700 }
        };

        // 获取Canvas和上下文
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        // 动画变量
        let animationFrame = 0;
        let particles = [];

        // 初始化游戏
        function initGame() {
            setupEventListeners();
            startAnimation();
            showExplanation();
            drawInitialMatrix();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 策略按钮事件
            const strategyButtons = document.querySelectorAll('.strategy-btn');
            strategyButtons.forEach(btn => {
                btn.addEventListener('click', handleStrategyClick);
            });

            // 选项卡片事件
            const optionCards = document.querySelectorAll('.option-card');
            optionCards.forEach(card => {
                card.addEventListener('click', handleOptionClick);
            });
        }

        // 处理策略选择
        function handleStrategyClick(event) {
            const btn = event.currentTarget;
            const website = btn.dataset.website;
            const strategy = btn.dataset.strategy;

            // 移除同组按钮的选中状态
            const sameGroupBtns = document.querySelectorAll(`[data-website="${website}"]`);
            sameGroupBtns.forEach(b => b.classList.remove('selected'));

            // 添加选中状态
            btn.classList.add('selected');

            // 更新游戏状态
            if (website === 'A') {
                gameState.websiteA = strategy;
            } else {
                gameState.websiteB = strategy;
            }

            // 如果两个网站都选择了策略，显示结果
            if (gameState.websiteA && gameState.websiteB) {
                showGameResult();
                drawPayoffMatrix();
            }
        }

        // 显示博弈结果
        function showGameResult() {
            const resultDisplay = document.getElementById('resultDisplay');
            const resultText = document.getElementById('resultText');

            const key = `${gameState.websiteA}-${gameState.websiteB}`;
            const payoff = payoffMatrix[key];

            const strategyText = {
                'high': '高价策略',
                'low': '低价策略'
            };

            resultText.innerHTML = `
                <strong>甲网站</strong>选择${strategyText[gameState.websiteA]}，获得<strong>${payoff.A}万元</strong>利润<br>
                <strong>乙网站</strong>选择${strategyText[gameState.websiteB]}，获得<strong>${payoff.B}万元</strong>利润
            `;

            resultDisplay.classList.add('show');

            // 创建粒子效果
            createResultParticles();
        }

        // 处理选项点击
        function handleOptionClick(event) {
            if (gameState.gameCompleted) return;

            const card = event.currentTarget;
            const option = card.dataset.option;

            // 重置所有卡片状态
            document.querySelectorAll('.option-card').forEach(c => {
                c.classList.remove('correct', 'wrong');
            });

            // 检查答案
            if (option === 'C') {
                card.classList.add('correct');
                gameState.gameCompleted = true;

                setTimeout(() => {
                    showCompletionMessage();
                }, 1000);
            } else {
                card.classList.add('wrong');
                setTimeout(() => {
                    card.classList.remove('wrong');
                }, 800);
            }
        }

        // 开始动画循环
        function startAnimation() {
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                drawBackground();

                // 绘制当前状态
                if (gameState.websiteA && gameState.websiteB) {
                    drawPayoffMatrix();
                } else {
                    drawInitialMatrix();
                }

                // 绘制粒子效果
                drawParticles();

                animationFrame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 绘制背景
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9ff');
            gradient.addColorStop(1, '#e8f4fd');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制初始矩阵
        function drawInitialMatrix() {
            // 绘制标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('博弈收益矩阵', canvas.width / 2, 40);

            // 绘制矩阵框架
            const matrixX = 150;
            const matrixY = 80;
            const cellWidth = 120;
            const cellHeight = 80;

            // 绘制表格线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;

            // 外框
            ctx.strokeRect(matrixX, matrixY, cellWidth * 3, cellHeight * 3);

            // 内部分割线
            for (let i = 1; i < 3; i++) {
                // 垂直线
                ctx.beginPath();
                ctx.moveTo(matrixX + cellWidth * i, matrixY);
                ctx.lineTo(matrixX + cellWidth * i, matrixY + cellHeight * 3);
                ctx.stroke();

                // 水平线
                ctx.beginPath();
                ctx.moveTo(matrixX, matrixY + cellHeight * i);
                ctx.lineTo(matrixX + cellWidth * 3, matrixY + cellHeight * i);
                ctx.stroke();
            }

            // 绘制标签
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';

            // 列标签（乙网站策略）
            ctx.fillText('乙网站', matrixX + cellWidth * 2, matrixY - 20);
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('高价', matrixX + cellWidth * 1.5, matrixY + 20);
            ctx.fillText('低价', matrixX + cellWidth * 2.5, matrixY + 20);

            // 行标签（甲网站策略）
            ctx.save();
            ctx.translate(matrixX - 30, matrixY + cellHeight * 1.5);
            ctx.rotate(-Math.PI / 2);
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('甲网站', 0, 0);
            ctx.restore();

            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('高价', matrixX + 30, matrixY + cellHeight * 1.5);
            ctx.fillText('低价', matrixX + 30, matrixY + cellHeight * 2.5);

            // 绘制收益值
            const payoffs = [
                ['1000,1000', '200,1500'],
                ['1500,200', '700,700']
            ];

            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';

            for (let row = 0; row < 2; row++) {
                for (let col = 0; col < 2; col++) {
                    const x = matrixX + cellWidth * (col + 1.5);
                    const y = matrixY + cellHeight * (row + 1.5);
                    ctx.fillText(payoffs[row][col], x, y);
                }
            }

            // 绘制说明
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('格式：甲网站利润,乙网站利润（万元）', canvas.width / 2, canvas.height - 30);
        }

        // 绘制收益矩阵（带高亮）
        function drawPayoffMatrix() {
            drawInitialMatrix();

            if (!gameState.websiteA || !gameState.websiteB) return;

            // 确定高亮位置
            const row = gameState.websiteA === 'high' ? 0 : 1;
            const col = gameState.websiteB === 'high' ? 0 : 1;

            const matrixX = 150;
            const matrixY = 80;
            const cellWidth = 120;
            const cellHeight = 80;

            // 高亮选中的格子
            const highlightX = matrixX + cellWidth * (col + 1);
            const highlightY = matrixY + cellHeight * (row + 1);

            ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.fillRect(highlightX, highlightY, cellWidth, cellHeight);

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 4;
            ctx.strokeRect(highlightX, highlightY, cellWidth, cellHeight);

            // 添加脉动效果
            const pulse = Math.sin(animationFrame * 0.1) * 10;
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.5)';
            ctx.lineWidth = 2;
            ctx.strokeRect(highlightX - pulse/2, highlightY - pulse/2, cellWidth + pulse, cellHeight + pulse);
        }

        // 创建结果粒子效果
        function createResultParticles() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            for (let i = 0; i < 30; i++) {
                particles.push({
                    x: centerX,
                    y: centerY,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    life: 100,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    size: Math.random() * 5 + 2
                });
            }
        }

        // 绘制粒子效果
        function drawParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                particle.vx *= 0.98;
                particle.vy *= 0.98;

                ctx.globalAlpha = particle.life / 100;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;

                return particle.life > 0;
            });
        }

        // 显示完成消息
        function showCompletionMessage() {
            alert('🎉 恭喜！您理解了博弈论的核心概念！\n\n✅ 正确答案：C - 甲采取低价策略，乙采取低价策略\n\n这是经典的囚徒困境：虽然双方合作（都选高价）能获得更好的整体结果，但理性分析导致双方都选择背叛（低价），最终陷入次优均衡。');
        }

        // 显示详细解释
        function showExplanation() {
            const explanationPanel = document.getElementById('explanationPanel');
            setTimeout(() => {
                explanationPanel.style.opacity = '1';
                explanationPanel.style.transform = 'translateY(0)';
            }, 3000);
        }

        // 重置游戏
        function resetGame() {
            gameState = {
                websiteA: null,
                websiteB: null,
                gameCompleted: false
            };

            // 重置按钮状态
            document.querySelectorAll('.strategy-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            // 重置选项卡片
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
            });

            // 隐藏结果显示
            const resultDisplay = document.getElementById('resultDisplay');
            resultDisplay.classList.remove('show');

            // 清除粒子
            particles = [];
        }

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
