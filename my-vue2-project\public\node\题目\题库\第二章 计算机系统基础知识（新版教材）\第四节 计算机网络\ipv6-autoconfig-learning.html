<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPv6无状态自动配置学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .concept-card.prefix {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .concept-card.mac {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .concept-card.eui64 {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .concept-card.linklocal {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .concept-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .concept-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .address-demo {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .address-demo h4 {
            font-family: 'Microsoft YaHei', sans-serif;
            margin-bottom: 15px;
        }

        .address-part {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }

        .address-label {
            min-width: 120px;
            font-weight: bold;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .address-value {
            flex: 1;
            font-size: 1.1rem;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .config-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .binary-display {
            background: #2c3e50;
            color: #00ff00;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .mac-breakdown {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .mac-byte {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }

        .eui64-process {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }

        .eui64-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #00d4aa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 IPv6无状态自动配置学习</h1>
            <p>探索IPv6链路本地地址的神奇生成过程</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>在IPv6无状态自动配置过程中，主机将其（ ）附加在地址前缀1111 1110 10之后，产生一个链路本地地址。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> IPv4地址
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> MAC地址
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 主机名
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 随机产生的字符串
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是IPv6链路本地地址？</h2>
            <div class="explanation">
                <p><span class="highlight">链路本地地址</span>是IPv6中用于同一链路上相邻节点间通信的特殊地址，前缀为<strong>1111 1110 10</strong>（FE80::/10）。</p>
                <p>🔑 <strong>核心特点：</strong>相当于IPv4的自动专用IP地址（169.254.x.x），无需配置即可自动生成。</p>
            </div>
            
            <div class="address-demo">
                <h4>📋 链路本地地址结构</h4>
                <div class="address-part">
                    <div class="address-label">前缀：</div>
                    <div class="address-value">FE80:0000:0000:0000</div>
                </div>
                <div class="address-part">
                    <div class="address-label">接口标识符：</div>
                    <div class="address-value">从MAC地址生成（EUI-64格式）</div>
                </div>
                <div class="address-part">
                    <div class="address-label">完整地址：</div>
                    <div class="address-value">FE80::接口标识符</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 IPv6自动配置核心概念</h2>
            <div class="concept-grid">
                <div class="concept-card prefix" onclick="showConceptDemo('prefix')">
                    <span class="icon">🏷️</span>
                    <h3>地址前缀</h3>
                    <p>FE80::/10</p>
                    <p>链路本地标识</p>
                </div>
                <div class="concept-card mac" onclick="showConceptDemo('mac')">
                    <span class="icon">🔗</span>
                    <h3>MAC地址</h3>
                    <p>48位硬件地址</p>
                    <p>网卡唯一标识</p>
                </div>
                <div class="concept-card eui64" onclick="showConceptDemo('eui64')">
                    <span class="icon">🔄</span>
                    <h3>EUI-64转换</h3>
                    <p>MAC转64位标识符</p>
                    <p>插入FFFE</p>
                </div>
                <div class="concept-card linklocal" onclick="showConceptDemo('linklocal')">
                    <span class="icon">🌐</span>
                    <h3>链路本地地址</h3>
                    <p>自动生成</p>
                    <p>本地通信</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 IPv6地址生成过程演示</h2>
            <div class="canvas-container">
                <canvas id="configCanvas"></canvas>
                <div class="config-indicator" id="configIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startAutoConfigDemo()">🔄 自动配置演示</button>
                <button class="btn" onclick="showMACtoEUI64()">🔗 MAC转EUI-64</button>
                <button class="btn" onclick="showAddressGeneration()">🌐 地址生成过程</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：IPv4地址 ❌</h3>
                <p><strong>错误原因：</strong>IPv4地址与IPv6地址生成<span class="highlight">完全无关</span></p>
                <ul>
                    <li>🌐 <strong>协议独立：</strong>IPv6有自己独立的地址生成机制</li>
                    <li>📏 <strong>长度不匹配：</strong>IPv4地址32位，无法填充IPv6的64位接口标识符</li>
                    <li>🎯 <strong>设计目标：</strong>IPv6设计就是为了摆脱IPv4的限制</li>
                    <li>⚡ <strong>自动配置：</strong>IPv6的无状态自动配置不依赖IPv4</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项B：MAC地址 ✅</h3>
                <p><strong>正确答案！</strong>IPv6无状态自动配置使用<span class="highlight">MAC地址</span>生成接口标识符</p>
                <ul>
                    <li>🔗 <strong>硬件标识：</strong>MAC地址是网卡的唯一硬件标识符</li>
                    <li>🔄 <strong>EUI-64转换：</strong>通过EUI-64格式将48位MAC转换为64位接口标识符</li>
                    <li>🌐 <strong>全球唯一：</strong>MAC地址的全球唯一性保证了IPv6地址的唯一性</li>
                    <li>⚡ <strong>自动获取：</strong>主机可以自动获取自己的MAC地址</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>MAC地址通过EUI-64转换成为IPv6地址的接口标识符部分，这是IPv6无状态自动配置的核心机制。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项C：主机名 ❌</h3>
                <p><strong>错误原因：</strong>主机名是<span class="highlight">人类可读</span>的标识符，不用于地址生成</p>
                <ul>
                    <li>📝 <strong>文本标识：</strong>主机名是文本形式的标识符，如"computer1"</li>
                    <li>🔄 <strong>需要解析：</strong>主机名需要通过DNS解析为IP地址</li>
                    <li>❌ <strong>非唯一性：</strong>主机名在不同网络中可能重复</li>
                    <li>🎯 <strong>用途不同：</strong>主机名用于人类识别，不用于自动地址生成</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：随机产生的字符串 ❌</h3>
                <p><strong>错误原因：</strong>虽然有些情况下使用随机值，但<span class="highlight">标准的无状态自动配置</span>使用MAC地址</p>
                <ul>
                    <li>🎲 <strong>隐私扩展：</strong>IPv6隐私扩展确实使用随机值，但这不是标准的无状态配置</li>
                    <li>📋 <strong>标准流程：</strong>RFC 4862定义的标准流程使用MAC地址</li>
                    <li>🔄 <strong>可重现性：</strong>使用MAC地址可以确保地址的可重现性</li>
                    <li>⚡ <strong>题目语境：</strong>题目问的是标准的无状态自动配置过程</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔧 EUI-64转换详细过程</h2>

            <div class="address-demo">
                <h4>🔢 MAC地址转EUI-64步骤</h4>
                <div class="eui64-process">
                    <div class="eui64-step">
                        <strong>步骤1：</strong>获取48位MAC地址
                        <div class="mac-breakdown">
                            <div class="mac-byte">00</div>
                            <div class="mac-byte">1B</div>
                            <div class="mac-byte">44</div>
                            <div class="mac-byte">11</div>
                            <div class="mac-byte">3A</div>
                            <div class="mac-byte">B7</div>
                        </div>
                    </div>
                    <div class="eui64-step">
                        <strong>步骤2：</strong>分割为OUI（前3字节）和NIC（后3字节）
                        <br>OUI: 00:1B:44 | NIC: 11:3A:B7
                    </div>
                    <div class="eui64-step">
                        <strong>步骤3：</strong>在中间插入FFFE
                        <br>00:1B:44 + FF:FE + 11:3A:B7
                    </div>
                    <div class="eui64-step">
                        <strong>步骤4：</strong>翻转第7位（U/L位）
                        <br>00 → 02 (00000000 → 00000010)
                    </div>
                    <div class="eui64-step">
                        <strong>结果：</strong>64位EUI-64接口标识符
                        <br><strong>021B:44FF:FE11:3AB7</strong>
                    </div>
                </div>
            </div>

            <div class="binary-display">
原始MAC: 00:1B:44:11:3A:B7
二进制:   00000000 00011011 01000100 00010001 00111010 10110111

EUI-64:   021B:44FF:FE11:3AB7
二进制:   00000010 00011011 01000100 11111111 11111110 00010001 00111010 10110111
          ↑第7位翻转              ↑插入的FFFE
            </div>
        </div>

        <div class="section">
            <h2>🌐 IPv6地址类型对比</h2>

            <div class="concept-box">
                <h4>📍 不同类型的IPv6地址</h4>
                <p><strong>链路本地地址（FE80::/10）：</strong>用于同一链路上的通信，自动配置</p>
                <p><strong>唯一本地地址（FC00::/7）：</strong>用于本地网络内部通信</p>
                <p><strong>全球单播地址（2000::/3）：</strong>用于互联网通信，可路由</p>
                <p><strong>多播地址（FF00::/8）：</strong>用于一对多通信</p>
            </div>

            <div class="address-demo">
                <h4>🔗 链路本地地址特点</h4>
                <div class="address-part">
                    <div class="address-label">前缀：</div>
                    <div class="address-value">FE80::/10（1111 1110 10）</div>
                </div>
                <div class="address-part">
                    <div class="address-label">作用域：</div>
                    <div class="address-value">仅限本地链路</div>
                </div>
                <div class="address-part">
                    <div class="address-label">配置：</div>
                    <div class="address-value">自动生成，无需手动配置</div>
                </div>
                <div class="address-part">
                    <div class="address-label">用途：</div>
                    <div class="address-value">邻居发现、路由器发现</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⚡ 无状态自动配置过程</h2>

            <div class="step">
                <h3>🔄 完整配置流程</h3>
                <ol>
                    <li><strong>生成链路本地地址：</strong>使用MAC地址和FE80::/10前缀</li>
                    <li><strong>重复地址检测（DAD）：</strong>确保地址在链路上唯一</li>
                    <li><strong>路由器发现：</strong>发送路由器请求消息</li>
                    <li><strong>接收路由器通告：</strong>获取网络前缀信息</li>
                    <li><strong>生成全球地址：</strong>使用网络前缀+接口标识符</li>
                </ol>
            </div>

            <div class="concept-box">
                <h4>🛡️ 重复地址检测（DAD）</h4>
                <p>主机生成链路本地地址后，会发送邻居请求消息检测地址是否已被使用。如果没有收到响应，则认为地址可用。</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "IPv6自动配，MAC地址是关键，<br>
                    FE80做前缀，EUI-64来转换"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>链路本地地址：</strong>"FE80"、"1111 1110 10"、"本地链路"</p>
                <p><strong>MAC地址：</strong>"48位"、"硬件地址"、"网卡标识"</p>
                <p><strong>EUI-64：</strong>"插入FFFE"、"翻转第7位"、"64位标识符"</p>
                <p><strong>自动配置：</strong>"无状态"、"自动生成"、"无需配置"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"无状态自动配置"、"链路本地地址" → <span class="highlight">MAC地址</span></li>
                    <li>看到"FE80"、"1111 1110 10" → <span class="highlight">链路本地地址前缀</span></li>
                    <li>看到"接口标识符"、"EUI-64" → <span class="highlight">从MAC地址生成</span></li>
                    <li>记住：<span class="highlight">IPv6自动配置的核心是MAC地址</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">链路本地地址</span>：前缀FE80::/10，用于本地链路通信</li>
                    <li><span class="highlight">MAC地址转换</span>：通过EUI-64格式生成接口标识符</li>
                    <li><span class="highlight">无状态配置</span>：主机自动生成IPv6地址，无需服务器</li>
                    <li><span class="highlight">地址唯一性</span>：MAC地址的全球唯一性保证IPv6地址唯一</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>企业网络中设备的自动IPv6配置</li>
                    <li>家庭网络设备的即插即用</li>
                    <li>物联网设备的自动网络接入</li>
                    <li>移动设备的快速网络连接</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('configCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：在IPv6无状态自动配置中，主机将其MAC地址通过EUI-64格式转换后，附加在链路本地地址前缀FE80::/10之后，生成链路本地地址。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'A':
                                hint = 'IPv4地址与IPv6地址生成无关，IPv6有自己的地址生成机制。';
                                break;
                            case 'C':
                                hint = '主机名是人类可读的标识符，不用于IPv6地址的自动生成。';
                                break;
                            case 'D':
                                hint = '虽然有些情况下会使用随机字符串，但标准的无状态自动配置使用MAC地址。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：IPv6无状态自动配置使用MAC地址生成接口标识符！');
                    }, 500);
                }
            });
        });

        // 绘制网络设备
        function drawDevice(x, y, label, type = 'computer') {
            ctx.save();

            // 设备主体
            if (type === 'computer') {
                ctx.fillStyle = '#3498db';
                ctx.fillRect(x - 30, y - 20, 60, 40);
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 30, y - 20, 60, 40);

                // 屏幕
                ctx.fillStyle = '#fff';
                ctx.fillRect(x - 25, y - 15, 50, 30);
            }

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);

            ctx.restore();
        }

        // 绘制MAC地址
        function drawMACAddress(x, y, mac, highlight = false) {
            ctx.save();

            const parts = mac.split(':');
            const partWidth = 40;
            const startX = x - (parts.length * partWidth) / 2;

            parts.forEach((part, index) => {
                const partX = startX + index * partWidth;

                // 背景
                ctx.fillStyle = highlight ? '#00d4aa' : '#74b9ff';
                ctx.fillRect(partX, y - 15, partWidth - 2, 30);

                // 文字
                ctx.fillStyle = '#fff';
                ctx.font = '12px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText(part, partX + partWidth/2 - 1, y + 5);
            });

            ctx.restore();
        }

        // 绘制IPv6地址
        function drawIPv6Address(x, y, address, title) {
            ctx.save();

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x, y - 20);

            // 地址背景
            ctx.fillStyle = '#00d4aa';
            ctx.fillRect(x - 200, y, 400, 30);

            // 地址文字
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText(address, x, y + 20);

            ctx.restore();
        }

        // 绘制转换箭头
        function drawArrow(fromX, fromY, toX, toY, label = '') {
            ctx.save();

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;

            // 箭头线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const headLength = 15;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(
                toX - headLength * Math.cos(angle - Math.PI / 6),
                toY - headLength * Math.sin(angle - Math.PI / 6)
            );
            ctx.moveTo(toX, toY);
            ctx.lineTo(
                toX - headLength * Math.cos(angle + Math.PI / 6),
                toY - headLength * Math.sin(angle + Math.PI / 6)
            );
            ctx.stroke();

            // 标签
            if (label) {
                const midX = (fromX + toX) / 2;
                const midY = (fromY + toY) / 2;

                ctx.fillStyle = '#667eea';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(label, midX, midY - 10);
            }

            ctx.restore();
        }

        // 自动配置演示
        function startAutoConfigDemo() {
            currentDemo = 'autoconfig';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const configIndicator = document.getElementById('configIndicator');
            configIndicator.textContent = '🔄 IPv6无状态自动配置过程';

            animateAutoConfig();
        }

        function animateAutoConfig() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);

            // 绘制主机
            drawDevice(centerX, centerY - 100, '主机');

            // 步骤1：显示MAC地址
            if (step >= 0) {
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('步骤1：获取MAC地址', centerX, centerY - 50);

                drawMACAddress(centerX, centerY - 20, '00:1B:44:11:3A:B7', true);
            }

            // 步骤2：转换为EUI-64
            if (step >= 1) {
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('步骤2：转换为EUI-64格式', centerX, centerY + 30);

                drawArrow(centerX, centerY + 10, centerX, centerY + 50, 'EUI-64转换');

                // EUI-64地址
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(centerX - 150, centerY + 60, 300, 25);
                ctx.fillStyle = '#fff';
                ctx.font = '12px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText('021B:44FF:FE11:3AB7', centerX, centerY + 77);
            }

            // 步骤3：生成链路本地地址
            if (step >= 2) {
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('步骤3：生成链路本地地址', centerX, centerY + 110);

                drawArrow(centerX, centerY + 90, centerX, centerY + 130, '添加前缀');

                drawIPv6Address(centerX, centerY + 150, 'FE80::021B:44FF:FE11:3AB7', '链路本地地址');
            }

            // 完成
            if (step >= 3) {
                ctx.fillStyle = '#00d4aa';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('✅ IPv6链路本地地址自动配置完成！', centerX, centerY + 200);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateAutoConfig);
        }

        // MAC转EUI-64演示
        function showMACtoEUI64() {
            currentDemo = 'eui64';
            if (animationId) cancelAnimationFrame(animationId);

            const configIndicator = document.getElementById('configIndicator');
            configIndicator.textContent = '🔗 MAC地址转EUI-64详细过程';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('MAC地址转EUI-64过程', centerX, 50);

            // 原始MAC地址
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('原始MAC地址（48位）', centerX, 100);
            drawMACAddress(centerX, 120, '00:1B:44:11:3A:B7');

            // 分割
            ctx.fillText('分割为两部分', centerX, 170);

            // OUI部分
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(centerX - 150, 190, 120, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText('00:1B:44', centerX - 90, 210);

            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.fillText('OUI（厂商标识）', centerX - 90, 230);

            // NIC部分
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(centerX + 30, 190, 120, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText('11:3A:B7', centerX + 90, 210);

            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.fillText('NIC（网卡标识）', centerX + 90, 230);

            // 插入FFFE
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('在中间插入FFFE', centerX, 270);

            // 结果
            ctx.fillText('EUI-64结果（64位）', centerX, 320);

            // EUI-64地址分段显示
            const eui64Parts = ['00:1B:44', 'FF:FE', '11:3A:B7'];
            const colors = ['#fd79a8', '#00d4aa', '#74b9ff'];

            eui64Parts.forEach((part, index) => {
                const partX = centerX - 120 + index * 80;
                ctx.fillStyle = colors[index];
                ctx.fillRect(partX, 340, 75, 30);
                ctx.fillStyle = '#fff';
                ctx.font = '12px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText(part, partX + 37.5, 360);
            });

            // 最终格式
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('最终EUI-64格式', centerX, 400);

            ctx.fillStyle = '#00d4aa';
            ctx.fillRect(centerX - 150, 420, 300, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.fillText('021B:44FF:FE11:3AB7', centerX, 440);
        }

        // 地址生成过程演示
        function showAddressGeneration() {
            currentDemo = 'generation';
            if (animationId) cancelAnimationFrame(animationId);

            const configIndicator = document.getElementById('configIndicator');
            configIndicator.textContent = '🌐 完整IPv6地址生成过程';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('IPv6链路本地地址生成', centerX, 50);

            // 前缀部分
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('链路本地前缀（固定）', centerX - 150, 100);

            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(centerX - 250, 120, 200, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText('FE80:0000:0000:0000', centerX - 150, 140);

            // 加号
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillText('+', centerX, 135);

            // 接口标识符部分
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('接口标识符（从MAC生成）', centerX + 150, 100);

            ctx.fillStyle = '#00d4aa';
            ctx.fillRect(centerX + 50, 120, 200, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Courier New';
            ctx.textAlign = 'center';
            ctx.fillText('021B:44FF:FE11:3AB7', centerX + 150, 140);

            // 等号
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillText('=', centerX, 185);

            // 最终地址
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('完整链路本地地址', centerX, 220);

            drawIPv6Address(centerX, 250, 'FE80::021B:44FF:FE11:3AB7', '');

            // 说明
            ctx.fillStyle = '#667eea';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('注意：中间的零可以省略，用::表示', centerX, 300);
            ctx.fillText('这个地址只能在本地链路上使用', centerX, 320);
        }

        // 概念演示
        function showConceptDemo(concept) {
            currentDemo = concept;
            if (animationId) cancelAnimationFrame(animationId);

            const configIndicator = document.getElementById('configIndicator');
            const descriptions = {
                'prefix': '🏷️ 地址前缀：FE80::/10，标识链路本地地址',
                'mac': '🔗 MAC地址：48位硬件地址，网卡的唯一标识符',
                'eui64': '🔄 EUI-64转换：将48位MAC转换为64位接口标识符',
                'linklocal': '🌐 链路本地地址：用于同一链路上设备间的通信'
            };
            configIndicator.textContent = descriptions[concept];

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(descriptions[concept], centerX, centerY);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const configIndicator = document.getElementById('configIndicator');
            configIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看IPv6自动配置演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 IPv6无状态自动配置学习总结

✅ 正确答案：B - MAC地址

📚 核心概念：
• IPv6无状态自动配置使用MAC地址生成接口标识符
• 链路本地地址前缀为FE80::/10（1111 1110 10）
• 通过EUI-64格式将48位MAC转换为64位接口标识符

🌐 链路本地地址结构：
┌─────────────────┬─────────────────────────┐
│   前缀部分      │     接口标识符部分        │
│  FE80::/10     │   从MAC地址生成          │
│ (固定前缀)      │   (EUI-64格式)          │
└─────────────────┴─────────────────────────┘

🔧 EUI-64转换过程：
1️⃣ 获取MAC地址：00:1B:44:11:3A:B7
2️⃣ 分割为两部分：00:1B:44 | 11:3A:B7
3️⃣ 插入FFFE：00:1B:44:FF:FE:11:3A:B7
4️⃣ 翻转第7位：00→02
5️⃣ 最终结果：021B:44FF:FE11:3AB7

🔍 选项分析：
✅ B. MAC地址 - 正确
   标准的无状态自动配置使用MAC地址

❌ A. IPv4地址 - 错误
   IPv6有独立的地址生成机制

❌ C. 主机名 - 错误
   主机名是文本标识符，不用于地址生成

❌ D. 随机字符串 - 错误
   虽然隐私扩展使用随机值，但标准流程用MAC

🌐 完整配置流程：
1. 生成链路本地地址（MAC + FE80前缀）
2. 重复地址检测（DAD）
3. 路由器发现
4. 接收路由器通告
5. 生成全球单播地址

🧠 记忆技巧：
• "IPv6自动配，MAC地址是关键"
• "FE80做前缀，EUI-64来转换"
• 关键词：FE80、MAC地址、EUI-64、无状态

⚡ 考试要点：
• 看到"无状态自动配置" → MAC地址
• 看到"1111 1110 10"或"FE80" → 链路本地地址
• 看到"接口标识符" → 从MAC地址生成
• 记住EUI-64转换的关键步骤

🔑 关键理解：
IPv6无状态自动配置的核心是利用MAC地址的全球
唯一性来生成IPv6地址的接口标识符部分，这样
既保证了地址的唯一性，又实现了真正的自动配置。

🎉 恭喜掌握IPv6无状态自动配置知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到IPv6学习世界！</h3>
                    <p>让我们一起探索IPv6无状态自动配置的奥秘</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
