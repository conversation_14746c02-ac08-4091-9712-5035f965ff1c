<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机存储器互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 0.8s ease-out;
        }

        .question-text {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        #memoryCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            border-left: 5px solid #2196f3;
        }

        .memory-type {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-type:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }

        .memory-name {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .memory-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🧠 计算机存储器学习之旅</h1>
            <p class="subtitle">通过互动动画，轻松掌握存储器知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <h2 class="question-text">💡 题目回顾：计算机系统的主存主要是由（ ）构成的？</h2>
            <div class="options" id="options">
                <div class="option" data-answer="A">
                    <h3>A. DRAM</h3>
                    <p>动态随机存取存储器</p>
                </div>
                <div class="option" data-answer="B">
                    <h3>B. SRAM</h3>
                    <p>静态随机存取存储器</p>
                </div>
                <div class="option" data-answer="C">
                    <h3>C. Cache</h3>
                    <p>高速缓存</p>
                </div>
                <div class="option" data-answer="D">
                    <h3>D. EEPROM</h3>
                    <p>电可擦可编程只读存储器</p>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎮 互动存储器模拟器</h3>
            <canvas id="memoryCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🚀 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn" onclick="showComparison()">📊 性能对比</button>
            </div>
        </div>

        <div class="explanation">
            <h2 style="color: #1976d2; margin-bottom: 30px;">📚 详细解析</h2>
            
            <div class="memory-type" onclick="animateMemoryType('dram')">
                <div class="memory-name">
                    <div class="memory-icon" style="background: #4CAF50; color: white;">💾</div>
                    DRAM - 动态随机存取存储器 ✅ 正确答案
                </div>
                <p><strong>特点：</strong>需要定期刷新，容量大，成本低，是主存的主要组成部分</p>
                <p><strong>用途：</strong>计算机主内存（RAM）</p>
                <p><strong>为什么是正确答案：</strong>DRAM因其大容量和相对低成本的特性，成为现代计算机主存储器的主要选择</p>
            </div>

            <div class="memory-type" onclick="animateMemoryType('sram')">
                <div class="memory-name">
                    <div class="memory-icon" style="background: #FF9800; color: white;">⚡</div>
                    SRAM - 静态随机存取存储器
                </div>
                <p><strong>特点：</strong>不需要刷新，速度快，但成本高，容量小</p>
                <p><strong>用途：</strong>CPU缓存、寄存器</p>
            </div>

            <div class="memory-type" onclick="animateMemoryType('cache')">
                <div class="memory-name">
                    <div class="memory-icon" style="background: #2196F3; color: white;">🏃</div>
                    Cache - 高速缓存
                </div>
                <p><strong>特点：</strong>位于CPU和主存之间，提高数据访问速度</p>
                <p><strong>用途：</strong>临时存储常用数据和指令</p>
            </div>

            <div class="memory-type" onclick="animateMemoryType('eeprom')">
                <div class="memory-name">
                    <div class="memory-icon" style="background: #9C27B0; color: white;">💿</div>
                    EEPROM - 电可擦可编程只读存储器
                </div>
                <p><strong>特点：</strong>非易失性存储，可电擦除和重编程</p>
                <p><strong>用途：</strong>BIOS、固件存储</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('memoryCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentStep = 0;
        let isAnimating = false;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 存储器数据
        const memoryTypes = {
            dram: { name: 'DRAM', color: '#4CAF50', speed: 60, capacity: 90, cost: 30, x: 100, y: 150 },
            sram: { name: 'SRAM', color: '#FF9800', speed: 95, capacity: 20, cost: 90, x: 300, y: 150 },
            cache: { name: 'Cache', color: '#2196F3', speed: 98, capacity: 10, cost: 95, x: 500, y: 150 },
            eeprom: { name: 'EEPROM', color: '#9C27B0', speed: 20, capacity: 40, cost: 60, x: 700, y: 150 }
        };

        // 绘制存储器块
        function drawMemoryBlock(type, highlight = false) {
            const mem = memoryTypes[type];
            const size = highlight ? 80 : 60;
            
            ctx.fillStyle = highlight ? mem.color : mem.color + '80';
            ctx.strokeStyle = mem.color;
            ctx.lineWidth = highlight ? 4 : 2;
            
            // 绘制存储器块
            ctx.fillRect(mem.x - size/2, mem.y - size/2, size, size);
            ctx.strokeRect(mem.x - size/2, mem.y - size/2, size, size);
            
            // 绘制标签
            ctx.fillStyle = '#333';
            ctx.font = highlight ? 'bold 16px Arial' : '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(mem.name, mem.x, mem.y + size/2 + 25);
            
            if (highlight) {
                // 绘制性能指标
                drawPerformanceBar(mem.x - 40, mem.y + size/2 + 40, 80, 8, mem.speed/100, '速度');
                drawPerformanceBar(mem.x - 40, mem.y + size/2 + 60, 80, 8, mem.capacity/100, '容量');
                drawPerformanceBar(mem.x - 40, mem.y + size/2 + 80, 80, 8, (100-mem.cost)/100, '性价比');
            }
        }

        // 绘制性能条
        function drawPerformanceBar(x, y, width, height, percentage, label) {
            ctx.fillStyle = '#e0e0e0';
            ctx.fillRect(x, y, width, height);
            
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(x, y, width * percentage, height);
            
            ctx.fillStyle = '#666';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(label, x, y - 5);
        }

        // 绘制数据流动画
        function drawDataFlow(progress) {
            const particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: 50 + (canvas.width - 100) * ((progress + i * 0.05) % 1),
                    y: 100 + Math.sin((progress + i * 0.1) * Math.PI * 2) * 20,
                    size: 3 + Math.sin((progress + i * 0.1) * Math.PI * 4) * 2
                });
            }
            
            particles.forEach(particle => {
                ctx.fillStyle = '#2196F3';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            });
        }

        // 主绘制函数
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            // 绘制存储器块
            Object.keys(memoryTypes).forEach(type => {
                drawMemoryBlock(type, false);
            });
            
            if (isAnimating) {
                drawDataFlow(currentStep * 0.02);
            }
        }

        // 动画函数
        function animate() {
            if (isAnimating) {
                currentStep++;
                draw();
                animationId = requestAnimationFrame(animate);
                
                // 更新进度条
                const progress = Math.min((currentStep / 500) * 100, 100);
                document.getElementById('progressFill').style.width = progress + '%';
            }
        }

        // 开始动画
        function startAnimation() {
            isAnimating = true;
            currentStep = 0;
            animate();
        }

        // 重置动画
        function resetAnimation() {
            isAnimating = false;
            currentStep = 0;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            document.getElementById('progressFill').style.width = '0%';
            draw();
        }

        // 显示性能对比
        function showComparison() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制对比图表
            const types = Object.keys(memoryTypes);
            const barWidth = canvas.width / (types.length * 2);
            
            types.forEach((type, index) => {
                const mem = memoryTypes[type];
                const x = (index + 0.5) * barWidth * 2;
                
                // 速度条
                ctx.fillStyle = mem.color;
                ctx.fillRect(x - barWidth/3, canvas.height - mem.speed * 2, barWidth/3, mem.speed * 2);
                
                // 容量条
                ctx.fillStyle = mem.color + '80';
                ctx.fillRect(x, canvas.height - mem.capacity * 2, barWidth/3, mem.capacity * 2);
                
                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(mem.name, x, canvas.height - 10);
            });
            
            // 图例
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('蓝色=速度, 浅色=容量', canvas.width/2, 30);
        }

        // 选项点击处理
        document.getElementById('options').addEventListener('click', function(e) {
            const option = e.target.closest('.option');
            if (!option) return;
            
            const answer = option.dataset.answer;
            const options = document.querySelectorAll('.option');
            
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.dataset.answer === 'A') {
                    opt.classList.add('correct');
                } else if (opt === option && answer !== 'A') {
                    opt.classList.add('wrong');
                }
            });
            
            // 更新进度
            setTimeout(() => {
                document.getElementById('progressFill').style.width = '100%';
            }, 1000);
        });

        // 存储器类型动画
        function animateMemoryType(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            draw();
            drawMemoryBlock(type, true);
        }

        // 初始绘制
        draw();

        // 添加点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            Object.keys(memoryTypes).forEach(type => {
                const mem = memoryTypes[type];
                const distance = Math.sqrt((x - mem.x) ** 2 + (y - mem.y) ** 2);
                if (distance < 40) {
                    animateMemoryType(type);
                }
            });
        });
    </script>
</body>
</html>
