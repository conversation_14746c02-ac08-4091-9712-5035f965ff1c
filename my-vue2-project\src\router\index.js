import Vue from 'vue'
import VueRouter from 'vue-router'
import HomePage from '../views/Home.vue'
import NoteList from '../views/NoteList.vue'
import Multimodal from '../views/Multimodal.vue'
import ReadingHistory from '../views/ReadingHistory.vue'
import LearningMonitoring from '../views/LearningMonitoring.vue'
import FloatingWindow from '../views/FloatingWindow.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'HomePage',
    component: HomePage
  },
  {
    path: '/notes',
    name: 'NoteList',
    component: NoteList
  },
  {
    path: '/multimodal',
    name: 'Multimodal',
    component: Multimodal
  },
  {
    path: '/history',
    name: 'ReadingHistory',
    component: ReadingHistory
  },
  {
    path: '/learning-monitoring',
    name: 'LearningMonitoring',
    component: LearningMonitoring
  },
  {
    path: '/floating',
    name: 'FloatingWindow',
    component: FloatingWindow
  }
]

const router = new VueRouter({
  mode: 'hash', // 改为hash模式，避免Electron中的路由问题
  base: process.env.BASE_URL,
  routes
})

export default router 