<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux服务器安全配置 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.2);
            border-radius: 3px;
            margin: 40px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 3px;
        }

        .lesson-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .lesson-card.active {
            transform: translateY(0);
            opacity: 1;
        }

        .lesson-title {
            font-size: 2.2rem;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .lesson-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .lesson-content {
            line-height: 1.8;
            color: #4a5568;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .demo-area {
            background: #1a202c;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .terminal {
            background: #2d3748;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', monospace;
            color: #68d391;
            font-size: 14px;
            line-height: 1.6;
            position: relative;
        }

        .terminal-header {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-red { background: #ff5f56; }
        .dot-yellow { background: #ffbd2e; }
        .dot-green { background: #27ca3f; }

        .command-line {
            opacity: 0;
            transform: translateX(-20px);
            animation: typeIn 0.8s ease-out forwards;
        }

        .interactive-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .interactive-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(79, 172, 254, 0.4);
        }

        .security-level {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }

        .level-indicator {
            width: 40px;
            height: 8px;
            border-radius: 4px;
            background: #e2e8f0;
            transition: all 0.5s ease;
        }

        .level-indicator.active {
            background: linear-gradient(90deg, #48bb78, #38a169);
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #2d3748;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
        }

        .quiz-option {
            background: white;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .quiz-option:hover {
            border-color: #4facfe;
            transform: translateX(5px);
        }

        .quiz-option.correct {
            background: #c6f6d5;
            border-color: #48bb78;
        }

        .quiz-option.wrong {
            background: #fed7d7;
            border-color: #f56565;
        }

        .canvas-demo {
            border-radius: 16px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .next-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            margin: 30px auto;
            display: block;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .next-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .completion-badge {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin: 10px 0;
            animation: bounce 2s infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes typeIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 0.8;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles"></div>
    
    <div class="container">
        <div class="header">
            <h1>🔐 Linux服务器安全配置</h1>
            <p>零基础学习服务器安全防护 - 交互式动画教程</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- 第一课：用户管理 -->
        <div class="lesson-card" id="lesson1">
            <div class="lesson-title">
                <div class="lesson-icon">👤</div>
                第一课：用户管理与权限控制
            </div>
            <div class="lesson-content">
                <p><strong>为什么要禁止root用户登录？</strong></p>
                <p>root用户拥有系统的最高权限，就像是房子的万能钥匙。如果黑客获得了root权限，就能完全控制你的服务器。</p>
                
                <div class="security-level">
                    <div class="level-indicator" id="security1"></div>
                    <div class="level-indicator" id="security2"></div>
                    <div class="level-indicator" id="security3"></div>
                    <div class="level-indicator" id="security4"></div>
                    <div class="level-indicator" id="security5"></div>
                    <span style="margin-left: 10px; color: #666;">安全等级</span>
                </div>
            </div>

            <div class="demo-area">
                <canvas class="canvas-demo" id="userCanvas" width="800" height="300"></canvas>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-dot dot-red"></div>
                        <div class="terminal-dot dot-yellow"></div>
                        <div class="terminal-dot dot-green"></div>
                    </div>
                    <div id="userCommands"></div>
                </div>
                <button class="interactive-button" onclick="demonstrateUserCreation()">🎮 演示创建普通用户</button>
                <button class="interactive-button" onclick="demonstrateRootDisable()">🔒 演示禁用root登录</button>
            </div>

            <div class="quiz-container">
                <div class="quiz-question">💡 小测试：为什么要创建普通用户而不是直接使用root？</div>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="checkAnswer(this, false)">A. 因为root用户名太难记</div>
                    <div class="quiz-option" onclick="checkAnswer(this, true)">B. 限制权限，降低安全风险</div>
                    <div class="quiz-option" onclick="checkAnswer(this, false)">C. 普通用户运行更快</div>
                </div>
            </div>

            <button class="next-button" onclick="nextLesson(2)">下一课：SSH端口配置 →</button>
        </div>

        <!-- 第二课：SSH端口配置 -->
        <div class="lesson-card" id="lesson2" style="display: none;">
            <div class="lesson-title">
                <div class="lesson-icon">🚪</div>
                第二课：更改SSH端口号
            </div>
            <div class="lesson-content">
                <p><strong>为什么要更改SSH端口？</strong></p>
                <p>默认的SSH端口是22，就像所有房子都用同一把锁。黑客会自动扫描22端口，更改端口就像换了一把特殊的锁。</p>
            </div>

            <div class="demo-area">
                <canvas class="canvas-demo" id="portCanvas" width="800" height="300"></canvas>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-dot dot-red"></div>
                        <div class="terminal-dot dot-yellow"></div>
                        <div class="terminal-dot dot-green"></div>
                    </div>
                    <div id="portCommands"></div>
                </div>
                <button class="interactive-button" onclick="demonstratePortChange()">🎮 演示端口修改</button>
                <button class="interactive-button" onclick="demonstratePortScan()">🔍 模拟端口扫描</button>
            </div>

            <button class="next-button" onclick="nextLesson(3)">下一课：密钥登录 →</button>
        </div>

        <!-- 第三课：密钥登录 -->
        <div class="lesson-card" id="lesson3" style="display: none;">
            <div class="lesson-title">
                <div class="lesson-icon">🔑</div>
                第三课：密钥登录配置
            </div>
            <div class="lesson-content">
                <p><strong>什么是密钥登录？</strong></p>
                <p>密钥登录就像是指纹锁，比密码更安全。即使别人知道你的用户名，没有对应的私钥文件也无法登录。</p>
            </div>

            <div class="demo-area">
                <canvas class="canvas-demo" id="keyCanvas" width="800" height="300"></canvas>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-dot dot-red"></div>
                        <div class="terminal-dot dot-yellow"></div>
                        <div class="terminal-dot dot-green"></div>
                    </div>
                    <div id="keyCommands"></div>
                </div>
                <button class="interactive-button" onclick="demonstrateKeyGeneration()">🎮 生成密钥对</button>
                <button class="interactive-button" onclick="demonstrateKeyLogin()">🔐 演示密钥登录</button>
            </div>

            <button class="next-button" onclick="nextLesson(4)">下一课：防火墙配置 →</button>
        </div>

        <!-- 第四课：防火墙 -->
        <div class="lesson-card" id="lesson4" style="display: none;">
            <div class="lesson-title">
                <div class="lesson-icon">🛡️</div>
                第四课：防火墙与安全防护
            </div>
            <div class="lesson-content">
                <p><strong>防火墙的作用</strong></p>
                <p>防火墙就像是门卫，只允许授权的访问通过，阻止恶意攻击。fail2ban可以自动封禁多次尝试登录失败的IP。</p>
            </div>

            <div class="demo-area">
                <canvas class="canvas-demo" id="firewallCanvas" width="800" height="300"></canvas>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-dot dot-red"></div>
                        <div class="terminal-dot dot-yellow"></div>
                        <div class="terminal-dot dot-green"></div>
                    </div>
                    <div id="firewallCommands"></div>
                </div>
                <button class="interactive-button" onclick="demonstrateFirewall()">🎮 配置防火墙</button>
                <button class="interactive-button" onclick="demonstrateFail2ban()">🚫 演示fail2ban</button>
            </div>

            <button class="next-button" onclick="nextLesson(5)">最后一课：Web安全 →</button>
        </div>

        <!-- 第五课：Web安全 -->
        <div class="lesson-card" id="lesson5" style="display: none;">
            <div class="lesson-title">
                <div class="lesson-icon">🌐</div>
                第五课：Web服务安全配置
            </div>
            <div class="lesson-content">
                <p><strong>Web服务安全要点</strong></p>
                <p>使用专门的www用户运行Web服务，配置nginx_waf防止SQL注入，限制文件权限，这些都是保护网站的重要措施。</p>
            </div>

            <div class="demo-area">
                <canvas class="canvas-demo" id="webCanvas" width="800" height="300"></canvas>
                <div class="terminal">
                    <div class="terminal-header">
                        <div class="terminal-dot dot-red"></div>
                        <div class="terminal-dot dot-yellow"></div>
                        <div class="terminal-dot dot-green"></div>
                    </div>
                    <div id="webCommands"></div>
                </div>
                <button class="interactive-button" onclick="demonstrateWebSecurity()">🎮 配置Web安全</button>
                <button class="interactive-button" onclick="demonstrateWAF()">🛡️ 演示WAF防护</button>
            </div>

            <div class="completion-badge">🎉 恭喜完成所有课程！</div>
        </div>
    </div>

    <script>
        let currentLesson = 1;
        let totalLessons = 5;

        // 创建浮动粒子效果
        function createParticles() {
            const container = document.querySelector('.floating-particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 6 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(particle);
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentLesson / totalLessons) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 显示下一课
        function nextLesson(lessonNumber) {
            document.getElementById(`lesson${currentLesson}`).style.display = 'none';
            currentLesson = lessonNumber;
            const nextLessonElement = document.getElementById(`lesson${lessonNumber}`);
            nextLessonElement.style.display = 'block';
            setTimeout(() => {
                nextLessonElement.classList.add('active');
            }, 100);
            updateProgress();
            updateSecurityLevel();
        }

        // 更新安全等级指示器
        function updateSecurityLevel() {
            for (let i = 1; i <= currentLesson; i++) {
                const indicator = document.getElementById(`security${i}`);
                if (indicator) {
                    indicator.classList.add('active');
                }
            }
        }

        // 检查答案
        function checkAnswer(element, isCorrect) {
            const options = element.parentNode.children;
            for (let option of options) {
                option.style.pointerEvents = 'none';
            }
            
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    alert('🎉 回答正确！你已经理解了这个概念。');
                }, 300);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    alert('❌ 回答错误，请重新思考。正确答案是：限制权限，降低安全风险。');
                }, 300);
            }
        }

        // 动画演示函数
        function typeCommand(containerId, command, delay = 50) {
            const container = document.getElementById(containerId);
            const commandDiv = document.createElement('div');
            commandDiv.className = 'command-line';
            commandDiv.innerHTML = '$ ';
            container.appendChild(commandDiv);
            
            let i = 0;
            const typeInterval = setInterval(() => {
                if (i < command.length) {
                    commandDiv.innerHTML += command[i];
                    i++;
                } else {
                    clearInterval(typeInterval);
                }
            }, delay);
        }

        // 用户创建演示
        function demonstrateUserCreation() {
            const canvas = document.getElementById('userCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制用户创建动画
            animateUserCreation(ctx);
            
            // 显示命令
            document.getElementById('userCommands').innerHTML = '';
            setTimeout(() => typeCommand('userCommands', 'sudo adduser newuser'), 500);
            setTimeout(() => typeCommand('userCommands', 'sudo usermod -aG sudo newuser'), 2000);
            setTimeout(() => typeCommand('userCommands', 'su - newuser'), 3500);
        }

        function animateUserCreation(ctx) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 800, 300);
                
                // 绘制服务器
                ctx.fillStyle = '#4a5568';
                ctx.fillRect(50, 100, 150, 100);
                ctx.fillStyle = '#68d391';
                ctx.fillRect(60, 110, 20, 20);
                ctx.fillText('服务器', 100, 250);
                
                // 绘制用户图标动画
                if (frame > 30) {
                    ctx.fillStyle = '#4facfe';
                    ctx.beginPath();
                    ctx.arc(350, 150, 30, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.fillStyle = 'white';
                    ctx.font = '20px Arial';
                    ctx.fillText('👤', 340, 160);
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText('普通用户', 310, 200);
                }
                
                // 绘制权限限制
                if (frame > 60) {
                    ctx.strokeStyle = '#f56565';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(250, 100);
                    ctx.lineTo(400, 100);
                    ctx.stroke();
                    ctx.fillStyle = '#f56565';
                    ctx.fillText('权限限制', 300, 90);
                }
                
                frame++;
                if (frame < 90) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 禁用root演示
        function demonstrateRootDisable() {
            document.getElementById('userCommands').innerHTML = '';
            setTimeout(() => typeCommand('userCommands', 'sudo nano /etc/ssh/sshd_config'), 500);
            setTimeout(() => typeCommand('userCommands', '# 修改: PermitRootLogin no'), 2000);
            setTimeout(() => typeCommand('userCommands', 'sudo systemctl restart sshd'), 3500);
        }

        // 端口修改演示
        function demonstratePortChange() {
            const canvas = document.getElementById('portCanvas');
            const ctx = canvas.getContext('2d');
            animatePortChange(ctx);
            
            document.getElementById('portCommands').innerHTML = '';
            setTimeout(() => typeCommand('portCommands', 'sudo nano /etc/ssh/sshd_config'), 500);
            setTimeout(() => typeCommand('portCommands', '# 修改: Port 2222'), 2000);
            setTimeout(() => typeCommand('portCommands', 'sudo systemctl restart sshd'), 3500);
        }

        function animatePortChange(ctx) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 800, 300);
                
                // 绘制端口门
                ctx.fillStyle = '#4a5568';
                ctx.fillRect(100, 50, 80, 120);
                ctx.fillStyle = '#f56565';
                ctx.fillText('端口 22', 110, 40);
                ctx.fillText('(危险)', 110, 190);
                
                if (frame > 60) {
                    ctx.fillStyle = '#48bb78';
                    ctx.fillRect(300, 50, 80, 120);
                    ctx.fillStyle = '#48bb78';
                    ctx.fillText('端口 2222', 300, 40);
                    ctx.fillText('(安全)', 310, 190);
                }
                
                // 绘制箭头
                if (frame > 30 && frame < 60) {
                    ctx.strokeStyle = '#4facfe';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(200, 110);
                    ctx.lineTo(280, 110);
                    ctx.stroke();
                    ctx.fillText('更改', 230, 100);
                }
                
                frame++;
                if (frame < 90) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 端口扫描演示
        function demonstratePortScan() {
            document.getElementById('portCommands').innerHTML = '';
            setTimeout(() => typeCommand('portCommands', '# 黑客扫描常见端口'), 500);
            setTimeout(() => typeCommand('portCommands', 'nmap -p 22 target-server'), 1500);
            setTimeout(() => typeCommand('portCommands', '# 22端口关闭，扫描失败'), 3000);
            setTimeout(() => typeCommand('portCommands', '# 自定义端口更安全！'), 4000);
        }

        // 密钥生成演示
        function demonstrateKeyGeneration() {
            const canvas = document.getElementById('keyCanvas');
            const ctx = canvas.getContext('2d');
            animateKeyGeneration(ctx);
            
            document.getElementById('keyCommands').innerHTML = '';
            setTimeout(() => typeCommand('keyCommands', 'ssh-keygen -t rsa -b 4096'), 500);
            setTimeout(() => typeCommand('keyCommands', 'ssh-copy-id user@server'), 2000);
            setTimeout(() => typeCommand('keyCommands', '# 密钥已安装到服务器'), 3500);
        }

        function animateKeyGeneration(ctx) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 800, 300);
                
                // 绘制密钥对
                if (frame > 20) {
                    ctx.fillStyle = '#4facfe';
                    ctx.fillRect(100, 100, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('🔑 私钥', 120, 135);
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText('(保密)', 120, 180);
                }
                
                if (frame > 40) {
                    ctx.fillStyle = '#48bb78';
                    ctx.fillRect(300, 100, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('🔓 公钥', 320, 135);
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText('(可分享)', 320, 180);
                }
                
                // 绘制连接线
                if (frame > 60) {
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(200, 130);
                    ctx.lineTo(300, 130);
                    ctx.stroke();
                    ctx.fillStyle = '#667eea';
                    ctx.fillText('配对', 240, 120);
                }
                
                frame++;
                if (frame < 90) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 密钥登录演示
        function demonstrateKeyLogin() {
            document.getElementById('keyCommands').innerHTML = '';
            setTimeout(() => typeCommand('keyCommands', 'ssh -i ~/.ssh/id_rsa user@server -p 2222'), 500);
            setTimeout(() => typeCommand('keyCommands', '# 无需输入密码，自动验证密钥'), 2500);
            setTimeout(() => typeCommand('keyCommands', 'Welcome to Ubuntu Server!'), 4000);
        }

        // 防火墙演示
        function demonstrateFirewall() {
            const canvas = document.getElementById('firewallCanvas');
            const ctx = canvas.getContext('2d');
            animateFirewall(ctx);
            
            document.getElementById('firewallCommands').innerHTML = '';
            setTimeout(() => typeCommand('firewallCommands', 'sudo ufw enable'), 500);
            setTimeout(() => typeCommand('firewallCommands', 'sudo ufw allow 2222/tcp'), 1500);
            setTimeout(() => typeCommand('firewallCommands', 'sudo ufw allow 80/tcp'), 2500);
            setTimeout(() => typeCommand('firewallCommands', 'sudo ufw allow 443/tcp'), 3500);
        }

        function animateFirewall(ctx) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 800, 300);
                
                // 绘制服务器
                ctx.fillStyle = '#4a5568';
                ctx.fillRect(500, 100, 100, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('🖥️ 服务器', 510, 250);
                
                // 绘制防火墙
                if (frame > 30) {
                    ctx.fillStyle = '#f56565';
                    ctx.fillRect(350, 80, 20, 140);
                    ctx.fillStyle = 'white';
                    ctx.fillText('🛡️', 345, 160);
                    ctx.fillStyle = '#f56565';
                    ctx.fillText('防火墙', 320, 250);
                }
                
                // 绘制攻击被阻止
                if (frame > 60) {
                    ctx.fillStyle = '#2d3748';
                    ctx.fillText('❌ 恶意请求', 50, 120);
                    ctx.strokeStyle = '#f56565';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(150, 120);
                    ctx.lineTo(340, 120);
                    ctx.stroke();
                    ctx.fillText('被阻止', 220, 110);
                }
                
                // 绘制正常请求通过
                if (frame > 90) {
                    ctx.fillStyle = '#48bb78';
                    ctx.fillText('✅ 正常请求', 50, 180);
                    ctx.strokeStyle = '#48bb78';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(150, 180);
                    ctx.lineTo(490, 180);
                    ctx.stroke();
                    ctx.fillText('允许通过', 280, 170);
                }
                
                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // fail2ban演示
        function demonstrateFail2ban() {
            document.getElementById('firewallCommands').innerHTML = '';
            setTimeout(() => typeCommand('firewallCommands', 'sudo apt install fail2ban'), 500);
            setTimeout(() => typeCommand('firewallCommands', 'sudo systemctl enable fail2ban'), 2000);
            setTimeout(() => typeCommand('firewallCommands', '# 自动封禁多次失败登录的IP'), 3500);
        }

        // Web安全演示
        function demonstrateWebSecurity() {
            const canvas = document.getElementById('webCanvas');
            const ctx = canvas.getContext('2d');
            animateWebSecurity(ctx);
            
            document.getElementById('webCommands').innerHTML = '';
            setTimeout(() => typeCommand('webCommands', 'sudo adduser www'), 500);
            setTimeout(() => typeCommand('webCommands', 'sudo chown -R www:www /var/www'), 2000);
            setTimeout(() => typeCommand('webCommands', 'sudo chmod 755 /var/www'), 3500);
        }

        function animateWebSecurity(ctx) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 800, 300);
                
                // 绘制Web服务器
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(300, 100, 120, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('🌐 Web服务器', 310, 145);
                ctx.fillStyle = '#2d3748';
                ctx.fillText('www用户运行', 310, 200);
                
                // 绘制文件权限
                if (frame > 40) {
                    ctx.fillStyle = '#48bb78';
                    ctx.fillRect(100, 120, 80, 40);
                    ctx.fillStyle = 'white';
                    ctx.fillText('📁 755', 120, 145);
                    ctx.fillStyle = '#48bb78';
                    ctx.fillText('安全权限', 100, 180);
                }
                
                // 绘制WAF保护
                if (frame > 70) {
                    ctx.fillStyle = '#f56565';
                    ctx.fillRect(500, 120, 80, 40);
                    ctx.fillStyle = 'white';
                    ctx.fillText('🛡️ WAF', 520, 145);
                    ctx.fillStyle = '#f56565';
                    ctx.fillText('SQL注入防护', 480, 180);
                }
                
                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // WAF演示
        function demonstrateWAF() {
            document.getElementById('webCommands').innerHTML = '';
            setTimeout(() => typeCommand('webCommands', '# 配置nginx WAF模块'), 500);
            setTimeout(() => typeCommand('webCommands', 'sudo nano /etc/nginx/nginx.conf'), 1500);
            setTimeout(() => typeCommand('webCommands', '# 添加SQL注入防护规则'), 3000);
            setTimeout(() => typeCommand('webCommands', 'sudo nginx -s reload'), 4000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            updateProgress();
            updateSecurityLevel();
            
            // 显示第一课
            setTimeout(() => {
                document.getElementById('lesson1').classList.add('active');
            }, 500);
        });
    </script>
</body>
</html>
