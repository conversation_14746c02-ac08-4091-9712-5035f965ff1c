<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export 动画演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(to bottom right, #f0f4f8, #d9e2ec);
            color: #333;
        }
        h1 {
            color: #005a9c;
            text-shadow: 1px 1px 2px #ccc;
        }
        canvas {
            border: 2px solid #005a9c;
            border-radius: 10px;
            background: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            text-align: center;
            max-width: 600px;
            line-height: 1.6;
        }
        button {
            margin-top: 15px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        button:hover {
            background-color: #005a9c;
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }
    </style>
</head>
<body>

    <h1>单词解析：export</h1>
    <canvas id="canvas" width="600" height="400"></canvas>
    <div id="explanation">
        大家好！今天我们来学习单词 "export"。这个词由前缀 "ex-" 和词根 "port" 构成。让我用一个动画故事来为你拆解它吧！
    </div>
    <button id="playBtn">开始动画</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const explanationDiv = document.getElementById('explanation');
        const playBtn = document.getElementById('playBtn');

        let stage = 0;

        // 动画元素
        const portArea = { x: 50, y: 150, width: 250, height: 200 };
        const ship = { x: 400, y: 250, width: 150, height: 80, cargo: [] };
        const goods = [
            { x: 80, y: 300, size: 20, color: '#ff8c00', state: 'in_port' },
            { x: 120, y: 300, size: 20, color: '#4682b4', state: 'in_port' },
            { x: 160, y: 300, size: 20, color: '#32cd32', state: 'in_port' }
        ];
        let crane = { x: 200, y: 150, armX: 200, armY: 250, holding: null };

        function drawText(text, x, y, size = 30, color = 'black') {
            ctx.fillStyle = color;
            ctx.font = `bold ${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function drawPort() {
            ctx.strokeStyle = '#8b4513';
            ctx.lineWidth = 4;
            ctx.strokeRect(portArea.x, portArea.y, portArea.width, portArea.height);
            drawText("Port (港口)", portArea.x + portArea.width / 2, portArea.y - 20, 25, '#8b4513');
        }

        function drawGoods() {
            goods.forEach(g => {
                 if (g.state !== 'on_crane') {
                    ctx.fillStyle = g.color;
                    ctx.fillRect(g.x, g.y, g.size, g.size);
                 }
            });
        }
        
        function drawShip() {
            ctx.fillStyle = '#696969';
            // 船体
            ctx.beginPath();
            ctx.moveTo(ship.x, ship.y);
            ctx.lineTo(ship.x + ship.width, ship.y);
            ctx.lineTo(ship.x + ship.width - 20, ship.y + ship.height);
            ctx.lineTo(ship.x + 20, ship.y + ship.height);
            ctx.closePath();
            ctx.fill();

            // 船舱
            ctx.fillStyle = '#d3d3d3';
            ctx.fillRect(ship.x + 30, ship.y - 30, 40, 30);
            
            // 绘制船上的货物
            ship.cargo.forEach(item => {
                ctx.fillStyle = item.color;
                ctx.fillRect(item.x, item.y, item.size, item.size);
            });
        }

        function drawCrane() {
            // base
            ctx.fillStyle = '#a9a9a9';
            ctx.fillRect(crane.x - 10, crane.y, 20, portArea.height);
            // arm
            ctx.lineWidth = 8;
            ctx.strokeStyle = '#a9a9a9';
            ctx.beginPath();
            ctx.moveTo(crane.x, crane.y);
            ctx.lineTo(crane.armX, crane.y);
            ctx.lineTo(crane.armX, crane.armY);
            ctx.stroke();

            // hook and held goods
            if(crane.holding) {
                ctx.fillStyle = crane.holding.color;
                ctx.fillRect(crane.armX - crane.holding.size/2, crane.armY, crane.holding.size, crane.holding.size);
            }
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawPort();
            drawGoods();
            drawShip();
            drawCrane();
        }

        function animate() {
            switch(stage) {
                case 1:
                    animatePortIntro();
                    break;
                case 2:
                    animateEx();
                    break;
                case 3:
                    animatePortRoot();
                    break;
                case 4:
                    animateExportAction();
                    break;
                case 5:
                    animateConclusion();
                    break;
            }
        }

        function updateExplanation(text) {
            explanationDiv.innerHTML = text;
        }

        function animatePortIntro() {
            drawScene();
            drawText("port", 300, 100, 50, '#005a9c');
            updateExplanation(`首先，我们看到词根 <b>port</b>，意思是"搬运"或"港口"。<br>画面里就是一个港口，有很多货物(goods)。`);
            playBtn.textContent = '下一步：前缀 ex-';
        }
        
        function animateEx() {
            drawScene();
            drawText("ex-", 150, 100, 50, '#d83b01');
            drawText("→", 340, 280, 50, '#d83b01');
            updateExplanation(`接下来是前缀 <b>ex-</b>，意思是"出，向外"。<br>一个指向外部的箭头出现了，表示动作的方向。`);
            playBtn.textContent = '下一步：词根 port';
        }
        
        function animatePortRoot() {
            drawScene();
            drawText("ex-", 150, 100, 50, '#d83b01');
            drawText("port", 450, 100, 50, '#005a9c');
            updateExplanation(`我们已经知道 <b>port</b> 是"搬运"的意思。<br>现在，我们要把这两部分结合起来。`);
            playBtn.textContent = '执行 "export"';
        }

        let currentGood = 0;
        let animationSubStep = 'pickup';
        let targetX, targetY;

        function animateExportAction() {
            playBtn.disabled = true;
            updateExplanation(`<b>ex- (向外)</b> + <b>port (搬运)</b> = <b>export (出口)</b><br>起重机(crane)开始把货物搬运出港口，装到船上。`);

            const good = goods[currentGood];
            
            let dx, dy, distance;

            const animationLoop = () => {
                drawScene();
                drawText("export", 300, 50, 60, '#0078d4');

                switch(animationSubStep) {
                    case 'pickup':
                        // 吊臂移动到货物上方
                        targetX = good.x + good.size / 2;
                        targetY = good.y;
                        dx = targetX - crane.armX;
                        dy = targetY - crane.armY;
                        if (Math.abs(dx) > 1 || Math.abs(dy) > 1) {
                            crane.armX += dx * 0.1;
                            crane.armY += dy * 0.1;
                        } else {
                            crane.holding = good;
                            good.state = 'on_crane';
                            animationSubStep = 'lift';
                        }
                        break;
                    case 'lift':
                        // 提起货物
                        targetY = portArea.y + 20;
                        dy = targetY - crane.armY;
                        if (Math.abs(dy) > 1) {
                            crane.armY += dy * 0.1;
                        } else {
                            animationSubStep = 'move_out';
                        }
                        break;
                    case 'move_out':
                        // 移出港口，到船的上方
                        targetX = ship.x + 50 + currentGood * 25;
                        dx = targetX - crane.armX;
                        if (Math.abs(dx) > 1) {
                            crane.armX += dx * 0.1;
                        } else {
                           animationSubStep = 'drop';
                        }
                        break;
                    case 'drop':
                        // 放到船上
                        targetY = ship.y - 20;
                        dy = targetY - crane.armY;
                         if (crane.armY < targetY -1) {
                            crane.armY += (targetY-crane.armY) * 0.1;
                        } else {
                           crane.holding.x = crane.armX - crane.holding.size/2;
                           crane.holding.y = crane.armY;
                           crane.holding.state = 'on_ship';
                           ship.cargo.push(crane.holding);
                           crane.holding = null;
                           animationSubStep = 'reset';
                        }
                        break;
                    case 'reset':
                        // 吊臂复位
                        targetX = crane.x;
                        targetY = crane.y + 100;
                        dx = targetX - crane.armX;
                        dy = targetY - crane.armY;
                        if (Math.abs(dx) > 1 || Math.abs(dy) > 1) {
                            crane.armX += dx * 0.1;
                            crane.armY += dy * 0.1;
                        } else {
                           currentGood++;
                           if (currentGood < goods.length) {
                               animationSubStep = 'pickup';
                           } else {
                               playBtn.disabled = false;
                               playBtn.textContent = '完成！';
                               stage++; // Move to conclusion stage trigger
                               return; // End loop for this good
                           }
                        }
                        break;
                }
                requestAnimationFrame(animationLoop);
            };
            
            requestAnimationFrame(animationLoop);
        }

        function animateConclusion() {
            playBtn.disabled = true;
            updateExplanation(`所有货物都已装船，准备"出口"！<br><b>Export</b> 的含义就是这么来的，你记住了吗？`);
            
            const sail = () => {
                ctx.clearRect(0,0,canvas.width,canvas.height);
                drawPort();
                drawGoods();
                drawCrane();
                ship.x += 1;
                drawShip();
                drawText("export = 出口", 300, 50, 60, '#0078d4');

                if (ship.x < canvas.width + 50) {
                    requestAnimationFrame(sail);
                } else {
                    playBtn.disabled = false;
                    playBtn.textContent = '重新开始';
                    stage = -1; // ready for reset
                }
            }
            sail();
        }


        playBtn.addEventListener('click', () => {
            stage++;
            if (stage > 5) {
                // Reset everything
                stage = 0;
                ship.x = 400;
                ship.cargo = [];
                currentGood = 0;
                animationSubStep = 'pickup';
                goods.forEach((g, i) => {
                    g.x = 80 + i * 40;
                    g.y = 300;
                    g.state = 'in_port';
                });
                crane = { x: 200, y: 150, armX: 200, armY: 250, holding: null };
                playBtn.textContent = '开始动画';
                updateExplanation('大家好！今天我们来学习单词 "export"。这个词由前缀 "ex-" 和词根 "port" 构成。让我用一个动画故事来为你拆解它吧！');
                ctx.clearRect(0,0,canvas.width,canvas.height);
                drawScene();
                return;
            }
            animate();
        });

        // Initial draw
        window.onload = () => {
            drawScene();
        };

    </script>

</body>
</html> 