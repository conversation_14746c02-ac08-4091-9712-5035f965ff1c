<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML类图关联关系 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .diagram-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .diagram-title {
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #umlCanvas {
            border: 2px solid #eee;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            animation: shake 0.5s ease-in-out;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out;
            display: none;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .concept-box {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .concept-box h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .concept-box p {
            color: #666;
            line-height: 1.5;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">UML类图关联关系</h1>
            <p class="subtitle">通过互动动画理解UML中的关联与导航</p>
        </div>

        <div class="diagram-section">
            <h2 class="diagram-title">🎯 UML类图可视化演示</h2>
            <div class="canvas-container">
                <canvas id="umlCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateAssociation()">🔄 演示关联关系</button>
                <button class="btn" onclick="showNavigation()">🧭 显示导航方向</button>
                <button class="btn" onclick="explainRoles()">🏷️ 解释角色名称</button>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                对于如下所示的UML类图，正确的描述是（ ）
            </div>
            
            <div class="options">
                <button class="option" onclick="selectAnswer(this, 'A')">
                    A. 类B的实例中包含了对类C的实例的引用
                </button>
                <button class="option" onclick="selectAnswer(this, 'B')">
                    B. 类A的实例中包含了对类B的实例的引用
                </button>
                <button class="option" onclick="selectAnswer(this, 'C')">
                    C. 类A的实例中包含了对类C的实例的引用
                </button>
                <button class="option" onclick="selectAnswer(this, 'D')">
                    D. 类B的实例中包含了对类A的实例的引用
                </button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>📚 知识解析</h3>
            <div id="explanationContent"></div>
        </div>

        <div class="concept-box">
            <h4>💡 关键概念</h4>
            <p><span class="highlight">关联(Association)</span>：表示两个类之间的结构关系，指明一个类的对象与另一个类的对象之间的联系。</p>
        </div>

        <div class="concept-box">
            <h4>🧭 导航方向</h4>
            <p><span class="highlight">单向箭头</span>：表示导航方向，从箭头起点的类可以访问箭头终点的类的实例。</p>
        </div>

        <div class="concept-box">
            <h4>🏷️ 角色名称</h4>
            <p><span class="highlight">角色名称</span>：标注在关联线一端，表示该端在关联中扮演的角色。</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('umlCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;

        function drawUMLDiagram() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制类A
            ctx.fillStyle = '#e8f4fd';
            ctx.fillRect(150, 150, 120, 80);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(150, 150, 120, 80);
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('A', 210, 195);
            
            // 绘制类B
            ctx.fillStyle = '#fff2e8';
            ctx.fillRect(450, 150, 120, 80);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(450, 150, 120, 80);
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('B', 510, 195);
            
            // 绘制关联线
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(270, 190);
            ctx.lineTo(450, 190);
            ctx.stroke();
            
            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(450, 190);
            ctx.lineTo(435, 185);
            ctx.moveTo(450, 190);
            ctx.lineTo(435, 195);
            ctx.stroke();
            
            // 绘制角色名称C
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('C', 430, 175);
        }

        function animateAssociation() {
            drawUMLDiagram();
            
            // 高亮关联线
            setTimeout(() => {
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 5;
                ctx.beginPath();
                ctx.moveTo(270, 190);
                ctx.lineTo(450, 190);
                ctx.stroke();
                
                // 重绘箭头
                ctx.beginPath();
                ctx.moveTo(450, 190);
                ctx.lineTo(435, 185);
                ctx.moveTo(450, 190);
                ctx.lineTo(435, 195);
                ctx.stroke();
            }, 500);
            
            // 显示说明文字
            setTimeout(() => {
                ctx.fillStyle = '#27ae60';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('关联关系', 360, 210);
            }, 1000);
        }

        function showNavigation() {
            drawUMLDiagram();
            
            // 高亮导航方向
            setTimeout(() => {
                // 从A到B的动画箭头
                animateNavigationArrow();
            }, 500);
        }

        function animateNavigationArrow() {
            let progress = 0;
            const animate = () => {
                if (progress <= 1) {
                    drawUMLDiagram();
                    
                    // 绘制动画箭头
                    const startX = 270;
                    const endX = 450;
                    const currentX = startX + (endX - startX) * progress;
                    
                    ctx.fillStyle = '#e74c3c';
                    ctx.beginPath();
                    ctx.arc(currentX, 190, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 添加说明文字
                    ctx.fillStyle = '#27ae60';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('A → B 导航方向', 360, 250);
                    
                    progress += 0.02;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function explainRoles() {
            drawUMLDiagram();
            
            // 高亮角色名称
            setTimeout(() => {
                ctx.fillStyle = '#f39c12';
                ctx.strokeStyle = '#f39c12';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(430, 175, 20, 0, 2 * Math.PI);
                ctx.stroke();
                
                ctx.fillStyle = '#f39c12';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('角色名称', 430, 140);
                ctx.fillText('(不是类名)', 430, 155);
            }, 500);
        }

        function selectAnswer(element, answer) {
            // 移除其他选项的状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            const explanationDiv = document.getElementById('explanation');
            const explanationContent = document.getElementById('explanationContent');
            
            if (answer === 'B') {
                element.classList.add('correct');
                explanationContent.innerHTML = `
                    <p><strong>🎉 恭喜答对！</strong></p>
                    <p><strong>正确解析：</strong></p>
                    <p>• UML类图中的<span class="highlight">单向箭头</span>表示导航方向：A → B</p>
                    <p>• 这意味着可以从类A的实例导航到类B的实例</p>
                    <p>• 因此，<span class="highlight">类A的实例中必须包含对类B实例的引用</span></p>
                    <p>• 图中的"C"是<span class="highlight">角色名称</span>，不是类名</p>
                    <p><strong>💡 记忆要点：</strong>箭头指向谁，就引用谁！</p>
                `;
            } else {
                element.classList.add('wrong');
                let wrongExplanation = '';
                switch(answer) {
                    case 'A':
                        wrongExplanation = '❌ 错误！"C"是角色名称，不是类名。箭头方向是A→B，所以A引用B。';
                        break;
                    case 'C':
                        wrongExplanation = '❌ 错误！"C"是角色名称，不是类名。关联是在A和B之间。';
                        break;
                    case 'D':
                        wrongExplanation = '❌ 错误！箭头方向是A→B，表示A引用B，而不是B引用A。';
                        break;
                }
                explanationContent.innerHTML = `
                    <p>${wrongExplanation}</p>
                    <p><strong>正确答案是B：</strong>类A的实例中包含了对类B的实例的引用</p>
                    <p><strong>关键理解：</strong></p>
                    <p>• 箭头方向 A → B 表示导航方向</p>
                    <p>• 从A可以访问到B，所以A中要有B的引用</p>
                    <p>• "C"是角色名称，标注在关联线的B端</p>
                `;
            }
            
            explanationDiv.style.display = 'block';
        }

        // 初始化
        drawUMLDiagram();
        
        // 欢迎动画
        setTimeout(() => {
            animateAssociation();
        }, 1500);
    </script>
</body>
</html>
