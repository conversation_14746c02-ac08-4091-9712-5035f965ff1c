<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据存储架构学习 - 互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .file-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .file-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .file-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .file-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .file-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 15px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .quiz-option.correct {
            background: #4CAF50;
            border-color: #45a049;
        }

        .quiz-option.wrong {
            background: #f44336;
            border-color: #da190b;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animated-element {
            animation: pulse 2s infinite;
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.7;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 数据存储架构学习</h1>
            <p>通过有趣的动画和交互，轻松掌握文件类型和数据库概念！</p>
        </div>

        <!-- 知识介绍部分 -->
        <div class="learning-section">
            <h2 class="section-title">📚 什么是数据存储？</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <h3>💡 核心概念</h3>
                <p>数据存储架构是系统设计中的重要环节。就像我们整理房间一样，数据也需要有序地存放在不同的"容器"中。主要有两种存储方式：<strong>文件</strong>和<strong>数据库</strong>。</p>
            </div>
        </div>

        <!-- 文件类型学习 -->
        <div class="learning-section">
            <h2 class="section-title">📁 文件类型大探索</h2>
            <div class="canvas-container">
                <canvas id="fileCanvas" width="800" height="500"></canvas>
            </div>
            <div class="file-types">
                <div class="file-card" data-type="master">
                    <div class="file-icon">🏢</div>
                    <h3>主文件 (Master Files)</h3>
                    <p>存储核心业务信息，如客户资料、订单信息等重要数据</p>
                </div>
                <div class="file-card" data-type="lookup">
                    <div class="file-icon">📋</div>
                    <h3>查找文件 (Look-up Files)</h3>
                    <p>包含静态值，如城市列表、有效代码等，用于数据验证</p>
                </div>
                <div class="file-card" data-type="transaction">
                    <div class="file-icon">💳</div>
                    <h3>事务文件 (Transaction Files)</h3>
                    <p>记录业务操作过程，如交易记录、操作日志等</p>
                </div>
                <div class="file-card" data-type="history">
                    <div class="file-icon">📜</div>
                    <h3>历史文件 (History Files)</h3>
                    <p>保存历史数据，用于分析和备份</p>
                </div>
            </div>
        </div>

        <!-- 数据库类型学习 -->
        <div class="learning-section">
            <h2 class="section-title">🗄️ 数据库世界</h2>
            <div class="canvas-container">
                <canvas id="databaseCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <h3>🔗 数据库类型</h3>
                <p><strong>传统数据库</strong>：基于较老技术，很少用于新应用开发</p>
                <p><strong>关系数据库</strong>：通过主键和外键建立表之间的关系，确保数据同步和有效性</p>
            </div>
        </div>

        <!-- 测验部分 -->
        <div class="learning-section quiz-section">
            <h2 class="section-title">🎯 知识测验</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="quiz-question">
                根据题目描述，哪种文件类型存储核心业务信息，如订单信息或客户邮寄信息？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" data-answer="A">
                    <h3>A. Master files</h3>
                    <p>主文件 - 存储核心业务信息</p>
                </div>
                <div class="quiz-option" data-answer="B">
                    <h3>B. Look-up files</h3>
                    <p>查找文件 - 包含静态验证值</p>
                </div>
                <div class="quiz-option" data-answer="C">
                    <h3>C. Transaction files</h3>
                    <p>事务文件 - 记录操作过程</p>
                </div>
                <div class="quiz-option" data-answer="D">
                    <h3>D. History files</h3>
                    <p>历史文件 - 保存历史数据</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 动画和交互逻辑
        class DataStorageLearning {
            constructor() {
                this.initCanvases();
                this.initInteractions();
                this.startAnimations();
            }

            initCanvases() {
                // 初始化介绍画布
                this.introCanvas = document.getElementById('introCanvas');
                this.introCtx = this.introCanvas.getContext('2d');
                
                // 初始化文件画布
                this.fileCanvas = document.getElementById('fileCanvas');
                this.fileCtx = this.fileCanvas.getContext('2d');
                
                // 初始化数据库画布
                this.databaseCanvas = document.getElementById('databaseCanvas');
                this.databaseCtx = this.databaseCanvas.getContext('2d');
                
                this.animationFrame = 0;
            }

            initInteractions() {
                // 文件卡片交互
                const fileCards = document.querySelectorAll('.file-card');
                fileCards.forEach(card => {
                    card.addEventListener('click', () => {
                        fileCards.forEach(c => c.classList.remove('active'));
                        card.classList.add('active');
                        this.animateFileType(card.dataset.type);
                    });
                });

                // 测验交互
                const quizOptions = document.querySelectorAll('.quiz-option');
                quizOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        this.handleQuizAnswer(option);
                    });
                });
            }

            startAnimations() {
                this.animate();
            }

            animate() {
                this.animationFrame++;
                this.drawIntroAnimation();
                this.drawFileAnimation();
                this.drawDatabaseAnimation();
                requestAnimationFrame(() => this.animate());
            }

            drawIntroAnimation() {
                const ctx = this.introCtx;
                const canvas = this.introCanvas;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数据流动动画
                const time = this.animationFrame * 0.02;
                
                // 文件图标
                ctx.fillStyle = '#667eea';
                ctx.font = '60px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📁', 200, 200);
                
                // 箭头动画
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(250, 180);
                ctx.lineTo(350 + Math.sin(time) * 10, 180);
                ctx.stroke();
                
                // 数据库图标
                ctx.fillText('🗄️', 600, 200);
                
                // 数据粒子动画
                for (let i = 0; i < 5; i++) {
                    const x = 300 + i * 60 + Math.sin(time + i) * 20;
                    const y = 160 + Math.cos(time + i) * 10;
                    ctx.fillStyle = `hsl(${240 + i * 20}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 5, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // 标题
                ctx.fillStyle = '#333';
                ctx.font = '24px Arial';
                ctx.fillText('数据在文件和数据库之间流动', 400, 300);
            }

            drawFileAnimation() {
                const ctx = this.fileCtx;
                const canvas = this.fileCanvas;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const time = this.animationFrame * 0.03;
                
                // 绘制四种文件类型
                const fileTypes = [
                    { icon: '🏢', name: 'Master Files', x: 150, color: '#4CAF50' },
                    { icon: '📋', name: 'Look-up Files', x: 300, color: '#2196F3' },
                    { icon: '💳', name: 'Transaction Files', x: 450, color: '#FF9800' },
                    { icon: '📜', name: 'History Files', x: 600, color: '#9C27B0' }
                ];
                
                fileTypes.forEach((file, index) => {
                    const y = 200 + Math.sin(time + index) * 20;
                    
                    // 文件图标
                    ctx.font = '50px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(file.icon, file.x, y);
                    
                    // 文件名
                    ctx.fillStyle = file.color;
                    ctx.font = '16px Arial';
                    ctx.fillText(file.name, file.x, y + 50);
                    
                    // 连接线动画
                    if (index < fileTypes.length - 1) {
                        ctx.strokeStyle = file.color;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(file.x + 30, y);
                        ctx.lineTo(fileTypes[index + 1].x - 30, y);
                        ctx.stroke();
                    }
                });
                
                // 中心说明
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('点击下方卡片了解每种文件类型', 400, 350);
            }

            drawDatabaseAnimation() {
                const ctx = this.databaseCtx;
                const canvas = this.databaseCanvas;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const time = this.animationFrame * 0.02;
                
                // 传统数据库
                ctx.fillStyle = '#757575';
                ctx.font = '40px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🗃️', 250, 150);
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.fillText('传统数据库', 250, 200);
                ctx.fillText('(Legacy Database)', 250, 220);
                
                // 关系数据库
                ctx.fillStyle = '#4CAF50';
                ctx.font = '40px Arial';
                ctx.fillText('🗄️', 550, 150);
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.fillText('关系数据库', 550, 200);
                ctx.fillText('(Relational Database)', 550, 220);
                
                // 连接动画
                ctx.strokeStyle = '#2196F3';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(300, 130);
                ctx.lineTo(500, 130);
                ctx.stroke();
                
                // 箭头
                ctx.fillStyle = '#2196F3';
                ctx.beginPath();
                ctx.moveTo(490, 125);
                ctx.lineTo(500, 130);
                ctx.lineTo(490, 135);
                ctx.fill();
                
                // 演进说明
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('数据库技术演进', 400, 100);
            }

            animateFileType(type) {
                // 根据选择的文件类型播放特定动画
                console.log(`Animating file type: ${type}`);
            }

            handleQuizAnswer(selectedOption) {
                const correctAnswer = 'A';
                const userAnswer = selectedOption.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 移除之前的状态
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (userAnswer === correctAnswer) {
                    selectedOption.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！Master Files 确实存储核心业务信息！');
                    }, 500);
                } else {
                    selectedOption.classList.add('wrong');
                    document.querySelector(`[data-answer="${correctAnswer}"]`).classList.add('correct');
                    setTimeout(() => {
                        alert('💡 答案是 A. Master Files。它们存储对业务最重要的核心信息！');
                    }, 500);
                }
            }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            new DataStorageLearning();
        });
    </script>
</body>
</html>
