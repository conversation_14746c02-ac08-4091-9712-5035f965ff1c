<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABSD方法 交互式解释</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            padding: 30px;
            text-align: center;
        }
        h1 {
            color: #1a73e8;
            font-size: 2em;
            margin-bottom: 10px;
        }
        h2 {
            color: #3c4043;
            border-bottom: 2px solid #e8eaed;
            padding-bottom: 10px;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .question-box {
            background-color: #f8f9fa;
            border: 1px solid #e8eaed;
            border-radius: 8px;
            padding: 20px;
            text-align: left;
            margin-bottom: 20px;
            font-size: 1.1em;
            line-height: 1.6;
        }
        .question-box code {
            background-color: #e8eaed;
            padding: 2px 5px;
            border-radius: 4px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        }
        #absd-canvas {
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            cursor: pointer;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .btn {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .btn:hover {
            background-color: #1558b8;
            transform: translateY(-2px);
        }
        .btn:active {
            transform: translateY(0);
        }
        .btn.secondary {
            background-color: #5f6368;
        }
        .btn.secondary:hover {
            background-color: #3c4043;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            line-height: 1.8;
            font-size: 1.1em;
        }
        .explanation .concept {
            background: #e3f2fd;
            border-left: 5px solid #1a73e8;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 8px 8px 0;
        }
        .explanation .concept strong {
            color: #1558b8;
            font-size: 1.2em;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>轻松理解ABSD方法</h1>
    <p>通过动画和交互，掌握基于架构的软件设计核心思想</p>

    <div class="question-box">
        <strong>原题回顾:</strong>
        <p>某公司采用基于架构的软件设计 (Architecture-Based Software Design, ABSD) 方法进行软件设计与开发。ABSD方法有三个基础...主要包括架构需求等6个主要活动，其中 <code>( C.架构复审 )</code> 活动的目标是标识潜在的风险, 及早发现架构设计中的缺陷和错误；<code>( D.架构演化 )</code> 活动针对用户的需求变化, 修改应用架构, 满足新的需求。</p>
    </div>

    <canvas id="absd-canvas" width="840" height="400"></canvas>

    <div class="controls">
        <button class="btn" id="reviewBtn">演示：架构复审 (发现问题)</button>
        <button class="btn" id="evolveBtn">演示：架构演化 (响应变化)</button>
        <button class="btn secondary" id="resetBtn">重置动画</button>
    </div>

    <div class="explanation">
        <h2>知识点解析</h2>
        <p>我们可以把软件开发想象成盖一座房子，ABSD就是一套科学的盖房子的方法论。</p>
        <div class="concept">
            <strong>🏠 架构设计 (Architecture Design)</strong><br>
            这就是房子的"设计蓝图"。它定义了房子的整体结构、有多少房间、水电管线怎么走等等。这是整个工程的基础。
        </div>
        <div class="concept">
            <strong>🔍 架构复审 (Architecture Review)</strong><br>
            <strong>这个就像是"图纸会审"。</strong>在动工之前，找来结构工程师、水电专家、消防员一起看蓝图，提前找出设计不合理的地方（比如承重墙位置不对、插座太少），避免建好了再返工，省时省力。我们的动画演示的就是这个过程。
        </div>
        <div class="concept">
            <strong>🌱 架构演化 (Architecture Evolution)</strong><br>
            <strong>这个就像是"房屋改造或扩建"。</strong>房子住几年后，家庭成员增加了，你可能想加盖一层或者把书房改成儿童房。软件也一样，用户的需求会变，市场会变，就需要对原来的"蓝图"进行修改升级，让它能适应新情况。
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('absd-canvas');
    const ctx = canvas.getContext('2d');
    const reviewBtn = document.getElementById('reviewBtn');
    const evolveBtn = document.getElementById('evolveBtn');
    const resetBtn = document.getElementById('resetBtn');

    const boxWidth = 120;
    const boxHeight = 50;
    const gap = 40;

    const stages = [
        { name: '需求', x: 50, y: 175, color: '#66bb6a' },
        { name: '架构设计', x: 220, y: 175, color: '#42a5f5' },
        { name: '架构复审', x: 390, y: 175, color: '#ffa726' },
        { name: '实现', x: 560, y: 175, color: '#ef5350' },
        { name: '演化', x: 730, y: 175, color: '#ab47bc' }
    ];

    let animationState = {
        type: 'none', // 'review', 'evolve'
        progress: 0,
        lastTime: 0
    };

    function drawBox(box) {
        ctx.fillStyle = box.color;
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(box.x, box.y, boxWidth, boxHeight, [8]);
        ctx.fill();
        ctx.stroke();

        ctx.fillStyle = 'white';
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(box.name, box.x + boxWidth / 2, box.y + boxHeight / 2);
    }

    function drawArrow(from, to, dashed = false, curved = false) {
        ctx.strokeStyle = '#555';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        const startX = from.x + boxWidth;
        const startY = from.y + boxHeight / 2;
        const endX = to.x;
        const endY = to.y + boxHeight / 2;

        if (curved) {
            const cp1x = startX + gap / 2;
            const cp1y = startY - 80;
            const cp2x = endX - gap / 2;
            const cp2y = endY - 80;
            ctx.moveTo(startX, startY);
            ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, endX, endY);
        } else {
             ctx.moveTo(startX, startY);
             ctx.lineTo(endX, endY);
        }

        if (dashed) {
            ctx.setLineDash([8, 6]);
        }
        ctx.stroke();
        ctx.setLineDash([]); // Reset

        // Arrowhead
        ctx.fillStyle = '#555';
        const angle = Math.atan2(endY - (curved ? to.y - 80 : startY), endX - (curved ? endX - gap/2 : startX));
        ctx.save();
        ctx.translate(endX, endY);
        if (curved) {
             const angle_curve = Math.atan2(endY - cp2y, endX - cp2x);
             ctx.rotate(angle_curve);
        } else {
             ctx.rotate(angle);
        }
        ctx.moveTo(0, 0);
        ctx.lineTo(-10, -5);
        ctx.lineTo(-10, 5);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
    }

    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        stages.forEach(drawBox);
        for (let i = 0; i < stages.length - 1; i++) {
             // Don't draw arrow from implementation to evolution initially
            if (stages[i].name !== '实现') {
                drawArrow(stages[i], stages[i + 1]);
            }
        }
    }
    
    function animateReview(timestamp) {
        if (!animationState.lastTime) animationState.lastTime = timestamp;
        const elapsed = timestamp - animationState.lastTime;
        animationState.lastTime = timestamp;
        animationState.progress += elapsed * 0.001;
        
        drawInitialState();

        const reviewBox = stages[2];
        const designBox = stages[1];

        // Magnifying glass
        const angle = animationState.progress * Math.PI * 2;
        const glassX = designBox.x + boxWidth / 2 + Math.cos(angle) * 40;
        const glassY = designBox.y + boxHeight / 2 + Math.sin(angle) * 40;

        ctx.strokeStyle = '#0277bd';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.arc(glassX, glassY, 20, 0, Math.PI * 2);
        ctx.moveTo(glassX + 14, glassY + 14);
        ctx.lineTo(glassX + 28, glassY + 28);
        ctx.stroke();
        
        // Show findings
        if (animationState.progress > 0.5) {
            ctx.fillStyle = 'red';
            ctx.font = 'italic bold 15px sans-serif';
            ctx.fillText('发现风险!', designBox.x + boxWidth / 2, designBox.y - 20);
        }
        if (animationState.progress > 1.0) {
            ctx.fillStyle = 'orange';
            ctx.fillText('存在缺陷!', designBox.x + boxWidth / 2, designBox.y + boxHeight + 25);
        }
        
        // Return arrow
        if (animationState.progress > 1.5) {
            drawArrow(reviewBox, designBox, true, true);
            ctx.fillStyle = '#d32f2f';
            ctx.font = '14px sans-serif';
            ctx.fillText('打回修改', reviewBox.x - 60, reviewBox.y - 40);
        }

        if (animationState.progress < 2.5) {
            requestAnimationFrame(animateReview);
        }
    }

    function animateEvolve(timestamp) {
        if (!animationState.lastTime) animationState.lastTime = timestamp;
        const elapsed = timestamp - animationState.lastTime;
        animationState.lastTime = timestamp;
        animationState.progress += elapsed * 0.0008;

        drawInitialState();
        
        const evolveBox = stages[4];
        
        const startX = evolveBox.x + boxWidth / 2;
        const startY = 30;
        
        // New requirement coming in
        const reqY = startY + animationState.progress * 120;
        
        ctx.fillStyle = '#7b1fa2';
        ctx.beginPath();
        ctx.roundRect(startX - 50, reqY, 100, 40, [8]);
        ctx.fill();
        ctx.fillStyle = 'white';
        ctx.font = '14px sans-serif';
        ctx.fillText('新需求!', startX, reqY + 20);

        if (reqY > evolveBox.y - 20) {
            // Morph the box
            const pulse = Math.sin(animationState.progress * 10) * 5;
            const morphedBox = {
                ...evolveBox,
                x: evolveBox.x - pulse / 2,
                y: evolveBox.y - pulse / 2,
                color: '#7b1fa2'
            };
            drawBox({ ...morphedBox, width: boxWidth + pulse, height: boxHeight + pulse });

            // Show text
            ctx.fillStyle = '#7b1fa2';
            ctx.font = 'italic bold 15px sans-serif';
            ctx.fillText('架构升级中...', evolveBox.x + boxWidth/2, evolveBox.y + boxHeight + 25);
        }

        if (animationState.progress < 2.5) {
            requestAnimationFrame(animateEvolve);
        } else {
             // Final state for evolution
             drawInitialState();
             drawBox({ ...evolveBox, name:'V2.0 架构', color: '#7b1fa2' });
        }
    }

    function startAnimation(type) {
        if(animationState.type !== 'none' && animationState.progress < 2.5) return; // Prevent re-triggering
        animationState = { type, progress: 0, lastTime: 0 };
        if (type === 'review') {
            requestAnimationFrame(animateReview);
        } else if (type === 'evolve') {
            requestAnimationFrame(animateEvolve);
        }
    }
    
    reviewBtn.addEventListener('click', () => startAnimation('review'));
    evolveBtn.addEventListener('click', () => startAnimation('evolve'));
    resetBtn.addEventListener('click', () => {
        animationState.type = 'none';
        animationState.progress = 100; // Stop any running animation
        drawInitialState();
    });

    drawInitialState();
});
</script>
</body>
</html> 