<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象系统单元测试学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 30px auto;
            background-color: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e6ea;
            padding-bottom: 10px;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .interactive-section {
            margin-bottom: 35px;
            padding: 25px;
            border-radius: 8px;
            background-color: #fcfcfc;
            border: 1px solid #e9ecef;
        }
        .question-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #0056b3;
        }
        .options div {
            margin-bottom: 12px;
            padding: 10px 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .options div:hover {
            background-color: #e6f7ff;
            border-color: #9cd7ff;
        }
        .options div.selected {
            background-color: #d0eaff;
            border-color: #007bff;
            font-weight: bold;
        }
        .correct-answer-display {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ffe9;
            border: 1px solid #00c853;
            color: #005a2b;
            border-radius: 6px;
            font-weight: bold;
        }
        .discrepancy-note {
            margin-top: 15px;
            padding: 15px;
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            border-radius: 6px;
            font-weight: bold;
        }
        .explanation-point {
            margin-bottom: 20px;
            padding-left: 15px;
            border-left: 4px solid #4a90e2;
        }
        .explanation-point h3 {
            margin-top: 0;
            font-size: 1.3em;
            color: #4a90e2;
            border-bottom: none;
            padding-bottom: 0;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            display: block;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.05);
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        .controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 8px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象系统单元测试学习页面</h1>

        <div class="question-section">
            <div class="question-title">
                题目：面向对象系统的单元测试包括方法层次的测试、类层次的测试和类树层次的测试。在常见的测试技术中，(<span id="blank1"></span>) 属于方法层次的测试，(<span id="blank2"></span>) 属于类层次的测试。
            </div>
            <div class="options">
                <div data-value="A" data-method-level="等价类划分测试" data-class-level="多态消息测试">A. 等价类划分测试和多态消息测试</div>
                <div data-value="B" data-method-level="组合功能测试" data-class-level="非模态类测试">B. 组合功能测试和非模态类测试</div>
                <div data-value="C" data-method-level="不变式边界测试" data-class-level="递归函数测试">C. 不变式边界测试和递归函数测试</div>
                <div data-value="D" data-method-level="不变式边界测试" data-class-level="模态类测试">D. 不变式边界测试和模态类测试</div>
            </div>
            <div class="correct-answer-display">
                图片中显示的正确答案是：D
            </div>
            <div class="discrepancy-note">
                <strong>注意：</strong>根据下方提供的详细解析，本题的正确答案应为 <strong>B (组合功能测试和非模态类测试)</strong>。图片中标注的答案 D 与解析内容存在矛盾。在本学习页面中，我们将基于解析内容进行讲解。
            </div>
        </div>

        <div class="explanation-section">
            <h2>题目解析</h2>
            <p>本题考查面向对象系统测试的基本概念。</p>
            <p>面向对象系统的单元测试包括方法层次的测试、类层次的测试和类树层次的测试。</p>

            <div class="explanation-point" id="method-level-explanation">
                <h3>(1) 方法层次的测试 (Method-level Testing)</h3>
                <p>类似于传统软件测试中对单个函数的测试，常用的测试技术包括：</p>
                <ul>
                    <li>**等价类划分测试**：将输入数据划分为有效等价类和无效等价类，从每个等价类中选取代表性数据进行测试。</li>
                    <li>**组合功能测试**：测试方法内部不同路径的组合，确保所有重要路径都被覆盖。</li>
                    <li>**递归函数测试**：针对递归方法的测试，关注基本情况和递归调用的正确性。</li>
                    <li>**多态消息测试**：测试通过多态机制调用不同实现的方法时的行为。</li>
                </ul>
                <div class="controls">
                    <button onclick="startMethodAnimation()">演示方法层次测试</button>
                </div>
                <canvas id="methodCanvas" width="600" height="200"></canvas>
            </div>

            <div class="explanation-point" id="class-level-explanation">
                <h3>(2) 类层次的测试 (Class-level Testing)</h3>
                <p>主要关注类的内部状态和行为，以及类成员之间的交互，包括：</p>
                <ul>
                    <li>**不变式边界测试**：测试类的不变式（始终为真的条件）在边界条件下的保持情况。</li>
                    <li>**模态类测试**：测试类在不同状态（模式）下的行为。</li>
                    <li>**非模态类测试**：测试没有明显状态的类。</li>
                </ul>
                <div class="controls">
                    <button onclick="startClassAnimation()">演示类层次测试</button>
                </div>
                <canvas id="classCanvas" width="600" height="250"></canvas>
            </div>

            <div class="explanation-point" id="classtree-level-explanation">
                <h3>(3) 类树层次的测试 (Class-tree Level Testing)</h3>
                <p>关注类继承结构（类树）中的行为，以及子类和父类之间的关系，包括：</p>
                <ul>
                    <li>**多态服务测试**：测试在类继承层次结构中，多态方法调用的正确性。</li>
                    <li>**展平测试**：测试子类是否正确继承和覆盖了父类的方法。</li>
                </ul>
                <div class="controls">
                    <button onclick="startClassTreeAnimation()">演示类树层次测试</button>
                </div>
                <canvas id="classTreeCanvas" width="600" height="300"></canvas>
            </div>
        </div>
    </div>

    <script>
        // JavaScript for interactivity and animations will go here
        const optionsDivs = document.querySelectorAll('.options div');
        const blank1 = document.getElementById('blank1');
        const blank2 = document.getElementById('blank2');

        optionsDivs.forEach(option => {
            option.addEventListener('click', () => {
                optionsDivs.forEach(o => o.classList.remove('selected'));
                option.classList.add('selected');
                blank1.textContent = option.dataset.methodLevel;
                blank2.textContent = option.dataset.classLevel;
            });
        });

        // Initial selection for D to show the problem as in the image
        document.querySelector('.options div[data-value="D"]').click();

        // Canvas animation functions (placeholders for now)
        const methodCanvas = document.getElementById('methodCanvas');
        const methodCtx = methodCanvas.getContext('2d');

        function startMethodAnimation() {
            methodCtx.clearRect(0, 0, methodCanvas.width, methodCanvas.height);
            methodCtx.font = '16px Arial';
            methodCtx.fillStyle = '#333';
            methodCtx.fillText('方法A', 50, 50);
            methodCtx.fillText('输入值', 50, 100);
            methodCtx.fillText('输出值', 200, 100);
            
            // Simple animation for input/output
            let x = 100;
            let requestId;
            function animate() {
                methodCtx.clearRect(100, 70, 100, 40); // Clear old "->"
                methodCtx.fillText('->', x, 100);
                x += 2;
                if (x < 180) {
                    requestId = requestAnimationFrame(animate);
                } else {
                    methodCtx.fillText('结果', 250, 100);
                    cancelAnimationFrame(requestId);
                }
            }
            requestId = requestAnimationFrame(animate);
        }

        const classCanvas = document.getElementById('classCanvas');
        const classCtx = classCanvas.getContext('2d');

        function startClassAnimation() {
            classCtx.clearRect(0, 0, classCanvas.width, classCanvas.height);
            classCtx.font = '16px Arial';
            classCtx.fillStyle = '#333';

            // Draw a class box
            classCtx.strokeStyle = '#4CAF50';
            classCtx.lineWidth = 2;
            classCtx.strokeRect(50, 50, 200, 150);
            classCtx.fillStyle = '#4CAF50';
            classCtx.fillRect(50, 50, 200, 30);
            classCtx.fillStyle = 'white';
            classCtx.fillText('Class MyClass', 60, 70);
            
            classCtx.fillStyle = '#333';
            classCtx.fillText('- property1: value', 60, 100);
            classCtx.fillText('- method1()', 60, 130);
            classCtx.fillText('- method2()', 60, 160);

            // Simple state change animation
            let state = '初始状态';
            let frame = 0;
            const maxFrames = 100;
            let requestId;

            function drawState() {
                classCtx.clearRect(300, 100, 200, 50);
                classCtx.fillText(`当前状态: ${state}`, 300, 120);
            }

            function animate() {
                drawState();
                if (frame === 0) {
                    state = '初始状态';
                } else if (frame === 30) {
                    state = '调用method1';
                } else if (frame === 60) {
                    state = '状态变更';
                } else if (frame === 90) {
                    state = '调用method2';
                }

                frame++;
                if (frame <= maxFrames) {
                    requestId = requestAnimationFrame(animate);
                } else {
                    cancelAnimationFrame(requestId);
                    frame = 0; // Reset for next run
                }
            }
            requestId = requestAnimationFrame(animate);
        }

        const classTreeCanvas = document.getElementById('classTreeCanvas');
        const classTreeCtx = classTreeCanvas.getContext('2d');

        function startClassTreeAnimation() {
            classTreeCtx.clearRect(0, 0, classTreeCanvas.width, classTreeCanvas.height);
            classTreeCtx.font = '16px Arial';
            classTreeCtx.fillStyle = '#333';
            classTreeCtx.strokeStyle = '#007bff';
            classTreeCtx.lineWidth = 2;

            // Draw Father Class
            classTreeCtx.strokeRect(200, 30, 200, 80);
            classTreeCtx.fillText('父类 (ParentClass)', 220, 75);

            // Draw two Child Classes
            classTreeCtx.strokeRect(100, 180, 180, 80);
            classTreeCtx.fillText('子类A (ChildA)', 120, 225);

            classTreeCtx.strokeRect(320, 180, 180, 80);
            classTreeCtx.fillText('子类B (ChildB)', 340, 225);

            // Draw inheritance lines
            classTreeCtx.beginPath();
            classTreeCtx.moveTo(300, 110);
            classTreeCtx.lineTo(190, 180);
            classTreeCtx.stroke();

            classTreeCtx.beginPath();
            classTreeCtx.moveTo(300, 110);
            classTreeCtx.lineTo(410, 180);
            classTreeCtx.stroke();

            // Simple animation for method call
            let dotX = 250;
            let dotY = 75;
            let requestId;
            let step = 0;

            function animateDot() {
                classTreeCtx.clearRect(0, 0, classTreeCanvas.width, classTreeCanvas.height);
                // Redraw static elements
                classTreeCtx.strokeRect(200, 30, 200, 80);
                classTreeCtx.fillText('父类 (ParentClass)', 220, 75);
                classTreeCtx.strokeRect(100, 180, 180, 80);
                classTreeCtx.fillText('子类A (ChildA)', 120, 225);
                classTreeCtx.strokeRect(320, 180, 180, 80);
                classTreeCtx.fillText('子类B (ChildB)', 340, 225);
                classTreeCtx.beginPath();
                classTreeCtx.moveTo(300, 110);
                classTreeCtx.lineTo(190, 180);
                classTreeCtx.stroke();
                classTreeCtx.beginPath();
                classTreeCtx.moveTo(300, 110);
                classTreeCtx.lineTo(410, 180);
                classTreeCtx.stroke();

                // Draw moving dot
                classTreeCtx.fillStyle = 'red';
                classTreeCtx.beginPath();
                classTreeCtx.arc(dotX, dotY, 5, 0, Math.PI * 2);
                classTreeCtx.fill();

                if (step === 0) { // Move from Parent to ChildA
                    if (dotX > 150) {
                        dotX -= 2;
                        dotY += 2;
                    } else {
                        step = 1;
                    }
                } else if (step === 1) { // Stay at ChildA for a bit, then move to ChildB
                    if (dotY < 225) { // Ensure it reaches ChildA
                        dotY += 1;
                    } else if (dotX < 370) {
                        dotX += 2;
                        dotY -= 2;
                    } else {
                        step = 2;
                    }
                } else if (step === 2) { // Stay at ChildB for a bit, then reset
                    if (dotY < 225) { // Ensure it reaches ChildB
                         dotY +=1
                    } else {
                        // Reset for next animation
                        dotX = 250;
                        dotY = 75;
                        step = 0;
                        cancelAnimationFrame(requestId);
                        return; // Stop animation
                    }
                }
                requestId = requestAnimationFrame(animateDot);
            }
            requestId = requestAnimationFrame(animateDot);
        }

    </script>
</body>
</html> 