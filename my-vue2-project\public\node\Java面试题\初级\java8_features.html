<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 8 新特性 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f0f4f8;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #1565c0;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        
        h2 {
            color: #1976d2;
            margin: 25px 0 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1f5fe;
        }
        
        .feature-section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f9f9f9;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .feature-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .tab-container {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 2px solid #e1f5fe;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #e1f5fe;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            margin-right: 5px;
            transition: background-color 0.3s;
        }
        
        .tab:hover {
            background-color: #b3e5fc;
        }
        
        .tab.active {
            background-color: #039be5;
            color: white;
        }
        
        .content {
            display: none;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .content.active {
            display: block;
        }
        
        code {
            font-family: Consolas, Monaco, 'Courier New', monospace;
            background-color: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            color: #e91e63;
        }
        
        pre {
            background-color: #282c34;
            color: #abb2bf;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        button {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            margin: 10px 0;
        }
        
        button:hover {
            background-color: #0d8bf2;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
        }
        
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .animation-container {
            position: relative;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
            background-color: #f5f5f5;
        }
        
        .lambda-item {
            position: absolute;
            padding: 10px;
            background-color: #bbdefb;
            border-radius: 5px;
            transition: all 1s ease;
        }
        
        .arrow {
            position: absolute;
            width: 50px;
            height: 20px;
            font-size: 20px;
            text-align: center;
            color: #1976d2;
        }
        
        .optional-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        .box {
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 20px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.5s ease;
        }
        
        .optional-box {
            border: 3px solid #1976d2;
            background-color: #e3f2fd;
        }
        
        .null-box {
            border: 3px dashed #f44336;
            background-color: #ffebee;
        }
        
        .operation-btn {
            margin: 0 5px;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            color: #757575;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java 8 新特性 - 交互式学习</h1>
        
        <div class="feature-section">
            <h2>1. Lambda 表达式</h2>
            <p>Lambda表达式是一个匿名函数，可以像传递数据一样传递代码，使代码更简洁、灵活。</p>
            
            <div class="tab-container">
                <div class="tab active" onclick="switchTab(this, 'lambda-demo')">演示</div>
                <div class="tab" onclick="switchTab(this, 'lambda-example')">代码示例</div>
            </div>
            
            <div id="lambda-demo" class="content active">
                <div class="animation-container" id="lambdaContainer">
                    <!-- 动画元素会在JS中添加 -->
                </div>
                <button onclick="runLambdaAnimation()">运行动画演示</button>
                <div class="result" id="lambdaResult">
                    点击按钮查看Lambda表达式如何简化代码
                </div>
            </div>
            
            <div id="lambda-example" class="content">
                <p>传统匿名内部类写法：</p>
                <pre>// 排序前：[5, 3, 8, 1, 2]
Collections.sort(numbers, new Comparator&lt;Integer&gt;() {
    @Override
    public int compare(Integer a, Integer b) {
        return a.compareTo(b);
    }
});
// 排序后：[1, 2, 3, 5, 8]</pre>

                <p>使用Lambda表达式：</p>
                <pre>// 排序前：[5, 3, 8, 1, 2]
Collections.sort(numbers, (a, b) -> a.compareTo(b));
// 排序后：[1, 2, 3, 5, 8]</pre>
                <p>Lambda表达式语法：<code>(参数) -> { 表达式或语句块 }</code></p>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>2. 方法引用</h2>
            <p>当Lambda表达式只是调用一个已存在的方法，可以使用方法引用使代码更简洁。</p>
            
            <div class="tab-container">
                <div class="tab active" onclick="switchTab(this, 'method-demo')">演示</div>
                <div class="tab" onclick="switchTab(this, 'method-example')">代码示例</div>
            </div>
            
            <div id="method-demo" class="content active">
                <canvas id="methodRefCanvas" width="600" height="250"></canvas>
                <button onclick="drawMethodReference()">显示方法引用类型</button>
                <div class="result" id="methodRefResult">
                    点击按钮查看三种方法引用类型
                </div>
            </div>
            
            <div id="method-example" class="content">
                <p>1. 对象::实例方法</p>
                <pre>// Lambda写法
Consumer&lt;String&gt; c1 = s -> System.out.println(s);
// 方法引用写法
Consumer&lt;String&gt; c2 = System.out::println;</pre>

                <p>2. 类::静态方法</p>
                <pre>// Lambda写法
Comparator&lt;Integer&gt; c1 = (x, y) -> Integer.compare(x, y);
// 方法引用写法
Comparator&lt;Integer&gt; c2 = Integer::compare;</pre>

                <p>3. 类::实例方法</p>
                <pre>// Lambda写法
BiPredicate&lt;String, String&gt; b1 = (x, y) -> x.equals(y);
// 方法引用写法
BiPredicate&lt;String, String&gt; b2 = String::equals;</pre>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>3. 函数式接口</h2>
            <p>函数式接口是只包含一个抽象方法的接口，可以使用Lambda表达式或方法引用创建其实例。</p>
            
            <div class="tab-container">
                <div class="tab active" onclick="switchTab(this, 'functional-demo')">演示</div>
                <div class="tab" onclick="switchTab(this, 'functional-example')">代码示例</div>
            </div>
            
            <div id="functional-demo" class="content active">
                <canvas id="functionalCanvas" width="600" height="300"></canvas>
                <button onclick="showCommonFunctionalInterfaces()">显示常用函数式接口</button>
                <div class="result" id="functionalResult">
                    Java 8提供了许多预定义的函数式接口，点击按钮查看它们的用途。
                </div>
            </div>
            
            <div id="functional-example" class="content">
                <p>自定义函数式接口：</p>
                <pre>@FunctionalInterface
public interface MyFunction&lt;T, R&gt; {
    R apply(T t); // 只有一个抽象方法
    
    // 可以有默认方法
    default void printInfo() {
        System.out.println("这是一个函数式接口");
    }
}</pre>

                <p>使用自定义函数式接口：</p>
                <pre>// 使用Lambda表达式实现接口
MyFunction&lt;String, Integer&gt; f = s -> s.length();
Integer result = f.apply("Hello"); // 返回5</pre>
            </div>
        </div>
        
        <div class="feature-section">
            <h2>4. Optional类</h2>
            <p>Optional类是一个容器对象，用于表示一个值存在或不存在，避免空指针异常。</p>
            
            <div class="tab-container">
                <div class="tab active" onclick="switchTab(this, 'optional-demo')">演示</div>
                <div class="tab" onclick="switchTab(this, 'optional-example')">代码示例</div>
            </div>
            
            <div id="optional-demo" class="content active">
                <div class="optional-container">
                    <div class="box optional-box" id="optionalBox">?</div>
                    <div class="box null-box" id="nullBox">null</div>
                </div>
                <div>
                    <button class="operation-btn" onclick="createOptional('value')">Optional.of("值")</button>
                    <button class="operation-btn" onclick="createOptional('empty')">Optional.empty()</button>
                    <button class="operation-btn" onclick="createOptional('nullable')">Optional.ofNullable()</button>
                    <button class="operation-btn" onclick="checkOptional()">isPresent()</button>
                    <button class="operation-btn" onclick="getOptionalValue()">orElse()</button>
                </div>
                <div class="result" id="optionalResult">
                    点击按钮操作Optional对象，观察其行为
                </div>
            </div>
            
            <div id="optional-example" class="content">
                <p>创建Optional：</p>
                <pre>// 创建一个包含值的Optional
Optional&lt;String&gt; opt1 = Optional.of("Hello");

// 创建一个空的Optional
Optional&lt;String&gt; opt2 = Optional.empty();

// 创建一个可能包含null的Optional
String nullableValue = null;
Optional&lt;String&gt; opt3 = Optional.ofNullable(nullableValue);</pre>

                <p>使用Optional：</p>
                <pre>// 检查是否存在值
if (opt1.isPresent()) {
    System.out.println("值存在");
}

// 获取值或默认值
String result = opt1.orElse("默认值");

// 通过函数处理值
Optional&lt;Integer&gt; length = opt1.map(s -> s.length());

// 仅在值存在时执行操作
opt1.ifPresent(s -> System.out.println(s));</pre>
            </div>
        </div>

        <div class="footer">
            <p>Java 8新特性交互式学习 © 2023</p>
        </div>
    </div>

    <script>
        // 切换标签功能
        function switchTab(tabElement, contentId) {
            // 移除所有标签的active类
            let tabs = tabElement.parentElement.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // 给当前标签添加active类
            tabElement.classList.add('active');
            
            // 隐藏所有内容
            let contents = tabElement.parentElement.parentElement.getElementsByClassName('content');
            for (let i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }
            
            // 显示选中的内容
            document.getElementById(contentId).classList.add('active');
        }
        
        // Lambda表达式动画
        function runLambdaAnimation() {
            const container = document.getElementById('lambdaContainer');
            container.innerHTML = '';
            const result = document.getElementById('lambdaResult');
            
            // 创建传统代码元素
            const traditional = document.createElement('div');
            traditional.className = 'lambda-item';
            traditional.style.left = '20px';
            traditional.style.top = '20px';
            traditional.innerHTML = 'new Comparator() {\n  @Override\n  public int compare(a, b) {\n    return a.compareTo(b);\n  }\n}';
            container.appendChild(traditional);
            
            // 创建箭头
            const arrow = document.createElement('div');
            arrow.className = 'arrow';
            arrow.style.left = '280px';
            arrow.style.top = '80px';
            arrow.textContent = '→';
            container.appendChild(arrow);
            
            // 创建Lambda表达式元素
            const lambda = document.createElement('div');
            lambda.className = 'lambda-item';
            lambda.style.left = '350px';
            lambda.style.top = '80px';
            lambda.style.opacity = '0';
            lambda.innerHTML = '(a, b) -> a.compareTo(b)';
            container.appendChild(lambda);
            
            // 执行动画
            setTimeout(() => {
                traditional.style.transform = 'scale(0.8)';
                traditional.style.opacity = '0.5';
                lambda.style.opacity = '1';
                result.innerHTML = '通过Lambda表达式，代码变得更加简洁直观！<br>匿名内部类的7行代码变成了1行。';
            }, 1000);
        }
        
        // 方法引用Canvas绘制
        function drawMethodReference() {
            const canvas = document.getElementById('methodRefCanvas');
            const ctx = canvas.getContext('2d');
            const result = document.getElementById('methodRefResult');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 设置样式
            ctx.font = '16px Arial';
            ctx.fillStyle = '#1976d2';
            ctx.textAlign = 'center';
            
            // 绘制三种方法引用类型
            const types = [
                { title: '对象::实例方法', example: 'System.out::println', desc: '引用特定对象的实例方法' },
                { title: '类::静态方法', example: 'Math::max', desc: '引用类的静态方法' },
                { title: '类::实例方法', example: 'String::length', desc: '引用特定类型的任意对象的实例方法' }
            ];
            
            // 定义位置参数
            const startY = 50;
            const boxHeight = 60;
            const spacing = 80;
            
            types.forEach((type, index) => {
                const y = startY + index * (boxHeight + spacing);
                
                // 绘制框
                ctx.fillStyle = '#e3f2fd';
                ctx.strokeStyle = '#1976d2';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.roundRect(150, y, 300, boxHeight, 10);
                ctx.fill();
                ctx.stroke();
                
                // 绘制文字
                ctx.fillStyle = '#1976d2';
                ctx.fillText(type.title, 300, y + 25);
                ctx.fillStyle = '#e91e63';
                ctx.fillText(type.example, 300, y + 50);
                
                // 逐渐显示动画
                setTimeout(() => {
                    ctx.fillStyle = 'rgba(25, 118, 210, 0.1)';
                    ctx.beginPath();
                    ctx.roundRect(100, y - 10, 400, boxHeight + 20, 15);
                    ctx.fill();
                }, index * 500);
            });
            
            result.innerHTML = '方法引用使用 :: 操作符，进一步简化了只调用一个方法的Lambda表达式';
        }
        
        // 函数式接口展示
        function showCommonFunctionalInterfaces() {
            const canvas = document.getElementById('functionalCanvas');
            const ctx = canvas.getContext('2d');
            const result = document.getElementById('functionalResult');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 设置样式
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            
            // 定义常用函数式接口
            const interfaces = [
                { name: 'Function<T, R>', desc: '接收一个参数，返回一个结果', example: 't -> t.toString()' },
                { name: 'Consumer<T>', desc: '接收一个参数，不返回结果', example: 't -> System.out.println(t)' },
                { name: 'Supplier<T>', desc: '不接收参数，返回一个结果', example: '() -> new Object()' },
                { name: 'Predicate<T>', desc: '接收一个参数，返回布尔值', example: 't -> t != null' }
            ];
            
            // 绘制中央节点
            ctx.fillStyle = '#1976d2';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, 50, 40, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.fillText('@Functional', canvas.width / 2, 45);
            ctx.fillText('Interface', canvas.width / 2, 65);
            
            // 绘制连接线和接口节点
            const radius = 150; // 环绕中心的半径
            const centerX = canvas.width / 2;
            const centerY = 150;
            
            interfaces.forEach((intf, i) => {
                const angle = Math.PI / 2 + i * (Math.PI / 2);
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle);
                
                // 绘制连接线
                ctx.strokeStyle = '#90caf9';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, 90);
                ctx.lineTo(x, y - 30);
                ctx.stroke();
                
                // 绘制节点
                ctx.fillStyle = '#bbdefb';
                ctx.strokeStyle = '#1976d2';
                ctx.beginPath();
                ctx.roundRect(x - 80, y - 30, 160, 80, 10);
                ctx.fill();
                ctx.stroke();
                
                // 绘制接口名称和描述
                ctx.fillStyle = '#1976d2';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(intf.name, x, y - 10);
                ctx.font = '12px Arial';
                ctx.fillText(intf.example, x, y + 10);
                ctx.fillStyle = '#555';
                ctx.font = '12px Arial';
                ctx.fillText(intf.desc, x, y + 30);
                
                // 动画效果
                setTimeout(() => {
                    ctx.fillStyle = 'rgba(25, 118, 210, 0.1)';
                    ctx.beginPath();
                    ctx.roundRect(x - 90, y - 40, 180, 100, 15);
                    ctx.fill();
                }, i * 500);
            });
            
            result.innerHTML = 'Java 8提供了多个内置的函数式接口，位于java.util.function包中，方便开发者使用Lambda表达式';
        }
        
        // Optional类演示
        let optionalHasValue = false;
        
        function createOptional(type) {
            const optionalBox = document.getElementById('optionalBox');
            const result = document.getElementById('optionalResult');
            
            if (type === 'value') {
                optionalBox.textContent = '"值"';
                optionalBox.style.backgroundColor = '#e3f2fd';
                optionalBox.style.border = '3px solid #1976d2';
                optionalHasValue = true;
                result.innerHTML = '已创建包含值的Optional: <code>Optional.of("值")</code>';
            } else if (type === 'empty') {
                optionalBox.textContent = 'Empty';
                optionalBox.style.backgroundColor = '#e8eaf6';
                optionalBox.style.border = '3px dashed #3f51b5';
                optionalHasValue = false;
                result.innerHTML = '已创建空的Optional: <code>Optional.empty()</code>';
            } else if (type === 'nullable') {
                // 随机决定是否为null
                const isNull = Math.random() > 0.5;
                if (isNull) {
                    optionalBox.textContent = 'Empty';
                    optionalBox.style.backgroundColor = '#e8eaf6';
                    optionalBox.style.border = '3px dashed #3f51b5';
                    optionalHasValue = false;
                    result.innerHTML = '已创建Optional: <code>Optional.ofNullable(null)</code> → 变为Empty';
                } else {
                    optionalBox.textContent = '"值"';
                    optionalBox.style.backgroundColor = '#e3f2fd';
                    optionalBox.style.border = '3px solid #1976d2';
                    optionalHasValue = true;
                    result.innerHTML = '已创建Optional: <code>Optional.ofNullable("值")</code> → 包含值';
                }
            }
            
            // 添加动画效果
            optionalBox.style.transform = 'scale(1.1)';
            setTimeout(() => {
                optionalBox.style.transform = 'scale(1)';
            }, 300);
        }
        
        function checkOptional() {
            const result = document.getElementById('optionalResult');
            const optionalBox = document.getElementById('optionalBox');
            
            if (optionalHasValue) {
                optionalBox.style.boxShadow = '0 0 15px #4caf50';
                result.innerHTML = '<code>isPresent()</code> 返回 <code>true</code> - Optional包含值';
            } else {
                optionalBox.style.boxShadow = '0 0 15px #f44336';
                result.innerHTML = '<code>isPresent()</code> 返回 <code>false</code> - Optional为空';
            }
            
            setTimeout(() => {
                optionalBox.style.boxShadow = 'none';
            }, 1000);
        }
        
        function getOptionalValue() {
            const result = document.getElementById('optionalResult');
            const optionalBox = document.getElementById('optionalBox');
            const nullBox = document.getElementById('nullBox');
            
            if (optionalHasValue) {
                optionalBox.style.boxShadow = '0 0 15px #4caf50';
                result.innerHTML = '<code>orElse("默认")</code> 返回 <code>"值"</code> - 使用Optional中的值';
            } else {
                nullBox.style.boxShadow = '0 0 15px #f44336';
                result.innerHTML = '<code>orElse("默认")</code> 返回 <code>"默认"</code> - 使用提供的默认值';
            }
            
            setTimeout(() => {
                optionalBox.style.boxShadow = 'none';
                nullBox.style.boxShadow = 'none';
            }, 1000);
        }
        
        // 初始化
        window.onload = function() {
            // 初始化Optional演示
            createOptional('value');
        };
    </script>
</body>
</html> 