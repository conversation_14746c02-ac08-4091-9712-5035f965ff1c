<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位示图（Bitmap）学习系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5em;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .concept-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.1em;
            line-height: 1.6;
            animation: pulse 2s infinite;
        }

        .demo-area {
            background: #f8fafc;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #e2e8f0;
        }

        .bitmap-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .word-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .word-label {
            font-weight: bold;
            color: #4a5568;
            min-width: 80px;
            text-align: right;
        }

        .bit-container {
            display: flex;
            gap: 2px;
        }

        .bit {
            width: 25px;
            height: 25px;
            border: 2px solid #cbd5e0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .bit.occupied {
            background: #f56565;
            color: white;
            border-color: #e53e3e;
        }

        .bit.free {
            background: #68d391;
            color: white;
            border-color: #38a169;
        }

        .bit.highlight {
            animation: highlight 1s ease-in-out;
            transform: scale(1.2);
            z-index: 10;
            position: relative;
        }

        .calculation-box {
            background: #edf2f7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 3px solid #4299e1;
            animation: slideInLeft 0.5s ease-out;
        }

        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: #4299e1;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: bold;
            margin-right: 10px;
        }

        .formula {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            text-align: center;
            margin: 15px 0;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .result-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.2em;
            margin: 20px 0;
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes highlight {
            0%, 100% { background: #ffd700; transform: scale(1.2); }
            50% { background: #ff6b6b; transform: scale(1.3); }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        #animationCanvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">位示图（Bitmap）学习系统</h1>
            <p class="subtitle">零基础也能轻松掌握的文件系统知识</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是位示图？</h2>
            <div class="concept-box">
                位示图就像一个"停车场管理表"！<br>
                每个停车位（物理块）用一个数字记录：0表示空闲，1表示占用<br>
                计算机用这种方法管理磁盘空间
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 题目分析</h2>
            <div class="demo-area">
                <h3>原题：2053号物理块分配给某文件</h3>
                <div class="calculation-box">
                    <div class="step">
                        <span class="step-number">1</span>
                        <strong>理解编号规则：</strong>物理块编号从0开始：0, 1, 2, 3, ..., 2053
                    </div>
                    <div class="step">
                        <span class="step-number">2</span>
                        <strong>系统字长：</strong>32位，意味着每个字包含32个二进制位
                    </div>
                    <div class="step">
                        <span class="step-number">3</span>
                        <strong>计算公式：</strong>
                        <div class="formula">
                            字编号 = 物理块号 ÷ 32（取整数部分）<br>
                            位编号 = 物理块号 % 32（取余数）
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧮 计算过程演示</h2>
            <div class="demo-area">
                <button class="interactive-btn" onclick="startCalculation()">开始计算演示</button>
                <div id="calculationSteps"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎨 位示图可视化</h2>
            <div class="demo-area">
                <button class="interactive-btn" onclick="showBitmap()">显示位示图</button>
                <div id="bitmapDisplay"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎬 动画演示</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startAnimation()">开始动画演示</button>
                <button class="interactive-btn" onclick="resetAnimation()">重置动画</button>
            </div>
        </div>
    </div>

    <script>
        let animationStep = 0;
        let canvas, ctx;

        window.onload = function() {
            canvas = document.getElementById('animationCanvas');
            ctx = canvas.getContext('2d');
            resetAnimation();
        };

        function startCalculation() {
            const container = document.getElementById('calculationSteps');
            container.innerHTML = '';
            
            setTimeout(() => {
                container.innerHTML += `
                    <div class="step">
                        <span class="step-number">1</span>
                        <strong>给定：</strong>2053号物理块需要分配
                    </div>
                `;
            }, 500);

            setTimeout(() => {
                container.innerHTML += `
                    <div class="step">
                        <span class="step-number">2</span>
                        <strong>计算字编号：</strong>
                        <div class="formula">2053 ÷ 32 = 64.15625</div>
                        取整数部分：<strong>64</strong>
                    </div>
                `;
            }, 1500);

            setTimeout(() => {
                container.innerHTML += `
                    <div class="step">
                        <span class="step-number">3</span>
                        <strong>计算位编号：</strong>
                        <div class="formula">2053 % 32 = 5</div>
                        余数为：<strong>5</strong>
                    </div>
                `;
            }, 2500);

            setTimeout(() => {
                container.innerHTML += `
                    <div class="step">
                        <span class="step-number">4</span>
                        <strong>但是注意！</strong>编号从0开始，所以：<br>
                        字编号 = 64 - 1 = <strong>63</strong><br>
                        位编号 = 5（保持不变）
                    </div>
                `;
            }, 3500);

            setTimeout(() => {
                container.innerHTML += `
                    <div class="result-box">
                        🎉 答案：第63号字的第5号位，设置为"1"（表示占用）
                    </div>
                `;
            }, 4500);
        }

        function showBitmap() {
            const container = document.getElementById('bitmapDisplay');
            container.innerHTML = '<h3>位示图结构（显示第63号字）：</h3>';
            
            // 创建第63号字的位示图
            const wordRow = document.createElement('div');
            wordRow.className = 'word-row';
            
            const label = document.createElement('div');
            label.className = 'word-label';
            label.textContent = '第63号字:';
            wordRow.appendChild(label);
            
            const bitContainer = document.createElement('div');
            bitContainer.className = 'bit-container';
            
            for (let i = 0; i < 32; i++) {
                const bit = document.createElement('div');
                bit.className = 'bit';
                bit.textContent = i === 5 ? '1' : '0';
                bit.title = `位${i}`;
                
                if (i === 5) {
                    setTimeout(() => {
                        bit.classList.add('highlight');
                        bit.classList.add('occupied');
                    }, 1000);
                } else {
                    bit.classList.add('free');
                }
                
                bitContainer.appendChild(bit);
            }
            
            wordRow.appendChild(bitContainer);
            container.appendChild(wordRow);
            
            setTimeout(() => {
                container.innerHTML += `
                    <div class="result-box">
                        ✨ 第5号位被设置为"1"，表示2053号物理块已被占用！
                    </div>
                `;
            }, 2000);
        }

        function startAnimation() {
            animationStep = 0;
            animate();
        }

        function resetAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationStep = 0;
            drawInitialState();
        }

        function drawInitialState() {
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('位示图动画演示', canvas.width/2, 40);
            
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('点击"开始动画演示"查看计算过程', canvas.width/2, 200);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#f8fafc';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            switch(animationStep) {
                case 0:
                    drawStep1();
                    break;
                case 1:
                    drawStep2();
                    break;
                case 2:
                    drawStep3();
                    break;
                case 3:
                    drawStep4();
                    break;
                default:
                    return;
            }
            
            setTimeout(() => {
                animationStep++;
                if (animationStep <= 3) {
                    animate();
                }
            }, 2000);
        }

        function drawStep1() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('步骤1: 理解问题', canvas.width/2, 50);
            
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('2053号物理块需要在位示图中标记为占用', canvas.width/2, 100);
            
            // 绘制物理块示意图
            ctx.fillStyle = '#e2e8f0';
            for (let i = 0; i < 10; i++) {
                ctx.fillRect(50 + i * 70, 150, 60, 40);
                ctx.fillStyle = '#4a5568';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(i.toString(), 80 + i * 70, 175);
                ctx.fillStyle = '#e2e8f0';
            }
            
            ctx.fillStyle = '#f56565';
            ctx.fillRect(50 + 3 * 70, 150, 60, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('2053', 80 + 3 * 70, 175);
        }

        function drawStep2() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('步骤2: 计算字编号', canvas.width/2, 50);
            
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('2053 ÷ 32 = 64.15625', canvas.width/2, 120);
            ctx.fillText('取整数部分 = 64', canvas.width/2, 160);
            ctx.fillText('但编号从0开始，所以字编号 = 63', canvas.width/2, 200);
            
            // 绘制计算过程
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(canvas.width/2, 250, 80, 0, 2 * Math.PI);
            ctx.stroke();
            
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('63', canvas.width/2, 260);
        }

        function drawStep3() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('步骤3: 计算位编号', canvas.width/2, 50);
            
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('2053 % 32 = 5', canvas.width/2, 120);
            ctx.fillText('位编号 = 5', canvas.width/2, 160);
            
            // 绘制32位示意图
            for (let i = 0; i < 32; i++) {
                if (i === 5) {
                    ctx.fillStyle = '#f56565';
                } else {
                    ctx.fillStyle = '#e2e8f0';
                }
                ctx.fillRect(50 + (i % 16) * 45, 200 + Math.floor(i / 16) * 50, 40, 40);
                
                ctx.fillStyle = '#4a5568';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(i.toString(), 70 + (i % 16) * 45, 225 + Math.floor(i / 16) * 50);
            }
        }

        function drawStep4() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('步骤4: 最终答案', canvas.width/2, 50);
            
            ctx.fillStyle = '#38a169';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('第63号字的第5号位设置为"1"', canvas.width/2, 150);
            
            ctx.fillStyle = '#4299e1';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('表示2053号物理块已被占用', canvas.width/2, 200);
            
            // 绘制成功标志
            ctx.strokeStyle = '#38a169';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.arc(canvas.width/2, 280, 50, 0, 2 * Math.PI);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(canvas.width/2 - 20, 280);
            ctx.lineTo(canvas.width/2 - 5, 295);
            ctx.lineTo(canvas.width/2 + 20, 265);
            ctx.stroke();
        }
    </script>
</body>
</html>
