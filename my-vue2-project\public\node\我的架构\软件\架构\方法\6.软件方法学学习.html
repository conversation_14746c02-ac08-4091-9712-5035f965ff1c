<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件方法学 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .method-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .method-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
        }

        .quiz-section {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
            text-align: center;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px 25px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 200px;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .option.correct {
            background: #4CAF50;
            border-color: #45a049;
        }

        .option.wrong {
            background: #f44336;
            border-color: #da190b;
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
            display: none;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .animated-element {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00f2fe, #4facfe);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件方法学探索之旅</h1>
            <p class="subtitle">通过动画和交互，轻松掌握软件开发方法</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">三种核心开发方法</h2>
            
            <div class="method-card" onclick="showMethod('topDown')" id="topDownCard">
                <h3>🔽 自顶向下开发方法</h3>
                <p>从整体到局部，逐层分解问题</p>
            </div>

            <div class="method-card" onclick="showMethod('bottomUp')" id="bottomUpCard">
                <h3>🔼 自底向上开发方法</h3>
                <p>从具体组件开始，逐步构建系统</p>
            </div>

            <div class="method-card" onclick="showMethod('formal')" id="formalCard">
                <h3>📐 形式化开发方法</h3>
                <p>基于严格数学基础的开发方法</p>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎯 知识测试</h2>
            <p style="margin-bottom: 30px;">根据描述选择正确的开发方法：</p>
            <p style="font-size: 1.1rem; margin-bottom: 20px; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px;">
                "先对最高层次中的问题进行定义、设计、编程和测试，而将其中未解决的问题作为一个子任务放到下一层次中去解决"
            </p>
            
            <div class="option" onclick="selectOption(this, false)">A. 面向对象开发方法</div>
            <div class="option" onclick="selectOption(this, false)">B. 形式化开发方法</div>
            <div class="option" onclick="selectOption(this, false)">C. 非形式化开发方法</div>
            <div class="option" onclick="selectOption(this, true)">D. 自顶向下开发方法</div>

            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：D. 自顶向下开发方法</strong></p>
                <p>自顶向下开发方法的核心特征就是从最高层次的问题开始，逐层分解到具体的子问题。这种方法先解决主要问题，然后将复杂的子问题留到下一层次处理，正好符合题目描述。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let currentAnimation = null;
        let animationFrame = 0;
        let progress = 0;

        function updateProgress(value) {
            progress = Math.min(100, progress + value);
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function showMethod(method) {
            // 重置所有卡片
            document.querySelectorAll('.method-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 激活当前卡片
            document.getElementById(method + 'Card').classList.add('active');
            
            currentAnimation = method;
            animationFrame = 0;
            updateProgress(10);
            animate();
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (currentAnimation === 'topDown') {
                drawTopDownAnimation();
            } else if (currentAnimation === 'bottomUp') {
                drawBottomUpAnimation();
            } else if (currentAnimation === 'formal') {
                drawFormalAnimation();
            }
            
            animationFrame++;
            if (animationFrame < 300) {
                requestAnimationFrame(animate);
            }
        }

        function drawTopDownAnimation() {
            const centerX = canvas.width / 2;
            const centerY = 100;
            
            // 绘制主问题
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(centerX - 80, centerY - 30, 160, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('主问题', centerX, centerY + 5);
            
            // 动画分解过程
            if (animationFrame > 60) {
                // 绘制箭头
                drawArrow(centerX, centerY + 30, centerX - 100, centerY + 120);
                drawArrow(centerX, centerY + 30, centerX, centerY + 120);
                drawArrow(centerX, centerY + 30, centerX + 100, centerY + 120);
                
                // 绘制子问题
                const subProblems = ['子问题1', '子问题2', '子问题3'];
                const positions = [centerX - 100, centerX, centerX + 100];
                
                subProblems.forEach((problem, index) => {
                    const alpha = Math.min(1, (animationFrame - 60 - index * 20) / 40);
                    if (alpha > 0) {
                        ctx.globalAlpha = alpha;
                        ctx.fillStyle = '#f093fb';
                        ctx.fillRect(positions[index] - 50, centerY + 120, 100, 40);
                        ctx.fillStyle = 'white';
                        ctx.fillText(problem, positions[index], centerY + 145);
                        ctx.globalAlpha = 1;
                    }
                });
            }
            
            // 进一步分解
            if (animationFrame > 180) {
                const detailY = centerY + 220;
                ctx.fillStyle = '#fee140';
                ctx.fillRect(centerX - 120, detailY, 80, 30);
                ctx.fillRect(centerX - 20, detailY, 80, 30);
                ctx.fillRect(centerX + 80, detailY, 80, 30);
                
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('具体实现', centerX - 80, detailY + 20);
                ctx.fillText('具体实现', centerX + 20, detailY + 20);
                ctx.fillText('具体实现', centerX + 120, detailY + 20);
            }
        }

        function drawBottomUpAnimation() {
            const baseY = 300;
            
            // 绘制基础组件
            const components = ['组件A', '组件B', '组件C', '组件D'];
            const positions = [150, 250, 350, 450];
            
            components.forEach((comp, index) => {
                const alpha = Math.min(1, (animationFrame - index * 15) / 30);
                if (alpha > 0) {
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = '#fa709a';
                    ctx.fillRect(positions[index] - 30, baseY, 60, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(comp, positions[index], baseY + 25);
                    ctx.globalAlpha = 1;
                }
            });
            
            // 组合过程
            if (animationFrame > 100) {
                // 绘制组合箭头
                positions.forEach(pos => {
                    drawArrow(pos, baseY, canvas.width / 2, baseY - 100);
                });
                
                // 绘制中间层
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(canvas.width / 2 - 80, baseY - 120, 160, 50);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('子系统', canvas.width / 2, baseY - 90);
            }
            
            // 最终系统
            if (animationFrame > 200) {
                drawArrow(canvas.width / 2, baseY - 120, canvas.width / 2, baseY - 220);
                
                ctx.fillStyle = '#00f2fe';
                ctx.fillRect(canvas.width / 2 - 100, baseY - 260, 200, 60);
                ctx.fillStyle = 'white';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('完整系统', canvas.width / 2, baseY - 225);
            }
        }

        function drawFormalAnimation() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制数学公式背景
            ctx.fillStyle = 'rgba(102, 126, 234, 0.1)';
            ctx.fillRect(50, 50, canvas.width - 100, canvas.height - 100);
            
            // 绘制数学符号
            const symbols = ['∀', '∃', '→', '∧', '∨', '¬'];
            symbols.forEach((symbol, index) => {
                const angle = (index / symbols.length) * 2 * Math.PI;
                const radius = 80 + Math.sin(animationFrame * 0.05) * 10;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                
                ctx.fillStyle = '#667eea';
                ctx.font = '24px serif';
                ctx.textAlign = 'center';
                ctx.fillText(symbol, x, y);
            });
            
            // 中心文字
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.fillText('严格数学基础', centerX, centerY);
            
            // 绘制验证过程
            if (animationFrame > 100) {
                ctx.fillStyle = 'rgba(76, 175, 80, 0.8)';
                ctx.fillRect(centerX - 100, centerY + 50, 200, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('形式化验证 ✓', centerX, centerY + 75);
            }
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = '#666';
            ctx.fill();
        }

        function selectOption(element, isCorrect) {
            // 禁用所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('D. 自顶向下')) {
                    opt.classList.add('correct');
                }
            });
            
            // 显示解析
            document.getElementById('explanation').style.display = 'block';
            updateProgress(30);
            
            if (isCorrect) {
                updateProgress(20);
            }
        }

        // 初始化
        showMethod('topDown');
    </script>
</body>
</html>
