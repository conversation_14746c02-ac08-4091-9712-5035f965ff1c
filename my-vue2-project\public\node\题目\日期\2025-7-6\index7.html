<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业应用集成 - 趣味学习</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700&display=swap');

        :root {
            --primary-color: #6C63FF; /* 紫色 */
            --secondary-color: #A0D9B2; /* 浅绿色 */
            --background-color: #F8F9FA; /* 浅灰色 */
            --text-color: #343A40; /* 深灰色 */
            --border-color: #E9ECEF; /* 更浅的灰色 */
            --card-background: #FFFFFF;
            --hover-color: #5A54D1;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            overflow-y: auto;
        }

        #app {
            background-color: var(--card-background);
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            margin: 30px;
            padding: 40px;
            max-width: 1200px;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        #header {
            text-align: center;
            margin-bottom: 20px;
        }

        #header h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        #header p {
            font-size: 1.1em;
            color: #6c757d;
        }

        #question-section {
            background-color: #f0f4f8;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid var(--border-color);
            line-height: 1.8;
        }

        #question-section h2 {
            font-size: 1.8em;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        #question-section .question-text {
            font-size: 1.1em;
            margin-bottom: 20px;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option-button {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px 20px;
            font-size: 1em;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .option-button:hover {
            background-color: #e6eaf0;
            border-color: var(--primary-color);
            transform: translateY(-3px);
        }

        .option-button.correct {
            background-color: #d4edda; /* 浅绿色 */
            border-color: #28a745; /* 绿色 */
            color: #155724;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
        }

        .option-button.wrong {
            background-color: #f8d7da; /* 浅红色 */
            border-color: #dc3545; /* 红色 */
            color: #721c24;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
        }

        #feedback {
            margin-top: 20px;
            font-size: 1.1em;
            font-weight: bold;
            text-align: center;
        }

        #explanation-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .nav-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-button:hover {
            background-color: var(--hover-color);
            transform: translateY(-2px);
        }

        .nav-button.active {
            background-color: var(--secondary-color);
            color: var(--text-color);
            box-shadow: 0 0 10px rgba(160, 217, 178, 0.5);
            transform: scale(1.02);
        }

        #canvas-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 比例 */
            background-color: #f0f4f8;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.05);
        }

        #eai-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: block;
        }

        #concept-explanation {
            background-color: var(--background-color);
            border-radius: 8px;
            padding: 25px;
            border: 1px dashed var(--primary-color);
            line-height: 1.7;
            font-size: 1em;
            min-height: 150px;
        }

        #concept-explanation h3 {
            color: var(--primary-color);
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        @media (min-width: 768px) {
            #app {
                grid-template-columns: 1fr 1fr;
            }
            #question-section {
                grid-column: span 2;
            }
            #explanation-nav {
                grid-column: span 2;
            }
            #canvas-container {
                grid-column: 1;
            }
            #concept-explanation {
                grid-column: 2;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div id="header">
            <h1>企业应用集成 (EAI) 趣味探索</h1>
            <p>你好，学习者！今天我们来一起探索企业应用集成的奥秘，让复杂概念变得生动有趣！</p>
        </div>

        <div id="question-section">
            <h2>思考题：</h2>
            <p class="question-text">企业应用集成是一个战略意义上的方法，它从服务和信息角度将多个信息系统绑定在一起，提供实时交换信息和影响流程的能力。（请作答此空）提供企业之间的信息共享能力，（ ）在用户使用角度能够对集成系统产生一个“整体”的感觉。</p>

            <div class="multiple-choice">
                <h3>问题1 (第一空答案):</h3>
                <div class="options-container">
                    <button class="option-button" data-option="A">A. API集成</button>
                    <button class="option-button" data-option="B">B. 数据集成</button>
                    <button class="option-button" data-option="C">C. 界面集成</button>
                    <button class="option-button" data-option="D">D. 过程集成</button>
                </div>
                <div id="feedback"></div>
            </div>
        </div>

        <div id="explanation-nav">
            <button class="nav-button active" data-concept="EAI">什么是EAI?</button>
            <button class="nav-button" data-concept="DataIntegration">数据集成</button>
            <button class="nav-button" data-concept="InterfaceIntegration">界面集成</button>
            <button class="nav-button" data-concept="ProcessIntegration">过程集成</button>
        </div>

        <div id="canvas-container">
            <canvas id="eai-canvas"></canvas>
        </div>

        <div id="concept-explanation">
            <!-- 解释内容将由 JavaScript 动态加载 -->
        </div>
    </div>

    <script>
        const canvas = document.getElementById('eai-canvas');
        const ctx = canvas.getContext('2d');
        let animationFrameId;
        let currentConcept = 'EAI';
        let animationProgress = 0;
        let lastTimestamp = 0;
        const animationDuration = 3000; // 3秒一个动画周期

        const conceptExplanations = {
            'EAI': {
                title: '什么是EAI?',
                text: `企业应用集成（EAI）就像是企业内部各个系统之间的“超级连接器”，它能让原本各自独立的系统（比如销售系统、库存系统、财务系统）互相“对话”，共享信息，协同工作，从而让整个企业像一个协调的整体高效运转。
                想象一下，没有EAI，每个系统都像一座孤岛，信息传递需要人工搬运或复杂的点对点连接，效率低下还容易出错。EAI就是为这些孤岛架起桥梁，让信息和业务流程像高速公路一样畅通无阻！`
            },
            'DataIntegration': {
                title: '数据集成：信息的血液循环',
                text: `数据集成就是把企业里分散在不同系统、不同数据库里的数据汇集起来，整理成统一的格式，让所有系统都能共享和使用。
                想象一下，销售部门有客户数据，财务部门有支付数据，如果这些数据不互通，你就不知道哪个客户欠费了。数据集成就像一个中央数据仓库，把这些数据汇总，让信息流动起来，提升决策效率！它解决了第一空的“提供信息共享能力”的问题。`
            },
            'InterfaceIntegration': {
                title: '界面集成：统一的“面子工程”',
                text: `界面集成就像是给企业里各种长相不一的应用系统穿上统一的“外套”，让用户通过一个门户就能访问所有应用。
                这就像你不需要记住每个应用复杂的网址和密码，只需要登录一个“超级App”，所有功能都在里面，极大提升了用户的使用体验，让用户感觉所有系统是一个“整体”！这也是我们题目第二空的关键哦！`
            },
            'ProcessIntegration': {
                title: '过程集成：业务流程的“超级大脑”',
                text: `过程集成（也叫业务流程集成）更进一步，它不仅连接系统和数据，更关注业务流程本身。它让跨系统的业务步骤（比如从“客户下单”到“商品发货”到“收款完成”）自动化、标准化。
                这就像一个智能的总指挥，指挥着订单从销售系统流转到库存系统，再到物流系统，确保每一步都自动顺利完成，大大提升了效率，减少了人工干预和错误！它超越了数据和系统，由一系列基于标准的、统一数据格式的工作流组成。`
            }
        };

        const options = [
            { text: "A. API集成", value: "A", correct: false },
            { text: "B. 数据集成", value: "B", correct: true }, // 这是根据题目答案设定的，对应第一空
            { text: "C. 界面集成", value: "C", correct: false },
            { text: "D. 过程集成", value: "D", correct: false }
        ];

        // 设置问题和选项
        const questionSection = document.getElementById('question-section');
        const optionsContainer = questionSection.querySelector('.options-container');
        const feedbackDiv = document.getElementById('feedback');
        const optionButtons = optionsContainer.querySelectorAll('.option-button');

        optionButtons.forEach(button => {
            button.addEventListener('click', () => handleOptionClick(button.dataset.option));
        });

        function handleOptionClick(selectedOption) {
            const correctAnswer = options.find(opt => opt.correct).value;
            optionButtons.forEach(button => {
                button.classList.remove('correct', 'wrong');
                button.disabled = true; // 选中后禁用所有选项
            });

            if (selectedOption === correctAnswer) {
                document.querySelector(`.option-button[data-option="${selectedOption}"]`).classList.add('correct');
                feedbackDiv.textContent = `太棒了！你的选择是正确的！第一空是“数据集成”，它强调了信息共享的基础。`;
                feedbackDiv.style.color = '#28a745';
            } else {
                document.querySelector(`.option-button[data-option="${selectedOption}"]`).classList.add('wrong');
                document.querySelector(`.option-button[data-option="${correctAnswer}"]`).classList.add('correct');
                feedbackDiv.textContent = `别灰心，正确答案是“B. 数据集成”。没关系，我们继续探索这些概念！`;
                feedbackDiv.style.color = '#dc3545';
            }
        }


        // Canvas 绘图函数
        function drawEAI(ctx, progress, colors) {
            const w = ctx.canvas.width;
            const h = ctx.canvas.height;
            ctx.clearRect(0, 0, w, h);

            // EAI 背景网格
            ctx.strokeStyle = colors.border;
            ctx.lineWidth = 1;
            for (let i = 0; i < w; i += 50) { ctx.beginPath(); ctx.moveTo(i, 0); ctx.lineTo(i, h); ctx.stroke(); }
            for (let i = 0; i < h; i += 50) { ctx.beginPath(); ctx.moveTo(0, i); ctx.lineTo(w, i); ctx.stroke(); }

            // 系统方块
            const systems = [
                { x: w * 0.2, y: h * 0.3, label: '销售系统' },
                { x: w * 0.8, y: h * 0.3, label: '库存系统' },
                { x: w * 0.5, y: h * 0.7, label: '财务系统' },
                { x: w * 0.2, y: h * 0.7, label: 'HR系统' }
            ];

            ctx.font = '20px Noto Sans SC';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            systems.forEach((sys, index) => {
                const size = 80;
                ctx.save();
                ctx.translate(sys.x, sys.y);
                const scale = Math.min(1, progress * 2 - (index * 0.1)); // 错开出现
                ctx.scale(scale, scale);
                // 使用计算出的颜色变量
                ctx.fillStyle = `rgba(${parseInt(colors.primary.slice(1, 3), 16)}, ${parseInt(colors.primary.slice(3, 5), 16)}, ${parseInt(colors.primary.slice(5, 7), 16)}, ${Math.min(1, progress * 1.5)})`;
                ctx.fillRect(-size / 2, -size / 2, size, size);
                ctx.strokeStyle = colors.border;
                ctx.lineWidth = 3;
                ctx.strokeRect(-size / 2, -size / 2, size, size);

                ctx.fillStyle = 'white';
                ctx.fillText(sys.label, 0, 0);
                ctx.restore();
            });

            // EAI 中心枢纽
            const hubX = w * 0.5;
            const hubY = h * 0.5;
            const hubRadius = 60;

            ctx.beginPath();
            ctx.arc(hubX, hubY, hubRadius * Math.min(1, progress * 2), 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 165, 0, ${Math.min(1, progress * 1.5)})`; // 橙色枢纽
            ctx.fill();
            ctx.strokeStyle = '#E6A500';
            ctx.lineWidth = 4;
            ctx.stroke();

            ctx.fillStyle = 'white';
            ctx.font = '24px Noto Sans SC';
            ctx.fillText('EAI Hub', hubX, hubY);

            // 连接线
            ctx.strokeStyle = colors.primary;
            ctx.lineWidth = 4;
            ctx.lineCap = 'round';

            systems.forEach((sys, index) => {
                if (progress > 0.5 + index * 0.1) {
                    const lineProgress = Math.min(1, (progress - (0.5 + index * 0.1)) * 3);
                    const dx = hubX - sys.x;
                    const dy = hubY - sys.y;
                    ctx.beginPath();
                    ctx.moveTo(sys.x, sys.y);
                    ctx.lineTo(sys.x + dx * lineProgress, sys.y + dy * lineProgress);
                    ctx.stroke();

                    // 连接线箭头
                    if (lineProgress > 0.9) {
                        const angle = Math.atan2(dy, dx);
                        const arrowSize = 10;
                        ctx.save();
                        ctx.translate(sys.x + dx, sys.y + dy);
                        ctx.rotate(angle);
                        ctx.beginPath();
                        ctx.moveTo(-arrowSize, -arrowSize / 2);
                        ctx.lineTo(0, 0);
                        ctx.lineTo(-arrowSize, arrowSize / 2);
                        ctx.fillStyle = colors.primary;
                        ctx.fill();
                        ctx.restore();
                    }
                }
            });
        }

        function drawDataIntegration(ctx, progress, colors) {
            const w = ctx.canvas.width;
            const h = ctx.canvas.height;
            ctx.clearRect(0, 0, w, h);

            // 数据库 1
            const db1X = w * 0.25;
            const dbY = h * 0.5;
            const dbWidth = 100;
            const dbHeight = 80;
            drawDatabase(ctx, db1X, dbY, dbWidth, dbHeight, '数据库 A', colors.primary);

            // 数据库 2
            const db2X = w * 0.75;
            drawDatabase(ctx, db2X, dbY, dbWidth, dbHeight, '数据库 B', colors.primary);

            // 数据转换区域
            const transformX = w * 0.5;
            const transformY = h * 0.5;
            const transformSize = 80;
            ctx.fillStyle = '#FFC107'; // 黄色代表转换
            ctx.fillRect(transformX - transformSize / 2, transformY - transformSize / 2, transformSize, transformSize);
            ctx.strokeStyle = '#E0A800';
            ctx.lineWidth = 3;
            ctx.strokeRect(transformX - transformSize / 2, transformY - transformSize / 2, transformSize, transformSize);
            ctx.fillStyle = 'white';
            ctx.font = '20px Noto Sans SC';
            ctx.fillText('ETL', transformX, transformY);

            // 数据包动画
            const numPackets = 5;
            for (let i = 0; i < numPackets; i++) {
                const packetProgress = (progress * (numPackets + 1) - i) % (numPackets + 1);
                if (packetProgress >= 0 && packetProgress <= 1) {
                    let currentX, currentY;
                    if (packetProgress < 0.5) { // 从 DB1 到转换区
                        currentX = db1X + (transformX - db1X) * (packetProgress * 2);
                        currentY = dbY;
                    } else { // 从转换区到 DB2
                        currentX = transformX + (db2X - transformX) * ((packetProgress - 0.5) * 2);
                        currentY = dbY;
                    }
                    drawDataPacket(ctx, currentX, currentY, 15, colors.secondary, packetProgress);
                }
            }

            // 流线和箭头
            ctx.strokeStyle = colors.border;
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';

            // DB1 到转换区
            ctx.beginPath();
            ctx.moveTo(db1X + dbWidth / 2, dbY);
            ctx.lineTo(transformX - transformSize / 2, dbY);
            ctx.stroke();
            drawArrowhead(ctx, transformX - transformSize / 2, dbY, 0, colors.border);

            // 转换区到 DB2
            ctx.beginPath();
            ctx.moveTo(transformX + transformSize / 2, dbY);
            ctx.lineTo(db2X - dbWidth / 2, dbY);
            ctx.stroke();
            drawArrowhead(ctx, db2X - dbWidth / 2, dbY, 0, colors.border);
        }

        function drawDatabase(ctx, x, y, w, h, label, color) {
            ctx.fillStyle = color;
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;

            // 基础矩形
            ctx.fillRect(x - w / 2, y - h / 2, w, h);
            ctx.strokeRect(x - w / 2, y - h / 2, w, h);

            // 顶部椭圆
            ctx.beginPath();
            ctx.ellipse(x, y - h / 2, w / 2, h / 4, 0, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();

            // 底部椭圆
            ctx.beginPath();
            ctx.ellipse(x, y + h / 2, w / 2, h / 4, 0, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();

            ctx.fillStyle = 'white';
            ctx.font = '18px Noto Sans SC';
            ctx.fillText(label, x, y);
        }

        function drawDataPacket(ctx, x, y, size, color, progress) {
            ctx.fillStyle = color;
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            const currentSize = size * (1 - Math.abs(0.5 - progress) * 0.5); // 脉冲效果
            ctx.fillRect(x - currentSize / 2, y - currentSize / 2, currentSize, currentSize);
            ctx.strokeRect(x - currentSize / 2, y - currentSize / 2, currentSize, currentSize);
        }

        function drawArrowhead(ctx, x, y, angle, color) {
            const arrowSize = 10;
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-arrowSize, -arrowSize / 2);
            ctx.lineTo(-arrowSize, arrowSize / 2);
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            ctx.restore();
        }


        function drawInterfaceIntegration(ctx, progress, colors) {
            const w = ctx.canvas.width;
            const h = ctx.canvas.height;
            ctx.clearRect(0, 0, w, h);

            const initialAppSize = 100;
            const finalPortalWidth = w * 0.7;
            const finalPortalHeight = h * 0.6;

            const apps = [
                { id: 'app1', x: w * 0.2, y: h * 0.3, label: '销售App', color: '#FF7F50' }, // Coral
                { id: 'app2', x: w * 0.5, y: h * 0.2, label: '财务App', color: '#6A5ACD' }, // SlateBlue
                { id: 'app3', x: w * 0.8, y: h * 0.3, label: '客服App', color: '#20B2AA' }  // LightSeaGreen
            ];

            const targetPortalX = w * 0.5;
            const targetPortalY = h * 0.5;

            // 绘制单个应用，并演示合并过程
            apps.forEach((app, index) => {
                const mergeProgress = Math.min(1, progress * 2 - (index * 0.2)); // 错开合并
                const currentX = app.x + (targetPortalX - app.x) * mergeProgress;
                const currentY = app.y + (targetPortalY - app.y) * mergeProgress;
                const currentSize = initialAppSize * (1 - mergeProgress * 0.5); // 稍微缩小

                // 使用计算出的颜色
                ctx.fillStyle = `rgba(${parseInt(app.color.slice(1, 3), 16)}, ${parseInt(app.color.slice(3, 5), 16)}, ${parseInt(app.color.slice(5, 7), 16)}, ${1 - mergeProgress * 0.8})`;
                ctx.strokeStyle = app.color;
                ctx.lineWidth = 3;
                ctx.fillRect(currentX - currentSize / 2, currentY - currentSize / 2, currentSize, currentSize);
                ctx.strokeRect(currentX - currentSize / 2, currentY - currentSize / 2, currentSize, currentSize);

                ctx.fillStyle = 'white';
                ctx.font = '16px Noto Sans SC';
                ctx.textAlign = 'center';
                ctx.fillText(app.label, currentX, currentY + currentSize / 2 + 20);
            });

            // 绘制统一门户
            if (progress > 0.5) {
                const portalAppearProgress = Math.min(1, (progress - 0.5) * 2);
                const portalWidth = finalPortalWidth * portalAppearProgress;
                const portalHeight = finalPortalHeight * portalAppearProgress;

                // 使用计算出的颜色
                ctx.fillStyle = `rgba(${parseInt(colors.primary.slice(1, 3), 16)}, ${parseInt(colors.primary.slice(3, 5), 16)}, ${parseInt(colors.primary.slice(5, 7), 16)}, ${portalAppearProgress * 0.8})`;
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 5;
                ctx.fillRect(targetPortalX - portalWidth / 2, targetPortalY - portalHeight / 2, portalWidth, portalHeight);
                ctx.strokeRect(targetPortalX - portalWidth / 2, targetPortalY - portalHeight / 2, portalWidth, portalHeight);

                ctx.fillStyle = 'white';
                ctx.font = '30px Noto Sans SC';
                ctx.fillText('统一用户门户', targetPortalX, targetPortalY);
                ctx.font = '18px Noto Sans SC';
                ctx.fillText('（一站式体验！）', targetPortalX, targetPortalY + 30);
            }
        }


        function drawProcessIntegration(ctx, progress, colors) {
            const w = ctx.canvas.width;
            const h = ctx.canvas.height;
            ctx.clearRect(0, 0, w, h);

            const nodes = [
                { x: w * 0.15, y: h * 0.5, label: '订单创建 (CRM)' },
                { x: w * 0.4, y: h * 0.5, label: '库存检查 (ERP)' },
                { x: w * 0.65, y: h * 0.5, label: '发货准备 (WMS)' },
                { x: w * 0.85, y: h * 0.5, label: '物流跟踪 (TMS)' }
            ];

            const nodeSize = 120; // 增加尺寸以容纳文本
            const nodeHeight = 80;

            // 绘制节点
            ctx.font = '18px Noto Sans SC';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            nodes.forEach(node => {
                ctx.fillStyle = colors.primary;
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 3;
                ctx.fillRect(node.x - nodeSize / 2, node.y - nodeHeight / 2, nodeSize, nodeHeight);
                ctx.strokeRect(node.x - nodeSize / 2, node.y - nodeHeight / 2, nodeSize, nodeHeight);
                ctx.fillStyle = 'white';
                ctx.fillText(node.label, node.x, node.y);
            });

            // 绘制箭头和流程流
            ctx.strokeStyle = colors.secondary;
            ctx.lineWidth = 5;
            ctx.lineCap = 'round';

            const flowDotRadius = 10;

            for (let i = 0; i < nodes.length - 1; i++) {
                const startNode = nodes[i];
                const endNode = nodes[i + 1];

                // 绘制静态箭头背景
                ctx.beginPath();
                ctx.moveTo(startNode.x + nodeSize / 2, startNode.y);
                ctx.lineTo(endNode.x - nodeSize / 2, endNode.y);
                ctx.stroke();
                drawArrowhead(ctx, endNode.x - nodeSize / 2, endNode.y, 0, colors.secondary); // 静态路径的箭头
            }

            // 动画流点
            ctx.fillStyle = '#FF4500'; // 橙红色代表活跃流
            ctx.strokeStyle = '#CC3700';
            ctx.lineWidth = 2;

            for (let i = 0; i < 3; i++) { // 三个流动的点
                const dotProgress = (progress * 1.5 + i * 0.2) % 1;
                // 计算沿整个路径的位置
                const pathStartX = nodes[0].x + nodeSize / 2;
                const pathEndX = nodes[nodes.length - 1].x - nodeSize / 2;
                const totalPathLength = pathEndX - pathStartX;
                const dotX = pathStartX + totalPathLength * dotProgress;

                ctx.beginPath();
                ctx.arc(dotX, h * 0.5, flowDotRadius * (1 + Math.sin(dotProgress * Math.PI * 2) * 0.2), 0, Math.PI * 2); // 脉冲效果
                ctx.fill();
                ctx.stroke();
            }

            // 文本 "端到端业务流程"
            ctx.fillStyle = colors.text;
            ctx.font = '24px Noto Sans SC';
            ctx.fillText('端到端业务流程管理', w * 0.5, h * 0.2);
            ctx.font = '16px Noto Sans SC';
            ctx.fillText('（自动化、标准化、协作共赢！）', w * 0.5, h * 0.2 + 30);
        }

        // 全局动画循环和概念切换
        function resizeCanvas() {
            const container = document.getElementById('canvas-container');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            lastTimestamp = 0; // 调整大小时重置时间戳以防止跳帧
            animate();
        }

        function animate(timestamp = 0) {
            if (!lastTimestamp) lastTimestamp = timestamp;
            const deltaTime = timestamp - lastTimestamp;
            lastTimestamp = timestamp;

            animationProgress = (animationProgress + (deltaTime / animationDuration)) % 1;

            ctx.clearRect(0, 0, canvas.width, canvas.height); // 清除画布

            // 获取 CSS 变量作为颜色
            const style = getComputedStyle(document.body);
            const colors = {
                primary: style.getPropertyValue('--primary-color').trim(),
                secondary: style.getPropertyValue('--secondary-color').trim(),
                text: style.getPropertyValue('--text-color').trim(),
                border: style.getPropertyValue('--border-color').trim(),
                cardBg: style.getPropertyValue('--card-background').trim()
            };

            switch (currentConcept) {
                case 'EAI':
                    drawEAI(ctx, animationProgress, colors);
                    break;
                case 'DataIntegration':
                    drawDataIntegration(ctx, animationProgress, colors);
                    break;
                case 'InterfaceIntegration':
                    drawInterfaceIntegration(ctx, animationProgress, colors);
                    break;
                case 'ProcessIntegration':
                    drawProcessIntegration(ctx, animationProgress, colors);
                    break;
            }
            animationFrameId = requestAnimationFrame(animate);
        }

        // 导航逻辑
        const navButtons = document.querySelectorAll('.nav-button');
        const conceptExplanationDiv = document.getElementById('concept-explanation');

        navButtons.forEach(button => {
            button.addEventListener('click', () => {
                navButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                currentConcept = button.dataset.concept;
                animationProgress = 0; // 重置动画进度
                lastTimestamp = 0; // 重置时间戳以平滑重启

                const explanation = conceptExplanations[currentConcept];
                conceptExplanationDiv.innerHTML = `<h3>${explanation.title}</h3><p>${explanation.text.replace(/\n/g, '<br>')}</p>`; // 处理换行
            });
        });

        // 初始化设置
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas(); // 设置初始画布大小并启动动画

        // 初始解释内容显示
        conceptExplanationDiv.innerHTML = `<h3>${conceptExplanations['EAI'].title}</h3><p>${conceptExplanations['EAI'].text.replace(/\n/g, '<br>')}</p>`;

    </script>
</body>
</html> 