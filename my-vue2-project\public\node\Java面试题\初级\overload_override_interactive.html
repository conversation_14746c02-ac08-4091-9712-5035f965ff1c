<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 重载(Overload) vs 重写(Override) 动画交互解释</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            width: 100%;
        }
        h1, h2 {
            text-align: center;
            color: #1a237e;
        }
        .card {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 25px;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        canvas {
            display: block;
            margin: 15px auto;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
        }
        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button:hover {
            background-color: #303f9f;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        button:active {
            background-color: #283593;
        }
        p {
            font-size: 1.1em;
            text-align: center;
        }
        .intro {
            font-size: 1.2em;
            color: #555;
            text-align: center;
            margin-bottom: 30px;
        }
        .summary-box {
            background-color: #e8eaf6;
            border-left: 5px solid #3f51b5;
            padding: 15px;
            margin-top: 20px;
            border-radius: 0 8px 8px 0;
        }
        .summary-box strong {
            color: #1a237e;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3f51b5;
            color: white;
            text-align: center;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            margin: 0;
            width: 400px;
            border: 1px solid #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java 重载(Overload) vs 重写(Override)</h1>
        <p class="intro">你好！让我们用动画和互动来轻松理解Java中两个重要的概念：<strong>重载</strong>和<strong>重写</strong>。</p>

        <div class="card">
            <h2>1. 重写 (Override) - 子类升级父类的"旧技能"</h2>
            <p><strong>重写</strong>就像儿子继承了父亲的<code>sayHello</code>技能，但他觉得可以做得更好，<br>于是他把这个技能的内容"升级"了，但技能的名字和用法（参数）保持不变。</p>
            <canvas id="overrideCanvas" width="800" height="300"></canvas>
            <div class="controls">
                <button id="playOverride">▶️ 播放动画</button>
                <pre id="overrideCode">// 点击播放按钮查看代码演示</pre>
            </div>
            <div class="summary-box">
                <strong>核心思想：</strong>发生在<strong>父子类</strong>之间，是对父类方法的<strong>重新实现</strong>。<br>
                <strong>规则：</strong>方法名、参数列表必须<strong>完全相同</strong>。就像是对旧功能的版本更新。
            </div>
        </div>

        <div class="card">
            <h2>2. 重载 (Overload) - 同一个人掌握多种"同名技能"</h2>
            <p><strong>重载</strong>就像一个计算器，它有好几个都叫<code>add</code>的技能。<br>一个用来加两个数，一个用来加三个数。虽然技能同名，但用法（参数）不同。</p>
            <canvas id="overloadCanvas" width="800" height="250"></canvas>
            <div class="controls">
                <button id="playOverload">▶️ 播放动画</button>
                <pre id="overloadCode">// 点击播放按钮查看代码演示</pre>
            </div>
            <div class="summary-box">
                <strong>核心思想：</strong>发生在<strong>同一个类</strong>中，让一个方法名能有<strong>多种用途</strong>。<br>
                <strong>规则：</strong>方法名<strong>相同</strong>，但参数列表（个数、类型或顺序）必须<strong>不同</strong>。
            </div>
        </div>

        <div class="card">
            <h2>总结：一张表看懂区别</h2>
            <table>
                <thead>
                    <tr>
                        <th>比较点</th>
                        <th>重写 (Override)</th>
                        <th>重载 (Overload)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>发生范围</strong></td>
                        <td>父类与子类之间</td>
                        <td>同一个类中</td>
                    </tr>
                    <tr>
                        <td><strong>方法名</strong></td>
                        <td>必须相同</td>
                        <td>必须相同</td>
                    </tr>
                    <tr>
                        <td><strong>参数列表</strong></td>
                        <td>必须相同</td>
                        <td>必须不同</td>
                    </tr>
                    <tr>
                        <td><strong>返回类型</strong></td>
                        <td>可兼容（子类可返回父类返回类型的子类型）</td>
                        <td>无要求，可相同也可不同</td>
                    </tr>
                     <tr>
                        <td><strong>核心目的</strong></td>
                        <td>实现运行时的多态性（子类替换父类）</td>
                        <td>实现编译时的多态性（方法选择）</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

<script>
// --- Canvas 绘制工具 ---
function drawClass(ctx, x, y, width, height, name, color = '#2196f3') {
    ctx.strokeStyle = color;
    ctx.fillStyle = color;
    ctx.lineWidth = 2;
    ctx.font = 'bold 16px sans-serif';
    ctx.textAlign = 'center';

    ctx.strokeRect(x, y, width, height);
    ctx.fillText(name, x + width / 2, y + 25);
    ctx.beginPath();
    ctx.moveTo(x, y + 35);
    ctx.lineTo(x + width, y + 35);
    ctx.stroke();
}

function drawMethod(ctx, x, y, width, height, name, color = '#4caf50', textColor = 'white') {
    ctx.fillStyle = color;
    ctx.fillRect(x, y, width, height);

    ctx.fillStyle = textColor;
    ctx.font = '14px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(name, x + width / 2, y + height / 2 + 5);
}

function drawText(ctx, text, x, y, color = 'black', size = 14) {
    ctx.fillStyle = color;
    ctx.font = `${size}px sans-serif`;
    ctx.textAlign = 'center';
    ctx.fillText(text, x, y);
}

function drawArrow(ctx, fromX, fromY, toX, toY, text) {
    const headlen = 10;
    const angle = Math.atan2(toY - fromY, toX - fromX);
    ctx.strokeStyle = '#e91e63';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
    ctx.stroke();

    if (text) {
        ctx.fillStyle = '#e91e63';
        ctx.font = 'italic 14px sans-serif';
        ctx.save();
        ctx.translate((fromX + toX) / 2, (fromY + toY) / 2);
        ctx.rotate(angle);
        ctx.textAlign = 'center';
        ctx.fillText(text, 0, -10);
        ctx.restore();
    }
}

// --- 重写(Override)动画 ---
const overrideCanvas = document.getElementById('overrideCanvas');
const overrideCtx = overrideCanvas.getContext('2d');
const overrideCodeEl = document.getElementById('overrideCode');
const playOverrideBtn = document.getElementById('playOverride');

const overrideStates = [
    {
        draw: () => {
            drawClass(overrideCtx, 50, 50, 200, 150, 'class Father');
            drawText(overrideCtx, '1. 首先，我们有一个父类 Father', 400, 30);
        },
        code: `public class Father {\n    // ...\n}`
    },
    {
        draw: () => {
            drawClass(overrideCtx, 50, 50, 200, 150, 'class Father');
            drawMethod(overrideCtx, 75, 100, 150, 30, 'sayHello()');
            drawText(overrideCtx, '2. Father 有一个 sayHello 方法', 400, 30);
        },
        code: `public class Father {\n    public void sayHello() {\n        System.out.println("Hello from Father");\n    }\n}`
    },
    {
        draw: () => {
            drawClass(overrideCtx, 50, 50, 200, 150, 'class Father');
            drawMethod(overrideCtx, 75, 100, 150, 30, 'sayHello()');
            drawClass(overrideCtx, 550, 50, 200, 150, 'class Son', '#ff9800');
            drawArrow(550, 125, 250, 125, 'extends');
            drawText(overrideCtx, '3. Son 继承了 Father', 400, 30);
        },
        code: `public class Son extends Father {\n    // 继承了 sayHello()\n}`
    },
    {
        draw: () => {
            drawClass(overrideCtx, 50, 50, 200, 150, 'class Father');
            drawMethod(overrideCtx, 75, 100, 150, 30, 'sayHello()');
            drawClass(overrideCtx, 550, 50, 200, 150, 'class Son', '#ff9800');
            drawArrow(550, 125, 250, 125, 'extends');
            drawMethod(overrideCtx, 575, 100, 150, 30, 'sayHello()', '#f44336');
            drawText(overrideCtx, '4. Son 决定重写 sayHello 方法', 400, 30);
        },
        code: `public class Son extends Father {\n    @Override\n    public void sayHello() {\n        System.out.println("HELLO from Son!");\n    }\n}`
    },
    {
        draw: () => {
            drawClass(overrideCtx, 50, 50, 200, 150, 'class Father');
            drawMethod(overrideCtx, 75, 100, 150, 30, 'sayHello()');
            drawClass(overrideCtx, 550, 50, 200, 150, 'class Son', '#ff9800');
            drawArrow(550, 125, 250, 125, 'extends');
            drawMethod(overrideCtx, 575, 100, 150, 30, 'sayHello()', '#f44336');
            drawText(overrideCtx, '@Override', 650, 90, '#f44336', 14);
            drawText(overrideCtx, '完成! 子类有了自己的实现', 400, 280);
        },
        code: `public class Son extends Father {\n    // @Override 注解表示这是一个重写方法\n    @Override\n    public void sayHello() {\n        System.out.println("HELLO from Son!");\n    }\n}`
    }
];

function animate(canvas, ctx, states) {
    let currentState = 0;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    const codeEl = canvas.id.includes('override') ? overrideCodeEl : overloadCodeEl;
    
    const interval = setInterval(() => {
        if (currentState >= states.length) {
            clearInterval(interval);
            return;
        }
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        states[currentState].draw();
        codeEl.textContent = states[currentState].code;
        currentState++;
    }, 1500);
}

playOverrideBtn.addEventListener('click', () => animate(overrideCanvas, overrideCtx, overrideStates));
overrideCodeEl.textContent = 'public class Father {\n    // ...\n}\n\nclass Son extends Father {\n    // ...\n}';


// --- 重载(Overload)动画 ---
const overloadCanvas = document.getElementById('overloadCanvas');
const overloadCtx = overloadCanvas.getContext('2d');
const overloadCodeEl = document.getElementById('overloadCode');
const playOverloadBtn = document.getElementById('playOverload');

const overloadStates = [
    {
        draw: () => {
            drawClass(overloadCtx, 300, 20, 300, 210, 'class Calculator');
            drawText(overloadCtx, '1. 我们有一个计算器类', 450, 240);
        },
        code: 'public class Calculator {\n    // ...\n}'
    },
    {
        draw: () => {
            drawClass(overloadCtx, 300, 20, 300, 210, 'class Calculator');
            drawMethod(overloadCtx, 325, 60, 250, 30, 'add(int a, int b)');
            drawText(overloadCtx, '2. 它有一个 add 方法, 用于加两个整数', 450, 240);
        },
        code: 'public class Calculator {\n    public int add(int a, int b) {\n        return a + b;\n    }\n}'
    },
    {
        draw: () => {
            drawClass(overloadCtx, 300, 20, 300, 210, 'class Calculator');
            drawMethod(overloadCtx, 325, 60, 250, 30, 'add(int a, int b)');
            drawMethod(overloadCtx, 325, 100, 250, 30, 'add(int a, int b, int c)', '#009688');
            drawText(overloadCtx, '3. 现在重载一个新方法, 用于加三个整数', 450, 240);
        },
        code: 'public class Calculator {\n    public int add(int a, int b) { ... }\n\n    // 方法名相同，参数个数不同\n    public int add(int a, int b, int c) {\n        return a + b + c;\n    }\n}'
    },
    {
        draw: () => {
            drawClass(overloadCtx, 300, 20, 300, 210, 'class Calculator');
            drawMethod(overloadCtx, 325, 60, 250, 30, 'add(int a, int b)');
            drawMethod(overloadCtx, 325, 100, 250, 30, 'add(int a, int b, int c)', '#009688');
            drawMethod(overloadCtx, 325, 140, 250, 30, 'add(double a, double b)', '#ff5722');
            drawText(overloadCtx, '4. 再重载一个, 用于加两个小数', 450, 240);
        },
        code: 'public class Calculator {\n    public int add(int a, int b) { ... }\n\n    public int add(int a, int b, int c) { ... }\n\n    // 方法名相同，参数类型不同\n    public double add(double a, double b) {\n        return a + b;\n    }\n}'
    }
];

playOverloadBtn.addEventListener('click', () => animate(overloadCanvas, overloadCtx, overloadStates));
overloadCodeEl.textContent = 'public class Calculator {\n    // ...\n}';

</script>
</body>
</html> 