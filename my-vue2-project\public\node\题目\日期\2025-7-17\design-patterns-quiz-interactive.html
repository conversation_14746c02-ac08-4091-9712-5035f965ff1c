<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式互动学习 - 图像处理软件案例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .scenario-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .scenario-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .patterns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .pattern-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .pattern-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pattern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .pattern-card:hover::before {
            transform: scaleX(1);
        }

        .pattern-title {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .pattern-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            background: #f8f9fa;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-canvas:hover {
            background: #e9ecef;
        }

        .interactive-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }

        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
        }

        .quiz-option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .quiz-option.correct {
            border-color: #27ae60;
            background: #d5f4e6;
            animation: correctAnswer 0.5s ease;
        }

        .quiz-option.wrong {
            border-color: #e74c3c;
            background: #fdf2f2;
            animation: wrongAnswer 0.5s ease;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .explanation.show {
            display: block;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1>设计模式互动学习</h1>
            <p>通过图像处理软件案例，轻松掌握三大核心设计模式</p>
        </div>

        <div class="scenario-card">
            <h2 class="scenario-title">📸 图像处理软件开发场景</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; color: #555; text-align: center;">
                某软件公司要开发一款图像处理软件，需要解决三个核心问题：<br>
                <strong>1. 撤销重做功能</strong> | <strong>2. 复杂逻辑处理</strong> | <strong>3. 算法灵活替换</strong>
            </p>
        </div>

        <div class="patterns-grid">
            <!-- 命令模式 -->
            <div class="pattern-card" onclick="showCommandDemo()">
                <h3 class="pattern-title">🔄 命令模式 (Command Pattern)</h3>
                <p class="pattern-description">
                    将请求封装成对象，支持撤销、重做、排队等操作。就像遥控器的每个按钮都是一个命令！
                </p>
                <canvas class="demo-canvas" id="commandCanvas" width="300" height="200"></canvas>
                <button class="interactive-button" onclick="executeCommand()">执行命令</button>
                <button class="interactive-button" onclick="undoCommand()">撤销</button>
                <button class="interactive-button" onclick="redoCommand()">重做</button>
            </div>

            <!-- 状态模式 -->
            <div class="pattern-card" onclick="showStateDemo()">
                <h3 class="pattern-title">🎭 状态模式 (State Pattern)</h3>
                <p class="pattern-description">
                    根据对象状态改变行为，将复杂的条件逻辑分散到不同状态类中。像变色龙一样智能适应！
                </p>
                <canvas class="demo-canvas" id="stateCanvas" width="300" height="200"></canvas>
                <button class="interactive-button" onclick="changeState('bright')">明亮照片</button>
                <button class="interactive-button" onclick="changeState('dark')">昏暗照片</button>
                <button class="interactive-button" onclick="changeState('blurry')">模糊照片</button>
            </div>

            <!-- 策略模式 -->
            <div class="pattern-card" onclick="showStrategyDemo()">
                <h3 class="pattern-title">🎯 策略模式 (Strategy Pattern)</h3>
                <p class="pattern-description">
                    定义算法族，让它们可以互相替换。就像工具箱里的不同工具，按需选择！
                </p>
                <canvas class="demo-canvas" id="strategyCanvas" width="300" height="200"></canvas>
                <button class="interactive-button" onclick="applyStrategy('blur')">模糊算法</button>
                <button class="interactive-button" onclick="applyStrategy('sharpen')">锐化算法</button>
                <button class="interactive-button" onclick="applyStrategy('vintage')">复古算法</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">🎯 知识检测</h2>
            <div class="quiz-question">
                根据题目描述，为了封装图像操作与照片特征之间的复杂逻辑关系，应该采用哪种设计模式？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, true)">
                    A. 状态模式 - 根据照片特征（状态）选择不同的处理逻辑
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    B. 适配器模式 - 用于接口转换，不适合处理复杂逻辑关系
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    C. 组合模式 - 用于树形结构，不适合条件逻辑处理
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    D. 单例模式 - 确保只有一个实例，与逻辑处理无关
                </div>
            </div>
            <div class="explanation" id="explanation">
                <h4>💡 详细解析</h4>
                <p><strong>正确答案：A. 状态模式</strong></p>
                <p>状态模式将每一个条件分支放入一个独立的类中，可以根据对象自身的情况将对象的状态作为一个对象，这一对象可以不依赖于其他对象而独立变化。在图像处理中，不同的照片特征（明亮、昏暗、模糊等）就是不同的状态，每种状态对应不同的处理逻辑。</p>
                <ul style="margin-top: 15px; padding-left: 20px;">
                    <li><strong>命令模式</strong>：适合撤销重做功能</li>
                    <li><strong>状态模式</strong>：适合复杂逻辑关系处理</li>
                    <li><strong>策略模式</strong>：适合算法的灵活选择与替换</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let commandHistory = [];
        let commandIndex = -1;
        let currentState = 'normal';
        let currentStrategy = 'original';

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (Math.random() * 3 + 3) + 's';
                container.appendChild(element);
            }
        }

        // 命令模式演示
        function showCommandDemo() {
            const canvas = document.getElementById('commandCanvas');
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('命令历史记录', canvas.width/2, 30);

            // 绘制命令历史
            commandHistory.forEach((cmd, index) => {
                const y = 60 + index * 25;
                const isExecuted = index <= commandIndex;

                ctx.fillStyle = isExecuted ? '#27ae60' : '#bdc3c7';
                ctx.fillRect(50, y - 15, 200, 20);

                ctx.fillStyle = 'white';
                ctx.font = '12px SF Pro Display';
                ctx.fillText(cmd, 150, y - 2);
            });

            // 绘制指针
            if (commandIndex >= 0) {
                const y = 60 + commandIndex * 25;
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.moveTo(30, y - 5);
                ctx.lineTo(45, y - 5);
                ctx.lineTo(40, y);
                ctx.lineTo(45, y + 5);
                ctx.lineTo(30, y + 5);
                ctx.fill();
            }
        }

        function executeCommand() {
            const commands = ['调整亮度', '应用滤镜', '裁剪图片', '添加文字', '旋转图片'];
            const newCommand = commands[Math.floor(Math.random() * commands.length)];

            // 如果当前不在历史末尾，删除后面的命令
            if (commandIndex < commandHistory.length - 1) {
                commandHistory = commandHistory.slice(0, commandIndex + 1);
            }

            commandHistory.push(newCommand);
            commandIndex = commandHistory.length - 1;
            showCommandDemo();
        }

        function undoCommand() {
            if (commandIndex >= 0) {
                commandIndex--;
                showCommandDemo();
            }
        }

        function redoCommand() {
            if (commandIndex < commandHistory.length - 1) {
                commandIndex++;
                showCommandDemo();
            }
        }

        // 状态模式演示
        function showStateDemo() {
            const canvas = document.getElementById('stateCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制照片框
            ctx.fillStyle = '#34495e';
            ctx.fillRect(50, 50, 200, 120);

            // 根据状态绘制不同效果
            switch(currentState) {
                case 'bright':
                    ctx.fillStyle = '#f39c12';
                    ctx.fillRect(60, 60, 180, 100);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('明亮照片', canvas.width/2, 30);
                    ctx.fillText('自动降低曝光', canvas.width/2, 190);
                    break;
                case 'dark':
                    ctx.fillStyle = '#2c3e50';
                    ctx.fillRect(60, 60, 180, 100);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('昏暗照片', canvas.width/2, 30);
                    ctx.fillText('自动提升亮度', canvas.width/2, 190);
                    break;
                case 'blurry':
                    // 模糊效果
                    for(let i = 0; i < 5; i++) {
                        ctx.fillStyle = `rgba(52, 152, 219, ${0.2})`;
                        ctx.fillRect(60 + i*2, 60 + i*2, 180, 100);
                    }
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('模糊照片', canvas.width/2, 30);
                    ctx.fillText('自动锐化处理', canvas.width/2, 190);
                    break;
                default:
                    ctx.fillStyle = '#3498db';
                    ctx.fillRect(60, 60, 180, 100);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('普通照片', canvas.width/2, 30);
                    ctx.fillText('标准处理', canvas.width/2, 190);
            }
        }

        function changeState(newState) {
            currentState = newState;
            showStateDemo();
        }

        // 策略模式演示
        function showStrategyDemo() {
            const canvas = document.getElementById('strategyCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制原始图片
            ctx.fillStyle = '#3498db';
            ctx.fillRect(50, 50, 200, 120);

            // 应用不同策略效果
            switch(currentStrategy) {
                case 'blur':
                    // 模糊效果
                    for(let i = 0; i < 3; i++) {
                        ctx.fillStyle = `rgba(52, 152, 219, ${0.3})`;
                        ctx.fillRect(50 + i*3, 50 + i*3, 200, 120);
                    }
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('模糊算法', canvas.width/2, 30);
                    break;
                case 'sharpen':
                    // 锐化效果
                    ctx.strokeStyle = '#e74c3c';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(50, 50, 200, 120);
                    ctx.strokeRect(52, 52, 196, 116);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('锐化算法', canvas.width/2, 30);
                    break;
                case 'vintage':
                    // 复古效果
                    ctx.fillStyle = '#d35400';
                    ctx.fillRect(50, 50, 200, 120);
                    ctx.fillStyle = 'rgba(243, 156, 18, 0.3)';
                    ctx.fillRect(50, 50, 200, 120);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('复古算法', canvas.width/2, 30);
                    break;
                default:
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px SF Pro Display';
                    ctx.textAlign = 'center';
                    ctx.fillText('原始图片', canvas.width/2, 30);
            }

            ctx.fillText('点击按钮切换算法', canvas.width/2, 190);
        }

        function applyStrategy(strategy) {
            currentStrategy = strategy;
            showStrategyDemo();
        }

        // 答题功能
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.startsWith('A.')) {
                    option.classList.add('correct');
                }
            });

            setTimeout(() => {
                document.getElementById('explanation').classList.add('show');
            }, 1000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            showCommandDemo();
            showStateDemo();
            showStrategyDemo();
        });
    </script>
</body>
</html>
