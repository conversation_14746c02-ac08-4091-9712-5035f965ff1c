<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记编辑器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .note-content {
            border: 1px solid #ddd;
            padding: 20px;
            min-height: 300px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
            cursor: text;
            user-select: text; /* 允许文本选择 */
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .edit-popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border: 1px solid #ccc;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            z-index: 1000;
            flex-direction: column;
            gap: 10px;
            max-width: 400px;
            width: 90%;
        }
        .edit-popup textarea {
            width: 100%;
            min-height: 80px;
            resize: vertical;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1em;
        }
        .edit-popup button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
        }
        .edit-popup button.save {
            background-color: #4CAF50;
            color: white;
        }
        .edit-popup button.save:hover {
            background-color: #45a049;
        }
        .edit-popup button.cancel {
            background-color: #f44336;
            color: white;
        }
        .edit-popup button.cancel:hover {
            background-color: #da190b;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的笔记</h1>
        <div class="note-content" id="noteContent">
            这是一段可以进行编辑的笔记内容。
            请尝试用鼠标左键选中一些文字，然后释放鼠标左键。
            选中文字后，会弹出一个编辑框，你可以在其中修改选中的内容。
            修改完成后，点击“保存”按钮，原来的文字就会被替换。
            这是一个多行的例子，你可以选中任何一部分文字进行编辑。
            希望这个功能符合你的需求！
        </div>

        <div class="overlay" id="overlay"></div>
        <div class="edit-popup" id="editPopup">
            <h2>编辑选中内容</h2>
            <textarea id="editTextarea"></textarea>
            <div style="display: flex; justify-content: flex-end; gap: 10px;">
                <button class="save" id="saveButton">保存</button>
                <button class="cancel" id="cancelButton">取消</button>
            </div>
        </div>
    </div>

    <script>
        const noteContent = document.getElementById('noteContent');
        const editPopup = document.getElementById('editPopup');
        const editTextarea = document.getElementById('editTextarea');
        const saveButton = document.getElementById('saveButton');
        const cancelButton = document.getElementById('cancelButton');
        const overlay = document.getElementById('overlay');

        let currentSelection = null; // 用于存储当前的文本选中范围

        noteContent.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            const selectedText = selection.toString();

            if (selectedText.length > 0 && noteContent.contains(selection.anchorNode)) {
                // 确保选中内容在noteContent内部
                currentSelection = {
                    range: selection.getRangeAt(0).cloneContents(), // 克隆选区内容，以备替换
                    originalRange: selection.getRangeAt(0).cloneRange(), // 原始范围，用于后续替换
                    text: selectedText
                };
                editTextarea.value = selectedText;
                editPopup.style.display = 'flex';
                overlay.style.display = 'block';
                editTextarea.focus();
            } else {
                // 如果没有选中内容或者选中内容不在noteContent内部，隐藏弹出框
                editPopup.style.display = 'none';
                overlay.style.display = 'none';
            }
        });

        saveButton.addEventListener('click', () => {
            if (currentSelection) {
                const newText = editTextarea.value;
                const originalRange = currentSelection.originalRange;

                // 删除原始选中的内容
                originalRange.deleteContents();

                // 创建新的文本节点并插入
                originalRange.insertNode(document.createTextNode(newText));

                // 清除选中
                window.getSelection().removeAllRanges();

                currentSelection = null;
                editPopup.style.display = 'none';
                overlay.style.display = 'none';
            }
        });

        cancelButton.addEventListener('click', () => {
            currentSelection = null;
            window.getSelection().removeAllRanges(); // 清除选中
            editPopup.style.display = 'none';
            overlay.style.display = 'none';
        });

        // 点击覆盖层也隐藏弹出框
        overlay.addEventListener('click', () => {
            cancelButton.click(); // 调用取消按钮的点击事件
        });

        // 初始化一些示例内容
        noteContent.innerHTML = `
            <p>这是一段可以进行编辑的笔记内容。</p>
            <p>请尝试用鼠标左键选中一些文字，然后释放鼠标左键。</p>
            <p>选中文字后，会弹出一个编辑框，你可以在其中修改选中的内容。</p>
            <p>修改完成后，点击“保存”按钮，原来的文字就会被替换。</p>
            <p>这是一个多行的例子，你可以选中任何一部分文字进行编辑。</p>
            <p>希望这个功能符合你的需求！</p>
        `;
    </script>
</body>
</html> 