<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可测试性质量属性深度学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: fadeInUp 1s ease-out;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .section-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border: 3px solid #e0e0e0;
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        canvas:hover {
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            transform: translateY(-8px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 35px;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(-2px);
        }

        .explanation {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 30px;
            margin: 25px 0;
            border-left: 6px solid #667eea;
            animation: slideInLeft 0.8s ease-out;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .explanation h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 10px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }

        .quiz-section::before {
            content: '🎯';
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 3rem;
            opacity: 0.3;
        }

        .quiz-question {
            font-size: 1.5rem;
            color: #2d3436;
            margin-bottom: 30px;
            font-weight: bold;
            line-height: 1.6;
            background: rgba(255,255,255,0.8);
            padding: 25px;
            border-radius: 15px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            background: white;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            text-align: center;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .quiz-option.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .status-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 20px 30px;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255,255,255,0.3);
            border-radius: 6px;
            margin: 25px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            transition: width 0.8s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .concept-card {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 20px;
            padding: 30px;
            margin: 25px 0;
            border-left: 6px solid #2196f3;
            position: relative;
        }

        .concept-card::before {
            content: '💡';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 2rem;
        }

        .highlight {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="status-panel">
        <div>🎓 学习进度: <span id="scoreText">0%</span></div>
        <div style="margin-top: 10px;">🏆 当前阶段: <span id="stageText">准备开始</span></div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🔧 可测试性质量属性</h1>
            <p class="subtitle">
                深度理解软件架构中的可测试性概念<br>
                通过车库门系统的远程诊断场景学习可观测性与可控制性
            </p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- 概念解释区 -->
        <div class="section">
            <h2 class="section-title">📚 可测试性质量属性详解</h2>
            
            <div class="concept-card">
                <h3>🎯 什么是可测试性？</h3>
                <p><strong>可测试性（Testability）</strong>是指软件系统能够被有效测试、调试、监控和诊断的能力。它是软件质量保证的重要基础。</p>
            </div>

            <div class="highlight">
                <h4>🔍 可测试性的两大核心要素：</h4>
                <p><strong>1. 可观测性（Observability）</strong>：系统能够暴露其内部状态和行为，便于观察和监控</p>
                <p><strong>2. 可控制性（Controllability）</strong>：系统能够被外部控制和操作，便于测试和调试</p>
            </div>

            <div class="explanation">
                <h3>🏠 车库门系统的可测试性需求</h3>
                <p>题目中提到："系统需要为部署在远程PC机上的智能家居系统留有控制接口，并支持在智能家居系统中对该系统进行远程错误诊断与调试"</p>
                <p><strong>这个需求体现了可测试性的两个方面：</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>🔌 <strong>控制接口</strong> → 可控制性</li>
                    <li>🔍 <strong>远程诊断</strong> → 可观测性</li>
                </ul>
            </div>
        </div>

        <!-- 交互式演示区 -->
        <div class="section">
            <h2 class="section-title">🎮 可测试性互动演示</h2>

            <div class="explanation">
                <h3>🎯 演示目标</h3>
                <p>通过模拟车库门系统与智能家居系统的交互，体验可测试性质量属性的实际应用。</p>
            </div>

            <div class="canvas-container">
                <canvas id="testabilityCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="establishConnection()">🔗 建立远程连接</button>
                <button class="btn btn-info" onclick="sendControlCommand()">🎮 发送控制指令</button>
                <button class="btn btn-danger" onclick="simulateSystemError()">⚠️ 模拟系统故障</button>
                <button class="btn btn-success" onclick="runDiagnostics()">🔍 执行远程诊断</button>
            </div>

            <div id="systemStatus" class="explanation" style="display: none;">
                <h3>📊 系统状态监控</h3>
                <div id="statusContent"></div>
            </div>
        </div>

        <!-- 对比分析区 -->
        <div class="section">
            <h2 class="section-title">⚖️ 质量属性对比分析</h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0;">
                <div class="concept-card">
                    <h3>🚀 性能质量属性</h3>
                    <p><strong>关注点：</strong>时间、速度、效率</p>
                    <p><strong>示例：</strong>"0.1秒内停止下降"</p>
                    <p><strong>衡量指标：</strong>响应时间、吞吐量、资源利用率</p>
                </div>

                <div class="concept-card" style="background: linear-gradient(135deg, #f3e5f5, #e1bee7); border-left-color: #9c27b0;">
                    <h3>🔧 可测试性质量属性</h3>
                    <p><strong>关注点：</strong>测试、调试、监控</p>
                    <p><strong>示例：</strong>"远程错误诊断与调试"</p>
                    <p><strong>衡量指标：</strong>可观测性、可控制性、测试覆盖率</p>
                </div>
            </div>

            <div class="highlight">
                <h4>🎯 关键区别</h4>
                <p><strong>性能</strong>关注"多快能完成"，<strong>可测试性</strong>关注"多容易测试和维护"</p>
            </div>
        </div>

        <!-- 知识测验区 -->
        <div class="quiz-section">
            <h2 class="section-title" style="color: #2d3436;">🎯 深度理解测验</h2>

            <div class="quiz-question" id="quizQuestion">
                "系统需要为部署在远程PC机上的智能家居系统留有控制接口，并支持在智能家居系统中对该系统进行远程错误诊断与调试"，这个需求与哪个质量属性相关？
            </div>

            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer(this, false, 'A')">
                    <strong>A. 可用性</strong><br>
                    <small>系统正常运行的能力</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 'B')">
                    <strong>B. 性能</strong><br>
                    <small>系统的速度和效率</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 'C')">
                    <strong>C. 可修改性</strong><br>
                    <small>系统易于修改的程度</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true, 'D')">
                    <strong>D. 可测试性</strong><br>
                    <small>系统易于测试和调试</small>
                </div>
            </div>

            <div id="quizFeedback"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let progress = 0;
        let currentStage = '准备开始';
        let canvas, ctx;
        let connectionEstablished = false;
        let systemError = false;
        let diagnosticsRunning = false;
        let animationId;
        let particles = [];

        // 初始化
        window.onload = function() {
            initCanvas();
            updateProgress(10, '学习概念中');
            drawInitialScene();
            startAnimation();
        };

        function initCanvas() {
            canvas = document.getElementById('testabilityCanvas');
            ctx = canvas.getContext('2d');
        }

        function startAnimation() {
            function animate() {
                drawInitialScene();
                animationId = requestAnimationFrame(animate);
            }
            animate();
        }

        function drawInitialScene() {
            ctx.clearRect(0, 0, 800, 500);

            // 绘制车库门系统
            drawGarageSystem();

            // 绘制智能家居系统
            drawSmartHomeSystem();

            // 绘制连接状态
            drawConnection();

            // 绘制状态信息
            drawStatusInfo();
        }

        function drawGarageSystem() {
            // 车库门系统主体
            ctx.fillStyle = systemError ? '#e74c3c' : '#34495e';
            ctx.fillRect(50, 200, 200, 150);

            // 系统标题
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('车库门系统', 150, 230);
            ctx.font = '12px Arial';
            ctx.fillText('(嵌入式设备)', 150, 250);

            // 状态指示器
            ctx.fillStyle = systemError ? '#c0392b' : '#27ae60';
            ctx.beginPath();
            ctx.arc(220, 220, 8, 0, Math.PI * 2);
            ctx.fill();

            // 传感器和接口
            ctx.fillStyle = '#3498db';
            ctx.fillRect(260, 240, 40, 20);
            ctx.fillStyle = '#fff';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('接口', 280, 252);

            // 错误指示
            if (systemError) {
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'bold 20px Arial';
                ctx.fillText('⚠️', 150, 290);
                ctx.font = '12px Arial';
                ctx.fillText('系统故障', 150, 310);
            }
        }

        function drawSmartHomeSystem() {
            // 智能家居系统
            ctx.fillStyle = '#3498db';
            ctx.fillRect(550, 200, 200, 150);

            // 系统标题
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('智能家居系统', 650, 230);
            ctx.font = '12px Arial';
            ctx.fillText('(远程PC)', 650, 250);

            // 屏幕效果
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(570, 260, 160, 70);

            if (connectionEstablished) {
                ctx.fillStyle = '#27ae60';
                ctx.font = '10px Arial';
                ctx.fillText('连接已建立', 650, 280);
                ctx.fillText('监控中...', 650, 295);

                if (diagnosticsRunning) {
                    ctx.fillStyle = '#f39c12';
                    ctx.fillText('正在诊断', 650, 310);
                    ctx.fillText('分析系统状态', 650, 325);
                }
            } else {
                ctx.fillStyle = '#95a5a6';
                ctx.fillText('等待连接', 650, 295);
            }
        }

        function drawConnection() {
            // 连接线
            ctx.strokeStyle = connectionEstablished ? '#27ae60' : '#95a5a6';
            ctx.lineWidth = connectionEstablished ? 4 : 2;
            ctx.setLineDash(connectionEstablished ? [] : [10, 5]);
            ctx.beginPath();
            ctx.moveTo(300, 275);
            ctx.lineTo(550, 275);
            ctx.stroke();
            ctx.setLineDash([]);

            // 数据传输动画
            if (connectionEstablished && diagnosticsRunning) {
                const time = Date.now() / 1000;
                const x = 300 + (Math.sin(time * 4) + 1) * 125;
                ctx.fillStyle = '#f39c12';
                ctx.beginPath();
                ctx.arc(x, 275, 6, 0, Math.PI * 2);
                ctx.fill();

                // 添加粒子效果
                if (Math.random() < 0.3) {
                    particles.push({
                        x: x,
                        y: 275,
                        vx: (Math.random() - 0.5) * 4,
                        vy: (Math.random() - 0.5) * 4,
                        life: 30
                    });
                }
            }

            // 绘制粒子
            particles.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;

                ctx.fillStyle = `rgba(243, 156, 18, ${particle.life / 30})`;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 2, 0, Math.PI * 2);
                ctx.fill();

                if (particle.life <= 0) {
                    particles.splice(index, 1);
                }
            });
        }

        function drawStatusInfo() {
            // 状态面板
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(50, 50, 700, 100);
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 50, 700, 100);

            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('🔧 可测试性演示状态', 70, 80);

            ctx.font = '14px Arial';
            let statusY = 105;

            ctx.fillStyle = connectionEstablished ? '#27ae60' : '#e74c3c';
            ctx.fillText(`• 远程连接: ${connectionEstablished ? '已建立' : '未连接'}`, 70, statusY);

            ctx.fillStyle = systemError ? '#e74c3c' : '#27ae60';
            ctx.fillText(`• 系统状态: ${systemError ? '故障' : '正常'}`, 250, statusY);

            ctx.fillStyle = diagnosticsRunning ? '#f39c12' : '#95a5a6';
            ctx.fillText(`• 诊断状态: ${diagnosticsRunning ? '运行中' : '待机'}`, 430, statusY);

            ctx.fillStyle = '#3498db';
            ctx.fillText(`• 可观测性: ${connectionEstablished ? '良好' : '受限'}`, 610, statusY);
        }

        // 交互功能函数
        function establishConnection() {
            connectionEstablished = !connectionEstablished;
            updateProgress(30, connectionEstablished ? '连接已建立' : '连接已断开');
            showSystemStatus('远程连接', connectionEstablished ?
                '✅ 成功建立与车库门系统的远程连接，体现了系统的可控制性' :
                '❌ 连接已断开');
        }

        function sendControlCommand() {
            if (!connectionEstablished) {
                showSystemStatus('控制指令', '❌ 请先建立远程连接');
                return;
            }

            updateProgress(50, '发送控制指令');
            showSystemStatus('控制指令', '✅ 成功发送车库门开关指令，远程控制功能正常');
        }

        function simulateSystemError() {
            systemError = !systemError;
            updateProgress(70, systemError ? '系统故障模拟' : '故障已修复');
            showSystemStatus('系统故障', systemError ?
                '⚠️ 模拟系统故障，错误状态可被远程观测，体现了系统的可观测性' :
                '✅ 系统故障已修复');
        }

        function runDiagnostics() {
            if (!connectionEstablished) {
                showSystemStatus('远程诊断', '❌ 请先建立远程连接');
                return;
            }

            diagnosticsRunning = !diagnosticsRunning;
            updateProgress(90, diagnosticsRunning ? '执行远程诊断' : '诊断完成');

            if (diagnosticsRunning) {
                showSystemStatus('远程诊断', '🔍 正在执行远程诊断，分析系统状态...');
                setTimeout(() => {
                    if (diagnosticsRunning) {
                        showSystemStatus('诊断报告', `
                            📊 诊断完成：<br>
                            • 传感器状态: 正常<br>
                            • 电机状态: 正常<br>
                            • 控制模块: ${systemError ? '异常' : '正常'}<br>
                            • 通信接口: 正常<br>
                            <strong>这展示了系统优秀的可观测性和可测试性！</strong>
                        `);
                    }
                }, 3000);
            } else {
                showSystemStatus('远程诊断', '✅ 诊断已停止');
            }
        }

        function showSystemStatus(title, message) {
            const statusDiv = document.getElementById('systemStatus');
            const contentDiv = document.getElementById('statusContent');

            statusDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <h4>🔧 ${title}</h4>
                <p>${message}</p>
                <small style="color: #7f8c8d;">时间: ${new Date().toLocaleTimeString()}</small>
            `;

            // 滚动到状态区域
            statusDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // 测验相关函数
        function selectAnswer(element, isCorrect, option) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('D. 可测试性')) {
                    opt.classList.add('correct');
                }
            });

            const feedback = document.getElementById('quizFeedback');
            if (isCorrect) {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #00b894; margin-top: 30px;">
                        <h3>🎉 回答完全正确！</h3>
                        <p><strong>远程控制接口和错误诊断功能</strong>确实属于<strong>可测试性质量属性</strong>。</p>
                        <div class="highlight">
                            <h4>🔍 深度分析：</h4>
                            <p><strong>• 控制接口</strong> → 提供可控制性，允许远程操作系统</p>
                            <p><strong>• 远程诊断</strong> → 提供可观测性，允许监控系统状态</p>
                            <p><strong>• 错误调试</strong> → 支持问题定位和修复</p>
                        </div>
                        <p>🏆 <strong>恭喜您深度理解了可测试性质量属性！</strong></p>
                    </div>
                `;
                updateProgress(100, '学习完成');
                setTimeout(() => {
                    alert('🎉 恭喜完成可测试性质量属性学习！\n\n您已掌握：\n• 可测试性的定义和重要性\n• 可观测性与可控制性的区别\n• 远程诊断系统的实际应用\n• 与其他质量属性的对比分析');
                }, 2000);
            } else {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #e17055; margin-top: 30px;">
                        <h3>❌ 回答错误，让我们分析一下</h3>
                        <p>您选择了 <strong>${option}</strong>，但正确答案是 <strong>D. 可测试性</strong>。</p>
                        <div class="highlight">
                            <h4>🤔 为什么不是${option}？</h4>
                            ${getWrongAnswerExplanation(option)}
                        </div>
                        <p><strong>记住：</strong>当题目提到"远程控制接口"和"远程诊断调试"时，这些都是可测试性的典型特征。</p>
                    </div>
                `;
                updateProgress(85, '继续学习');
            }
        }

        function getWrongAnswerExplanation(option) {
            switch(option) {
                case 'A':
                    return '<p><strong>可用性</strong>关注系统的正常运行时间和故障恢复能力，而不是测试和诊断能力。</p>';
                case 'B':
                    return '<p><strong>性能</strong>关注系统的响应时间、吞吐量等时间相关指标，而不是诊断接口。</p>';
                case 'C':
                    return '<p><strong>可修改性</strong>关注系统易于修改和扩展的程度，而不是测试诊断功能。</p>';
                default:
                    return '<p>请重新理解题目要求。</p>';
            }
        }

        // 工具函数
        function updateProgress(newProgress, stage) {
            progress = newProgress;
            currentStage = stage;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('scoreText').textContent = `${progress}%`;
            document.getElementById('stageText').textContent = currentStage;
        }

        // 页面加载完成后的额外初始化
        setTimeout(() => {
            updateProgress(20, '准备就绪');
        }, 1000);
    </script>
</body>
</html>
