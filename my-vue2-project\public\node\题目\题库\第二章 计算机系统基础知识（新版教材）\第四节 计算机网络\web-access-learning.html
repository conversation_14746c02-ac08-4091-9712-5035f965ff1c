<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web页面访问过程 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
            animation: shake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #animationCanvas {
            width: 100%;
            height: 500px;
            border-radius: 12px;
            background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #667eea;
            background: #f8f9ff;
            border-radius: 0 12px 12px 0;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.5s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateX(0);
        }

        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 15px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Web页面访问过程学习</h1>
            <p>通过动画和交互，轻松理解网络访问的每一个步骤</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                <strong>题目：</strong>Web页面访问过程中，在浏览器发出HTTP请求报文之前不可能执行的操作是（）。
            </div>
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 查询本机DNS缓存，获取主机名对应的IP地址
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 发起DNS请求，获取主机名对应的IP地址
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 发送请求信息，获取将要访问的Web应用
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 发送ARP协议广播数据包，请求网关的MAC地址
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎬 Web访问过程动画演示</h3>
            <canvas id="animationCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn" onclick="showAnswer()">💡 查看答案</button>
            </div>
        </div>

        <div class="explanation">
            <h3 style="color: #333; margin-bottom: 20px;">📚 知识点详解</h3>
            <div class="step" id="step1">
                <span class="step-number">1</span>
                <strong>查询本机DNS缓存</strong><br>
                浏览器首先检查本地缓存，看看是否已经知道网站的IP地址。就像查看电话簿一样！
            </div>
            <div class="step" id="step2">
                <span class="step-number">2</span>
                <strong>发起DNS请求</strong><br>
                如果本地没有找到，就向DNS服务器询问："www.example.com的IP地址是什么？"
            </div>
            <div class="step" id="step3">
                <span class="step-number">3</span>
                <strong>ARP协议获取MAC地址</strong><br>
                知道IP地址后，需要找到网关的物理地址（MAC地址）来发送数据包。
            </div>
            <div class="step" id="step4">
                <span class="step-number">4</span>
                <strong>发送HTTP请求</strong><br>
                现在可以向服务器发送HTTP请求了："请给我这个网页的内容！"
            </div>
            <div class="step" id="step5">
                <span class="step-number">5</span>
                <strong>服务器处理请求</strong><br>
                <span class="highlight">这一步才是"发送请求信息，获取将要访问的Web应用"</span><br>
                服务器收到请求后，才开始处理并准备网页内容。
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationStep = 0;
        let animationId;

        // 绘制元素的位置和状态
        const elements = {
            browser: { x: 50, y: 200, active: false },
            dnsCache: { x: 200, y: 100, active: false },
            dnsServer: { x: 400, y: 100, active: false },
            gateway: { x: 600, y: 200, active: false },
            server: { x: 800, y: 200, active: false }
        };

        function drawElement(name, x, y, active, emoji, label) {
            ctx.save();
            
            // 绘制发光效果
            if (active) {
                ctx.shadowColor = '#667eea';
                ctx.shadowBlur = 20;
            }
            
            // 绘制圆形背景
            ctx.fillStyle = active ? '#667eea' : '#e0e0e0';
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制emoji
            ctx.font = '30px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = active ? 'white' : '#666';
            ctx.fillText(emoji, x, y + 10);
            
            // 绘制标签
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText(label, x, y + 70);
            
            ctx.restore();
        }

        function drawArrow(fromX, fromY, toX, toY, active) {
            if (!active) return;
            
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 绘制箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = '#667eea';
            ctx.fill();
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制所有元素
            drawElement('browser', elements.browser.x, elements.browser.y, elements.browser.active, '🌐', '浏览器');
            drawElement('dnsCache', elements.dnsCache.x, elements.dnsCache.y, elements.dnsCache.active, '💾', 'DNS缓存');
            drawElement('dnsServer', elements.dnsServer.x, elements.dnsServer.y, elements.dnsServer.active, '🏢', 'DNS服务器');
            drawElement('gateway', elements.gateway.x, elements.gateway.y, elements.gateway.active, '🚪', '网关');
            drawElement('server', elements.server.x, elements.server.y, elements.server.active, '🖥️', 'Web服务器');
            
            // 绘制连接线
            if (animationStep >= 1) {
                drawArrow(elements.browser.x + 40, elements.browser.y - 20, elements.dnsCache.x - 40, elements.dnsCache.y + 20, true);
            }
            if (animationStep >= 2) {
                drawArrow(elements.dnsCache.x + 40, elements.dnsCache.y, elements.dnsServer.x - 40, elements.dnsServer.y, true);
            }
            if (animationStep >= 3) {
                drawArrow(elements.browser.x + 40, elements.browser.y, elements.gateway.x - 40, elements.gateway.y, true);
            }
            if (animationStep >= 4) {
                drawArrow(elements.gateway.x + 40, elements.gateway.y, elements.server.x - 40, elements.server.y, true);
            }
            
            // 绘制当前步骤说明
            const stepTexts = [
                '',
                '1. 查询本机DNS缓存',
                '2. 向DNS服务器请求IP地址',
                '3. 使用ARP协议获取网关MAC地址',
                '4. 发送HTTP请求到服务器',
                '5. 服务器处理请求并返回网页内容'
            ];
            
            if (stepTexts[animationStep]) {
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText(stepTexts[animationStep], canvas.width / 2, 50);
            }
        }

        function startAnimation() {
            resetAnimation();
            animationId = setInterval(() => {
                animationStep++;
                
                // 激活对应的元素
                switch(animationStep) {
                    case 1:
                        elements.browser.active = true;
                        elements.dnsCache.active = true;
                        showStep('step1');
                        break;
                    case 2:
                        elements.dnsServer.active = true;
                        showStep('step2');
                        break;
                    case 3:
                        elements.gateway.active = true;
                        showStep('step3');
                        break;
                    case 4:
                        elements.server.active = true;
                        showStep('step4');
                        break;
                    case 5:
                        showStep('step5');
                        break;
                    default:
                        clearInterval(animationId);
                        return;
                }
                
                drawScene();
            }, 2000);
        }

        function resetAnimation() {
            if (animationId) clearInterval(animationId);
            animationStep = 0;
            
            // 重置所有元素状态
            Object.keys(elements).forEach(key => {
                elements[key].active = false;
            });
            
            // 隐藏所有步骤
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });
            
            drawScene();
        }

        function showStep(stepId) {
            document.getElementById(stepId).classList.add('active');
        }

        function showAnswer() {
            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'C') {
                    option.classList.add('correct');
                } else {
                    option.classList.add('wrong');
                }
            });
            
            // 显示所有步骤
            document.querySelectorAll('.step').forEach(step => {
                step.classList.add('active');
            });
        }

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'C') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        alert('🎉 恭喜你答对了！\n\n正确答案是C。在浏览器发出HTTP请求之前，不可能"发送请求信息，获取将要访问的Web应用"，因为这个操作是在HTTP请求发送之后，服务器收到请求时才会执行的。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    setTimeout(() => {
                        alert('❌ 答案不正确，请仔细观看动画演示，理解每个步骤的顺序！');
                    }, 500);
                }
            });
        });

        // 初始化绘制
        drawScene();
    </script>
</body>
</html>
