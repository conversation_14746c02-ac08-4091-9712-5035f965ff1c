<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis缓存击穿 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .explanation h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #4a5568;
            line-height: 1.8;
            font-size: 1rem;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9rem;
        }

        .game-area {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 20px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight {
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .solution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .solution-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .solution-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
        }

        .solution-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.5rem;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Redis 缓存击穿</h1>
            <p class="subtitle">通过动画和交互学习缓存击穿的原理与解决方案</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是缓存击穿？</h2>
            <div class="explanation">
                <h3>🎯 核心概念</h3>
                <p>缓存击穿是指<span class="highlight">缓存中没有但数据库中有的数据</span>（一般是缓存时间到期），这时由于并发用户特别多，同时读缓存没读到数据，又同时去数据库去取数据，引起数据库压力瞬间增大，造成过大压力。</p>
            </div>
            
            <div class="explanation">
                <h3>🔍 与缓存雪崩的区别</h3>
                <p><strong>缓存击穿：</strong>并发查<span class="highlight">同一条数据</span><br>
                <strong>缓存雪崩：</strong><span class="highlight">不同数据都过期了</span>，很多数据都查不到从而查数据库</p>
            </div>

            <div class="demo-area">
                <h3 style="text-align: center; margin-bottom: 20px;">🎮 缓存击穿模拟演示</h3>
                <div class="canvas-container">
                    <canvas id="breakdownCanvas" width="800" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="startBreakdownDemo()">开始演示</button>
                    <button class="btn btn-danger" onclick="simulateBreakdown()">模拟击穿</button>
                    <button class="btn btn-success" onclick="resetDemo()">重置</button>
                </div>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="requestCount">0</div>
                        <div class="stat-label">并发请求数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="dbLoad">0%</div>
                        <div class="stat-label">数据库负载</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="cacheHit">100%</div>
                        <div class="stat-label">缓存命中率</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">解决方案</h2>
            <div class="solution-grid">
                <div class="solution-card">
                    <div class="solution-icon">🔒</div>
                    <h3 style="text-align: center; margin-bottom: 15px;">互斥锁</h3>
                    <p>使用互斥锁确保只有一个线程去数据库查询数据，其他线程等待结果。</p>
                    <button class="btn btn-primary" style="width: 100%; margin-top: 15px;" onclick="demonstrateMutex()">演示互斥锁</button>
                </div>
                <div class="solution-card">
                    <div class="solution-icon">♾️</div>
                    <h3 style="text-align: center; margin-bottom: 15px;">热点数据永不过期</h3>
                    <p>设置热点数据永远不过期，避免缓存失效导致的击穿问题。</p>
                    <button class="btn btn-primary" style="width: 100%; margin-top: 15px;" onclick="demonstrateNeverExpire()">演示永不过期</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">缓存预热</h2>
            <div class="explanation">
                <h3>💡 什么是缓存预热？</h3>
                <p>缓存预热就是系统上线后，将相关的缓存数据直接加载到缓存系统。这样就可以避免在用户请求的时候，先查询数据库，然后再将数据缓存的问题！用户直接查询事先被预热的缓存数据！</p>
            </div>

            <div class="game-area">
                <h3>🎯 缓存预热小游戏</h3>
                <p>帮助系统预热缓存，点击数据块将其加载到缓存中！</p>
                <div class="score">得分: <span id="gameScore">0</span></div>
                <div class="canvas-container">
                    <canvas id="preheatGame" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="startPreheatGame()">开始游戏</button>
                    <button class="btn btn-primary" onclick="resetGame()">重新开始</button>
                </div>
            </div>

            <div class="solution-grid">
                <div class="solution-card">
                    <div class="solution-icon">🖱️</div>
                    <h3 style="text-align: center; margin-bottom: 15px;">手工操作</h3>
                    <p>直接写个缓存刷新页面，上线时手工操作一下。</p>
                </div>
                <div class="solution-card">
                    <div class="solution-icon">🚀</div>
                    <h3 style="text-align: center; margin-bottom: 15px;">启动时加载</h3>
                    <p>数据量不大，可以在项目启动的时候自动进行加载。</p>
                </div>
                <div class="solution-card">
                    <div class="solution-icon">⏰</div>
                    <h3 style="text-align: center; margin-bottom: 15px;">定时刷新</h3>
                    <p>定时刷新缓存，保持数据的新鲜度。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let breakdownCanvas, breakdownCtx;
        let preheatCanvas, preheatCtx;
        let animationId;
        let gameScore = 0;
        let gameRunning = false;
        let dataBlocks = [];

        // 初始化
        window.onload = function() {
            initCanvases();
            setupEventListeners();
        };

        function initCanvases() {
            // 缓存击穿演示画布
            breakdownCanvas = document.getElementById('breakdownCanvas');
            breakdownCtx = breakdownCanvas.getContext('2d');
            
            // 缓存预热游戏画布
            preheatCanvas = document.getElementById('preheatGame');
            preheatCtx = preheatCanvas.getContext('2d');
            
            drawInitialState();
        }

        function setupEventListeners() {
            preheatCanvas.addEventListener('click', handleGameClick);
        }

        function drawInitialState() {
            // 绘制初始状态
            breakdownCtx.clearRect(0, 0, breakdownCanvas.width, breakdownCanvas.height);
            
            // 绘制缓存
            drawCache(breakdownCtx, 150, 100, true);
            
            // 绘制数据库
            drawDatabase(breakdownCtx, 550, 100);
            
            // 绘制用户
            drawUsers(breakdownCtx, 50, 250, 3);
            
            // 添加标签
            breakdownCtx.fillStyle = '#2d3748';
            breakdownCtx.font = '16px Arial';
            breakdownCtx.fillText('缓存', 160, 90);
            breakdownCtx.fillText('数据库', 560, 90);
            breakdownCtx.fillText('用户请求', 30, 240);
        }

        function drawCache(ctx, x, y, hasData) {
            ctx.fillStyle = hasData ? '#51cf66' : '#ff6b6b';
            ctx.fillRect(x, y, 100, 80);
            ctx.strokeStyle = '#2d3748';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 100, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText(hasData ? '有数据' : '无数据', x + 25, y + 45);
        }

        function drawDatabase(ctx, x, y) {
            ctx.fillStyle = '#4299e1';
            ctx.fillRect(x, y, 100, 80);
            ctx.strokeStyle = '#2d3748';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 100, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('数据库', x + 25, y + 45);
        }

        function drawUsers(ctx, x, y, count) {
            for (let i = 0; i < count; i++) {
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(x + i * 30, y + i * 20, 15, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('U', x + i * 30 - 4, y + i * 20 + 4);
            }
        }

        function startBreakdownDemo() {
            let step = 0;
            const maxSteps = 100;
            
            function animate() {
                breakdownCtx.clearRect(0, 0, breakdownCanvas.width, breakdownCanvas.height);
                
                // 绘制基本组件
                drawCache(breakdownCtx, 150, 100, step < 50);
                drawDatabase(breakdownCtx, 550, 100);
                drawUsers(breakdownCtx, 50, 250, Math.min(10, Math.floor(step / 5) + 3));
                
                // 绘制请求箭头
                if (step > 20) {
                    drawArrow(breakdownCtx, 120, 260, 150, 140, '#667eea');
                }
                
                if (step > 50) {
                    // 缓存失效后，所有请求直接到数据库
                    for (let i = 0; i < Math.min(8, Math.floor((step - 50) / 5)); i++) {
                        drawArrow(breakdownCtx, 120 + i * 10, 260 + i * 5, 550, 140, '#ff6b6b');
                    }
                }
                
                // 更新统计数据
                updateStats(step);
                
                step++;
                if (step < maxSteps) {
                    animationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function drawArrow(ctx, x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI / 6), y2 - 10 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI / 6), y2 - 10 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        function updateStats(step) {
            const requestCount = Math.min(100, step * 2);
            const dbLoad = step > 50 ? Math.min(100, (step - 50) * 4) : 10;
            const cacheHit = step > 50 ? Math.max(0, 100 - (step - 50) * 2) : 95;
            
            document.getElementById('requestCount').textContent = requestCount;
            document.getElementById('dbLoad').textContent = dbLoad + '%';
            document.getElementById('cacheHit').textContent = cacheHit + '%';
        }

        function simulateBreakdown() {
            // 模拟缓存击穿场景
            breakdownCtx.clearRect(0, 0, breakdownCanvas.width, breakdownCanvas.height);
            
            // 绘制失效的缓存
            drawCache(breakdownCtx, 150, 100, false);
            drawDatabase(breakdownCtx, 550, 100);
            
            // 绘制大量用户
            drawUsers(breakdownCtx, 50, 250, 10);
            
            // 绘制大量请求直接到数据库
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    drawArrow(breakdownCtx, 120 + i * 10, 260 + i * 5, 550, 140, '#ff6b6b');
                }, i * 100);
            }
            
            // 更新统计数据显示危险状态
            document.getElementById('requestCount').textContent = '1000+';
            document.getElementById('dbLoad').textContent = '95%';
            document.getElementById('cacheHit').textContent = '0%';
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            drawInitialState();
            document.getElementById('requestCount').textContent = '0';
            document.getElementById('dbLoad').textContent = '0%';
            document.getElementById('cacheHit').textContent = '100%';
        }

        function demonstrateMutex() {
            alert('🔒 互斥锁演示：\n\n1. 第一个请求获得锁，查询数据库\n2. 其他请求等待\n3. 数据返回后，所有请求共享结果\n4. 避免了数据库压力激增');
        }

        function demonstrateNeverExpire() {
            alert('♾️ 永不过期演示：\n\n1. 热点数据设置为永不过期\n2. 避免缓存失效\n3. 后台异步更新数据\n4. 用户始终能从缓存获取数据');
        }

        // 缓存预热游戏
        function startPreheatGame() {
            gameRunning = true;
            gameScore = 0;
            dataBlocks = [];
            
            // 生成随机数据块
            for (let i = 0; i < 10; i++) {
                dataBlocks.push({
                    x: Math.random() * (preheatCanvas.width - 50),
                    y: Math.random() * (preheatCanvas.height - 50),
                    width: 40,
                    height: 30,
                    loaded: false,
                    color: '#ff6b6b'
                });
            }
            
            drawPreheatGame();
        }

        function drawPreheatGame() {
            preheatCtx.clearRect(0, 0, preheatCanvas.width, preheatCanvas.height);
            
            // 绘制缓存区域
            preheatCtx.fillStyle = '#e2e8f0';
            preheatCtx.fillRect(450, 50, 120, 200);
            preheatCtx.strokeStyle = '#4a5568';
            preheatCtx.strokeRect(450, 50, 120, 200);
            preheatCtx.fillStyle = '#4a5568';
            preheatCtx.font = '14px Arial';
            preheatCtx.fillText('缓存区域', 470, 40);
            
            // 绘制数据块
            dataBlocks.forEach(block => {
                preheatCtx.fillStyle = block.loaded ? '#51cf66' : block.color;
                preheatCtx.fillRect(block.x, block.y, block.width, block.height);
                preheatCtx.strokeStyle = '#2d3748';
                preheatCtx.strokeRect(block.x, block.y, block.width, block.height);
                
                preheatCtx.fillStyle = 'white';
                preheatCtx.font = '12px Arial';
                preheatCtx.fillText('数据', block.x + 8, block.y + 20);
            });
            
            // 更新得分
            document.getElementById('gameScore').textContent = gameScore;
        }

        function handleGameClick(event) {
            if (!gameRunning) return;
            
            const rect = preheatCanvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 检查是否点击了数据块
            dataBlocks.forEach(block => {
                if (x >= block.x && x <= block.x + block.width &&
                    y >= block.y && y <= block.y + block.height &&
                    !block.loaded) {
                    
                    block.loaded = true;
                    gameScore += 10;
                    
                    // 动画效果：数据块移动到缓存区域
                    animateBlockToCache(block);
                }
            });
            
            // 检查游戏是否完成
            if (dataBlocks.every(block => block.loaded)) {
                setTimeout(() => {
                    alert('🎉 恭喜！缓存预热完成！\n所有数据都已加载到缓存中。');
                    gameRunning = false;
                }, 500);
            }
        }

        function animateBlockToCache(block) {
            const targetX = 480;
            const targetY = 150;
            const steps = 20;
            let currentStep = 0;
            
            const originalX = block.x;
            const originalY = block.y;
            
            function moveBlock() {
                currentStep++;
                const progress = currentStep / steps;
                
                block.x = originalX + (targetX - originalX) * progress;
                block.y = originalY + (targetY - originalY) * progress;
                
                drawPreheatGame();
                
                if (currentStep < steps) {
                    requestAnimationFrame(moveBlock);
                }
            }
            
            moveBlock();
        }

        function resetGame() {
            gameRunning = false;
            gameScore = 0;
            dataBlocks = [];
            preheatCtx.clearRect(0, 0, preheatCanvas.width, preheatCanvas.height);
            document.getElementById('gameScore').textContent = '0';
        }
    </script>
</body>
</html>
