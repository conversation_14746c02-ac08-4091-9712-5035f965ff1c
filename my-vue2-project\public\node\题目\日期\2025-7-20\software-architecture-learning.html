<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构标准 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .concept-card:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .canvas-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        canvas {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
        }

        .quiz-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #2c3e50;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: white;
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
            font-weight: bold;
        }

        .quiz-option:hover {
            transform: scale(1.05);
            border-color: #667eea;
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
            animation: pulse 0.6s ease-in-out;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            animation: shake 0.6s ease-in-out;
        }

        .explanation {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #2196f3;
            display: none;
            animation: slideDown 0.5s ease-out;
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            animation: float 3s ease-in-out infinite;
            opacity: 0.7;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shine {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes slideDown {
            from { opacity: 0; max-height: 0; }
            to { opacity: 1; max-height: 200px; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="floating-element" style="top: 10%; left: 5%;"></div>
    <div class="floating-element" style="top: 20%; right: 10%; animation-delay: -1s;"></div>
    <div class="floating-element" style="bottom: 15%; left: 15%; animation-delay: -2s;"></div>

    <div class="container">
        <div class="header">
            <h1>🏗️ 软件架构标准学习</h1>
            <p>ANSI/IEEE 1471-2000 互动探索之旅</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2>📚 核心概念解析</h2>
            
            <div class="concept-card" onclick="showConcept('stakeholder')">
                <h3>👥 利益相关人 (Stakeholder)</h3>
                <p>对系统有利益关系的人员，包括用户、开发者、管理者等</p>
            </div>

            <div class="concept-card" onclick="showConcept('concern')">
                <h3>🎯 关注点 (Concern)</h3>
                <p>利益相关人对系统重要的利益和需求</p>
            </div>

            <div class="concept-card" onclick="showConcept('viewpoint')">
                <h3>👁️ 视角 (Viewpoint)</h3>
                <p>描述某个利益相关人所关注架构模型的某一方面</p>
            </div>

            <div class="concept-card" onclick="showConcept('view')">
                <h3>🖼️ 视图 (View)</h3>
                <p>从特定视角表述架构的独立方面</p>
            </div>

            <div class="concept-card" onclick="showConcept('architecture')">
                <h3>🏛️ 架构 (Architecture)</h3>
                <p>对所有利益相关人关注点的响应和回答</p>
            </div>

            <div class="canvas-container">
                <h3>🎮 互动架构图</h3>
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
                <p>点击画布中的元素来探索它们之间的关系！</p>
            </div>
        </div>
    </div>
        <div class="quiz-section">
            <h2>🧠 知识测试</h2>
            <div class="quiz-question">
                根据ANSI/IEEE 1471-2000标准，<strong>（ ）</strong>这一概念主要用于描述软件架构模型。
                在此基础上，通常采用<strong>（ ）</strong>描述某个利益相关人所关注架构模型的某一方面。
                <strong>（ ）</strong>则是对所有利益相关人关注点的响应和回答。
            </div>

            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer('A', false)">A. 上下文</div>
                <div class="quiz-option" onclick="selectAnswer('B', false)">B. 架构风格</div>
                <div class="quiz-option" onclick="selectAnswer('C', false)">C. 组件</div>
                <div class="quiz-option" onclick="selectAnswer('D', true)">D. 视图</div>
            </div>

            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：D. 视图</strong></p>
                <p>在ANSI/IEEE 1471-2000标准中：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>视图(View)</strong>：从特定视角表述架构的某一个独立方面</li>
                    <li><strong>视角(Viewpoint)</strong>：描述某个利益相关人所关注架构模型的某一方面</li>
                    <li><strong>架构(Architecture)</strong>：对所有利益相关人关注点的响应和回答</li>
                </ul>
                <p>每个视图包含一个或多个架构模型，通过可视化方式更容易理解、检查和分析。</p>
            </div>
        </div>
    </div>

    <script>
        // 画布相关变量
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame;
        let currentStep = 0;
        let progress = 0;

        // 架构元素定义
        const elements = {
            stakeholder: { x: 100, y: 100, radius: 40, color: '#ff6b6b', label: '利益相关人', active: false },
            concern: { x: 300, y: 100, radius: 35, color: '#feca57', label: '关注点', active: false },
            viewpoint: { x: 500, y: 100, radius: 35, color: '#48dbfb', label: '视角', active: false },
            view: { x: 700, y: 100, radius: 40, color: '#ff9ff3', label: '视图', active: false },
            architecture: { x: 400, y: 300, radius: 50, color: '#54a0ff', label: '架构', active: false }
        };

        // 连接线定义
        const connections = [
            { from: 'stakeholder', to: 'concern' },
            { from: 'concern', to: 'viewpoint' },
            { from: 'viewpoint', to: 'view' },
            { from: 'view', to: 'architecture' },
            { from: 'stakeholder', to: 'architecture' }
        ];

        // 初始化画布
        function initCanvas() {
            canvas.addEventListener('click', handleCanvasClick);
            drawArchitecture();
            startAnimation();
        }

        // 绘制架构图
        function drawArchitecture() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制连接线
            connections.forEach(conn => {
                const from = elements[conn.from];
                const to = elements[conn.to];

                ctx.beginPath();
                ctx.moveTo(from.x, from.y);
                ctx.lineTo(to.x, to.y);
                ctx.strokeStyle = from.active || to.active ? '#667eea' : '#e9ecef';
                ctx.lineWidth = from.active || to.active ? 3 : 1;
                ctx.stroke();
            });

            // 绘制元素
            Object.keys(elements).forEach(key => {
                const element = elements[key];

                // 绘制圆形
                ctx.beginPath();
                ctx.arc(element.x, element.y, element.radius, 0, 2 * Math.PI);
                ctx.fillStyle = element.active ? element.color : lightenColor(element.color, 0.3);
                ctx.fill();
                ctx.strokeStyle = element.active ? '#333' : '#ccc';
                ctx.lineWidth = element.active ? 3 : 1;
                ctx.stroke();

                // 绘制标签
                ctx.fillStyle = element.active ? '#333' : '#666';
                ctx.font = element.active ? 'bold 14px Microsoft YaHei' : '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(element.label, element.x, element.y + element.radius + 20);
            });
        }

        // 处理画布点击
        function handleCanvasClick(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            Object.keys(elements).forEach(key => {
                const element = elements[key];
                const distance = Math.sqrt((x - element.x) ** 2 + (y - element.y) ** 2);

                if (distance <= element.radius) {
                    // 重置所有元素
                    Object.keys(elements).forEach(k => elements[k].active = false);
                    // 激活点击的元素
                    element.active = true;
                    showConceptInfo(key);
                    updateProgress();
                }
            });

            drawArchitecture();
        }

        // 显示概念信息
        function showConceptInfo(concept) {
            const info = {
                stakeholder: '利益相关人是对系统有利益关系的人员，包括最终用户、开发人员、项目经理、系统管理员等。他们各自有不同的关注点和需求。',
                concern: '关注点是利益相关人对系统重要的利益和需求，比如性能、安全性、可维护性、成本等方面的考虑。',
                viewpoint: '视角定义了创建视图的方法和规则，决定了用来表述架构的语言、符号和模型，以及相关的建模方法。',
                view: '视图是从特定视角来表述架构的某个独立方面，每个视图包含一个或多个架构模型。',
                architecture: '架构是对所有利益相关人关注点的响应和回答，通过架构描述来说明系统的整体结构。'
            };

            alert(`🎯 ${elements[concept].label}\n\n${info[concept]}`);
        }

        // 显示概念详情
        function showConcept(concept) {
            // 重置所有元素
            Object.keys(elements).forEach(k => elements[k].active = false);
            // 激活对应元素
            if (elements[concept]) {
                elements[concept].active = true;
                drawArchitecture();
                showConceptInfo(concept);
                updateProgress();
            }
        }

        // 颜色处理函数
        function lightenColor(color, factor) {
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            const newR = Math.min(255, Math.floor(r + (255 - r) * factor));
            const newG = Math.min(255, Math.floor(g + (255 - g) * factor));
            const newB = Math.min(255, Math.floor(b + (255 - b) * factor));

            return `rgb(${newR}, ${newG}, ${newB})`;
        }

        // 动画效果
        function startAnimation() {
            let time = 0;

            function animate() {
                time += 0.02;

                // 为元素添加呼吸效果
                Object.keys(elements).forEach((key, index) => {
                    const element = elements[key];
                    const baseRadius = key === 'architecture' ? 50 : (key === 'stakeholder' || key === 'view' ? 40 : 35);
                    element.radius = baseRadius + Math.sin(time + index) * 2;
                });

                drawArchitecture();
                animationFrame = requestAnimationFrame(animate);
            }

            animate();
        }

        // 测试相关函数
        function selectAnswer(option, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const explanation = document.getElementById('explanation');

            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.textContent.startsWith(option)) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.startsWith('D')) {
                    opt.classList.add('correct');
                }
            });

            explanation.style.display = 'block';
            updateProgress();
        }

        // 更新进度
        function updateProgress() {
            progress = Math.min(100, progress + 20);
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCanvas();

            // 添加概念卡片悬停效果
            const conceptCards = document.querySelectorAll('.concept-card');
            conceptCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
