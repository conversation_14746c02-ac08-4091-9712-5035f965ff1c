<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除上网痕迹 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .browser-demo {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        .browser-window {
            width: 600px;
            height: 400px;
            background: #f5f5f5;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            position: relative;
        }

        .browser-header {
            height: 40px;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            padding: 0 15px;
        }

        .browser-buttons {
            display: flex;
            gap: 8px;
        }

        .browser-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .close { background: #ff5f57; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #28ca42; }

        .address-bar {
            flex: 1;
            margin-left: 20px;
            height: 24px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            padding: 0 15px;
            font-size: 12px;
            color: #666;
        }

        .browser-content {
            height: 360px;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .trace-item {
            position: absolute;
            padding: 10px 15px;
            background: rgba(255, 107, 107, 0.1);
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            font-size: 12px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trace-item:hover {
            transform: scale(1.05);
            background: rgba(255, 107, 107, 0.2);
        }

        .trace-item.removing {
            animation: removeTrace 1s ease-out forwards;
        }

        @keyframes removeTrace {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 0; transform: scale(0); }
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 40px 0;
        }

        .option-card {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .option-card.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.6s ease-out;
        }

        .option-card.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.6s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .option-letter {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #333;
            line-height: 1.4;
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #333;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .cookie-visual {
            width: 60px;
            height: 60px;
            background: #8B4513;
            border-radius: 50%;
            position: relative;
            margin: 0 auto 20px;
            animation: float 3s ease-in-out infinite;
        }

        .cookie-visual::before,
        .cookie-visual::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: #654321;
            border-radius: 50%;
        }

        .cookie-visual::before {
            top: 15px;
            left: 20px;
        }

        .cookie-visual::after {
            top: 35px;
            right: 18px;
        }
    </style>
</head>
<body>
    <canvas class="floating-particles"></canvas>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔒 清除上网痕迹</h1>
            <p class="subtitle">让我们一起学习如何保护隐私！</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识讲解</h2>
            
            <div class="browser-demo">
                <div class="browser-window">
                    <div class="browser-header">
                        <div class="browser-buttons">
                            <div class="browser-button close"></div>
                            <div class="browser-button minimize"></div>
                            <div class="browser-button maximize"></div>
                        </div>
                        <div class="address-bar">https://example.com</div>
                    </div>
                    <div class="browser-content" id="browserContent">
                        <!-- 动态生成上网痕迹 -->
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <button class="demo-button" onclick="showTraces()">显示上网痕迹</button>
                <button class="demo-button" onclick="clearCookies()">清除Cookie</button>
                <button class="demo-button" onclick="resetDemo()">重置演示</button>
            </div>

            <div style="background: #f0f8ff; padding: 25px; border-radius: 15px; margin: 30px 0;">
                <div class="cookie-visual"></div>
                <h3 style="text-align: center; color: #1976d2; margin-bottom: 15px;">什么是Cookie？</h3>
                <p style="text-align: center; color: #333; line-height: 1.6;">
                    Cookie是网站存储在你电脑上的小文件，记录你的浏览习惯、登录状态等信息。
                    清除Cookie可以删除这些上网痕迹，保护你的隐私！
                </p>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 题目练习</h2>
            
            <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
                <h3 style="color: #856404; margin-bottom: 10px;">题目：</h3>
                <p style="font-size: 1.2rem; color: #333;">如果要清除上网痕迹，必须（   ）。</p>
            </div>

            <div class="options-grid">
                <div class="option-card" onclick="selectOption(this, 'A', false)">
                    <div class="option-letter">A</div>
                    <div class="option-text">禁用ActiveX控件</div>
                </div>
                <div class="option-card" onclick="selectOption(this, 'B', false)">
                    <div class="option-letter">B</div>
                    <div class="option-text">查杀病毒</div>
                </div>
                <div class="option-card" onclick="selectOption(this, 'C', true)">
                    <div class="option-letter">C</div>
                    <div class="option-text">清除Cookie</div>
                </div>
                <div class="option-card" onclick="selectOption(this, 'D', false)">
                    <div class="option-letter">D</div>
                    <div class="option-text">禁用脚本</div>
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：C - 清除Cookie</strong></p>
                <p>🍪 <strong>Cookie</strong>：网站存储在浏览器中的小文件，记录用户的浏览历史、登录状态、购物车内容等信息。</p>
                <p>🔍 <strong>为什么其他选项不对：</strong></p>
                <p>• <strong>ActiveX控件</strong>：主要用于网页交互功能，禁用它不能清除已有的上网痕迹</p>
                <p>• <strong>查杀病毒</strong>：用于清除恶意软件，与清除上网痕迹无关</p>
                <p>• <strong>禁用脚本</strong>：阻止网页脚本运行，但不能清除历史记录</p>
                <p>🎯 <strong>记忆技巧</strong>：想象Cookie像面包屑一样，记录你走过的路径，要清除痕迹就要清理这些"面包屑"！</p>
            </div>
        </div>
    </div>

    <script>
        // 粒子动画
        const canvas = document.querySelector('.floating-particles');
        const ctx = canvas.getContext('2d');
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        const particles = [];
        
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
        
        function animateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                ctx.fill();
            });
            
            requestAnimationFrame(animateParticles);
        }
        
        animateParticles();
        
        // 上网痕迹演示
        const traces = [
            { text: 'Cookie: user_id=12345', x: 50, y: 50 },
            { text: 'Cookie: login_token=abc123', x: 300, y: 80 },
            { text: 'Cookie: shopping_cart=item1,item2', x: 100, y: 150 },
            { text: 'Cookie: last_visit=2024-01-15', x: 350, y: 200 },
            { text: 'Cookie: preferences=dark_mode', x: 150, y: 280 },
            { text: 'Cookie: session_id=xyz789', x: 400, y: 320 }
        ];
        
        function showTraces() {
            const browserContent = document.getElementById('browserContent');
            browserContent.innerHTML = '';
            
            traces.forEach((trace, index) => {
                setTimeout(() => {
                    const traceElement = document.createElement('div');
                    traceElement.className = 'trace-item';
                    traceElement.textContent = trace.text;
                    traceElement.style.left = trace.x + 'px';
                    traceElement.style.top = trace.y + 'px';
                    traceElement.style.opacity = '0';
                    traceElement.style.transform = 'scale(0)';
                    
                    browserContent.appendChild(traceElement);
                    
                    setTimeout(() => {
                        traceElement.style.opacity = '1';
                        traceElement.style.transform = 'scale(1)';
                        traceElement.style.transition = 'all 0.5s ease';
                    }, 100);
                }, index * 300);
            });
        }
        
        function clearCookies() {
            const traceItems = document.querySelectorAll('.trace-item');
            traceItems.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('removing');
                    setTimeout(() => {
                        item.remove();
                    }, 1000);
                }, index * 200);
            });
        }
        
        function resetDemo() {
            const browserContent = document.getElementById('browserContent');
            browserContent.innerHTML = '';
        }
        
        // 选项选择
        function selectOption(element, option, isCorrect) {
            // 移除所有选项的状态
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
                card.style.pointerEvents = 'none';
            });
            
            // 添加选择状态
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    document.getElementById('explanation').classList.add('show');
                }, 600);
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                setTimeout(() => {
                    document.querySelector('.option-card:nth-child(3)').classList.add('correct');
                    setTimeout(() => {
                        document.getElementById('explanation').classList.add('show');
                    }, 300);
                }, 600);
            }
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // 页面加载完成后的动画
        window.addEventListener('load', () => {
            setTimeout(showTraces, 1000);
        });
    </script>
</body>
</html>
