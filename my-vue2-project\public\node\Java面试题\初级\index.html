<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java核心概念交互式学习</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f9;
            color: #333;
            display: flex;
            min-height: 100vh;
        }
        nav {
            width: 220px;
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        nav h2 {
            margin-top: 0;
            text-align: center;
            color: #1abc9c;
        }
        nav ul {
            list-style: none;
            padding: 0;
        }
        nav ul li a {
            display: block;
            padding: 12px 15px;
            color: #ecf0f1;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: background-color 0.3s, color 0.3s;
        }
        nav ul li a:hover, nav ul li a.active {
            background-color: #1abc9c;
            color: #2c3e50;
        }
        main {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }
        section {
            display: none;
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        section.active {
            display: block;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #1abc9c;
            padding-bottom: 10px;
        }
        .content-wrapper {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .explanation, .code-view {
            flex: 1;
        }
        .interactive-demo {
            flex-basis: 450px;
            min-width: 450px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        canvas {
            border: 1px solid #bdc3c7;
            background-color: #ecf0f1;
            border-radius: 4px;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
        }
        code .keyword { color: #f92672; }
        code .type { color: #66d9ef; }
        code .string { color: #a6e22e; }
        code .comment { color: #75715e; }
        code .number { color: #ae81ff; }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        input[type="text"], input[type="number"] {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
            width: 100px;
        }
    </style>
</head>
<body>
    <nav>
        <h2>Java核心概念</h2>
        <ul>
            <li><a href="#encapsulation" class="nav-link active">1. 封装 (Encapsulation)</a></li>
            <li><a href="#inheritance" class="nav-link">2. 继承 (Inheritance)</a></li>
            <li><a href="#polymorphism" class="nav-link">3. 多态 (Polymorphism)</a></li>
        </ul>
    </nav>
    <main>
        <section id="encapsulation" class="active">
            <h1>1. 封装 (Encapsulation)</h1>
            <div class="content-wrapper">
                <div class="explanation">
                    <p><strong>定义：</strong>封装就像一个"胶囊"，把对象的<strong>数据（属性）</strong>和<strong>操作数据的方法（行为）</strong>包装在一起。</p>
                    <p>它把内部复杂的实现细节隐藏起来，只对外提供一些简单的"接口"（公共方法）来访问这些数据。</p>
                    <p><strong>好处：</strong></p>
                    <ul>
                        <li><strong>安全：</strong>可以保护数据不被错误地修改（例如，年龄不能是负数）。</li>
                        <li><strong>简单：</strong>使用者不需要知道内部怎么工作，只需调用方法即可。</li>
                    </ul>
                    <p>下面的动画演示了一个 `Person` 对象。它的 `name` 和 `age` 属性是私有的，你只能通过 `set` 和 `get` 方法来与它们交互。</p>
                </div>
                <div class="interactive-demo">
                    <canvas id="encapsulation-canvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <input type="text" id="person-name" placeholder="姓名">
                        <input type="number" id="person-age" placeholder="年龄">
                        <button id="encap-set-btn">设置属性</button>
                        <button id="encap-get-btn">获取属性</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="inheritance">
            <h1>2. 继承 (Inheritance)</h1>
             <div class="content-wrapper">
                <div class="explanation">
                    <p><strong>定义：</strong>继承允许一个类（子类）获取另一个类（父类）的属性和方法。就像孩子继承父母的特征一样。</p>
                    <p>子类不仅拥有父类的一切（非私有的），还可以添加自己独有的新功能，或者"重写"父类的功能，让它更适合自己。</p>
                    <p><strong>好处：</strong></p>
                    <ul>
                        <li><strong>代码复用：</strong>通用的代码放在父类，子类直接用，不用重复写。</li>
                        <li><strong>扩展性：</strong>可以方便地创建功能更强大的新类。</li>
                    </ul>
                    <p>动画里有一个 `Animal` 父类和 `Dog` 子类。`Dog` 从 `Animal` 继承了 `name` 属性和 `eat()` 方法，并添加了自己的 `bark()` 方法。</p>
                </div>
                <div class="interactive-demo">
                    <canvas id="inheritance-canvas" width="400" height="300"></canvas>
                    <div class="controls">
                         <button id="dog-eat-btn">调用 dog.eat()</button>
                         <button id="dog-bark-btn">调用 dog.bark()</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="polymorphism">
            <h1>3. 多态 (Polymorphism)</h1>
            <div class="content-wrapper">
                <div class="explanation">
                    <p><strong>定义：</strong>多态的字面意思是"多种形态"。在编程中，它指的是<strong>同一个指令（比如方法调用），作用在不同的对象上，会产生不同的行为</strong>。</p>
                    <p>它通常需要和<strong>继承</strong>与<strong>方法重写</strong>配合使用。我们用一个父类的引用去指向一个子类的对象。</p>
                    <p><strong>例子：</strong>命令"叫！"，对狗来说是"汪汪"，对猫来说是"喵喵"。指令相同，行为不同。</p>
                    <p>下方的演示中，我们都使用 `Shape` 类型的引用 `s` 来执行 `draw()` 方法，但它会根据 `s` 实际指向的是 `Circle` 对象还是 `Rectangle` 对象，来画出不同的图形。</p>
                </div>
                <div class="interactive-demo">
                    <canvas id="polymorphism-canvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button id="poly-circle-btn">Shape s = new Circle()</button>
                        <button id="poly-rect-btn">Shape s = new Rectangle()</button>
                        <button id="poly-draw-btn" disabled>调用 s.draw()</button>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <script>
    // 通用工具函数
    function drawBox(ctx, x, y, width, height, color, text, textColor = '#fff') {
        ctx.fillStyle = color;
        ctx.fillRect(x, y, width, height);
        ctx.fillStyle = textColor;
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x + width / 2, y + height / 2);
    }

    // 导航逻辑
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('main section');

    navLinks.forEach(link => {
        link.addEventListener('click', e => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);

            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            
            sections.forEach(s => {
                s.classList.remove('active');
                if (s.id === targetId) {
                    s.classList.add('active');
                }
            });
            // 初始化或重绘当前活动标签的动画
            switch(targetId) {
                case 'encapsulation': initEncapsulation(); break;
                case 'inheritance': initInheritance(); break;
                case 'polymorphism': initPolymorphism(); break;
            }
        });
    });


    // 1. 封装 (Encapsulation)
    const encapCanvas = document.getElementById('encapsulation-canvas');
    const encapCtx = encapCanvas.getContext('2d');
    let personData = { name: '?', age: '?' };
    let message = { text: '', alpha: 0 };

    function drawEncapsulation(highlight = null, dataFlow = null) {
        encapCtx.clearRect(0, 0, encapCanvas.width, encapCanvas.height);
        
        // Person a's box
        encapCtx.strokeStyle = '#2c3e50';
        encapCtx.lineWidth = 2;
        encapCtx.strokeRect(75, 50, 250, 200);
        encapCtx.font = 'bold 16px Arial';
        encapCtx.fillStyle = '#2c3e50';
        encapCtx.textAlign = 'center';
        encapCtx.fillText('Person 对象', 200, 35);

        // Private properties
        drawBox(encapCtx, 125, 120, 150, 40, '#c0392b', `private String name = "${personData.name}"`);
        drawBox(encapCtx, 125, 180, 150, 40, '#c0392b', `private int age = ${personData.age}`);
        
        // Public methods (gates)
        drawBox(encapCtx, 10, 80, 100, 30, highlight === 'set' ? '#2980b9' : '#3498db', 'setName()');
        drawBox(encapCtx, 10, 120, 100, 30, highlight === 'set' ? '#2980b9' : '#3498db', 'setAge()');
        drawBox(encapCtx, 290, 80, 100, 30, highlight === 'get' ? '#2980b9' : '#3498db', 'getName()');
        drawBox(encapCtx, 290, 120, 100, 30, highlight === 'get' ? '#2980b9' : '#3498db', 'getAge()');

        // Data flow animation
        if (dataFlow) {
            encapCtx.fillStyle = dataFlow.color;
            encapCtx.font = '14px Arial';
            encapCtx.fillText(dataFlow.text, dataFlow.x, dataFlow.y);
        }
        
        // Message
        if (message.alpha > 0) {
            encapCtx.fillStyle = `rgba(231, 76, 60, ${message.alpha})`;
            encapCtx.font = 'bold 16px Arial';
            encapCtx.fillText(message.text, 200, 280);
        }
    }

    function animateMessage(text) {
        message.text = text;
        message.alpha = 1.0;
        let fadeOut = setInterval(() => {
            message.alpha -= 0.05;
            if (message.alpha <= 0) {
                message.alpha = 0;
                clearInterval(fadeOut);
            }
            drawEncapsulation();
        }, 100);
    }

    function initEncapsulation() {
        personData = { name: '?', age: '?' };
        drawEncapsulation();
    }

    document.getElementById('encap-set-btn').addEventListener('click', () => {
        const nameInput = document.getElementById('person-name');
        const ageInput = document.getElementById('person-age');
        const newName = nameInput.value || personData.name;
        const newAge = parseInt(ageInput.value);

        if (isNaN(newAge) || newAge < 0) {
            animateMessage("年龄不能为负数!");
            return;
        }

        personData.name = newName;
        personData.age = newAge;

        let x = -30;
        const anim = setInterval(() => {
            x += 3;
            if (x > 90) {
                clearInterval(anim);
                drawEncapsulation();
            } else {
                drawEncapsulation('set', { text: `"${newName}", ${newAge}`, x: x, y: 105, color: '#27ae60' });
            }
        }, 10);
    });

    document.getElementById('encap-get-btn').addEventListener('click', () => {
         let x = 350;
         const anim = setInterval(() => {
            x -= 3;
            if (x < 200) {
                clearInterval(anim);
                drawEncapsulation();
                animateMessage(`获取到: ${personData.name}, ${personData.age}`);
            } else {
                drawEncapsulation('get', { text: `"${personData.name}", ${personData.age}`, x: x, y: 105, color: '#f39c12' });
            }
        }, 10);
    });

    // 2. 继承 (Inheritance)
    const inhCanvas = document.getElementById('inheritance-canvas');
    const inhCtx = inhCanvas.getContext('2d');
    
    function drawInheritance(highlight = null) {
        inhCtx.clearRect(0, 0, inhCanvas.width, inhCanvas.height);
        
        // Animal (Parent)
        inhCtx.strokeStyle = '#2c3e50';
        inhCtx.lineWidth = 2;
        inhCtx.strokeRect(100, 20, 200, 100);
        inhCtx.fillStyle = '#2c3e50';
        inhCtx.textAlign = 'center';
        inhCtx.font = 'bold 16px Arial';
        inhCtx.fillText('Animal (父类)', 200, 40);
        drawBox(inhCtx, 125, 60, 150, 25, '#3498db', 'name: "旺财"');
        drawBox(inhCtx, 125, 90, 150, 25, highlight === 'eat' ? '#2ecc71' : '#16a085', 'eat()');

        // Dog (Child)
        inhCtx.strokeRect(100, 180, 200, 80);
        inhCtx.font = 'bold 16px Arial';
        inhCtx.fillStyle = '#2c3e50';
        inhCtx.fillText('Dog (子类)', 200, 200);
        drawBox(inhCtx, 125, 220, 150, 25, highlight === 'bark' ? '#e74c3c' : '#c0392b', 'bark()');

        // Inheritance arrow
        inhCtx.beginPath();
        inhCtx.moveTo(200, 180);
        inhCtx.lineTo(200, 130);
        inhCtx.lineTo(195, 135);
        inhCtx.moveTo(200, 130);
        inhCtx.lineTo(205, 135);
        inhCtx.strokeStyle = '#2c3e50';
        inhCtx.lineWidth = 2;
        inhCtx.stroke();
    }

    function initInheritance() {
        drawInheritance();
    }
    
    document.getElementById('dog-eat-btn').addEventListener('click', () => {
        let alpha = 0;
        const anim = setInterval(() => {
            alpha += 0.1;
            drawInheritance('eat');
            inhCtx.fillStyle = `rgba(46, 204, 113, ${alpha})`;
            inhCtx.font = '14px Arial';
            inhCtx.fillText("调用父类的 eat() 方法", 200, 150);
            if(alpha >= 1) clearInterval(anim);
        }, 50);
        setTimeout(() => drawInheritance(), 1500);
    });

    document.getElementById('dog-bark-btn').addEventListener('click', () => {
        let alpha = 0;
        const anim = setInterval(() => {
            alpha += 0.1;
            drawInheritance('bark');
            inhCtx.fillStyle = `rgba(231, 76, 60, ${alpha})`;
            inhCtx.font = '14px Arial';
            inhCtx.fillText("调用自己的 bark() 方法", 200, 275);
            if(alpha >= 1) clearInterval(anim);
        }, 50);
        setTimeout(() => drawInheritance(), 1500);
    });


    // 3. 多态 (Polymorphism)
    const polyCanvas = document.getElementById('polymorphism-canvas');
    const polyCtx = polyCanvas.getContext('2d');
    let currentShape = null;

    function drawPolymorphism(state) {
        polyCtx.clearRect(0, 0, polyCanvas.width, polyCanvas.height);

        // Code display area
        polyCtx.fillStyle = '#34495e';
        polyCtx.font = "16px 'Consolas', monospace";
        polyCtx.textAlign = 'left';
        polyCtx.fillText("Shape s;", 20, 30);
        if(state.line2) {
            polyCtx.fillText(state.line2, 20, 60);
        }
        
        // Drawing area
        polyCtx.strokeStyle = '#bdc3c7';
        polyCtx.strokeRect(20, 100, 200, 180);
        polyCtx.fillStyle = '#7f8c8d';
        polyCtx.font = '14px Arial';
        polyCtx.textAlign = 'center';
        polyCtx.fillText('绘制区域', 120, 90);

        if(state.shapeToDraw) {
            polyCtx.fillStyle = '#e74c3c';
            if (state.shapeToDraw === 'circle') {
                polyCtx.beginPath();
                polyCtx.arc(120, 190, 50, 0, 2 * Math.PI);
                polyCtx.fill();
            } else if(state.shapeToDraw === 'rectangle') {
                polyCtx.fillRect(70, 140, 100, 100);
            }
            polyCtx.fillStyle = '#fff';
            polyCtx.font = 'bold 16px Arial';
            polyCtx.fillText(state.shapeToDraw, 120, 190);
        }
        
        // Object area
        polyCtx.font = '14px Arial';
        polyCtx.fillStyle = '#7f8c8d';
        polyCtx.fillText('对象内存区域', 310, 90);
        drawBox(polyCtx, 250, 120, 120, 50, '#3498db', 'new Circle()');
        drawBox(polyCtx, 250, 200, 120, 50, '#9b59b6', 'new Rectangle()');

        // Pointer
        if(state.pointsTo) {
            polyCtx.beginPath();
            polyCtx.moveTo(100, 45); // from 's'
            polyCtx.strokeStyle = '#2c3e50';
            polyCtx.lineWidth = 2;
            if(state.pointsTo === 'circle') {
                 polyCtx.lineTo(250, 145);
            } else if(state.pointsTo === 'rectangle') {
                 polyCtx.lineTo(250, 225);
            }
            polyCtx.stroke();
        }
    }

    function initPolymorphism() {
        currentShape = null;
        document.getElementById('poly-draw-btn').disabled = true;
        drawPolymorphism({});
    }

    document.getElementById('poly-circle-btn').addEventListener('click', () => {
        currentShape = 'circle';
        document.getElementById('poly-draw-btn').disabled = false;
        drawPolymorphism({ line2: 's = new Circle();', pointsTo: 'circle'});
    });
    
    document.getElementById('poly-rect-btn').addEventListener('click', () => {
        currentShape = 'rectangle';
        document.getElementById('poly-draw-btn').disabled = false;
        drawPolymorphism({ line2: 's = new Rectangle();', pointsTo: 'rectangle'});
    });
    
    document.getElementById('poly-draw-btn').addEventListener('click', () => {
        if(!currentShape) return;
        drawPolymorphism({
            line2: `s = new ${currentShape.charAt(0).toUpperCase() + currentShape.slice(1)}();`,
            pointsTo: currentShape,
            shapeToDraw: currentShape
        });
        
        let alpha = 0;
        const anim = setInterval(() => {
            alpha += 0.1;
            polyCtx.fillStyle = `rgba(46, 204, 113, ${alpha})`;
            polyCtx.font = 'bold 16px Arial';
            polyCtx.textAlign = 'left';
            polyCtx.fillText('s.draw();', 20, 85);
            if (alpha >= 1) clearInterval(anim);
        }, 50)

    });

    // 初始加载
    window.onload = () => {
        document.querySelector('.nav-link.active').click();
    };

    </script>
</body>
</html> 