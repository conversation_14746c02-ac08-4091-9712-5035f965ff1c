<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动内存管理 - 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        #container {
            width: 100%;
            max-width: 800px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 25px;
            text-align: center;
        }
        h1 {
            color: #1a237e;
            font-size: 2em;
            margin-bottom: 10px;
        }
        h2 {
            color: #3949ab;
            font-size: 1.5em;
            margin-top: 30px;
            border-bottom: 2px solid #e8eaf6;
            padding-bottom: 5px;
        }
        canvas {
            border: 2px solid #c5cae9;
            border-radius: 8px;
            margin-top: 15px;
            background-color: #e8f5e9;
            width: 100%;
            height: auto;
        }
        #controls {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 24px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button:hover {
            background-color: #303f9f;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        button.secondary {
            background-color: #f44336;
        }
        button.secondary:hover {
            background-color: #d32f2f;
        }
        #explanation, #summary {
            text-align: left;
            margin: 20px auto;
            padding: 0 15px;
            line-height: 1.8;
            font-size: 1.1em;
        }
        #summary ul {
            list-style-type: none;
            padding-left: 0;
        }
        #summary li {
            background: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%233f51b5" d="M10 17l5-5-5-5v10z"/></svg>') no-repeat left center;
            padding-left: 30px;
            margin-bottom: 12px;
            background-size: 20px;
        }
        .status-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background-color: #e3f2fd;
            color: #0d47a1;
            font-weight: bold;
            transition: all 0.5s;
        }
    </style>
</head>
<body>

<div id="container">
    <h1>4. 自动内存管理</h1>
    <div id="summary">
        <p>现代编程语言大多提供自动内存管理，核心是以下几点：</p>
        <ul>
            <li>通过<b>垃圾回收 (GC)</b> 机制回收无用对象，自动释放内存。</li>
            <li>减少手动管理内存带来的<b>内存泄漏</b>风险，让开发者更专注于业务逻辑。</li>
            <li>但需注意避免<b>内存溢出 (OOM)</b> 问题，即内存被耗尽。</li>
        </ul>
    </div>

    <h2>交互式演示：内存的世界</h2>
    <div id="explanation">
        <p>把下面这块绿色区域想象成你程序的<b>内存空间</b>。我们用彩色的圆圈代表程序中创建的<b>"对象"</b>。一个特殊的"程序根目录"（左边的蓝色方块）会连接到所有正在被使用的对象。</p>
    </div>

    <canvas id="memoryCanvas" width="740" height="400"></canvas>
    <div id="status-box">当前状态：准备就绪。请创建一些对象。</div>

    <div id="controls">
        <button id="createBtn">1. 创建对象</button>
        <button id="unreferenceBtn">2. 随机释放引用</button>
        <button id="gcBtn" class="secondary">3. 启动垃圾回收 (GC)</button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // --- 캔버스 및 컨텍스트 설정 ---
    const canvas = document.getElementById('memoryCanvas');
    const ctx = canvas.getContext('2d');
    const statusBox = document.getElementById('status-box');

    // --- 상태 변수 ---
    let objects = [];
    let animationFrameId;
    const root = { x: 50, y: canvas.height / 2, size: 40 };

    // --- 유틸리티 클래스 ---
    class MemoryObject {
        constructor() {
            this.radius = 12;
            this.x = root.x + 100 + Math.random() * (canvas.width - 250);
            this.y = this.radius + Math.random() * (canvas.height - this.radius * 2);
            this.isReferenced = true;
            this.color = '#4CAF50'; // Green: Referenced
            this.targetColor = '#4CAF50';
        }

        update() {
            // 색상 전환 효과
            if (this.color !== this.targetColor) {
                const r = parseInt(this.color.slice(1, 3), 16);
                const g = parseInt(this.color.slice(3, 5), 16);
                const b = parseInt(this.color.slice(5, 7), 16);
                const tr = parseInt(this.targetColor.slice(1, 3), 16);
                const tg = parseInt(this.targetColor.slice(3, 5), 16);
                const tb = parseInt(this.targetColor.slice(5, 7), 16);

                const newR = Math.round(r + (tr - r) * 0.1);
                const newG = Math.round(g + (tg - g) * 0.1);
                const newB = Math.round(b + (tb - b) * 0.1);

                this.color = `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
            }
        }

        draw() {
            // Draw object
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.stroke();
            ctx.closePath();

            // Draw reference line
            if (this.isReferenced) {
                ctx.beginPath();
                ctx.moveTo(root.x, root.y);
                ctx.lineTo(this.x - this.radius, this.y);
                ctx.strokeStyle = 'rgba(63, 81, 181, 0.6)';
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.lineWidth = 1;
            }
        }

        loseReference() {
            this.isReferenced = false;
            this.targetColor = '#9E9E9E'; // Gray: Unreferenced
        }
    }

    // --- 핵심 로직 ---
    function createObject() {
        if (objects.length > 30) {
            updateStatus("内存已满！(模拟OOM)", 'error');
            return;
        }
        const obj = new MemoryObject();
        objects.push(obj);
        updateStatus("创建了一个新对象！它正被程序使用。");
    }

    function unreferenceRandomObjects() {
        const referencedObjects = objects.filter(o => o.isReferenced);
        if (referencedObjects.length === 0) {
            updateStatus("没有可释放引用的对象。", "warn");
            return;
        }
        const objToUnref = referencedObjects[Math.floor(Math.random() * referencedObjects.length)];
        objToUnref.loseReference();
        updateStatus("一个对象不再被需要，我们断开了它的引用（它变灰了）。");
    }

    function runGarbageCollection() {
        updateStatus("垃圾回收(GC)启动！正在寻找无用的灰色对象...");
        // 1. Mark phase
        const garbage = objects.filter(o => !o.isReferenced);
        if (garbage.length === 0) {
            updateStatus("GC 完成，没有发现可回收的对象。", "info");
            return;
        }

        // 2. Sweep phase animation
        setTimeout(() => {
            updateStatus(`GC 发现 ${garbage.length} 个无用对象，正在清理...`);
            let collectedCount = 0;
            const sweepInterval = setInterval(() => {
                if (collectedCount >= garbage.length) {
                    clearInterval(sweepInterval);
                    objects = objects.filter(o => o.isReferenced);
                    updateStatus(`GC 完成！${garbage.length} 个对象被清理，内存已释放。`, "success");
                    return;
                }
                const objToSweep = garbage[collectedCount];
                objToSweep.radius -= 0.5; // Shrink animation
                if (objToSweep.radius <= 0) {
                    collectedCount++;
                }
            }, 20);
        }, 1000);
    }

    function updateStatus(message, type = 'info') {
        statusBox.textContent = message;
        statusBox.style.backgroundColor = {
            info: '#e3f2fd',
            success: '#e8f5e9',
            warn: '#fffde7',
            error: '#ffebee'
        }[type];
        statusBox.style.color = {
            info: '#0d47a1',
            success: '#1b5e20',
            warn: '#f57f17',
            error: '#b71c1c'
        }[type];
    }

    // --- 그리기 함수 ---
    function draw() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw background
        ctx.fillStyle = '#f1f8e9';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw the root
        ctx.fillStyle = '#3f51b5';
        ctx.fillRect(root.x - root.size / 2, root.y - root.size / 2, root.size, root.size);
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 12px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('ROOT', root.x, root.y + 4);


        // Draw all objects
        objects.forEach(obj => {
            obj.update();
            obj.draw();
        });
    }

    // --- 애니메이션 루프 ---
    function animate() {
        draw();
        animationFrameId = requestAnimationFrame(animate);
    }

    // --- 이벤트 리스너 ---
    document.getElementById('createBtn').addEventListener('click', createObject);
    document.getElementById('unreferenceBtn').addEventListener('click', unreferenceRandomObjects);
    document.getElementById('gcBtn').addEventListener('click', runGarbageCollection);

    // --- 초기화 ---
    animate();
});
</script>

</body>
</html> 