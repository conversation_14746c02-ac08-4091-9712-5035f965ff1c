<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库三级模式结构 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-2px);
        }

        .option.selected {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        .canvas-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
        }

        #animationCanvas {
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #4caf50;
            color: white;
            transform: scale(1.2);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 数据库三级模式结构</h1>
            <p class="subtitle">通过动画和交互，轻松理解数据库的层次结构</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">
                📝 【软考达人-回忆版】采用三级模式结构的数据库系统中，如果对一个表创建聚索引，那么改变的是数据库的（ ）
            </h2>
            
            <div class="options">
                <div class="option" data-answer="A">A. 外模式</div>
                <div class="option" data-answer="B">B. 模式</div>
                <div class="option" data-answer="C">C. 内模式</div>
                <div class="option" data-answer="D">D. 用户模式</div>
            </div>

            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
        </div>

        <div class="learning-section">
            <h2>🎯 让我们一步步理解数据库三级模式结构</h2>
            
            <div class="canvas-container">
                <canvas id="animationCanvas"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn" onclick="showIndexDemo()">🔍 聚索引演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
            </div>

            <div id="explanationArea">
                <div class="explanation">
                    <h3>🤔 什么是数据库三级模式结构？</h3>
                    <p>想象数据库就像一座大楼，有三个不同的层次：</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>外模式（用户视图）</strong>：就像大楼的不同房间，每个用户看到的都不一样</li>
                        <li><strong>模式（逻辑结构）</strong>：就像大楼的整体设计图，定义了所有房间的布局</li>
                        <li><strong>内模式（物理存储）</strong>：就像大楼的地基和钢筋结构，决定数据如何实际存储</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let isAnimating = false;

        // 设置canvas尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 动画状态
        let currentStep = 1;
        let selectedAnswer = null;

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (selectedAnswer) return; // 已经选择过了
                
                selectedAnswer = this.dataset.answer;
                this.classList.add('selected');
                
                // 显示正确答案
                setTimeout(() => {
                    document.querySelectorAll('.option').forEach(opt => {
                        if (opt.dataset.answer === 'C') {
                            opt.classList.add('selected');
                        } else if (opt.dataset.answer !== selectedAnswer) {
                            opt.style.opacity = '0.5';
                        } else if (opt.dataset.answer !== 'C') {
                            opt.classList.remove('selected');
                            opt.classList.add('wrong');
                        }
                    });
                    
                    updateStep(2);
                    showAnswerExplanation();
                }, 1000);
            });
        });

        function updateStep(step) {
            document.querySelectorAll('.step').forEach((s, index) => {
                s.classList.remove('active');
                if (index + 1 <= step) {
                    s.classList.add('active');
                }
            });
            currentStep = step;
        }

        function showAnswerExplanation() {
            const explanationArea = document.getElementById('explanationArea');
            explanationArea.innerHTML = `
                <div class="explanation">
                    <h3>✅ 正确答案：C. 内模式</h3>
                    <p><strong>为什么是内模式？</strong></p>
                    <p>聚索引（聚集索引）决定了数据在磁盘上的<strong>物理存储顺序</strong>。当你创建聚索引时，数据库会重新组织表中数据的物理排列方式，这直接影响的是数据的存储结构，也就是内模式。</p>
                </div>
                <div class="explanation">
                    <h3>🏗️ 三级模式详解：</h3>
                    <p><strong>外模式</strong>：用户看到的视图，不涉及物理存储</p>
                    <p><strong>模式</strong>：逻辑结构定义，表的字段、关系等</p>
                    <p><strong>内模式</strong>：物理存储方式，包括索引、存储路径、数据排列等</p>
                </div>
            `;
            updateStep(3);
        }

        // 绘制函数
        function drawBuilding() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制三层建筑
            const buildingWidth = 300;
            const floorHeight = 80;
            
            // 内模式（地基）
            ctx.fillStyle = '#8d6e63';
            ctx.fillRect(centerX - buildingWidth/2, centerY + floorHeight, buildingWidth, floorHeight);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('内模式 (物理存储)', centerX, centerY + floorHeight + 40);
            ctx.fillText('索引、存储路径、数据排列', centerX, centerY + floorHeight + 60);
            
            // 模式（中间层）
            ctx.fillStyle = '#5c6bc0';
            ctx.fillRect(centerX - buildingWidth/2, centerY, buildingWidth, floorHeight);
            ctx.fillStyle = 'white';
            ctx.fillText('模式 (逻辑结构)', centerX, centerY + 40);
            ctx.fillText('表结构、字段、关系', centerX, centerY + 60);
            
            // 外模式（顶层）
            ctx.fillStyle = '#42a5f5';
            ctx.fillRect(centerX - buildingWidth/2, centerY - floorHeight, buildingWidth, floorHeight);
            ctx.fillStyle = 'white';
            ctx.fillText('外模式 (用户视图)', centerX, centerY - floorHeight + 40);
            ctx.fillText('用户看到的界面', centerX, centerY - floorHeight + 60);
        }

        function startAnimation() {
            updateStep(4);
            isAnimating = true;
            animationStep = 0;
            animateBuilding();
        }

        function animateBuilding() {
            if (!isAnimating) return;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const buildingWidth = 300;
            const floorHeight = 80;
            
            // 动画效果：从下往上建造
            const progress = Math.min(animationStep / 100, 1);
            
            if (progress > 0.33) {
                // 内模式
                ctx.fillStyle = '#8d6e63';
                ctx.fillRect(centerX - buildingWidth/2, centerY + floorHeight, buildingWidth, floorHeight);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('内模式 (物理存储)', centerX, centerY + floorHeight + 40);
                ctx.fillText('🔍 聚索引在这里工作！', centerX, centerY + floorHeight + 60);
            }
            
            if (progress > 0.66) {
                // 模式
                ctx.fillStyle = '#5c6bc0';
                ctx.fillRect(centerX - buildingWidth/2, centerY, buildingWidth, floorHeight);
                ctx.fillStyle = 'white';
                ctx.fillText('模式 (逻辑结构)', centerX, centerY + 40);
                ctx.fillText('表结构、字段、关系', centerX, centerY + 60);
            }
            
            if (progress > 0.99) {
                // 外模式
                ctx.fillStyle = '#42a5f5';
                ctx.fillRect(centerX - buildingWidth/2, centerY - floorHeight, buildingWidth, floorHeight);
                ctx.fillStyle = 'white';
                ctx.fillText('外模式 (用户视图)', centerX, centerY - floorHeight + 40);
                ctx.fillText('用户看到的界面', centerX, centerY - floorHeight + 60);
                
                isAnimating = false;
                return;
            }
            
            animationStep += 2;
            requestAnimationFrame(animateBuilding);
        }

        function showIndexDemo() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制数据存储示意图
            ctx.fillStyle = '#8d6e63';
            ctx.fillRect(centerX - 200, centerY, 400, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('💾 物理存储层 (内模式)', centerX, centerY + 30);
            ctx.fillText('聚索引重新排列数据的物理顺序', centerX, centerY + 55);
            ctx.fillText('📊 数据按索引键值有序存储', centerX, centerY + 80);
            
            // 绘制箭头和说明
            ctx.strokeStyle = '#f44336';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 250, centerY + 50);
            ctx.lineTo(centerX - 210, centerY + 50);
            ctx.stroke();
            
            ctx.fillStyle = '#f44336';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('聚索引影响这里！', centerX - 350, centerY + 55);
        }

        function resetAnimation() {
            isAnimating = false;
            animationStep = 0;
            drawBuilding();
        }

        // 初始化
        drawBuilding();
    </script>
</body>
</html>
