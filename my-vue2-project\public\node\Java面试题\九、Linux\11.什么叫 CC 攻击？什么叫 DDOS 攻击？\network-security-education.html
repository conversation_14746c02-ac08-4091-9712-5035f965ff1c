<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络攻击防护 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .game-area {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 网络攻击防护学习</h1>
            <p>通过动画和交互体验，轻松掌握网络安全知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是网络攻击？</h2>
            <div class="explanation">
                <strong>网络攻击</strong>是指通过大量合法的请求占用大量网络资源，以达到瘫痪网络的目的。
                就像很多人同时挤进一个小门，导致正常人无法通过。
            </div>
            <div class="canvas-container">
                <canvas id="attackCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startAttackDemo()">🚀 开始攻击演示</button>
            <button class="interactive-btn" onclick="resetAttackDemo()">🔄 重置演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">💥 CC攻击详解</h2>
            <div class="explanation">
                <strong>CC攻击</strong>主要是用来攻击页面的，模拟多个用户不停的对你的页面进行访问，
                从而使你的系统资源消耗殆尽。就像无数人不停地敲门，让主人无法正常生活。
            </div>
            <div class="canvas-container">
                <canvas id="ccCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startCCDemo()">⚡ 模拟CC攻击</button>
            <button class="interactive-btn" onclick="stopCCDemo()">⏹️ 停止攻击</button>
        </div>

        <div class="section">
            <h2 class="section-title">🌐 DDOS攻击原理</h2>
            <div class="explanation">
                <strong>DDOS攻击</strong>是分布式拒绝攻击，攻击者操纵大量计算机，在短时间内通过将攻击伪装成大量的合法请求，
                向服务器资源发动攻击，导致请求数量超过了服务器的处理能力，造成服务器运行缓慢或者宕机。
            </div>
            <div class="canvas-container">
                <canvas id="ddosCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startDDOSDemo()">🔥 启动DDOS演示</button>
            <button class="interactive-btn" onclick="defendDDOS()">🛡️ 启动防护</button>
        </div>

        <div class="section">
            <h2 class="section-title">🛡️ 防护措施</h2>
            <div class="explanation">
                防CC、DDOS攻击，主要通过<strong>硬件防火墙</strong>做流量清洗，将攻击流量引入黑洞。
                流量清洗主要是购买ISP服务商的防攻击服务，机房一般有空余流量来处理这些攻击。
            </div>
            <div class="canvas-container">
                <canvas id="defenseCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startDefenseDemo()">🔧 演示防护系统</button>
        </div>

        <div class="section">
            <div class="game-area">
                <h2 style="color: white; margin-bottom: 20px;">🎮 防护游戏挑战</h2>
                <div class="score" id="gameScore">得分: 0</div>
                <div class="canvas-container">
                    <canvas id="gameCanvas" width="600" height="400"></canvas>
                </div>
                <button class="interactive-btn" onclick="startGame()" style="background: white; color: #f5576c;">🎯 开始游戏</button>
                <p style="color: white; margin-top: 15px;">点击恶意请求来阻止它们攻击服务器！</p>
            </div>
        </div>
    </div>

    <script>
        let progress = 0;
        let gameScore = 0;
        let gameRunning = false;
        let attackAnimations = [];
        let defenseActive = false;

        // 更新进度条
        function updateProgress(value) {
            progress = Math.min(100, progress + value);
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 攻击演示动画
        function startAttackDemo() {
            const canvas = document.getElementById('attackCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器
            drawServer(ctx, canvas.width - 100, canvas.height / 2);
            
            // 绘制攻击请求
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    drawAttackRequest(ctx, 50, 50 + i * 20, canvas.width - 150, canvas.height / 2);
                }, i * 200);
            }
            
            updateProgress(20);
        }

        function resetAttackDemo() {
            const canvas = document.getElementById('attackCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawServer(ctx, canvas.width - 100, canvas.height / 2);
        }

        // CC攻击演示
        function startCCDemo() {
            const canvas = document.getElementById('ccCanvas');
            const ctx = canvas.getContext('2d');
            
            // 绘制网页服务器
            drawWebServer(ctx, canvas.width - 100, canvas.height / 2);
            
            // 持续的页面请求攻击
            const ccInterval = setInterval(() => {
                if (!document.getElementById('ccCanvas')) {
                    clearInterval(ccInterval);
                    return;
                }
                
                for (let i = 0; i < 5; i++) {
                    setTimeout(() => {
                        drawPageRequest(ctx, 50, Math.random() * canvas.height, canvas.width - 150, canvas.height / 2);
                    }, i * 100);
                }
            }, 500);
            
            // 存储interval以便停止
            canvas.ccInterval = ccInterval;
            updateProgress(20);
        }

        function stopCCDemo() {
            const canvas = document.getElementById('ccCanvas');
            if (canvas.ccInterval) {
                clearInterval(canvas.ccInterval);
            }
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawWebServer(ctx, canvas.width - 100, canvas.height / 2);
        }

        // DDOS攻击演示
        function startDDOSDemo() {
            const canvas = document.getElementById('ddosCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawServer(ctx, canvas.width / 2, canvas.height - 50);
            
            // 从多个位置发起攻击
            const attackSources = [
                {x: 50, y: 50},
                {x: 150, y: 80},
                {x: 250, y: 60},
                {x: 350, y: 90},
                {x: 450, y: 70}
            ];
            
            attackSources.forEach((source, index) => {
                drawAttacker(ctx, source.x, source.y);
                setTimeout(() => {
                    for (let i = 0; i < 8; i++) {
                        setTimeout(() => {
                            drawDistributedAttack(ctx, source.x, source.y, canvas.width / 2, canvas.height - 50);
                        }, i * 150);
                    }
                }, index * 300);
            });
            
            updateProgress(20);
        }

        function defendDDOS() {
            const canvas = document.getElementById('ddosCanvas');
            const ctx = canvas.getContext('2d');
            
            // 绘制防火墙
            drawFirewall(ctx, canvas.width / 2 - 100, canvas.height - 100);
            
            // 显示流量清洗效果
            setTimeout(() => {
                ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#00ff00';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🛡️ 防护成功！', canvas.width / 2, canvas.height / 2);
            }, 1000);
            
            updateProgress(20);
        }

        // 防护系统演示
        function startDefenseDemo() {
            const canvas = document.getElementById('defenseCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制完整的防护架构
            drawDefenseArchitecture(ctx);
            updateProgress(20);
        }

        // 游戏逻辑
        function startGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            gameRunning = true;
            gameScore = 0;
            updateGameScore();
            
            // 游戏循环
            gameLoop(ctx, canvas);
        }

        function gameLoop(ctx, canvas) {
            if (!gameRunning) return;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器
            drawServer(ctx, canvas.width / 2, canvas.height - 50);
            
            // 随机生成攻击
            if (Math.random() < 0.3) {
                const attack = {
                    x: Math.random() * (canvas.width - 100),
                    y: 0,
                    speed: 2 + Math.random() * 3,
                    type: Math.random() < 0.5 ? 'cc' : 'ddos'
                };
                attackAnimations.push(attack);
            }
            
            // 更新和绘制攻击
            attackAnimations = attackAnimations.filter(attack => {
                attack.y += attack.speed;
                
                if (attack.type === 'cc') {
                    drawCCAttack(ctx, attack.x, attack.y);
                } else {
                    drawDDOSAttack(ctx, attack.x, attack.y);
                }
                
                // 检查是否到达服务器
                if (attack.y > canvas.height - 100) {
                    gameScore = Math.max(0, gameScore - 10);
                    updateGameScore();
                    return false;
                }
                
                return attack.y < canvas.height;
            });
            
            setTimeout(() => gameLoop(ctx, canvas), 50);
        }

        // 游戏点击事件
        document.getElementById('gameCanvas').addEventListener('click', function(e) {
            if (!gameRunning) return;
            
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 检查是否点击到攻击
            attackAnimations = attackAnimations.filter(attack => {
                const distance = Math.sqrt((x - attack.x) ** 2 + (y - attack.y) ** 2);
                if (distance < 30) {
                    gameScore += 10;
                    updateGameScore();
                    
                    // 绘制爆炸效果
                    const ctx = this.getContext('2d');
                    drawExplosion(ctx, attack.x, attack.y);
                    
                    return false;
                }
                return true;
            });
        });

        function updateGameScore() {
            document.getElementById('gameScore').textContent = `得分: ${gameScore}`;
            if (gameScore >= 100) {
                gameRunning = false;
                alert('🎉 恭喜！您成功防护了服务器！');
                updateProgress(20);
            }
        }

        // 绘制函数
        function drawServer(ctx, x, y) {
            ctx.fillStyle = '#4a90e2';
            ctx.fillRect(x - 30, y - 20, 60, 40);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🖥️', x, y + 5);
        }

        function drawWebServer(ctx, x, y) {
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(x - 30, y - 20, 60, 40);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🌐', x, y + 5);
        }

        function drawAttacker(ctx, x, y) {
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💻', x, y + 4);
        }

        function drawFirewall(ctx, x, y) {
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(x, y, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🛡️', x + 40, y + 35);
        }

        function drawAttackRequest(ctx, startX, startY, endX, endY) {
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            
            // 箭头
            const angle = Math.atan2(endY - startY, endX - startX);
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - 10 * Math.cos(angle - Math.PI/6), endY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(endX - 10 * Math.cos(angle + Math.PI/6), endY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }

        function drawPageRequest(ctx, startX, startY, endX, endY) {
            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        function drawDistributedAttack(ctx, startX, startY, endX, endY) {
            ctx.strokeStyle = '#8e44ad';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
        }

        function drawDefenseArchitecture(ctx) {
            // ISP服务商
            ctx.fillStyle = '#3498db';
            ctx.fillRect(50, 50, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ISP服务商', 100, 85);
            
            // 防火墙
            drawFirewall(ctx, 250, 50);
            
            // 流量清洗
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(450, 50, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('流量清洗', 500, 85);
            
            // 服务器
            drawServer(ctx, 300, 200);
            
            // 连接线
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(150, 80);
            ctx.lineTo(250, 80);
            ctx.moveTo(330, 80);
            ctx.lineTo(450, 80);
            ctx.moveTo(300, 110);
            ctx.lineTo(300, 180);
            ctx.stroke();
        }

        function drawCCAttack(ctx, x, y) {
            ctx.fillStyle = '#f39c12';
            ctx.beginPath();
            ctx.arc(x, y, 10, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CC', x, y + 4);
        }

        function drawDDOSAttack(ctx, x, y) {
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(x, y, 12, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('DDOS', x, y + 3);
        }

        function drawExplosion(ctx, x, y) {
            ctx.fillStyle = '#f1c40f';
            for (let i = 0; i < 8; i++) {
                const angle = (i / 8) * 2 * Math.PI;
                const explosionX = x + Math.cos(angle) * 20;
                const explosionY = y + Math.sin(angle) * 20;
                ctx.beginPath();
                ctx.arc(explosionX, explosionY, 5, 0, 2 * Math.PI);
                ctx.fill();
            }
        }

        // 初始化
        window.onload = function() {
            // 初始化所有画布
            resetAttackDemo();
            
            // 添加悬停效果
            document.querySelectorAll('.interactive-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.classList.add('pulse');
                });
                btn.addEventListener('mouseleave', function() {
                    this.classList.remove('pulse');
                });
            });
        };
    </script>
</body>
</html>
