<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作系统死锁问题 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: #2d3748;
            text-align: center;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
        }

        .explanation h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #4a5568;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .quiz-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .quiz-option:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .quiz-option.correct {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .quiz-option.wrong {
            border-color: #f56565;
            background: #fff5f5;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">操作系统死锁问题</h1>
            <p class="subtitle">通过动画和交互学习进程调度与资源分配</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 第一部分：单处理器进程调度 -->
        <div class="section">
            <h2 class="section-title">🖥️ 单处理器进程调度</h2>
            <div class="explanation">
                <h3>核心概念</h3>
                <p>在<span class="highlight">单处理器系统</span>中，虽然可以有多个并发进程，但同一时刻只能有<span class="highlight">1个进程</span>占用处理器执行。</p>
                <p>其他进程处于等待状态，通过时间片轮转等调度算法实现"并发"执行的效果。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="processorCanvas" width="800" height="300"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startProcessorDemo()">开始演示</button>
                <button class="btn btn-secondary" onclick="resetProcessorDemo()">重置</button>
            </div>
        </div>

        <!-- 第二部分：死锁问题演示 -->
        <div class="section">
            <h2 class="section-title">⚠️ 死锁问题演示</h2>
            <div class="explanation">
                <h3>死锁条件</h3>
                <p>当3个进程都需要2个资源R，如果只有3个资源R：</p>
                <p>• 每个进程先获得1个资源R</p>
                <p>• 然后都等待第2个资源R</p>
                <p>• 但没有多余的资源可分配 → <span class="highlight">死锁！</span></p>
            </div>
            
            <div class="canvas-container">
                <canvas id="deadlockCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="demonstrateDeadlock()">演示死锁(3个资源)</button>
                <button class="btn btn-success" onclick="demonstrateNoDeadlock()">演示无死锁(4个资源)</button>
                <button class="btn btn-secondary" onclick="resetDeadlockDemo()">重置</button>
            </div>
        </div>

        <!-- 第三部分：互动测验 -->
        <div class="quiz-section">
            <h2 class="quiz-title">🎯 知识测验</h2>
            <p style="text-align: center; margin-bottom: 30px; font-size: 1.1rem;">
                如果这3个进程都要求使用2个互斥资源R，那么系统不产生死锁的最少的R资源数为多少个？
            </p>

            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <h3>A. 3个</h3>
                    <p>每个进程1个，刚好分完</p>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">
                    <h3>B. 4个</h3>
                    <p>确保至少有1个进程能完成</p>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <h3>C. 5个</h3>
                    <p>资源过多，浪费</p>
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    <h3>D. 6个</h3>
                    <p>每个进程2个，理想但非最少</p>
                </div>
            </div>

            <div id="quizResult" style="text-align: center; margin-top: 30px; font-size: 1.2rem; font-weight: 600;"></div>
        </div>

        <!-- 第四部分：总结 -->
        <div class="section">
            <h2 class="section-title">📚 知识总结</h2>
            <div class="explanation">
                <h3>银行家算法思想</h3>
                <p>避免死锁的关键是确保系统始终处于<span class="highlight">安全状态</span>：</p>
                <p>• 至少保留足够的资源让一个进程完成执行</p>
                <p>• 该进程完成后释放资源，供其他进程使用</p>
                <p>• 形成安全序列，避免死锁</p>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn btn-primary pulse" onclick="restartLearning()">重新学习</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let animationId;
        let processorCtx, deadlockCtx;

        // 初始化
        window.onload = function() {
            initCanvases();
            updateProgress(0);
        };

        function initCanvases() {
            const processorCanvas = document.getElementById('processorCanvas');
            const deadlockCanvas = document.getElementById('deadlockCanvas');

            processorCtx = processorCanvas.getContext('2d');
            deadlockCtx = deadlockCanvas.getContext('2d');

            drawInitialProcessor();
            drawInitialDeadlock();
        }

        // 处理器演示相关函数
        function drawInitialProcessor() {
            processorCtx.clearRect(0, 0, 800, 300);

            // 绘制处理器
            drawProcessor(400, 150, '#667eea', '处理器 (CPU)');

            // 绘制3个进程
            drawProcess(150, 80, '#f093fb', '进程1', 'waiting');
            drawProcess(150, 150, '#4facfe', '进程2', 'waiting');
            drawProcess(150, 220, '#48bb78', '进程3', 'waiting');

            // 添加说明文字
            processorCtx.fillStyle = '#2d3748';
            processorCtx.font = 'bold 16px Arial';
            processorCtx.textAlign = 'center';
            processorCtx.fillText('同一时刻只能有1个进程占用处理器', 400, 50);
        }

        function drawProcessor(x, y, color, label) {
            processorCtx.fillStyle = color;
            processorCtx.fillRect(x - 60, y - 30, 120, 60);
            processorCtx.fillStyle = 'white';
            processorCtx.font = 'bold 14px Arial';
            processorCtx.textAlign = 'center';
            processorCtx.fillText(label, x, y + 5);
        }

        function drawProcess(x, y, color, label, state) {
            // 进程圆圈
            processorCtx.fillStyle = color;
            processorCtx.beginPath();
            processorCtx.arc(x, y, 25, 0, 2 * Math.PI);
            processorCtx.fill();

            // 状态指示
            if (state === 'running') {
                processorCtx.strokeStyle = '#ffd700';
                processorCtx.lineWidth = 4;
                processorCtx.stroke();
            }

            // 标签
            processorCtx.fillStyle = 'white';
            processorCtx.font = 'bold 12px Arial';
            processorCtx.textAlign = 'center';
            processorCtx.fillText(label, x, y + 4);
        }

        function startProcessorDemo() {
            let step = 0;
            const processes = [
                {x: 150, y: 80, color: '#f093fb', label: '进程1'},
                {x: 150, y: 150, color: '#4facfe', label: '进程2'},
                {x: 150, y: 220, color: '#48bb78', label: '进程3'}
            ];

            function animate() {
                processorCtx.clearRect(0, 0, 800, 300);

                // 绘制处理器
                drawProcessor(400, 150, '#667eea', '处理器 (CPU)');

                // 绘制进程
                processes.forEach((process, index) => {
                    const state = index === step % 3 ? 'running' : 'waiting';
                    drawProcess(process.x, process.y, process.color, process.label, state);

                    // 如果是运行中的进程，绘制箭头
                    if (state === 'running') {
                        drawArrow(process.x + 30, process.y, 340, 150);
                    }
                });

                // 说明文字
                processorCtx.fillStyle = '#2d3748';
                processorCtx.font = 'bold 16px Arial';
                processorCtx.textAlign = 'center';
                processorCtx.fillText(`当前运行: ${processes[step % 3].label}`, 400, 50);

                step++;
                if (step < 12) { // 演示4轮切换
                    setTimeout(animate, 1000);
                } else {
                    updateProgress(33);
                }
            }

            animate();
        }

        function drawArrow(x1, y1, x2, y2) {
            processorCtx.strokeStyle = '#ffd700';
            processorCtx.lineWidth = 3;
            processorCtx.beginPath();
            processorCtx.moveTo(x1, y1);
            processorCtx.lineTo(x2, y2);
            processorCtx.stroke();

            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            processorCtx.beginPath();
            processorCtx.moveTo(x2, y2);
            processorCtx.lineTo(x2 - 15 * Math.cos(angle - Math.PI/6), y2 - 15 * Math.sin(angle - Math.PI/6));
            processorCtx.moveTo(x2, y2);
            processorCtx.lineTo(x2 - 15 * Math.cos(angle + Math.PI/6), y2 - 15 * Math.sin(angle + Math.PI/6));
            processorCtx.stroke();
        }

        function resetProcessorDemo() {
            drawInitialProcessor();
            updateProgress(0);
        }

        // 死锁演示相关函数
        function drawInitialDeadlock() {
            deadlockCtx.clearRect(0, 0, 800, 400);

            // 绘制3个进程
            drawDeadlockProcess(150, 100, '#f093fb', '进程1', []);
            drawDeadlockProcess(150, 200, '#4facfe', '进程2', []);
            drawDeadlockProcess(150, 300, '#48bb78', '进程3', []);

            // 绘制资源池
            drawResourcePool(600, 200, []);

            // 说明文字
            deadlockCtx.fillStyle = '#2d3748';
            deadlockCtx.font = 'bold 16px Arial';
            deadlockCtx.textAlign = 'center';
            deadlockCtx.fillText('每个进程需要2个资源R才能完成执行', 400, 50);
        }

        function drawDeadlockProcess(x, y, color, label, resources) {
            // 进程圆圈
            deadlockCtx.fillStyle = color;
            deadlockCtx.beginPath();
            deadlockCtx.arc(x, y, 30, 0, 2 * Math.PI);
            deadlockCtx.fill();

            // 标签
            deadlockCtx.fillStyle = 'white';
            deadlockCtx.font = 'bold 12px Arial';
            deadlockCtx.textAlign = 'center';
            deadlockCtx.fillText(label, x, y - 5);
            deadlockCtx.fillText(`需要:2`, x, y + 8);
            deadlockCtx.fillText(`拥有:${resources.length}`, x, y + 20);

            // 绘制拥有的资源
            resources.forEach((resource, index) => {
                deadlockCtx.fillStyle = '#ffd700';
                deadlockCtx.beginPath();
                deadlockCtx.arc(x + 40 + index * 25, y, 8, 0, 2 * Math.PI);
                deadlockCtx.fill();
                deadlockCtx.fillStyle = 'black';
                deadlockCtx.font = '10px Arial';
                deadlockCtx.fillText('R', x + 40 + index * 25, y + 3);
            });
        }

        function drawResourcePool(x, y, availableResources) {
            // 资源池背景
            deadlockCtx.fillStyle = '#e2e8f0';
            deadlockCtx.fillRect(x - 80, y - 60, 160, 120);
            deadlockCtx.fillStyle = '#2d3748';
            deadlockCtx.font = 'bold 14px Arial';
            deadlockCtx.textAlign = 'center';
            deadlockCtx.fillText('资源池', x, y - 40);
            deadlockCtx.fillText(`可用: ${availableResources.length}`, x, y + 40);

            // 绘制可用资源
            availableResources.forEach((resource, index) => {
                const row = Math.floor(index / 4);
                const col = index % 4;
                deadlockCtx.fillStyle = '#ffd700';
                deadlockCtx.beginPath();
                deadlockCtx.arc(x - 30 + col * 20, y - 20 + row * 20, 8, 0, 2 * Math.PI);
                deadlockCtx.fill();
                deadlockCtx.fillStyle = 'black';
                deadlockCtx.font = '10px Arial';
                deadlockCtx.fillText('R', x - 30 + col * 20, y - 17 + row * 20);
            });
        }

        function demonstrateDeadlock() {
            let step = 0;
            const totalResources = 3;
            let processResources = [[], [], []];
            let availableResources = Array(totalResources).fill().map((_, i) => i);

            function animate() {
                deadlockCtx.clearRect(0, 0, 800, 400);

                if (step < 3) {
                    // 第一阶段：每个进程获得1个资源
                    if (availableResources.length > 0) {
                        processResources[step].push(availableResources.pop());
                    }

                    deadlockCtx.fillStyle = '#e53e3e';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText(`第${step + 1}步：进程${step + 1}获得1个资源`, 400, 50);
                } else {
                    // 第二阶段：显示死锁状态
                    deadlockCtx.fillStyle = '#e53e3e';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText('死锁！所有进程都在等待资源，但没有可用资源', 400, 50);

                    // 绘制等待箭头
                    for (let i = 0; i < 3; i++) {
                        drawWaitingArrow(180, 100 + i * 100, 520, 200);
                    }
                }

                // 绘制进程和资源
                drawDeadlockProcess(150, 100, '#f093fb', '进程1', processResources[0]);
                drawDeadlockProcess(150, 200, '#4facfe', '进程2', processResources[1]);
                drawDeadlockProcess(150, 300, '#48bb78', '进程3', processResources[2]);
                drawResourcePool(600, 200, availableResources);

                step++;
                if (step <= 4) {
                    setTimeout(animate, 2000);
                } else {
                    updateProgress(66);
                }
            }

            animate();
        }

        function demonstrateNoDeadlock() {
            let step = 0;
            const totalResources = 4;
            let processResources = [[], [], []];
            let availableResources = Array(totalResources).fill().map((_, i) => i);

            function animate() {
                deadlockCtx.clearRect(0, 0, 800, 400);

                if (step < 3) {
                    // 第一阶段：每个进程获得1个资源
                    if (availableResources.length > 0) {
                        processResources[step].push(availableResources.pop());
                    }

                    deadlockCtx.fillStyle = '#38a169';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText(`第${step + 1}步：进程${step + 1}获得1个资源`, 400, 50);
                } else if (step === 3) {
                    // 第二阶段：进程1获得第2个资源并完成
                    processResources[0].push(availableResources.pop());

                    deadlockCtx.fillStyle = '#38a169';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText('第4步：进程1获得第2个资源，完成执行！', 400, 50);
                } else if (step === 4) {
                    // 第三阶段：进程1释放资源
                    availableResources.push(...processResources[0]);
                    processResources[0] = [];

                    deadlockCtx.fillStyle = '#38a169';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText('第5步：进程1完成，释放2个资源', 400, 50);
                } else {
                    deadlockCtx.fillStyle = '#38a169';
                    deadlockCtx.font = 'bold 16px Arial';
                    deadlockCtx.textAlign = 'center';
                    deadlockCtx.fillText('无死锁！其他进程可以依次完成', 400, 50);
                }

                // 绘制进程和资源
                drawDeadlockProcess(150, 100, step >= 4 ? '#c6f6d5' : '#f093fb', '进程1', processResources[0]);
                drawDeadlockProcess(150, 200, '#4facfe', '进程2', processResources[1]);
                drawDeadlockProcess(150, 300, '#48bb78', '进程3', processResources[2]);
                drawResourcePool(600, 200, availableResources);

                step++;
                if (step <= 6) {
                    setTimeout(animate, 2000);
                } else {
                    updateProgress(100);
                }
            }

            animate();
        }

        function drawWaitingArrow(x1, y1, x2, y2) {
            deadlockCtx.strokeStyle = '#e53e3e';
            deadlockCtx.lineWidth = 2;
            deadlockCtx.setLineDash([5, 5]);
            deadlockCtx.beginPath();
            deadlockCtx.moveTo(x1, y1);
            deadlockCtx.lineTo(x2, y2);
            deadlockCtx.stroke();
            deadlockCtx.setLineDash([]);

            // 添加"等待"文字
            deadlockCtx.fillStyle = '#e53e3e';
            deadlockCtx.font = '12px Arial';
            deadlockCtx.textAlign = 'center';
            deadlockCtx.fillText('等待', (x1 + x2) / 2, (y1 + y2) / 2 - 10);
        }

        function resetDeadlockDemo() {
            drawInitialDeadlock();
            updateProgress(33);
        }

        // 测验相关函数
        function selectAnswer(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 标记当前选择
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('quizResult').innerHTML =
                    '🎉 正确！4个资源确保至少有1个进程能完成执行，避免死锁。';
                document.getElementById('quizResult').style.color = '#38a169';
                updateProgress(100);
            } else {
                element.classList.add('wrong');
                document.getElementById('quizResult').innerHTML =
                    '❌ 不正确，请重新思考。提示：需要确保至少有1个进程能获得所需的全部资源。';
                document.getElementById('quizResult').style.color = '#e53e3e';
            }
        }

        // 工具函数
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function restartLearning() {
            resetProcessorDemo();
            resetDeadlockDemo();
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            document.getElementById('quizResult').innerHTML = '';
            updateProgress(0);
            window.scrollTo({top: 0, behavior: 'smooth'});
        }
    </script>
</body>
</html>
