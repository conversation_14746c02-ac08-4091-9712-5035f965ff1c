<template>
  <div class="mind-map">
    <div class="mind-map-container">
      <!-- 根节点 -->
      <div class="root-node" v-if="rootNode">
        <div 
          class="node-item root" 
          :class="{ 'viewed': isNodeViewed(rootNode) }"
          @click="handleNodeClick(rootNode)"
        >
          <i :class="rootNode.icon || 'el-icon-folder'"></i>
          <span>{{ rootNode.name }}</span>
        </div>
        
        <!-- 子节点层级 -->
        <div class="children-container" v-if="rootNode.children && rootNode.children.length > 0">
          <div 
            v-for="child in rootNode.children" 
            :key="child.name"
            class="child-branch"
          >
            <div class="connection-line"></div>
            <mind-map-node 
              :node="child" 
              :level="1"
              :is-viewed="isNodeViewed"
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MindMapNode from './MindMapNode.vue'

export default {
  name: 'MindMap',
  components: {
    MindMapNode
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    viewedFiles: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    rootNode() {
      return this.data
    }
  },
  methods: {
    isNodeViewed(node) {
      if (!node) return false
      
      // 检查节点本身的文件是否被查看过
      if (node.files && node.files.length > 0) {
        return node.files.some(file => this.viewedFiles.includes(file.path))
      }
      
      // 检查子节点是否有被查看过的文件
      if (node.children && node.children.length > 0) {
        return this.hasViewedChildren(node)
      }
      
      return false
    },
    
    hasViewedChildren(node) {
      if (!node.children) return false
      
      return node.children.some(child => {
        // 检查子节点的文件
        if (child.files && child.files.length > 0) {
          if (child.files.some(file => this.viewedFiles.includes(file.path))) {
            return true
          }
        }
        
        // 递归检查子节点的子节点
        if (child.children && child.children.length > 0) {
          return this.hasViewedChildren(child)
        }
        
        return false
      })
    },
    
    handleNodeClick(node) {
      this.$emit('node-click', node)
    }
  }
}
</script>

<style scoped>
.mind-map {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 20px;
  background: var(--bg-color);
}

.mind-map-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
}

.root-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: 8px;
  border-radius: 25px;
  background: #f5f5f5;
  border: 2px solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  justify-content: center;
}

.node-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.node-item.root {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 16px;
  border: none;
}

.node-item.viewed {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border-color: #4CAF50;
}

.node-item:not(.viewed):not(.root) {
  background: #e0e0e0;
  color: #666;
  border-color: #ccc;
}

.node-item i {
  margin-right: 8px;
  font-size: 16px;
}

.children-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 1200px;
  margin-top: 30px;
  position: relative;
}

.child-branch {
  position: relative;
  margin: 10px;
}

.connection-line {
  position: absolute;
  top: -20px;
  left: 50%;
  width: 2px;
  height: 20px;
  background: #ddd;
  transform: translateX(-50%);
}

.connection-line::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  width: 100px;
  height: 2px;
  background: #ddd;
  transform: translateX(-50%);
}

/* 暗黑模式适配 */
:global(.dark-mode) .node-item:not(.viewed):not(.root) {
  background: #424242;
  color: #bbb;
  border-color: #555;
}

:global(.dark-mode) .connection-line,
:global(.dark-mode) .connection-line::before {
  background: #555;
}

:global(.dark-mode) .mind-map {
  background: var(--bg-color-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .children-container {
    max-width: 100%;
  }
  
  .node-item {
    min-width: 100px;
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .node-item.root {
    font-size: 15px;
  }
}
</style>
