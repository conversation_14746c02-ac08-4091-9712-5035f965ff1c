const { app, BrowserWindow, screen, Menu, globalShortcut, ipcMain } = require('electron')
const path = require('path')
const fs = require('fs')

// 应用程序监控相关
let appMonitorInterval = null
let isMonitoring = false
let mainWindowRef = null
let lastActiveApp = null
let lastActiveWebsite = null

// 根据平台导入不同的监控模块
let platformMonitor = null
try {
  if (process.platform === 'win32') {
    // Windows平台使用powershell命令
    const { exec } = require('child_process')
    platformMonitor = {
      getCurrentApp: () => getCurrentAppWindows(),
      getCurrentWebsite: () => getCurrentWebsiteWindows()
    }
  } else if (process.platform === 'darwin') {
    // macOS平台使用AppleScript
    const { exec } = require('child_process')
    platformMonitor = {
      getCurrentApp: () => getCurrentAppMacOS(),
      getCurrentWebsite: () => getCurrentWebsiteMacOS()
    }
  } else {
    // Linux平台使用xdotool等工具
    const { exec } = require('child_process')
    platformMonitor = {
      getCurrentApp: () => getCurrentAppLinux(),
      getCurrentWebsite: () => getCurrentWebsiteLinux()
    }
  }
} catch (error) {
  console.warn('无法加载平台特定的监控模块:', error)
}

// Windows平台应用监控函数
function getCurrentAppWindows() {
  return new Promise((resolve) => {
    const { exec } = require('child_process')
    // 获取当前活动窗口信息
    const command = `powershell "Get-Process | Where-Object {$_.MainWindowTitle -ne ''} | Select-Object ProcessName, MainWindowTitle, Id, Path | ConvertTo-Json"`

    exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
      if (error) {
        console.error('获取Windows应用信息失败:', error)
        resolve(null)
        return
      }

      try {
        const processes = JSON.parse(stdout)
        if (Array.isArray(processes) && processes.length > 0) {
          // 获取第一个有窗口标题的进程（通常是当前活动的）
          const activeProcess = processes[0]
          resolve({
            name: activeProcess.ProcessName,
            title: activeProcess.MainWindowTitle,
            pid: activeProcess.Id,
            path: activeProcess.Path,
            timestamp: new Date().toISOString()
          })
        } else {
          resolve(null)
        }
      } catch (parseError) {
        console.error('解析Windows应用信息失败:', parseError)
        resolve(null)
      }
    })
  })
}

// macOS平台应用监控函数
function getCurrentAppMacOS() {
  return new Promise((resolve) => {
    const { exec } = require('child_process')
    // 使用AppleScript获取当前活动应用
    const script = `
      tell application "System Events"
        set frontApp to first application process whose frontmost is true
        set appName to name of frontApp
        set appPath to POSIX path of (file of frontApp as alias)
      end tell
      return appName & "|" & appPath
    `

    exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
      if (error) {
        console.error('获取macOS应用信息失败:', error)
        resolve(null)
        return
      }

      const parts = stdout.trim().split('|')
      if (parts.length >= 2) {
        resolve({
          name: parts[0],
          title: parts[0],
          pid: 'unknown',
          path: parts[1],
          timestamp: new Date().toISOString()
        })
      } else {
        resolve(null)
      }
    })
  })
}

// Linux平台应用监控函数
function getCurrentAppLinux() {
  return new Promise((resolve) => {
    const { exec } = require('child_process')
    // 使用xdotool获取当前活动窗口
    exec('xdotool getactivewindow getwindowname', (error, stdout, stderr) => {
      if (error) {
        console.error('获取Linux应用信息失败:', error)
        resolve(null)
        return
      }

      const windowName = stdout.trim()
      if (windowName) {
        resolve({
          name: windowName.split(' - ')[0] || windowName,
          title: windowName,
          pid: 'unknown',
          path: 'unknown',
          timestamp: new Date().toISOString()
        })
      } else {
        resolve(null)
      }
    })
  })
}

// Windows平台网站监控函数
function getCurrentWebsiteWindows() {
  return new Promise((resolve) => {
    const { exec } = require('child_process')
    // 获取浏览器窗口标题（通常包含网站信息）
    const command = `powershell "Get-Process chrome,firefox,msedge,iexplore -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -ne ''} | Select-Object ProcessName, MainWindowTitle | ConvertTo-Json"`

    exec(command, { encoding: 'utf8' }, (error, stdout, stderr) => {
      if (error) {
        resolve(null)
        return
      }

      try {
        const browsers = JSON.parse(stdout)
        if (Array.isArray(browsers) && browsers.length > 0) {
          const browser = browsers[0]
          const title = browser.MainWindowTitle

          // 尝试从标题中提取网站信息
          let domain = 'unknown'
          let url = 'unknown'

          // 简单的域名提取逻辑
          if (title.includes(' - ')) {
            const parts = title.split(' - ')
            domain = parts[parts.length - 1]
          }

          resolve({
            url: url,
            title: title,
            domain: domain,
            browser: browser.ProcessName,
            timestamp: new Date().toISOString()
          })
        } else {
          resolve(null)
        }
      } catch (parseError) {
        resolve(null)
      }
    })
  })
}

// macOS平台网站监控函数
function getCurrentWebsiteMacOS() {
  return new Promise((resolve) => {
    const { exec } = require('child_process')
    // 获取Safari或Chrome的当前标签页
    const script = `
      tell application "System Events"
        set frontApp to first application process whose frontmost is true
        set appName to name of frontApp
      end tell

      if appName is "Safari" then
        tell application "Safari"
          if (count of windows) > 0 then
            set currentTab to current tab of front window
            return name of currentTab & "|" & URL of currentTab
          end if
        end tell
      else if appName is "Google Chrome" then
        tell application "Google Chrome"
          if (count of windows) > 0 then
            set currentTab to active tab of front window
            return title of currentTab & "|" & URL of currentTab
          end if
        end tell
      end if
      return ""
    `

    exec(`osascript -e '${script}'`, (error, stdout, stderr) => {
      if (error) {
        resolve(null)
        return
      }

      const result = stdout.trim()
      if (result) {
        const parts = result.split('|')
        if (parts.length >= 2) {
          const url = parts[1]
          let domain = 'unknown'
          try {
            domain = new URL(url).hostname
          } catch (e) {
            domain = 'unknown'
          }

          resolve({
            url: url,
            title: parts[0],
            domain: domain,
            timestamp: new Date().toISOString()
          })
        }
      }
      resolve(null)
    })
  })
}

// Linux平台网站监控函数
function getCurrentWebsiteLinux() {
  return new Promise((resolve) => {
    // Linux下的网站监控比较复杂，这里提供基础实现
    resolve(null)
  })
}

// 悬浮窗管理
let floatingWindows = new Map() // 存储所有悬浮窗
let floatingWindowCounter = 0 // 悬浮窗计数器

// 创建悬浮窗
function createFloatingWindow(mainWindow, options = {}) {
  floatingWindowCounter++
  const windowId = `floating-${floatingWindowCounter}`

  // 默认悬浮窗配置 - 圆形设计
  const defaultOptions = {
    width: 200,
    height: 200,
    minWidth: 150,
    minHeight: 150,
    maxWidth: 300,
    maxHeight: 300,
    x: 100,
    y: 100,
    alwaysOnTop: true,
    frame: false, // 无边框
    resizable: false, // 不可调整大小以保持圆形
    minimizable: false,
    maximizable: false,
    closable: true,
    skipTaskbar: false,
    title: '小程序悬浮窗',
    show: false,
    transparent: true, // 透明背景
    hasShadow: true
  }

  const windowOptions = { ...defaultOptions, ...options }

  // 创建悬浮窗
  const floatingWindow = new BrowserWindow({
    ...windowOptions,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: true,
      parent: mainWindow // 设置父窗口
    },
    titleBarStyle: 'customButtonsOnHover',
    autoHideMenuBar: true, // 隐藏菜单栏
    roundedCorners: true
  })

  // 检测是否为开发模式
  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

  if (isDev) {
    // 开发模式：加载悬浮窗页面
    floatingWindow.loadURL('http://localhost:8080/#/floating')
  } else {
    // 生产模式：从打包后的文件加载
    const indexPath = path.join(__dirname, 'dist', 'index.html')
    floatingWindow.loadFile(indexPath, { hash: 'floating' })
  }

  // 窗口准备显示时的处理
  floatingWindow.once('ready-to-show', () => {
    floatingWindow.show()

    // 发送窗口信息到悬浮窗
    const bounds = floatingWindow.getBounds()
    floatingWindow.webContents.send('floating-window-info', {
      id: windowId,
      width: bounds.width,
      height: bounds.height,
      x: bounds.x,
      y: bounds.y
    })
  })

  // 监听悬浮窗关闭事件
  floatingWindow.on('closed', () => {
    floatingWindows.delete(windowId)
    // 通知主窗口悬浮窗已关闭
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('floating-window-closed', windowId)
    }
  })

  // 监听悬浮窗位置和大小变化
  floatingWindow.on('moved', () => {
    const bounds = floatingWindow.getBounds()
    floatingWindow.webContents.send('floating-window-moved', {
      id: windowId,
      x: bounds.x,
      y: bounds.y
    })
  })

  floatingWindow.on('resized', () => {
    const bounds = floatingWindow.getBounds()
    floatingWindow.webContents.send('floating-window-resized', {
      id: windowId,
      width: bounds.width,
      height: bounds.height
    })
  })

  // 存储悬浮窗引用
  floatingWindows.set(windowId, floatingWindow)

  return { windowId, window: floatingWindow }
}

// 关闭悬浮窗
function closeFloatingWindow(windowId) {
  const floatingWindow = floatingWindows.get(windowId)
  if (floatingWindow && !floatingWindow.isDestroyed()) {
    floatingWindow.close()
  }
}

// 关闭所有悬浮窗
function closeAllFloatingWindows() {
  floatingWindows.forEach((window, id) => {
    if (!window.isDestroyed()) {
      window.close()
    }
  })
  floatingWindows.clear()
}

// 开始应用程序监控
function startApplicationMonitoring() {
  if (isMonitoring) {
    console.log('应用监控已经在运行中')
    return
  }

  isMonitoring = true
  console.log('开始应用程序监控')

  // 立即检查一次
  checkCurrentApplication()

  // 每2秒检查一次应用状态
  appMonitorInterval = setInterval(() => {
    checkCurrentApplication()
  }, 2000)
}

// 停止应用程序监控
function stopApplicationMonitoring() {
  if (!isMonitoring) {
    return
  }

  isMonitoring = false
  console.log('停止应用程序监控')

  if (appMonitorInterval) {
    clearInterval(appMonitorInterval)
    appMonitorInterval = null
  }

  lastActiveApp = null
  lastActiveWebsite = null
}

// 检查当前应用程序
async function checkCurrentApplication() {
  if (!isMonitoring || !mainWindowRef || mainWindowRef.isDestroyed()) {
    return
  }

  try {
    // 获取当前应用信息
    if (platformMonitor && platformMonitor.getCurrentApp) {
      const currentApp = await platformMonitor.getCurrentApp()
      if (currentApp && (!lastActiveApp ||
          lastActiveApp.name !== currentApp.name ||
          lastActiveApp.title !== currentApp.title)) {

        lastActiveApp = currentApp
        console.log('检测到应用变化:', currentApp)

        // 发送应用变化事件到渲染进程
        mainWindowRef.webContents.send('app-changed', currentApp)
      }
    }

    // 获取当前网站信息
    if (platformMonitor && platformMonitor.getCurrentWebsite) {
      const currentWebsite = await platformMonitor.getCurrentWebsite()
      if (currentWebsite && (!lastActiveWebsite ||
          lastActiveWebsite.url !== currentWebsite.url ||
          lastActiveWebsite.title !== currentWebsite.title)) {

        lastActiveWebsite = currentWebsite
        console.log('检测到网站变化:', currentWebsite)

        // 发送网站变化事件到渲染进程
        mainWindowRef.webContents.send('website-changed', currentWebsite)
      }
    }
  } catch (error) {
    console.error('检查应用程序状态时出错:', error)
  }
}

// 创建应用菜单
function createMenu(mainWindow) {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new')
          }
        },
        {
          label: '打开',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-action', 'open')
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '视图',
      submenu: [
        {
          label: '重新加载',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload()
          }
        },
        {
          label: '强制重新加载',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: () => {
            mainWindow.webContents.reloadIgnoringCache()
          }
        },
        {
          label: '开发者工具',
          accelerator: 'F12',
          click: () => {
            mainWindow.webContents.toggleDevTools()
          }
        },
        { type: 'separator' },
        {
          label: '实际大小',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            mainWindow.webContents.setZoomLevel(0)
          }
        },
        {
          label: '放大',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel()
            mainWindow.webContents.setZoomLevel(currentZoom + 0.5)
          }
        },
        {
          label: '缩小',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel()
            mainWindow.webContents.setZoomLevel(currentZoom - 0.5)
          }
        },
        { type: 'separator' },
        {
          label: '全屏',
          accelerator: 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen())
          }
        }
      ]
    },
    {
      label: '窗口',
      submenu: [
        {
          label: '最小化',
          accelerator: 'CmdOrCtrl+M',
          click: () => {
            mainWindow.minimize()
          }
        },
        {
          label: '最大化',
          click: () => {
            if (mainWindow.isMaximized()) {
              mainWindow.unmaximize()
            } else {
              mainWindow.maximize()
            }
          }
        },
        { type: 'separator' },
        {
          label: '打开悬浮窗',
          accelerator: 'CmdOrCtrl+Shift+F',
          click: () => {
            const { windowId } = createFloatingWindow(mainWindow)
            mainWindow.webContents.send('floating-window-created', windowId)
          }
        },
        {
          label: '关闭所有悬浮窗',
          accelerator: 'CmdOrCtrl+Shift+W',
          click: () => {
            closeAllFloatingWindows()
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

function createWindow () {
  // 获取主显示器信息
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize

  // 计算窗口大小（屏幕的80%，但不小于最小尺寸）
  const minWidth = 800
  const minHeight = 600
  const windowWidth = Math.max(Math.floor(screenWidth * 0.8), minWidth)
  const windowHeight = Math.max(Math.floor(screenHeight * 0.8), minHeight)

  // 创建浏览器窗口
  const mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: minWidth,
    minHeight: minHeight,
    center: true, // 窗口居中显示
    show: false, // 先不显示，等加载完成后再显示
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false, // For Vue 2 with no contextBridge
      webSecurity: true, // 保持安全性
      zoomFactor: 1.0 // 初始缩放比例
    },
    titleBarStyle: 'default',
    autoHideMenuBar: false, // 显示菜单栏
    resizable: true, // 允许调整大小
    maximizable: true, // 允许最大化
    minimizable: true, // 允许最小化
    fullscreenable: true // 允许全屏
  })

  // 检测是否为开发模式
  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

  console.log('Environment:', isDev ? 'development' : 'production')
  console.log('App packaged:', app.isPackaged)
  console.log('Current directory:', __dirname)

  if (isDev) {
    // 开发模式：从开发服务器加载
    console.log('Loading from development server...')
    mainWindow.loadURL('http://localhost:8080')
    mainWindow.webContents.openDevTools()
  } else {
    // 生产模式：从打包后的文件加载
    const indexPath = path.join(__dirname, 'dist', 'index.html')
    console.log('Loading from file:', indexPath)
    console.log('File exists:', fs.existsSync(indexPath))

    mainWindow.loadFile(indexPath)
    // 生产模式下也打开开发者工具，方便调试
    mainWindow.webContents.openDevTools()
  }

  // 窗口准备显示时的处理
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 如果是开发模式，可以最大化窗口
    if (isDev) {
      // mainWindow.maximize()
    }
  })

  // 监听页面加载完成事件
  mainWindow.webContents.once('dom-ready', () => {
    console.log('DOM ready')

    // 发送窗口信息到渲染进程
    const bounds = mainWindow.getBounds()
    mainWindow.webContents.send('window-info', {
      width: bounds.width,
      height: bounds.height,
      isMaximized: mainWindow.isMaximized(),
      isFullScreen: mainWindow.isFullScreen()
    })
  })

  // 监听页面加载失败事件
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log('Failed to load:', errorCode, errorDescription, validatedURL)
  })

  // 监听窗口大小变化
  mainWindow.on('resize', () => {
    const bounds = mainWindow.getBounds()
    mainWindow.webContents.send('window-resize', {
      width: bounds.width,
      height: bounds.height
    })
  })

  // 监听窗口最大化/还原
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window-maximize', true)
  })

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window-maximize', false)
  })

  // 监听全屏状态变化
  mainWindow.on('enter-full-screen', () => {
    mainWindow.webContents.send('window-fullscreen', true)
  })

  mainWindow.on('leave-full-screen', () => {
    mainWindow.webContents.send('window-fullscreen', false)
  })

  // 监听显示器变化（多显示器支持）
  screen.on('display-metrics-changed', () => {
    // 重新计算窗口大小
    const display = screen.getDisplayMatching(mainWindow.getBounds())
    console.log('Display metrics changed:', display.workAreaSize)
  })

  // 设置全局主窗口引用，用于应用监控
  mainWindowRef = mainWindow

  return mainWindow
}

// 设置IPC通信处理
function setupIPC(mainWindow) {
  // 处理创建悬浮窗请求
  ipcMain.handle('create-floating-window', async (event, options) => {
    try {
      const { windowId } = createFloatingWindow(mainWindow, options)
      return { success: true, windowId }
    } catch (error) {
      console.error('Failed to create floating window:', error)
      return { success: false, error: error.message }
    }
  })

  // 处理关闭悬浮窗请求
  ipcMain.handle('close-floating-window', async (event, windowId) => {
    try {
      closeFloatingWindow(windowId)
      return { success: true }
    } catch (error) {
      console.error('Failed to close floating window:', error)
      return { success: false, error: error.message }
    }
  })

  // 处理关闭所有悬浮窗请求
  ipcMain.handle('close-all-floating-windows', async () => {
    try {
      closeAllFloatingWindows()
      return { success: true }
    } catch (error) {
      console.error('Failed to close all floating windows:', error)
      return { success: false, error: error.message }
    }
  })

  // 处理获取悬浮窗列表请求
  ipcMain.handle('get-floating-windows', async () => {
    try {
      const windows = []
      floatingWindows.forEach((window, id) => {
        if (!window.isDestroyed()) {
          const bounds = window.getBounds()
          windows.push({
            id,
            bounds,
            isVisible: window.isVisible(),
            isMinimized: window.isMinimized()
          })
        }
      })
      return { success: true, windows }
    } catch (error) {
      console.error('Failed to get floating windows:', error)
      return { success: false, error: error.message }
    }
  })

  // 处理悬浮窗最小化请求
  ipcMain.on('minimize-floating-window', (event) => {
    const senderWindow = BrowserWindow.fromWebContents(event.sender)
    if (senderWindow) {
      senderWindow.minimize()
    }
  })

  // 处理悬浮窗关闭请求
  ipcMain.on('close-floating-window', (event) => {
    const senderWindow = BrowserWindow.fromWebContents(event.sender)
    if (senderWindow) {
      senderWindow.close()
    }
  })

  // 处理打开笔记页面请求
  ipcMain.on('open-notes-page', (event) => {
    // 将主窗口切换到笔记页面
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('navigate-to-notes')
      mainWindow.focus() // 将主窗口置于前台
    }
  })

  // 处理开始应用监控请求
  ipcMain.on('start-app-monitoring', (event) => {
    console.log('收到开始应用监控请求')
    startApplicationMonitoring()
  })

  // 处理停止应用监控请求
  ipcMain.on('stop-app-monitoring', (event) => {
    console.log('收到停止应用监控请求')
    stopApplicationMonitoring()
  })

  // 处理获取当前应用请求
  ipcMain.on('get-current-app', async (event) => {
    if (isMonitoring && platformMonitor && platformMonitor.getCurrentApp) {
      try {
        const currentApp = await platformMonitor.getCurrentApp()
        if (currentApp) {
          event.reply('current-app-info', currentApp)
        }
      } catch (error) {
        console.error('获取当前应用信息失败:', error)
      }
    }
  })

  // 处理获取当前网站请求
  ipcMain.on('get-current-website', async (event) => {
    if (isMonitoring && platformMonitor && platformMonitor.getCurrentWebsite) {
      try {
        const currentWebsite = await platformMonitor.getCurrentWebsite()
        if (currentWebsite) {
          event.reply('current-website-info', currentWebsite)
        }
      } catch (error) {
        console.error('获取当前网站信息失败:', error)
      }
    }
  })
}

// Electron 初始化完成后创建窗口
app.whenReady().then(() => {
  const mainWindow = createWindow()

  // 设置IPC通信
  setupIPC(mainWindow)

  // 创建菜单
  createMenu(mainWindow)

  // 注册全局快捷键
  globalShortcut.register('CmdOrCtrl+Shift+I', () => {
    mainWindow.webContents.toggleDevTools()
  })

  // 注册缩放快捷键
  globalShortcut.register('CmdOrCtrl+0', () => {
    mainWindow.webContents.setZoomLevel(0)
  })

  // 注册悬浮窗快捷键
  globalShortcut.register('CmdOrCtrl+Shift+F', () => {
    const { windowId } = createFloatingWindow(mainWindow)
    mainWindow.webContents.send('floating-window-created', windowId)
  })

  app.on('activate', () => {
    // 在 macOS 上，当点击 dock 中的图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) {
      const newWindow = createWindow()
      createMenu(newWindow)
    }
  })
})

// 当所有窗口都关闭时退出应用程序
app.on('window-all-closed', () => {
  // 停止应用监控
  stopApplicationMonitoring()

  // 关闭所有悬浮窗
  closeAllFloatingWindows()

  // 注销所有全局快捷键
  globalShortcut.unregisterAll()

  // 清理IPC监听器
  ipcMain.removeAllListeners('create-floating-window')
  ipcMain.removeAllListeners('close-floating-window')
  ipcMain.removeAllListeners('close-all-floating-windows')
  ipcMain.removeAllListeners('get-floating-windows')
  ipcMain.removeAllListeners('minimize-floating-window')
  ipcMain.removeAllListeners('open-notes-page')
  ipcMain.removeAllListeners('start-app-monitoring')
  ipcMain.removeAllListeners('stop-app-monitoring')
  ipcMain.removeAllListeners('get-current-app')
  ipcMain.removeAllListeners('get-current-website')

  // 在 macOS 上，应用程序及其菜单栏通常保持活动状态，直到用户使用 Cmd + Q 明确退出。
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用即将退出时的清理工作
app.on('before-quit', () => {
  // 停止应用监控
  stopApplicationMonitoring()

  // 关闭所有悬浮窗
  closeAllFloatingWindows()

  // 注销所有全局快捷键
  globalShortcut.unregisterAll()

  // 清理IPC监听器
  ipcMain.removeAllListeners()
})