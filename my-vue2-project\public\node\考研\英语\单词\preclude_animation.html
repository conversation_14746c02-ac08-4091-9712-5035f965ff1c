<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习单词: Preclude</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            color: #4a90e2; /* A slightly different blue */
            margin-bottom: 10px;
        }
        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            background-image: 
                radial-gradient(circle at 1px 1px, #ddd 1px, transparent 0);
            background-size: 20px 20px;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            padding: 12px 24px;
            font-size: 1em;
            cursor: pointer;
            border: none;
            border-radius: 8px;
            background-color: #4a90e2;
            color: white;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button:hover {
            background-color: #357abd;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        button.disabled, button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .info-box {
            background-color: #eaf2fd;
            border-left: 5px solid #4a90e2;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>学习单词: Preclude (阻止)</h1>
    <p id="explanation">这是一个关于探险家寻宝的故事，它将生动地解释 "preclude" 的含义。</p>
    <canvas id="wordCanvas" width="800" height="400"></canvas>
    <div class="controls">
        <button id="startBtn">开始动画</button>
        <button id="resetBtn" style="display: none;">重新开始</button>
    </div>
    <div id="interactive-section" style="display: none;">
        <p class="info-box"><strong>轮到你了！</strong><br>探险家再次来到了门口，要不要在他到达<strong>前</strong>就<strong>关上</strong>门 (Preclude)？</p>
        <button id="precludeBtn">关门阻止他</button>
        <button id="waitBtn">等他通过</button>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const explanation = document.getElementById('explanation');
    const startBtn = document.getElementById('startBtn');
    const resetBtn = document.getElementById('resetBtn');
    const interactiveSection = document.getElementById('interactive-section');
    const precludeBtn = document.getElementById('precludeBtn');
    const waitBtn = document.getElementById('waitBtn');

    let animationFrameId;
    let stage = 'initial'; // initial, pre, clude, story, interactive
    let adventurerX = 50;
    let gateWidth = 100;

    function clearCanvas() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    function drawText(text, x, y, size = 20, color = '#333', textAlign = 'center') {
        ctx.font = `bold ${size}px Arial`;
        ctx.fillStyle = color;
        ctx.textAlign = textAlign;
        ctx.fillText(text, x, y);
    }

    function drawAdventurer(x, y) {
        // Simple stick figure
        ctx.strokeStyle = '#d2691e'; // brown
        ctx.lineWidth = 4;
        // Head
        ctx.beginPath();
        ctx.arc(x, y - 50, 15, 0, Math.PI * 2);
        ctx.fillStyle = '#ffdead'; // navajo white
        ctx.fill();
        ctx.stroke();
        // Hat
        ctx.beginPath();
        ctx.moveTo(x - 20, y - 60);
        ctx.lineTo(x + 20, y - 60);
        ctx.lineTo(x, y - 75);
        ctx.closePath();
        ctx.fillStyle = '#8b4513'; //saddlebrown
        ctx.fill();
        // Body
        ctx.beginPath();
        ctx.moveTo(x, y - 35);
        ctx.lineTo(x, y + 15);
        ctx.stroke();
        // Legs (running)
        const legAngle = Math.sin(Date.now() / 100) * 20;
        ctx.beginPath();
        ctx.moveTo(x, y + 15);
        ctx.lineTo(x - 15 + legAngle, y + 50);
        ctx.moveTo(x, y + 15);
        ctx.lineTo(x + 15 - legAngle, y + 50);
        ctx.stroke();
    }
    
    function drawGate(width) {
        const gateX = 650;
        const gateHeight = 200;
        const gateY = canvas.height - gateHeight - 50;
        ctx.fillStyle = '#a9a9a9'; // darkgray
        ctx.strokeStyle = '#696969';
        ctx.lineWidth = 5;
        ctx.fillRect(gateX, gateY, width, gateHeight);
        ctx.strokeRect(gateX, gateY, 100, gateHeight); // The full gate outline
        drawText('关闭', gateX + 50, gateY - 10, 18, '#696969');
    }

    function drawTreasure() {
        const x = 720;
        const y = canvas.height - 80;
        ctx.fillStyle = '#ffd700'; // gold
        ctx.fillRect(x - 25, y - 30, 50, 30);
        ctx.fillStyle = '#daa520'; // goldenrod
        ctx.fillRect(x - 25, y - 30, 50, 5); // lid
        drawText('宝藏', x, y + 20, 16, '#daa520');
    }

    function animatePre() {
        explanation.textContent = "前缀 'pre-' 的意思是 '在...之前'。就像时间上的提前。";
        let time = 0;
        const animate = () => {
            clearCanvas();
            drawText("pre- = 之前", canvas.width / 2, 50, 24, '#4a90e2');
            
            // Timeline
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(100, 200);
            ctx.lineTo(700, 200);
            ctx.stroke();
            
            // Event Point
            ctx.fillStyle = 'red';
            ctx.beginPath();
            ctx.arc(500, 200, 10, 0, 2 * Math.PI);
            ctx.fill();
            drawText("事件", 500, 240);

            // "Pre" point
            const preX = 500 - time;
            ctx.fillStyle = '#4a90e2';
            ctx.beginPath();
            ctx.arc(preX, 200, 8, 0, 2 * Math.PI);
            ctx.fill();
            drawText("Pre (之前)", preX, 180);

            if (time < 200) {
                time += 2;
                animationFrameId = requestAnimationFrame(animate);
            } else {
                drawText("'pre-' 指的是在某个事件发生之前的时间点。", canvas.width / 2, 350, 20);
            }
        };
        animate();
    }

    function animateClude() {
        explanation.textContent = "词根 '-clude' 来自拉丁语 'claudere'，意思是 '关闭'。";
        let gateW = 100;
        const animate = () => {
            clearCanvas();
            drawText("-clude = 关闭", canvas.width / 2, 50, 24, '#4a90e2');
            
            drawGate(gateW);

            if (gateW > 5) {
                gateW -= 1;
                animationFrameId = requestAnimationFrame(animate);
            } else {
                 drawText("想象一扇门正在关闭。", canvas.width / 2, 350, 20);
            }
        };
        animate();
    }
    
    function runStoryAnimation(precludeAction) {
        adventurerX = 50;
        gateWidth = 100;
        let isGateClosing = false;
        
        const animate = () => {
            clearCanvas();
            drawTreasure();
            drawGate(gateWidth);
            drawAdventurer(adventurerX, canvas.height - 70);
            
            drawText("pre- (之前)", adventurerX, 100, 16, '#4a90e2');
            ctx.beginPath();
            ctx.moveTo(adventurerX, 110);
            ctx.lineTo(adventurerX, canvas.height - 120);
            ctx.setLineDash([5, 5]);
            ctx.strokeStyle = '#4a90e2';
            ctx.stroke();
            ctx.setLineDash([]);


            if (precludeAction && adventurerX > 300) {
                isGateClosing = true;
            }

            if (isGateClosing && gateWidth > 0) {
                gateWidth -= 3;
            }

            if (adventurerX < 600) {
                 adventurerX += 2;
            } else {
                // Adventurer reached the gate
                if (gateWidth <= 5) {
                    explanation.textContent = "太迟了！门在他到达前就关了，阻止(precluded)了他拿宝藏。";
                    drawText("被阻止了！", adventurerX - 50, 150, 20, 'red');
                } else {
                    explanation.textContent = "探险家成功拿到了宝藏！";
                    drawText("成功！", adventurerX, 150, 20, 'green');
                }
                
                if(stage === 'story') {
                    setTimeout(() => {
                        stage = 'interactive';
                        interactiveSection.style.display = 'block';
                        explanation.textContent = "现在你来决定探险家的命运！";
                    }, 2000);
                } else if (stage === 'interactive') {
                     resetBtn.style.display = 'inline-block';
                }

                cancelAnimationFrame(animationFrameId);
                return;
            }
            
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }

    function animateStory() {
        explanation.textContent = "'pre' (之前) + 'clude' (关闭) = 提前关闭，也就是'阻止'。";
        runStoryAnimation(true); // In the main story, the gate always closes
    }

    function reset() {
        cancelAnimationFrame(animationFrameId);
        stage = 'initial';
        adventurerX = 50;
        gateWidth = 100;
        clearCanvas();
        drawText("点击'开始动画'学习 Preclude", canvas.width / 2, canvas.height / 2);
        startBtn.style.display = 'inline-block';
        startBtn.disabled = false;
        resetBtn.style.display = 'none';
        interactiveSection.style.display = 'none';
        explanation.textContent = "这是一个关于探险家寻宝的故事，它将生动地解释 'preclude' 的含义。";
    }
    
    startBtn.addEventListener('click', () => {
        startBtn.disabled = true;
        
        let currentAnimation = 0;
        const animations = [
            { func: animatePre, duration: 3000 },
            { func: animateClude, duration: 3000 },
            { func: animateStory, duration: 6000 }
        ];

        function nextAnimation() {
            if (currentAnimation < animations.length) {
                cancelAnimationFrame(animationFrameId);
                const anim = animations[currentAnimation];
                stage = ['pre', 'clude', 'story'][currentAnimation];
                anim.func();
                currentAnimation++;
                setTimeout(nextAnimation, anim.duration);
            } else {
                 startBtn.style.display = 'none';
                 resetBtn.style.display = 'inline-block';
            }
        }
        nextAnimation();
    });

    precludeBtn.addEventListener('click', () => {
        explanation.textContent = "你选择了'提前关闭'大门...让我们看看结果！";
        runStoryAnimation(true);
        precludeBtn.disabled = true;
        waitBtn.disabled = true;
    });

    waitBtn.addEventListener('click', () => {
        explanation.textContent = "你选择了等待...他能成功吗？";
        runStoryAnimation(false);
        precludeBtn.disabled = true;
        waitBtn.disabled = true;
    });

    resetBtn.addEventListener('click', () => {
        precludeBtn.disabled = false;
        waitBtn.disabled = false;
        reset();
    });

    // Initial state
    window.onload = () => {
        drawText("点击'开始动画'学习 Preclude", canvas.width / 2, canvas.height / 2);
    };

</script>
</body>
</html> 