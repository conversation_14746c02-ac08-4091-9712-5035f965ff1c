<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合的特点 - 互动学习版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #e17055);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            opacity: 0;
            transform: translateY(60px);
            animation: slideInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.3s; }
        .section:nth-child(3) { animation-delay: 0.6s; }
        .section:nth-child(4) { animation-delay: 0.9s; }
        .section:nth-child(5) { animation-delay: 1.2s; }

        .section-title {
            font-size: 2.5rem;
            color: #2d3436;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
            background: linear-gradient(45deg, #74b9ff, #6c5ce7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .text-content {
            font-size: 1.2rem;
            line-height: 2;
            color: #2d3436;
            margin: 30px 0;
            text-align: center;
        }

        .highlight {
            background: linear-gradient(120deg, #74b9ff 0%, #e17055 100%);
            padding: 4px 12px;
            border-radius: 8px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin: 0 5px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .interactive-btn {
            background: linear-gradient(45deg, #74b9ff, #6c5ce7);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            margin: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .interactive-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .interactive-btn:hover::before {
            left: 100%;
        }

        .interactive-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.3);
        }

        .game-area {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #74b9ff;
            min-height: 200px;
            position: relative;
        }

        .object-item {
            display: inline-block;
            background: linear-gradient(45deg, #fd79a8, #e84393);
            color: white;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .object-item:hover {
            transform: scale(1.1) rotate(5deg);
        }

        .progress-container {
            background: rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            width: 0%;
            transition: width 0.8s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .bounce {
            animation: bounce 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 集合的特点</h1>
            <p class="subtitle">通过互动游戏理解集合的核心特性</p>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p style="color: white; margin: 0;">学习进度</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎪 集合特点概述</h2>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>
            <div class="text-content">
                <p>集合的特点主要有如下两点：</p>
                <p><span class="highlight">1. 对象封装数据，对象多了也需要存储。集合用于存储对象。</span></p>
                <p><span class="highlight">2. 对象的个数确定可以使用数组，对象的个数不确定的可以用集合。因为集合是可变长度的。</span></p>
                <button class="interactive-btn" onclick="animateOverview()">🎬 播放概述动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📦 特点一：存储对象</h2>
            <div class="canvas-container">
                <canvas id="storageCanvas" width="700" height="450"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">对象封装数据，对象多了也需要存储。集合用于存储对象。</span></p>
                <p>就像一个智能仓库，专门用来存放各种各样的对象！</p>
                <button class="interactive-btn" onclick="animateStorage()">📦 看看对象存储</button>
                <button class="interactive-btn" onclick="playStorageGame()">🎮 对象存储游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📏 特点二：可变长度</h2>
            <div class="canvas-container">
                <canvas id="variableLengthCanvas" width="700" height="450"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">对象的个数确定可以使用数组，对象的个数不确定的可以用集合。因为集合是可变长度的。</span></p>
                <p>集合就像一个神奇的魔法袋，可以根据需要自动伸缩！</p>
                <button class="interactive-btn" onclick="animateVariableLength()">📏 观看长度变化</button>
                <button class="interactive-btn" onclick="playLengthGame()">🎮 长度变化游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚖️ 数组 VS 集合</h2>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="400"></canvas>
            </div>
            <div class="text-content">
                <p>让我们来看看数组和集合的区别：</p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0;">
                    <div style="background: linear-gradient(45deg, #fd79a8, #e84393); padding: 25px; border-radius: 20px; color: white; text-align: center;">
                        <h3>📊 数组</h3>
                        <p>✅ 长度固定</p>
                        <p>✅ 适合确定个数</p>
                        <p>❌ 不能动态调整</p>
                    </div>
                    <div style="background: linear-gradient(45deg, #74b9ff, #0984e3); padding: 25px; border-radius: 20px; color: white; text-align: center;">
                        <h3>📦 集合</h3>
                        <p>✅ 长度可变</p>
                        <p>✅ 适合不确定个数</p>
                        <p>✅ 可以动态调整</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateComparison()">⚖️ 对比演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="700" height="300"></canvas>
            </div>
            <div class="text-content">
                <p>🎉 恭喜！你已经掌握了集合的两大特点：</p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0;">
                    <div style="background: linear-gradient(45deg, #fd79a8, #e84393); padding: 20px; border-radius: 15px; color: white; text-align: center;">
                        <h3>📦 存储对象</h3>
                        <p>集合专门用于存储各种对象</p>
                    </div>
                    <div style="background: linear-gradient(45deg, #74b9ff, #0984e3); padding: 20px; border-radius: 15px; color: white; text-align: center;">
                        <h3>📏 可变长度</h3>
                        <p>集合长度可以动态调整</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateSummary()">🎊 播放总结动画</button>
                <button class="interactive-btn" onclick="playAllAnimations()">🎬 播放全部动画</button>
            </div>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        let gameObjects = [];
        
        function updateProgress(progress) {
            currentProgress = Math.min(100, currentProgress + progress);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 概述动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景渐变
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#74b9ff');
                gradient.addColorStop(0.5, '#6c5ce7');
                gradient.addColorStop(1, '#fd79a8');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const time = frame * 0.05;
                
                // 主标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('集合的两大特点', centerX, 80);
                
                // 特点1区域
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress1;
                    
                    // 特点1背景
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(50, 120, 300, 200);
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(50, 120, 300, 200);
                    
                    // 特点1内容
                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('特点一', 200, 150);
                    ctx.font = '16px Arial';
                    ctx.fillText('存储对象', 200, 180);
                    
                    // 对象图标
                    for (let i = 0; i < 3; i++) {
                        ctx.fillStyle = '#fd79a8';
                        ctx.fillRect(120 + i * 60, 200, 40, 40);
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(`对象${i+1}`, 140 + i * 60, 225);
                    }
                    
                    ctx.restore();
                }
                
                // 特点2区域
                if (frame > 60) {
                    const progress2 = Math.min(1, (frame - 60) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress2;
                    
                    // 特点2背景
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(450, 120, 300, 200);
                    ctx.strokeStyle = '#6c5ce7';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(450, 120, 300, 200);
                    
                    // 特点2内容
                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('特点二', 600, 150);
                    ctx.font = '16px Arial';
                    ctx.fillText('可变长度', 600, 180);
                    
                    // 可变长度演示
                    const boxCount = Math.floor(3 + Math.sin(time) * 2);
                    for (let i = 0; i < boxCount; i++) {
                        ctx.fillStyle = '#6c5ce7';
                        ctx.fillRect(480 + i * 35, 200, 30, 30);
                        ctx.fillStyle = 'white';
                        ctx.font = '10px Arial';
                        ctx.fillText(i+1, 495 + i * 35, 220);
                    }
                    
                    ctx.restore();
                }
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(25);
                }
            }
            
            draw();
        }

        // 存储对象动画
        function animateStorage() {
            const canvas = document.getElementById('storageCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const objects = [
                { name: '学生对象', color: '#fd79a8', data: ['姓名', '年龄', '成绩'] },
                { name: '汽车对象', color: '#fdcb6e', data: ['品牌', '颜色', '价格'] },
                { name: '书籍对象', color: '#6c5ce7', data: ['标题', '作者', '页数'] },
                { name: '手机对象', color: '#00b894', data: ['型号', '内存', '价格'] }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createRadialGradient(350, 225, 0, 350, 225, 400);
                gradient.addColorStop(0, '#ddd6fe');
                gradient.addColorStop(1, '#8b5cf6');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 集合容器
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 4;
                ctx.setLineDash([15, 10]);
                ctx.lineDashOffset = -frame * 0.5;
                ctx.strokeRect(50, 50, 600, 350);

                // 容器标签
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🗂️ 集合容器', 350, 35);

                // 对象飞入动画
                objects.forEach((obj, index) => {
                    const delay = index * 40;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 80));

                    if (progress > 0) {
                        const startX = -100;
                        const startY = 100 + index * 80;
                        const endX = 100 + (index % 2) * 250;
                        const endY = 120 + Math.floor(index / 2) * 120;

                        const currentX = startX + (endX - startX) * progress;
                        const currentY = startY + (endY - startY) * progress;

                        ctx.save();
                        ctx.translate(currentX, currentY);
                        ctx.scale(progress, progress);

                        // 对象外框
                        ctx.fillStyle = obj.color;
                        ctx.fillRect(-60, -40, 120, 80);
                        ctx.strokeStyle = '#fff';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(-60, -40, 120, 80);

                        // 对象名称
                        ctx.fillStyle = '#fff';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(obj.name, 0, -20);

                        // 对象数据
                        obj.data.forEach((data, i) => {
                            ctx.font = '10px Arial';
                            ctx.fillText(data, 0, -5 + i * 12);
                        });

                        ctx.restore();

                        // 轨迹线
                        if (progress < 1) {
                            ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                            ctx.lineWidth = 2;
                            ctx.setLineDash([5, 5]);
                            ctx.beginPath();
                            ctx.moveTo(startX, startY);
                            ctx.lineTo(currentX, currentY);
                            ctx.stroke();
                        }
                    }
                });

                // 存储说明
                if (frame > 160) {
                    ctx.fillStyle = '#fff';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✨ 集合可以存储各种不同类型的对象', 350, 430);
                }

                frame++;
                if (frame < 250) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(25);
                }
            }

            draw();
        }

        // 对象存储游戏
        function playStorageGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px;">🎮 对象存储游戏</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px;">拖拽下面的对象到集合容器中！</p>
                <div id="objectPool" style="margin-bottom: 20px;">
                    <div class="object-item" draggable="true" data-type="student">👨‍🎓 学生</div>
                    <div class="object-item" draggable="true" data-type="car">🚗 汽车</div>
                    <div class="object-item" draggable="true" data-type="book">📚 书籍</div>
                    <div class="object-item" draggable="true" data-type="phone">📱 手机</div>
                    <div class="object-item" draggable="true" data-type="food">🍎 食物</div>
                </div>
                <div id="collectionContainer" style="min-height: 100px; border: 3px dashed #74b9ff; border-radius: 15px; padding: 20px; background: rgba(116, 185, 255, 0.1); text-align: center;">
                    <p style="color: #74b9ff; font-weight: bold;">📦 集合容器（拖拽对象到这里）</p>
                    <div id="storedObjects"></div>
                </div>
                <p id="gameScore" style="text-align: center; margin-top: 15px; font-weight: bold; color: #00b894;">已存储对象: 0</p>
            `;

            // 插入到当前section后面
            const currentSection = document.querySelector('.section:nth-child(3)');
            currentSection.appendChild(gameArea);

            // 添加拖拽功能
            let score = 0;

            document.querySelectorAll('.object-item').forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', e.target.dataset.type);
                    e.target.style.opacity = '0.5';
                });

                item.addEventListener('dragend', (e) => {
                    e.target.style.opacity = '1';
                });
            });

            const container = document.getElementById('collectionContainer');
            container.addEventListener('dragover', (e) => {
                e.preventDefault();
                container.style.background = 'rgba(116, 185, 255, 0.3)';
            });

            container.addEventListener('dragleave', () => {
                container.style.background = 'rgba(116, 185, 255, 0.1)';
            });

            container.addEventListener('drop', (e) => {
                e.preventDefault();
                const objectType = e.dataTransfer.getData('text/plain');

                // 创建存储的对象
                const storedObject = document.createElement('div');
                storedObject.style.cssText = `
                    display: inline-block;
                    background: linear-gradient(45deg, #00b894, #00cec9);
                    color: white;
                    padding: 8px 15px;
                    margin: 5px;
                    border-radius: 12px;
                    animation: bounce 0.5s ease;
                `;

                const objectNames = {
                    student: '👨‍🎓 学生',
                    car: '🚗 汽车',
                    book: '📚 书籍',
                    phone: '📱 手机',
                    food: '🍎 食物'
                };

                storedObject.textContent = objectNames[objectType];
                document.getElementById('storedObjects').appendChild(storedObject);

                score++;
                document.getElementById('gameScore').textContent = `已存储对象: ${score}`;
                container.style.background = 'rgba(116, 185, 255, 0.1)';

                if (score >= 3) {
                    setTimeout(() => {
                        alert('🎉 恭喜！你已经理解了集合存储对象的特点！');
                        updateProgress(20);
                    }, 500);
                }
            });
        }

        // 可变长度动画
        function animateVariableLength() {
            const canvas = document.getElementById('variableLengthCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            let collectionSize = 1;
            let growing = true;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#a29bfe');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 动态调整集合大小
                if (frame % 60 === 0) {
                    if (growing) {
                        collectionSize++;
                        if (collectionSize >= 8) growing = false;
                    } else {
                        collectionSize--;
                        if (collectionSize <= 1) growing = true;
                    }
                }

                // 集合容器（可变大小）
                const containerWidth = 80 + collectionSize * 40;
                const containerHeight = 100;

                ctx.fillStyle = 'rgba(255,255,255,0.9)';
                ctx.fillRect(centerX - containerWidth/2, centerY - containerHeight/2, containerWidth, containerHeight);
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 3;
                ctx.strokeRect(centerX - containerWidth/2, centerY - containerHeight/2, containerWidth, containerHeight);

                // 集合中的元素
                for (let i = 0; i < collectionSize; i++) {
                    const x = centerX - containerWidth/2 + 20 + i * 35;
                    const y = centerY;

                    ctx.fillStyle = '#fd79a8';
                    ctx.beginPath();
                    ctx.arc(x, y, 12, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, x, y + 3);
                }

                // 标题和说明
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📏 集合的可变长度', centerX, 80);

                ctx.font = '16px Arial';
                ctx.fillText(`当前大小: ${collectionSize}`, centerX, centerY + 80);

                // 箭头指示
                if (growing) {
                    ctx.fillStyle = '#00b894';
                    ctx.font = '20px Arial';
                    ctx.fillText('↗️ 增长中', centerX, centerY + 110);
                } else {
                    ctx.fillStyle = '#e17055';
                    ctx.font = '20px Arial';
                    ctx.fillText('↘️ 缩小中', centerX, centerY + 110);
                }

                frame++;
                if (frame < 600) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(20);
                }
            }

            draw();
        }

        // 长度变化游戏
        function playLengthGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px;">🎮 集合长度变化游戏</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px;">点击按钮来增加或减少集合中的元素！</p>
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="interactive-btn" onclick="addElement()" style="margin: 5px;">➕ 添加元素</button>
                    <button class="interactive-btn" onclick="removeElement()" style="margin: 5px;">➖ 删除元素</button>
                    <button class="interactive-btn" onclick="clearCollection()" style="margin: 5px;">🗑️ 清空集合</button>
                </div>
                <div id="dynamicCollection" style="min-height: 80px; border: 3px solid #74b9ff; border-radius: 15px; padding: 20px; background: rgba(116, 185, 255, 0.1); text-align: center; display: flex; flex-wrap: wrap; justify-content: center; align-items: center;">
                    <p style="color: #74b9ff; font-weight: bold; width: 100%;">📦 动态集合</p>
                </div>
                <p id="collectionSize" style="text-align: center; margin-top: 15px; font-weight: bold; color: #00b894;">集合大小: 0</p>
            `;

            const currentSection = document.querySelector('.section:nth-child(4)');
            currentSection.appendChild(gameArea);

            let elementCount = 0;

            window.addElement = function() {
                elementCount++;
                const element = document.createElement('div');
                element.style.cssText = `
                    background: linear-gradient(45deg, #74b9ff, #0984e3);
                    color: white;
                    padding: 10px 15px;
                    margin: 5px;
                    border-radius: 12px;
                    animation: bounce 0.5s ease;
                    display: inline-block;
                `;
                element.textContent = `元素${elementCount}`;
                element.id = `element-${elementCount}`;

                document.getElementById('dynamicCollection').appendChild(element);
                document.getElementById('collectionSize').textContent = `集合大小: ${document.getElementById('dynamicCollection').children.length - 1}`;
            };

            window.removeElement = function() {
                const collection = document.getElementById('dynamicCollection');
                const elements = collection.querySelectorAll('div[id^="element-"]');
                if (elements.length > 0) {
                    elements[elements.length - 1].remove();
                    document.getElementById('collectionSize').textContent = `集合大小: ${collection.children.length - 1}`;
                }
            };

            window.clearCollection = function() {
                const collection = document.getElementById('dynamicCollection');
                const elements = collection.querySelectorAll('div[id^="element-"]');
                elements.forEach(el => el.remove());
                document.getElementById('collectionSize').textContent = `集合大小: 0`;
                elementCount = 0;
            };
        }

        // 对比动画
        function animateComparison() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#ffeaa7');
                gradient.addColorStop(1, '#fab1a0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数组 VS 集合', canvas.width / 2, 50);

                // 数组部分
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress1;

                    // 数组容器（固定大小）
                    ctx.fillStyle = '#fd79a8';
                    ctx.fillRect(100, 150, 250, 80);
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(100, 150, 250, 80);

                    // 数组元素（固定5个）
                    for (let i = 0; i < 5; i++) {
                        ctx.fillStyle = '#fff';
                        ctx.fillRect(120 + i * 42, 170, 35, 40);
                        ctx.strokeStyle = '#2d3436';
                        ctx.strokeRect(120 + i * 42, 170, 35, 40);
                        ctx.fillStyle = '#2d3436';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(i + 1, 137.5 + i * 42, 195);
                    }

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 18px Arial';
                    ctx.fillText('📊 数组 (固定长度)', 225, 130);
                    ctx.font = '14px Arial';
                    ctx.fillText('长度: 5 (不可变)', 225, 270);

                    ctx.restore();
                }

                // 集合部分
                if (frame > 60) {
                    const progress2 = Math.min(1, (frame - 60) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress2;

                    // 集合容器（可变大小）
                    const collectionSize = 3 + Math.floor(Math.sin(frame * 0.05) * 2 + 2);
                    const containerWidth = 50 + collectionSize * 35;

                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(450, 150, containerWidth, 80);
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(450, 150, containerWidth, 80);

                    // 集合元素（可变数量）
                    for (let i = 0; i < collectionSize; i++) {
                        ctx.fillStyle = '#fff';
                        ctx.fillRect(465 + i * 30, 170, 25, 40);
                        ctx.strokeStyle = '#2d3436';
                        ctx.strokeRect(465 + i * 30, 170, 25, 40);
                        ctx.fillStyle = '#2d3436';
                        ctx.font = '10px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(i + 1, 477.5 + i * 30, 195);
                    }

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 18px Arial';
                    ctx.fillText('📦 集合 (可变长度)', 450 + containerWidth/2, 130);
                    ctx.font = '14px Arial';
                    ctx.fillText(`长度: ${collectionSize} (可变)`, 450 + containerWidth/2, 270);

                    ctx.restore();
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 总结动画
        function animateSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createRadialGradient(350, 150, 0, 350, 150, 300);
                gradient.addColorStop(0, '#fd79a8');
                gradient.addColorStop(0.5, '#74b9ff');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 庆祝烟花效果
                for (let i = 0; i < 15; i++) {
                    const angle = (frame * 0.03 + i * 0.4) % (Math.PI * 2);
                    const radius = 80 + Math.sin(frame * 0.02 + i) * 40;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;

                    ctx.fillStyle = `hsl(${(frame * 2 + i * 25) % 360}, 80%, 70%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 4 + Math.sin(frame * 0.1 + i) * 2, 0, Math.PI * 2);
                    ctx.fill();

                    // 闪烁效果
                    if (Math.sin(frame * 0.2 + i) > 0.5) {
                        ctx.fillStyle = 'white';
                        ctx.beginPath();
                        ctx.arc(x, y, 2, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                // 中心成就徽章
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(frame * 0.01);

                // 徽章外圈
                ctx.fillStyle = 'gold';
                ctx.beginPath();
                ctx.arc(0, 0, 50, 0, Math.PI * 2);
                ctx.fill();

                // 徽章内圈
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(0, 0, 35, 0, Math.PI * 2);
                ctx.fill();

                // 徽章图标
                ctx.fillStyle = '#fd79a8';
                ctx.font = 'bold 30px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎓', 0, 10);

                ctx.restore();

                // 成就文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 学习完成！', centerX, centerY - 100);

                ctx.font = '16px Arial';
                ctx.fillText('你已经掌握了集合的两大特点', centerX, centerY + 100);

                // 特点总结
                if (frame > 60) {
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(centerX - 150, centerY + 120, 300, 60);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(centerX - 150, centerY + 120, 300, 60);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText('📦 存储对象  📏 可变长度', centerX, centerY + 155);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(100);
                }
            }

            draw();
        }

        // 播放全部动画
        async function playAllAnimations() {
            const animations = [
                animateOverview,
                animateStorage,
                animateVariableLength,
                animateComparison,
                animateSummary
            ];

            for (let i = 0; i < animations.length; i++) {
                animations[i]();
                await new Promise(resolve => setTimeout(resolve, 4000));
            }
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    animateOverview();
                    break;
                case '2':
                    animateStorage();
                    break;
                case '3':
                    animateVariableLength();
                    break;
                case '4':
                    animateComparison();
                    break;
                case '5':
                    animateSummary();
                    break;
                case ' ':
                    e.preventDefault();
                    playAllAnimations();
                    break;
            }
        });

        // 添加提示信息
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        `;
        hint.innerHTML = '💡 提示：按数字键1-5播放对应动画<br>空格键播放全部动画';
        document.body.appendChild(hint);

        // 3秒后隐藏提示
        setTimeout(() => {
            hint.style.opacity = '0';
            hint.style.transition = 'opacity 1s';
        }, 4000);

        // 页面加载完成后自动播放概述动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateOverview();
            }, 1000);
        });

        // 鼠标悬停效果
        document.querySelectorAll('.section').forEach(section => {
            section.addEventListener('mouseenter', () => {
                section.style.transform = 'translateY(-8px)';
                section.style.boxShadow = '0 30px 60px rgba(0,0,0,0.2)';
            });

            section.addEventListener('mouseleave', () => {
                section.style.transform = 'translateY(0)';
                section.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';
            });
        });
    </script>
</body>
</html>
