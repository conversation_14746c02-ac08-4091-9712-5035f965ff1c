<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程同步与PV操作 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .canvas-container {
            position: relative;
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        canvas {
            display: block;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .semaphore-display {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            min-width: 80px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #4caf50;
            color: white;
            transform: scale(1.1);
        }

        .process-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .process {
            background: #fff3e0;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .process.running {
            background: #4caf50;
            color: white;
            animation: pulse 1s infinite;
        }

        .process.waiting {
            background: #ff9800;
            color: white;
        }

        .process.finished {
            background: #9e9e9e;
            color: white;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .quiz-section {
            grid-column: 1 / -1;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quiz-option {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: #e3f2fd;
            border-color: #667eea;
        }

        .quiz-option.correct {
            background: #c8e6c9;
            border-color: #4caf50;
        }

        .quiz-option.wrong {
            background: #ffcdd2;
            border-color: #f44336;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 进程同步与PV操作</h1>
            <p>零基础学习操作系统进程同步机制</p>
        </div>

        <div class="content-grid">
            <div class="card">
                <h2>📚 基础知识</h2>
                <div class="explanation">
                    <h3>什么是进程同步？</h3>
                    <p>进程同步是指多个进程按照一定的顺序执行，确保它们之间的协调配合。就像接力赛一样，前一个选手跑完，后一个选手才能开始。</p>
                </div>
                
                <div class="explanation">
                    <h3>什么是信号量？</h3>
                    <p>信号量就像是一个计数器，用来控制进程的执行顺序：</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li><strong>P操作</strong>：等待信号（-1），如果信号量为0就等待</li>
                        <li><strong>V操作</strong>：发送信号（+1），通知其他进程可以继续</li>
                    </ul>
                </div>

                <div class="step-indicator">
                    <div class="step active" id="step1">1</div>
                    <div class="step" id="step2">2</div>
                    <div class="step" id="step3">3</div>
                    <div class="step" id="step4">4</div>
                </div>
            </div>

            <div class="card">
                <h2>🎮 交互演示</h2>
                <div class="canvas-container">
                    <canvas id="processCanvas" width="500" height="400"></canvas>
                </div>
                
                <div class="controls">
                    <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                    <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                    <button class="btn" onclick="stepByStep()">👆 单步执行</button>
                </div>

                <div class="semaphore-display">
                    <div class="semaphore" id="s1">S1: <span id="s1-value">0</span></div>
                    <div class="semaphore" id="s2">S2: <span id="s2-value">0</span></div>
                    <div class="semaphore" id="s3">S3: <span id="s3-value">0</span></div>
                    <div class="semaphore" id="s4">S4: <span id="s4-value">0</span></div>
                    <div class="semaphore" id="s5">S5: <span id="s5-value">0</span></div>
                </div>

                <div class="process-status">
                    <div class="process" id="p1">P1<br>就绪</div>
                    <div class="process" id="p2">P2<br>等待</div>
                    <div class="process" id="p3">P3<br>等待</div>
                    <div class="process" id="p4">P4<br>等待</div>
                    <div class="process" id="p5">P5<br>等待</div>
                </div>
            </div>
        </div>

        <div class="content-grid">
            <div class="quiz-section">
                <h2>🧠 题目解析</h2>
                <div class="explanation">
                    <h3>题目分析：</h3>
                    <p>根据前趋图，我们可以看到：</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>P1 执行完后，P2 和 P3 才能执行</li>
                        <li>P2 执行完后，P4 才能执行</li>
                        <li>P3 执行完后，P4 才能执行</li>
                        <li>P4 执行完后，P5 才能执行</li>
                    </ul>
                </div>

                <h3>问题1：a处应填写什么？</h3>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    A. P(S1)和P(S2) - 等待信号
                </div>
                <div class="quiz-option" onclick="selectOption(this, true)">
                    B. V(S1)和V(S2) - 发送信号 ✓
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    C. P(S1)和V(S2) - 混合操作
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    D. P(S2)和V(S1) - 混合操作
                </div>

                <div class="explanation" id="answer-explanation" style="display: none;">
                    <h3>💡 解析：</h3>
                    <p><strong>正确答案是B：V(S1)和V(S2)</strong></p>
                    <p>因为P1执行完成后，需要<strong>通知</strong>P2和P3可以开始执行了。V操作就是发送信号，告诉等待的进程"我完成了，你们可以开始了"。</p>
                    <p>就像接力赛中，第一棒跑完后要把接力棒传给第二棒和第三棒选手。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('processCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationStep = 0;
        let isAnimating = false;
        let semaphoreValues = {s1: 0, s2: 0, s3: 0, s4: 0, s5: 0};
        let processStates = {p1: 'ready', p2: 'waiting', p3: 'waiting', p4: 'waiting', p5: 'waiting'};

        function drawProcess(x, y, name, state) {
            ctx.save();
            
            // 设置颜色
            switch(state) {
                case 'ready':
                    ctx.fillStyle = '#2196F3';
                    break;
                case 'running':
                    ctx.fillStyle = '#4CAF50';
                    break;
                case 'waiting':
                    ctx.fillStyle = '#FF9800';
                    break;
                case 'finished':
                    ctx.fillStyle = '#9E9E9E';
                    break;
            }
            
            // 绘制进程圆圈
            ctx.beginPath();
            ctx.arc(x, y, 25, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制进程名称
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 5);
            
            ctx.restore();
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 绘制箭头
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 10;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI/6), toY - arrowLength * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI/6), toY - arrowLength * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function drawPVOperation(x, y, operation, semaphore) {
            ctx.fillStyle = '#E3F2FD';
            ctx.fillRect(x - 25, y - 15, 50, 30);
            ctx.strokeStyle = '#2196F3';
            ctx.strokeRect(x - 25, y - 15, 50, 30);
            
            ctx.fillStyle = '#1976D2';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${operation}(${semaphore})`, x, y + 4);
        }

        function drawCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制进程
            drawProcess(80, 100, 'P1', processStates.p1);
            drawProcess(200, 80, 'P2', processStates.p2);
            drawProcess(200, 200, 'P3', processStates.p3);
            drawProcess(350, 140, 'P4', processStates.p4);
            drawProcess(450, 140, 'P5', processStates.p5);
            
            // 绘制箭头（前趋关系）
            drawArrow(105, 100, 175, 85);  // P1 -> P2
            drawArrow(105, 100, 175, 195); // P1 -> P3
            drawArrow(225, 85, 325, 135);  // P2 -> P4
            drawArrow(225, 195, 325, 145); // P3 -> P4
            drawArrow(375, 140, 425, 140); // P4 -> P5
            
            // 绘制PV操作
            drawPVOperation(140, 60, 'V', 'S1');
            drawPVOperation(140, 140, 'V', 'S2');
            drawPVOperation(160, 50, 'P', 'S1');
            drawPVOperation(160, 220, 'P', 'S2');
            drawPVOperation(280, 100, 'V', 'S3');
            drawPVOperation(280, 180, 'V', 'S4');
            drawPVOperation(320, 110, 'P', 'S3');
            drawPVOperation(320, 170, 'P', 'S4');
            drawPVOperation(400, 120, 'V', 'S5');
            drawPVOperation(430, 120, 'P', 'S5');
        }

        function updateSemaphoreDisplay() {
            Object.keys(semaphoreValues).forEach(key => {
                document.getElementById(`${key}-value`).textContent = semaphoreValues[key];
                const element = document.getElementById(key);
                if (semaphoreValues[key] > 0) {
                    element.classList.add('active');
                } else {
                    element.classList.remove('active');
                }
            });
        }

        function updateProcessDisplay() {
            Object.keys(processStates).forEach(key => {
                const element = document.getElementById(key);
                element.className = `process ${processStates[key]}`;
                
                let statusText = '';
                switch(processStates[key]) {
                    case 'ready': statusText = '就绪'; break;
                    case 'running': statusText = '运行中'; break;
                    case 'waiting': statusText = '等待'; break;
                    case 'finished': statusText = '完成'; break;
                }
                element.innerHTML = `${key.toUpperCase()}<br>${statusText}`;
            });
        }

        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationStep = 0;
            resetStates();
            animateStep();
        }

        function resetAnimation() {
            isAnimating = false;
            animationStep = 0;
            resetStates();
            drawCanvas();
            updateSemaphoreDisplay();
            updateProcessDisplay();
            updateStepIndicator();
        }

        function resetStates() {
            semaphoreValues = {s1: 0, s2: 0, s3: 0, s4: 0, s5: 0};
            processStates = {p1: 'ready', p2: 'waiting', p3: 'waiting', p4: 'waiting', p5: 'waiting'};
        }

        function stepByStep() {
            if (animationStep < 4) {
                executeAnimationStep(animationStep);
                animationStep++;
                updateStepIndicator();
            }
        }

        function animateStep() {
            if (!isAnimating || animationStep >= 4) {
                isAnimating = false;
                return;
            }
            
            executeAnimationStep(animationStep);
            animationStep++;
            updateStepIndicator();
            
            setTimeout(() => {
                animateStep();
            }, 2000);
        }

        function executeAnimationStep(step) {
            switch(step) {
                case 0:
                    // P1开始执行
                    processStates.p1 = 'running';
                    break;
                case 1:
                    // P1完成，执行V(S1)和V(S2)
                    processStates.p1 = 'finished';
                    semaphoreValues.s1 = 1;
                    semaphoreValues.s2 = 1;
                    processStates.p2 = 'ready';
                    processStates.p3 = 'ready';
                    break;
                case 2:
                    // P2和P3开始执行
                    processStates.p2 = 'running';
                    processStates.p3 = 'running';
                    semaphoreValues.s1 = 0;
                    semaphoreValues.s2 = 0;
                    break;
                case 3:
                    // P2和P3完成，P4开始执行
                    processStates.p2 = 'finished';
                    processStates.p3 = 'finished';
                    semaphoreValues.s3 = 1;
                    semaphoreValues.s4 = 1;
                    processStates.p4 = 'ready';
                    setTimeout(() => {
                        processStates.p4 = 'running';
                        semaphoreValues.s3 = 0;
                        semaphoreValues.s4 = 0;
                        drawCanvas();
                        updateSemaphoreDisplay();
                        updateProcessDisplay();
                    }, 1000);
                    break;
            }
            
            drawCanvas();
            updateSemaphoreDisplay();
            updateProcessDisplay();
        }

        function updateStepIndicator() {
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                if (i <= animationStep) {
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else if (i === animationStep + 1) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    step.classList.remove('active', 'completed');
                }
            }
        }

        function selectOption(element, isCorrect) {
            // 移除所有选项的样式
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            
            // 添加正确或错误的样式
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('answer-explanation').style.display = 'block';
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                document.querySelectorAll('.quiz-option').forEach((option, index) => {
                    if (index === 1) { // B选项
                        option.classList.add('correct');
                    }
                });
                document.getElementById('answer-explanation').style.display = 'block';
            }
        }

        // 初始化
        drawCanvas();
        updateSemaphoreDisplay();
        updateProcessDisplay();
        updateStepIndicator();
    </script>
</body>
</html>
