<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：contribute</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        #word-pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        canvas {
            background: #ffffff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }
        .explanation h2 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
            margin-top: 0;
        }
        .explanation p {
            font-size: 1.1em;
            line-height: 1.8;
        }
        .explanation .highlight {
            font-weight: bold;
            color: #d93025;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        #interactive-instruction {
            color: #5f6368;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>contribute</h1>
        <p id="word-pronunciation">[kənˈtrɪbjuːt] / "肯吹BIU特"</p>
        
        <canvas id="wordCanvas" width="600" height="350"></canvas>
        <p id="interactive-instruction">点击画布或按钮开始动画</p>
        <button id="playButton">播放动画</button>

        <div class="explanation">
            <h2>单词拆解教学 📖</h2>
            <p>
                你好！今天我们要学习的单词是 <span class="highlight">contribute</span>，一个充满了"分享"和"协作"精神的词。
                我们来把它拆解开：
            </p>
            <p>
                1. 前缀 <span class="highlight">con-</span>：这个前缀表示"<span class="highlight">共同，一起</span>"。它和 `com-` 很像，比如 `connect` (共同链接 -> 连接)，`combine` (共同捆绑 -> 结合)。
            </p>
            <p>
                2. 词根 <span class="highlight">-tribute-</span>：这个词根来自拉丁语 `tribuere`，意思是"<span class="highlight">给予，分配</span>"。比如 `attribute` (把...归给 -> 归因于)，`distribute` (分开给 -> 分配)。
            </p>
            <p>
                把它们放在一起，故事就出来了：<span class="highlight">con (一起)</span> + <span class="highlight">tribute (给予)</span> = <span class="highlight">contribute (一起给予)</span>。
                大家一起把自己的力量、时间或金钱"给予"同一个目标，这不就是"<span class="highlight">贡献</span>"、"<span class="highlight">捐助</span>"吗？它还有"<span class="highlight">促成</span>"一件事情发生的意思。
            </p>
            <h2>翻译与用法 ✍️</h2>
            <p>
                <b>及物动词 & 不及物动词 (vt. & vi.):</b>
                <ul>
                    <li><b>贡献，捐献:</b> She <em>contributed</em> a lot of money to the charity. (她给慈善机构捐了很多钱。)</li>
                    <li><b>促成，导致:</b> Human error <em>contributed</em> to the accident. (人为失误导致了这次事故的发生。)</li>
                    <li><b>投稿:</b> He <em>contributes</em> to a weekly magazine. (他为一家周刊撰稿。)</li>
                </ul>
            </p>
            <p>
                <b>相关词汇:</b>
                <ul>
                    <li><b>contribution (n.):</b> 贡献，捐款</li>
                    <li><b>contributor (n.):</b> 贡献者，捐助者</li>
                </ul>
            </p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        let animationState = 'initial';
        let frame = 0;
        const totalFrames = 500;

        const box = { x: 250, y: 200, width: 100, height: 80 };
        const items = [
            { id: 1, startX: 50, startY: 100, x: 50, y: 100, color: '#fbbc05' },
            { id: 2, startX: 550, startY: 100, x: 550, y: 100, color: '#34a853' },
            { id: 3, startX: 50, startY: 300, x: 50, y: 300, color: '#4285f4' },
            { id: 4, startX: 550, startY: 300, x: 550, y: 300, color: '#ea4335' }
        ];
        let itemsInBox = 0;

        function drawBox() {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(box.x, box.y, box.width, box.height);

             ctx.fillStyle = '#333';
             ctx.font = '20px Arial';
             ctx.textAlign = 'center';
             ctx.fillText('目标', box.x + box.width / 2, box.y - 10);
        }

        function drawItems() {
            items.forEach(item => {
                ctx.fillStyle = item.color;
                ctx.beginPath();
                ctx.arc(item.x, item.y, 15, 0, Math.PI * 2);
                ctx.fill();
            });
        }
        
        function resetItems() {
            items.forEach(item => {
                item.x = item.startX;
                item.y = item.startY;
            });
            itemsInBox = 0;
        }

        function drawInitial() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBox();
            resetItems();
            drawItems();
        }

        function animate() {
            frame++;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (animationState === 'initial') {
                drawInitial();
                return;
            }

            drawBox();

            // Phase 1: Show "con-" (together)
            if (frame > 50 && frame < 200) {
                const alpha = Math.min(1, (frame - 50) / 50);
                ctx.font = 'bold 70px Arial';
                ctx.fillStyle = `rgba(217, 48, 37, ${alpha})`;
                ctx.fillText('con-', 150, 150);
                ctx.font = 'bold 30px Arial';
                ctx.fillText('一起', 150, 200);
            }
            
            // Phase 2: Show "tribute" (give)
            if (frame > 100 && frame < 200) {
                const alpha = Math.min(1, (frame - 100) / 50);
                ctx.font = 'bold 70px Arial';
                ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                ctx.fillText('-tribute', 450, 150);
                ctx.font = 'bold 30px Arial';
                ctx.fillText('给予', 450, 200);
            }

            // Phase 3: Items move to the box
            if (frame > 200 && frame < 350) {
                const progress = (frame - 200) / 150;
                items.forEach(item => {
                    const targetX = box.x + box.width / 2;
                    const targetY = box.y + box.height / 2;
                    item.x = item.startX + (targetX - item.startX) * progress;
                    item.y = item.startY + (targetY - item.startY) * progress;
                });
                
                if (progress >= 1) {
                    itemsInBox = items.length;
                }
            } else if (frame >= 350) {
                itemsInBox = items.length;
            }
            
            if (itemsInBox < items.length) {
                 drawItems();
            }
           
            // Draw items inside the box
             ctx.fillStyle = '#777';
             ctx.font = '16px Arial';
             ctx.textAlign = 'center';
             ctx.fillText(`已收集: ${itemsInBox}`, box.x + box.width / 2, box.y + box.height + 20);


            // Phase 4: Show final word
            if (frame > 350) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const alpha = Math.min(1, (frame - 350) / 80);
                ctx.font = 'bold 90px Arial';
                ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                ctx.textAlign = 'center';
                ctx.fillText('contribute', canvas.width / 2, 180);
                
                ctx.font = '40px Arial';
                ctx.fillStyle = `rgba(51, 51, 51, ${alpha})`;
                ctx.fillText('一起给予 = 贡献 / 捐助', canvas.width / 2, 260);
            }

            if (frame < totalFrames) {
                requestAnimationFrame(animate);
            } else {
                animationState = 'finished';
                playButton.textContent = '重新播放';
            }
        }

        function startAnimation() {
            if (animationState === 'finished' || animationState === 'initial') {
                frame = 0;
                animationState = 'playing';
                playButton.textContent = '播放中...';
                drawInitial();
                requestAnimationFrame(animate);
            }
        }

        canvas.addEventListener('click', startAnimation);
        playButton.addEventListener('click', startAnimation);

        drawInitial();
    </script>
</body>
</html> 