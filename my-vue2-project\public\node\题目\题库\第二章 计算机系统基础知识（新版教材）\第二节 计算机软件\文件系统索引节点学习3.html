<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件系统索引节点 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            color: #4a5568;
            font-size: 2em;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .inode-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        .inode {
            background: #f7fafc;
            border: 3px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            width: 300px;
            transition: all 0.3s ease;
        }

        .inode:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .address-item {
            background: white;
            border: 2px solid #cbd5e0;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .address-item:hover {
            background: #edf2f7;
            transform: scale(1.02);
        }

        .direct {
            border-color: #48bb78;
            background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
        }

        .indirect1 {
            border-color: #ed8936;
            background: linear-gradient(135deg, #fed7aa, #fbb6ce);
        }

        .indirect2 {
            border-color: #9f7aea;
            background: linear-gradient(135deg, #e9d8fd, #d6bcfa);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: white;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f7fafc;
            border-left: 5px solid #667eea;
            padding: 25px;
            margin: 30px 0;
            border-radius: 0 15px 15px 0;
            font-size: 1.1em;
            line-height: 1.8;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            color: #2d3436;
        }

        .quiz-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: #f8f9fa;
            transform: translateX(10px);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        .calculation-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
        }

        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 文件系统索引节点学习</h1>
            <p>通过动画和交互，轻松理解文件系统的索引机制</p>
        </div>

        <div class="section">
            <h2>🎯 学习目标</h2>
            <div class="explanation">
                <p><strong>今天我们要学习：</strong></p>
                <ul style="margin-left: 30px; margin-top: 15px;">
                    <li>什么是文件索引节点（inode）</li>
                    <li>直接地址索引、一级间接地址索引、二级间接地址索引的区别</li>
                    <li>如何计算不同逻辑块号使用哪种索引方式</li>
                    <li>解题思路和方法</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📚 基础知识</h2>
            <div class="explanation">
                <p><strong>文件索引节点（inode）</strong>就像是文件的"目录"，告诉系统文件的数据存储在磁盘的哪些位置。</p>
                <p>想象一下，你有一本很厚的书，为了快速找到内容，你需要一个目录。inode就是文件系统中的"目录"。</p>
            </div>

            <div class="inode-container">
                <div class="inode">
                    <h3 style="text-align: center; margin-bottom: 20px;">索引节点结构</h3>
                    <div class="address-item direct" onclick="explainDirect()">
                        直接地址索引 1 (4字节)
                    </div>
                    <div class="address-item direct" onclick="explainDirect()">
                        直接地址索引 2 (4字节)
                    </div>
                    <div class="address-item direct" onclick="explainDirect()">
                        直接地址索引 3 (4字节)
                    </div>
                    <div class="address-item direct" onclick="explainDirect()">
                        直接地址索引 4 (4字节)
                    </div>
                    <div class="address-item direct" onclick="explainDirect()">
                        直接地址索引 5 (4字节)
                    </div>
                    <div class="address-item indirect1" onclick="explainIndirect1()">
                        一级间接地址索引 1 (4字节)
                    </div>
                    <div class="address-item indirect1" onclick="explainIndirect1()">
                        一级间接地址索引 2 (4字节)
                    </div>
                    <div class="address-item indirect2" onclick="explainIndirect2()">
                        二级间接地址索引 (4字节)
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎨 可视化演示</h2>
            <div class="canvas-container">
                <canvas id="visualCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showDirectDemo()">演示直接索引</button>
                <button class="btn" onclick="showIndirect1Demo()">演示一级间接索引</button>
                <button class="btn" onclick="showIndirect2Demo()">演示二级间接索引</button>
                <button class="btn" onclick="calculateExample()">计算示例</button>
            </div>
        </div>

        <div class="section">
            <h2>🧮 计算过程详解</h2>
            <div class="calculation-box">
                <div class="step">
                    <strong>第一步：理解容量计算</strong><br>
                    • 每个地址项：4字节<br>
                    • 磁盘块大小：1KB = 1024字节<br>
                    • 每个间接块可容纳地址数：1024 ÷ 4 = 256个地址
                </div>
                <div class="step">
                    <strong>第二步：计算各级索引范围</strong><br>
                    • 直接地址索引：5个，覆盖逻辑块号 0-4<br>
                    • 一级间接地址索引：2个 × 256 = 512个，覆盖逻辑块号 5-516<br>
                    • 二级间接地址索引：256 × 256 = 65536个，覆盖逻辑块号 517开始
                </div>
                <div class="step">
                    <strong>第三步：判断具体块号</strong><br>
                    • 逻辑块号1：在0-4范围内 → 直接地址索引<br>
                    • 逻辑块号518：大于516 → 二级间接地址索引
                </div>
            </div>
        </div>

        <div class="section quiz-section">
            <h2>🎯 题目练习</h2>
            <div class="explanation">
                <p><strong>题目：</strong>若要访问iclsClient.dll文件的逻辑块号分别为1、518，则系统应分别采用？</p>
            </div>

            <div class="quiz-option" onclick="selectAnswer(this, false)">
                A. 直接地址索引和直接地址索引
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, false)">
                B. 直接地址索引和一级间接地址索引
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, true)">
                C. 直接地址索引和二级间接地址索引
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, false)">
                D. 一级间接地址索引和二级间接地址索引
            </div>

            <div id="quizResult" style="margin-top: 20px; padding: 20px; border-radius: 10px; display: none;"></div>
        </div>

        <div class="section">
            <h2>📈 学习进度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p style="text-align: center; margin-top: 10px;">完成度: <span id="progressText">0%</span></p>
        </div>
    </div>

    <script>
        let canvas = document.getElementById('visualCanvas');
        let ctx = canvas.getContext('2d');
        let progress = 0;

        // 初始化画布
        function initCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制圆角矩形
        function roundRect(x, y, width, height, radius, fillColor, strokeColor) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();

            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }

        // 绘制箭头
        function drawArrow(fromX, fromY, toX, toY, color = '#667eea') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            let angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI / 6), toY - 15 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI / 6), toY - 15 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 添加文字
        function addText(text, x, y, size = 14, color = '#333', align = 'center') {
            ctx.fillStyle = color;
            ctx.font = `${size}px Microsoft YaHei`;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        // 演示直接索引
        function showDirectDemo() {
            initCanvas();

            // 绘制inode
            roundRect(50, 50, 150, 300, 10, '#e8f5e8', '#48bb78');
            addText('索引节点', 125, 30, 16, '#333');

            // 绘制直接地址项
            for (let i = 0; i < 5; i++) {
                roundRect(60, 70 + i * 50, 130, 40, 5, '#c6f6d5', '#48bb78');
                addText(`直接地址${i + 1}`, 125, 95 + i * 50);
            }

            // 绘制数据块
            for (let i = 0; i < 5; i++) {
                roundRect(300, 70 + i * 50, 100, 40, 5, '#fff5f5', '#e53e3e');
                addText(`数据块${i}`, 350, 95 + i * 50);

                // 绘制箭头
                drawArrow(190, 90 + i * 50, 300, 90 + i * 50, '#48bb78');
            }

            addText('直接地址索引：直接指向数据块', 400, 400, 16, '#48bb78');
            addText('覆盖逻辑块号：0-4', 400, 420, 14, '#666');

            updateProgress(25);
        }

        // 演示一级间接索引
        function showIndirect1Demo() {
            initCanvas();

            // 绘制inode
            roundRect(30, 100, 120, 200, 10, '#fff5e6', '#ed8936');
            addText('索引节点', 90, 85, 16, '#333');

            // 一级间接地址项
            roundRect(40, 120, 100, 30, 5, '#fed7aa', '#ed8936');
            addText('一级间接', 90, 140);

            // 间接块
            roundRect(250, 50, 120, 300, 10, '#fff0f0', '#e53e3e');
            addText('间接块', 310, 35, 16, '#333');
            addText('(256个地址)', 310, 370, 12, '#666');

            // 间接块中的地址
            for (let i = 0; i < 6; i++) {
                roundRect(260, 70 + i * 45, 100, 30, 5, '#fbb6ce', '#e53e3e');
                addText(`地址${i + 1}`, 310, 90 + i * 45);
            }

            // 数据块
            for (let i = 0; i < 3; i++) {
                roundRect(500, 100 + i * 80, 80, 40, 5, '#f0fff4', '#38a169');
                addText(`数据块`, 540, 125 + i * 80);
            }

            // 箭头
            drawArrow(150, 135, 250, 135, '#ed8936');
            drawArrow(360, 85, 500, 120, '#e53e3e');
            drawArrow(360, 130, 500, 200, '#e53e3e');
            drawArrow(360, 175, 500, 280, '#e53e3e');

            addText('一级间接索引：通过间接块指向数据块', 400, 400, 16, '#ed8936');
            addText('每个间接块可容纳256个地址', 400, 420, 14, '#666');
            addText('2个一级间接索引覆盖：逻辑块号5-516', 400, 440, 14, '#666');

            updateProgress(50);
        }

        // 演示二级间接索引
        function showIndirect2Demo() {
            initCanvas();

            // 绘制inode
            roundRect(20, 150, 100, 100, 10, '#f3e8ff', '#9f7aea');
            addText('索引节点', 70, 135, 16, '#333');

            // 二级间接地址项
            roundRect(30, 170, 80, 30, 5, '#e9d8fd', '#9f7aea');
            addText('二级间接', 70, 190);

            // 二级间接块
            roundRect(200, 100, 100, 200, 10, '#fff5f5', '#e53e3e');
            addText('二级间接块', 250, 85, 14, '#333');

            for (let i = 0; i < 4; i++) {
                roundRect(210, 120 + i * 40, 80, 25, 3, '#fbb6ce', '#e53e3e');
                addText(`地址${i + 1}`, 250, 137 + i * 40);
            }

            // 一级间接块
            roundRect(400, 50, 80, 150, 10, '#fff0f0', '#f56565');
            addText('一级间接块', 440, 35, 12, '#333');

            for (let i = 0; i < 3; i++) {
                roundRect(410, 70 + i * 40, 60, 25, 3, '#fed7d7', '#f56565');
                addText(`地址`, 440, 87 + i * 40);
            }

            // 数据块
            for (let i = 0; i < 3; i++) {
                roundRect(600, 70 + i * 50, 60, 30, 5, '#f0fff4', '#38a169');
                addText(`数据`, 630, 90 + i * 50);
            }

            // 箭头
            drawArrow(120, 185, 200, 185, '#9f7aea');
            drawArrow(300, 140, 400, 125, '#e53e3e');
            drawArrow(480, 85, 600, 85, '#f56565');
            drawArrow(480, 125, 600, 135, '#f56565');
            drawArrow(480, 165, 600, 185, '#f56565');

            addText('二级间接索引：间接块指向一级间接块', 350, 400, 16, '#9f7aea');
            addText('可索引：256 × 256 = 65536 个数据块', 350, 420, 14, '#666');
            addText('覆盖逻辑块号：517开始', 350, 440, 14, '#666');

            updateProgress(75);
        }

        // 计算示例
        function calculateExample() {
            initCanvas();

            // 标题
            addText('逻辑块号计算示例', 400, 30, 20, '#333');

            // 绘制计算过程
            roundRect(50, 60, 700, 380, 15, '#f8f9fa', '#e2e8f0');

            // 逻辑块号1的计算
            addText('逻辑块号 1 的索引方式：', 100, 100, 16, '#333', 'left');
            roundRect(100, 110, 600, 80, 10, '#e8f5e8', '#48bb78');
            addText('1 在范围 [0, 4] 内', 150, 135, 14, '#333', 'left');
            addText('→ 使用直接地址索引', 150, 155, 14, '#48bb78', 'left');
            addText('✓ 第2个直接地址项', 150, 175, 14, '#48bb78', 'left');

            // 逻辑块号518的计算
            addText('逻辑块号 518 的索引方式：', 100, 220, 16, '#333', 'left');
            roundRect(100, 230, 600, 120, 10, '#f3e8ff', '#9f7aea');
            addText('直接索引范围：[0, 4] ✗', 150, 255, 14, '#333', 'left');
            addText('一级间接索引范围：[5, 516] ✗', 150, 275, 14, '#333', 'left');
            addText('518 > 516', 150, 295, 14, '#e53e3e', 'left');
            addText('→ 使用二级间接地址索引', 150, 315, 14, '#9f7aea', 'left');
            addText('✓ 二级间接地址项', 150, 335, 14, '#9f7aea', 'left');

            // 答案
            roundRect(100, 370, 600, 60, 10, '#fff3cd', '#ffc107');
            addText('答案：C. 直接地址索引和二级间接地址索引', 400, 405, 16, '#333');

            updateProgress(100);
        }

        // 更新进度
        function updateProgress(newProgress) {
            progress = Math.max(progress, newProgress);
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = progress + '%';
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('quizResult').innerHTML =
                    '<div style="color: #28a745; font-weight: bold;">🎉 恭喜你答对了！</div>' +
                    '<p>逻辑块号1使用直接地址索引，逻辑块号518使用二级间接地址索引。</p>';
                updateProgress(100);
            } else {
                element.classList.add('wrong');
                document.getElementById('quizResult').innerHTML =
                    '<div style="color: #dc3545; font-weight: bold;">❌ 答案不正确，再想想看</div>' +
                    '<p>提示：计算一下518在哪个索引范围内。</p>';
            }

            document.getElementById('quizResult').style.display = 'block';
        }

        // 解释函数
        function explainDirect() {
            alert('直接地址索引：直接指向数据块的地址，最简单快速的索引方式。覆盖逻辑块号0-4。');
        }

        function explainIndirect1() {
            alert('一级间接地址索引：指向一个间接块，间接块中存储多个数据块地址。每个间接块可存储256个地址，2个一级间接索引共覆盖逻辑块号5-516。');
        }

        function explainIndirect2() {
            alert('二级间接地址索引：指向一个间接块，该间接块中存储多个一级间接块的地址。可索引256×256=65536个数据块，覆盖逻辑块号517开始。');
        }

        // 初始化
        initCanvas();
        addText('点击上方按钮开始学习！', 400, 250, 18, '#667eea');
    </script>
</body>
</html>
