<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCP/IP协议学习 - 互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            text-align: center;
            font-size: 1.5rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-color: #4facfe;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
            color: white;
            border-color: #ff6b6b;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #animationCanvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4facfe;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">TCP/IP协议学习</h1>
            <p class="subtitle">通过动画和互动理解网络协议的奥秘</p>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">3</div>
            <div class="step" id="step4">4</div>
        </div>

        <!-- 题目展示 -->
        <div class="section question-box" id="questionSection">
            <h2>📝 题目</h2>
            <p>TCP/IP（ ）are the standards around which the Internet was developed.</p>
            <div class="options">
                <div class="option" data-answer="A" onclick="selectOption(this, 'A')">
                    A. protocols
                </div>
                <div class="option" data-answer="B" onclick="selectOption(this, 'B')">
                    B. agreements
                </div>
                <div class="option" data-answer="C" onclick="selectOption(this, 'C')">
                    C. conventions
                </div>
                <div class="option" data-answer="D" onclick="selectOption(this, 'D')">
                    D. coordination
                </div>
            </div>
            <button class="btn" onclick="showExplanation()">查看解析</button>
        </div>

        <!-- 动画演示区域 -->
        <div class="section">
            <h2>🎬 动画演示</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            <button class="btn" onclick="startAnimation()">开始动画演示</button>
            <button class="btn" onclick="resetAnimation()">重新播放</button>
        </div>

        <!-- 知识解析 -->
        <div class="section explanation" id="explanationSection">
            <h2>💡 知识解析</h2>
            
            <div class="concept-card">
                <h3>🔍 题目分析</h3>
                <p><strong>正确答案：A (protocols)</strong></p>
                <p>这道题考查的是对TCP/IP基本概念的理解。TCP/IP是一套<strong>协议</strong>，而不是协议、约定或协调。</p>
            </div>

            <div class="concept-card">
                <h3>📚 词汇辨析</h3>
                <ul>
                    <li><strong>Protocols (协议)</strong>：计算机网络中的正式规则和标准</li>
                    <li><strong>Agreements (协议/协定)</strong>：更多指人与人之间的约定</li>
                    <li><strong>Conventions (惯例)</strong>：约定俗成的做法</li>
                    <li><strong>Coordination (协调)</strong>：协调配合的行为</li>
                </ul>
            </div>

            <div class="concept-card">
                <h3>🌐 TCP/IP协议详解</h3>
                <p>TCP/IP是互联网的基础协议套件，包含多个层次的协议，确保数据能够在网络中可靠传输。</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let selectedAnswer = null;
        let animationRunning = false;

        // Canvas动画相关
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');

        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            selectedAnswer = answer;
            
            if (answer === 'A') {
                element.classList.add('correct');
                setTimeout(() => {
                    updateStep(2);
                    showExplanation();
                }, 1000);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    // 显示正确答案
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                }, 1000);
            }
        }

        function showExplanation() {
            const explanation = document.getElementById('explanationSection');
            explanation.classList.add('show');
            updateStep(3);
        }

        function updateStep(step) {
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step}`).classList.add('active');
            currentStep = step;
        }

        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            updateStep(4);
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 动画序列
            animateNetworkConcept();
        }

        function resetAnimation() {
            animationRunning = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            setTimeout(() => startAnimation(), 100);
        }

        function animateNetworkConcept() {
            let frame = 0;
            const maxFrames = 300;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                drawBackground();
                
                // 根据帧数绘制不同阶段
                if (frame < 60) {
                    drawTitle("什么是协议 (Protocol)?", frame);
                } else if (frame < 120) {
                    drawProtocolConcept(frame - 60);
                } else if (frame < 180) {
                    drawTCPIPLayers(frame - 120);
                } else if (frame < 240) {
                    drawDataFlow(frame - 180);
                } else {
                    drawConclusion(frame - 240);
                }

                frame++;
                if (frame < maxFrames && animationRunning) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            }

            animate();
        }

        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function drawTitle(text, frame) {
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            
            const alpha = Math.min(frame / 30, 1);
            ctx.globalAlpha = alpha;
            ctx.fillText(text, canvas.width / 2, 50);
            ctx.globalAlpha = 1;
        }

        function drawProtocolConcept(frame) {
            // 绘制协议概念
            ctx.fillStyle = '#4facfe';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            
            const texts = [
                "协议 = 规则 + 标准",
                "就像交通规则一样",
                "让数据有序传输"
            ];
            
            texts.forEach((text, index) => {
                const alpha = Math.max(0, Math.min((frame - index * 20) / 20, 1));
                ctx.globalAlpha = alpha;
                ctx.fillText(text, canvas.width / 2, 100 + index * 40);
            });
            
            ctx.globalAlpha = 1;
        }

        function drawTCPIPLayers(frame) {
            // 绘制TCP/IP四层模型
            const layers = [
                { name: "应用层", color: "#ff6b6b", y: 80 },
                { name: "传输层 (TCP)", color: "#4ecdc4", y: 140 },
                { name: "网络层 (IP)", color: "#45b7d1", y: 200 },
                { name: "链路层", color: "#96ceb4", y: 260 }
            ];

            layers.forEach((layer, index) => {
                const alpha = Math.max(0, Math.min((frame - index * 15) / 15, 1));
                ctx.globalAlpha = alpha;
                
                ctx.fillStyle = layer.color;
                ctx.fillRect(200, layer.y, 400, 50);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(layer.name, 400, layer.y + 30);
            });
            
            ctx.globalAlpha = 1;
        }

        function drawDataFlow(frame) {
            // 绘制数据流动
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText("数据在协议栈中的流动", canvas.width / 2, 50);

            // 绘制数据包
            const x = 100 + (frame * 10) % 600;
            ctx.fillStyle = '#ff9a9e';
            ctx.fillRect(x, 180, 60, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText("数据包", x + 30, 205);
        }

        function drawConclusion(frame) {
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            
            const alpha = Math.min(frame / 30, 1);
            ctx.globalAlpha = alpha;
            
            ctx.fillText("TCP/IP protocols", canvas.width / 2, 150);
            ctx.fillText("是互联网的基础标准!", canvas.width / 2, 200);
            
            ctx.globalAlpha = 1;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 添加一些交互提示
            setTimeout(() => {
                if (!selectedAnswer) {
                    document.querySelector('.question-box').classList.add('pulse');
                }
            }, 3000);
        });
    </script>
</body>
</html>
