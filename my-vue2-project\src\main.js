import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import './styles/main.css'

// 导入并全局注册 PageHeader 组件
import PageHeader from './components/PageHeader.vue'
Vue.component('PageHeader', PageHeader)

// 导入并全局注册 TextButton 组件
import TextButton from './components/common/TextButton.vue'
Vue.component('TextButton', TextButton)

Vue.use(ElementUI);

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
