<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强类型语言 - 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            text-align: center;
        }
        h1 {
            color: #1a2a4c;
            font-size: 2em;
            margin-bottom: 10px;
        }
        .explanation {
            color: #555;
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 25px;
        }
        .code-block {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            text-align: left;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 1em;
            margin: 20px 0;
            white-space: pre;
            overflow-x: auto;
        }
        .code-block .keyword { color: #569cd6; }
        .code-block .type { color: #4ec9b0; }
        .code-block .variable { color: #9cdcfe; }
        .code-block .number { color: #b5cea8; }
        .code-block .string { color: #ce9178; }
        .code-block .comment { color: #6a9955; }
        canvas {
            background-color: #fdfdfd;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 20px;
        }
        .controls {
            margin-top: 25px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1em;
            font-weight: bold;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        button.error-btn {
            background-color: #dc3545;
        }
        button.error-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>3. 强类型语言</h1>
        <div class="explanation">
            <p>在强类型语言中，<strong>所有变量必须先声明类型</strong>。</p>
            <p>程序在编译时会进行严格的<strong>类型检查</strong>，如果类型不匹配，就会报错。</p>
        </div>
        
        <h2>示例：</h2>
        <div class="code-block" aria-label="Java代码示例">
<span class="code-line"><span class="type">int</span> <span class="variable">number</span> = <span class="number">10</span>;     <span class="comment">// 正确 ✅</span></span>
<span class="code-line"><span class="variable">number</span> = <span class="string">"text"</span>; <span class="comment">// 编译错误 ❌</span></span>
        </div>

        <h2>交互动画：</h2>
        <canvas id="animationCanvas" width="740" height="250"></canvas>
        <div class="controls">
            <button id="runCorrect">执行正确代码</button>
            <button id="runError" class="error-btn">执行错误代码</button>
        </div>
    </div>

    <script>
    const canvas = document.getElementById('animationCanvas');
    const ctx = canvas.getContext('2d');
    const runCorrectBtn = document.getElementById('runCorrect');
    const runErrorBtn = document.getElementById('runError');

    // 动画参数
    let valueX = 50;
    const valueY = canvas.height / 2;
    const boxX = 500;
    const boxY = canvas.height / 2 - 40;
    const boxWidth = 150;
    const boxHeight = 80;
    let animationFrameId;

    // 清空画布并绘制初始状态
    function drawInitialState(valueText, valueColor, boxLabel) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制变量盒子
        ctx.strokeStyle = '#007bff';
        ctx.lineWidth = 3;
        ctx.strokeRect(boxX, boxY, boxWidth, boxHeight);
        ctx.fillStyle = '#555';
        ctx.font = 'bold 20px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(`变量 number`, boxX + boxWidth / 2, boxY - 30);
        ctx.font = 'italic 18px sans-serif';
        ctx.fillStyle = '#007bff';
        ctx.fillText(`(类型: ${boxLabel})`, boxX + boxWidth / 2, boxY - 10);

        // 绘制要赋的值
        ctx.font = 'bold 30px sans-serif';
        ctx.fillStyle = valueColor;
        ctx.textAlign = 'center';
        ctx.fillText(valueText, valueX, valueY + 10);
    }

    function animate(type) {
        cancelAnimationFrame(animationFrameId); // 取消上一个动画

        let currentValueX = 50;
        const targetX = boxX + boxWidth / 2;
        const speed = 5;

        let valueText, valueColor, boxLabel;
        if (type === 'correct') {
            valueText = '10';
            valueColor = '#28a745';
            boxLabel = 'int (整数)';
        } else {
            valueText = '"text"';
            valueColor = '#dc3545';
            boxLabel = 'int (整数)';
        }

        function step() {
            drawInitialState(valueText, valueColor, boxLabel);
            
            // 绘制移动的值
            ctx.font = 'bold 30px sans-serif';
            ctx.fillStyle = valueColor;
            ctx.textAlign = 'center';
            ctx.fillText(valueText, currentValueX, valueY + 10);

            currentValueX += speed;

            if (currentValueX < boxX) {
                animationFrameId = requestAnimationFrame(step);
            } else {
                // 到达盒子位置
                if (type === 'correct') {
                    animateSuccess();
                } else {
                    animateFailure();
                }
            }
        }
        step();
    }
    
    function animateSuccess() {
        drawInitialState('10', '#28a745', 'int (整数)');
        
        // 在盒子里绘制值
        ctx.font = 'bold 30px sans-serif';
        ctx.fillStyle = '#28a745';
        ctx.textAlign = 'center';
        ctx.fillText('10', boxX + boxWidth / 2, boxY + boxHeight / 2 + 10);

        // 显示成功信息
        ctx.font = 'bold 28px sans-serif';
        ctx.fillStyle = 'green';
        ctx.fillText('赋值成功 ✅', canvas.width / 2, 40);
    }
    
    function animateFailure() {
        drawInitialState('"text"', '#dc3545', 'int (整数)');
        
        // 绘制值
        ctx.font = 'bold 30px sans-serif';
        ctx.fillStyle = '#dc3545';
        ctx.textAlign = 'center';
        ctx.fillText('"text"', boxX, valueY + 10);
        
        // 绘制拒绝效果
        ctx.strokeStyle = '#dc3545';
        ctx.lineWidth = 5;
        // 画一个红叉
        ctx.beginPath();
        ctx.moveTo(boxX - 20, valueY - 20);
        ctx.lineTo(boxX + 20, valueY + 20);
        ctx.moveTo(boxX + 20, valueY - 20);
        ctx.lineTo(boxX - 20, valueY + 20);
        ctx.stroke();

        // 显示错误信息
        ctx.font = 'bold 28px sans-serif';
        ctx.fillStyle = '#dc3545';
        ctx.fillText('类型不匹配，赋值失败！ ❌', canvas.width / 2, 40);
        
        // 盒子震动效果
        let shakes = 10;
        function shake() {
            if (shakes > 0) {
                const shakeX = (Math.random() - 0.5) * 8;
                const shakeY = (Math.random() - 0.5) * 8;
                ctx.save();
                ctx.translate(shakeX, shakeY);
                drawInitialState('', '#dc3545', 'int (整数)');
                ctx.restore();
                shakes--;
                setTimeout(shake, 30);
            }
        }
        shake();
    }

    runCorrectBtn.addEventListener('click', () => animate('correct'));
    runErrorBtn.addEventListener('click', () => animate('error'));

    // 初始加载动画
    window.onload = () => {
       drawInitialState('?', '#ccc', 'int (整数)');
       ctx.font = 'bold 24px sans-serif';
       ctx.fillStyle = '#aaa';
       ctx.fillText('点击按钮开始动画', canvas.width / 2, 40);
    };
    </script>
</body>
</html> 