<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java基础概念互动学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            font-size: 2em;
            margin-bottom: 30px;
        }
        .container {
            width: 90%;
            max-width: 1200px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        h1, h2, h3 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-top: 0;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 30px;
        }
        h3 {
            font-size: 1.5em;
            margin-top: 20px;
        }
        p {
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .explanation {
            background-color: #e8f0fe;
            border-left: 5px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fafafa;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
        }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #2980b9;
        }
        .code-example {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin-bottom: 20px;
        }
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header>
        Java核心概念互动学习之旅
    </header>

    <div class="container">
        <h2>27、静态变量和实例变量的区别</h2>
        <p>
            在Java中，变量的分类有很多种，但理解"静态变量"和"实例变量"是掌握Java面向对象编程的关键一步。它们主要区别在于存储的位置、生命周期以及访问方式。
        </p>
        <div class="explanation">
            <strong>静态变量（Static Variables）：</strong>
            <p>静态变量属于类本身，不依赖于任何特定的对象实例。这意味着无论你创建多少个类的对象，静态变量在内存中都只有一份副本，被所有该类的对象共享。它们在类加载时被初始化。</p>
            <p>例如，如果你有一个 `Car` 类，并且想记录总共创建了多少辆车，这个"总数"就应该是一个静态变量。</p>
            <div class="code-example">
                <pre><code>
public class Car {
    public static int numberOfCarsCreated = 0; // 静态变量，所有Car对象共享

    public String color; // 实例变量，每个Car对象独有

    public Car(String color) {
        this.color = color;
        numberOfCarsCreated++; // 每创建一个Car对象，静态变量就加1
    }
}
                </code></pre>
            </div>

            <strong>实例变量（Instance Variables）：</strong>
            <p>实例变量属于类的每一个具体对象（实例）。每当你创建一个新对象时，都会为这个对象分配独立的内存空间来存储它的实例变量。因此，每个对象的实例变量都是独立的，互不影响。</p>
            <p>例如，每辆车都有自己的"颜色"，这个"颜色"就是实例变量。一辆车的颜色变了，不会影响到其他车的颜色。</p>
            <div class="code-example">
                <pre><code>
public class Car {
    public static int numberOfCarsCreated = 0; // 静态变量

    public String color; // 实例变量

    public Car(String color) {
        this.color = color;
        numberOfCarsCreated++;
    }

    public void displayInfo() {
        System.out.println("这辆车的颜色是: " + color + "。目前总共有 " + numberOfCarsCreated + " 辆车被创建。");
    }
}

// 演示：
// Car car1 = new Car("红色");   // car1 有自己的 color="红色"
// Car car2 = new Car("蓝色");   // car2 有自己的 color="蓝色"
// car1.numberOfCarsCreated 和 car2.numberOfCarsCreated 都指向同一个共享的静态变量
                </code></pre>
            </div>
        </div>

        <h3>互动演示：静态变量与实例变量</h3>
        <p>点击下方的按钮，观察"Car"类和它的对象是如何被创建的，以及静态变量和实例变量的不同表现。</p>
        <div class="controls">
            <button id="createCarBtn">创建一辆Car（点击观察变化）</button>
            <button id="resetBtn">重置演示</button>
        </div>
        <canvas id="carCanvas" width="900" height="450"></canvas>
    </div>

    <div class="container">
        <h2>28、什么是内部类？</h2>
        <p>
            在Java中，可以将一个类的定义放在另外一个类的定义内部，这就是内部类。内部类本身就是类的一个属性，与其他属性定义方式一致。它能够访问外部类的所有成员（包括私有成员），即使外部类的成员是私有的。
        </p>
        <div class="explanation">
            <p>想象一下，你有一个"外壳"类（Outer Class），这个外壳里面有一个"核心"类（Inner Class）。这个核心类就是外壳的一部分，它的存在与外壳紧密相关。就像手机（外部类）里面有震动马达（内部类），马达是手机的一部分，可以访问手机的电量、信号等信息。</p>
            <div class="code-example">
                <pre><code>
public class OuterClass {
    private String outerMessage = "我是外部类的信息。";

    public class InnerClass { // 这是一个成员内部类
        public void displayOuterMessage() {
            System.out.println("内部类访问外部类信息: " + outerMessage);
        }
    }

    public void createInner() {
        InnerClass inner = new InnerClass();
        inner.displayOuterMessage();
    }
}

// 如何使用：
// OuterClass outer = new OuterClass();
// OuterClass.InnerClass inner = outer.new InnerClass(); // 注意这里创建内部类实例的方式
// inner.displayOuterMessage();
                </code></pre>
            </div>
        </div>

        <h3>互动演示：内部类概念</h3>
        <p>下方动画将直观展示外部类和内部类之间的包含关系。</p>
        <div class="controls">
            <button id="showInnerClassBtn">演示内部类</button>
            <button id="resetInnerClassBtn">重置</button>
        </div>
        <canvas id="innerClassCanvas" width="900" height="300"></canvas>
    </div>

    <div class="container">
        <h2>29、内部类的分类有哪些？</h2>
        <p>
            内部类并不是单一的一种形式，根据它们的定义位置和特性，可以分为四种主要类型。
        </p>
        <div class="explanation">
            <p>这四种内部类分别是：</p>
            <ol>
                <li><strong>成员内部类（Member Inner Class）：</strong> 最常见的内部类，作为外部类的成员存在，可以访问外部类的所有成员。</li>
                <li><strong>局部内部类（Local Inner Class）：</strong> 定义在方法或代码块内部的类，作用范围仅限于该方法或代码块。</li>
                <li><strong>匿名内部类（Anonymous Inner Class）：</strong> 没有名字的内部类，通常用于简化代码，在创建对象时同时定义类的实现。常用于实现接口或继承抽象类。</li>
                <li><strong>静态内部类（Static Nested Class）：</strong> 用 `static` 关键字修饰的内部类。它不持有外部类对象的引用，因此不能直接访问外部类的非静态成员。它更像是一个独立的顶级类，只是恰好定义在另一个类的内部。</li>
            </ol>
            <div class="code-example">
                <pre><code>
// 成员内部类示例 (同上)
public class Outer {
    class MemberInner { }
}

// 局部内部类示例
public class OuterMethod {
    public void method() {
        class LocalInner { } // 定义在方法内部
        LocalInner li = new LocalInner();
    }
}

// 匿名内部类示例
interface Greeting {
    void greet();
}

public class AnonDemo {
    public void sayHello() {
        new Greeting() { // 匿名内部类
            @Override
            public void greet() {
                System.out.println("Hello from anonymous inner class!");
            }
        }.greet();
    }
}

// 静态内部类示例
public class OuterStatic {
    static class StaticNested { // 用static修饰
        // 可以直接创建：OuterStatic.StaticNested nested = new OuterStatic.StaticNested();
    }
}
                </code></pre>
            </div>
        </div>
        <h3>互动演示：内部类分类</h3>
        <p>点击按钮，我们将逐一演示这四种内部类的概念和特点。</p>
        <div class="controls">
            <button id="showMemberInnerBtn">成员内部类</button>
            <button id="showLocalInnerBtn">局部内部类</button>
            <button id="showAnonymousInnerBtn">匿名内部类</button>
            <button id="showStaticNestedBtn">静态内部类</button>
            <button id="resetTypesBtn">重置</button>
        </div>
        <canvas id="innerClassTypesCanvas" width="900" height="350"></canvas>
    </div>

    <script>
        // JavaScript for Canvas animations will go here
        const carCanvas = document.getElementById('carCanvas');
        const carCtx = carCanvas.getContext('2d');
        const createCarBtn = document.getElementById('createCarBtn');
        const resetCarBtn = document.getElementById('resetBtn');

        const innerClassCanvas = document.getElementById('innerClassCanvas');
        const innerClassCtx = innerClassCanvas.getContext('2d');
        const showInnerClassBtn = document.getElementById('showInnerClassBtn');
        const resetInnerClassBtn = document.getElementById('resetInnerClassBtn');

        const innerClassTypesCanvas = document.getElementById('innerClassTypesCanvas');
        const innerClassTypesCtx = innerClassTypesCanvas.getContext('2d');
        const showMemberInnerBtn = document.getElementById('showMemberInnerBtn');
        const showLocalInnerBtn = document.getElementById('showLocalInnerBtn');
        const showAnonymousInnerBtn = document.getElementById('showAnonymousInnerBtn');
        const showStaticNestedBtn = document.getElementById('showStaticNestedBtn');
        const resetTypesBtn = document.getElementById('resetTypesBtn');

        let cars = [];
        let staticCarCount = 0;
        const maxCars = 4; // Maximum number of cars to display for readability

        // Car colors for instance variables
        const carColors = ["#e74c3c", "#3498db", "#2ecc71", "#f1c40f"]; // Red, Blue, Green, Yellow

        function drawCarScene() {
            carCtx.clearRect(0, 0, carCanvas.width, carCanvas.height);

            // Draw Class Box
            carCtx.fillStyle = '#f39c12';
            carCtx.fillRect(50, 50, 200, 300);
            carCtx.strokeStyle = '#d35400';
            carCtx.lineWidth = 3;
            carCtx.strokeRect(50, 50, 200, 300);

            carCtx.fillStyle = 'white';
            carCtx.font = '24px Arial';
            carCtx.fillText('Car.class', 75, 85);

            // Draw Static Variable (inside Class Box)
            carCtx.fillStyle = '#c0392b';
            carCtx.fillRect(65, 120, 170, 60);
            carCtx.strokeStyle = '#a93226';
            carCtx.strokeRect(65, 120, 170, 60);
            carCtx.fillStyle = 'white';
            carCtx.font = '16px Arial';
            carCtx.fillText('static int numberOfCarsCreated', 70, 145);
            carCtx.fillText(`= ${staticCarCount}`, 70, 170);

            carCtx.fillStyle = '#333';
            carCtx.font = '18px Arial';
            carCtx.fillText('类在内存中只有一份', 60, 250);
            carCtx.fillText('静态变量属于类，也只有一份！', 60, 275);
            carCtx.fillText('所有对象共享它。', 60, 300);


            // Draw explanation for instance variable
            carCtx.fillStyle = '#333';
            carCtx.font = '18px Arial';
            carCtx.fillText('实例变量属于对象，', carCanvas.width / 2 + 100, 80);
            carCtx.fillText('每个对象都有一份独立的。', carCanvas.width / 2 + 100, 105);

            // Draw Objects
            let startX = 300;
            let startY = 150;
            let offset = 180;

            cars.forEach((car, index) => {
                let x = startX + (index % 2) * offset;
                let y = startY + Math.floor(index / 2) * offset;

                // Object Box
                carCtx.fillStyle = '#27ae60';
                carCtx.fillRect(x, y, 150, 120);
                carCtx.strokeStyle = '#229a58';
                carCtx.lineWidth = 2;
                carCtx.strokeRect(x, y, 150, 120);
                carCtx.fillStyle = 'white';
                carCtx.font = '18px Arial';
                carCtx.fillText(`Car对象 ${index + 1}`, x + 15, y + 30);

                // Instance Variable
                carCtx.fillStyle = car.color;
                carCtx.fillRect(x + 10, y + 50, 130, 40);
                carCtx.strokeStyle = '#1e8449';
                carCtx.strokeRect(x + 10, y + 50, 130, 40);
                carCtx.fillStyle = 'white';
                carCtx.font = '14px Arial';
                carCtx.fillText(`String color = "${car.colorName}"`, x + 15, y + 75);

                carCtx.fillStyle = '#333';
                carCtx.font = '14px Arial';
                carCtx.fillText(`在内存中独立存储`, x + 10, y + 105);
            });
        }

        function createCar() {
            if (cars.length < maxCars) {
                staticCarCount++;
                const colorIndex = cars.length % carColors.length;
                const color = carColors[colorIndex];
                const colorName = ["红色", "蓝色", "绿色", "黄色"][colorIndex];
                cars.push({ color: color, colorName: colorName });
                drawCarScene();
            } else {
                alert("已达到演示最大车辆数，请点击重置。");
            }
        }

        function resetCarDemo() {
            cars = [];
            staticCarCount = 0;
            drawCarScene();
        }

        createCarBtn.addEventListener('click', createCar);
        resetCarBtn.addEventListener('click', resetCarDemo);

        // Initial draw
        drawCarScene();

        // ----------------------- Internal Class Canvas -----------------------

        let innerClassAnimState = 0; // 0: initial, 1: showing outer, 2: showing inner
        function drawInnerClassScene() {
            innerClassCtx.clearRect(0, 0, innerClassCanvas.width, innerClassCanvas.height);

            // Outer Class
            innerClassCtx.fillStyle = '#3498db';
            innerClassCtx.fillRect(100, 50, 700, 200);
            innerClassCtx.strokeStyle = '#2980b9';
            innerClassCtx.lineWidth = 3;
            innerClassCtx.strokeRect(100, 50, 700, 200);
            innerClassCtx.fillStyle = 'white';
            innerClassCtx.font = '28px Arial';
            innerClassCtx.fillText('OuterClass', 120, 90);
            innerClassCtx.font = '18px Arial';
            innerClassCtx.fillText('外部类 - 包含一切', 120, 120);

            if (innerClassAnimState >= 1) {
                // Inner Class
                innerClassCtx.fillStyle = '#2ecc71';
                innerClassCtx.fillRect(150, 100, 300, 120);
                innerClassCtx.strokeStyle = '#27ae60';
                innerClassCtx.lineWidth = 2;
                innerClassCtx.strokeRect(150, 100, 300, 120);
                innerClassCtx.fillStyle = 'white';
                innerClassCtx.font = '22px Arial';
                innerClassCtx.fillText('InnerClass', 170, 135);
                innerClassCtx.font = '16px Arial';
                innerClassCtx.fillText('内部类 - 外部类的一部分', 170, 160);
                innerClassCtx.fillText('可以访问外部类的成员', 170, 185);

                // Arrow
                innerClassCtx.strokeStyle = '#e74c3c';
                innerClassCtx.lineWidth = 3;
                innerClassCtx.beginPath();
                innerClassCtx.moveTo(450, 160);
                innerClassCtx.lineTo(550, 160);
                innerClassCtx.lineTo(540, 150);
                innerClassCtx.moveTo(550, 160);
                innerClassCtx.lineTo(540, 170);
                innerClassCtx.stroke();
                innerClassCtx.fillStyle = '#e74c3c';
                innerClassCtx.font = '18px Arial';
                innerClassCtx.fillText('包含关系', 570, 165);
            }
        }

        function showInnerClassAnimation() {
            innerClassAnimState = 1;
            drawInnerClassScene();
        }

        function resetInnerClassAnimation() {
            innerClassAnimState = 0;
            drawInnerClassScene();
        }

        showInnerClassBtn.addEventListener('click', showInnerClassAnimation);
        resetInnerClassBtn.addEventListener('click', resetInnerClassAnimation);

        // Initial draw
        drawInnerClassScene();

        // ----------------------- Internal Class Types Canvas -----------------------

        let currentInnerType = 0; // 0: none, 1: Member, 2: Local, 3: Anonymous, 4: Static
        const innerTypeExplanations = {
            1: "成员内部类：最常见，作为外部类的成员。",
            2: "局部内部类：定义在方法内，作用域仅限方法。",
            3: "匿名内部类：没有名字，用于简化代码，实现接口或继承。",
            4: "静态内部类：用static修饰，不依赖外部类对象，更独立。"
        };

        function drawInnerClassTypesScene() {
            innerClassTypesCtx.clearRect(0, 0, innerClassTypesCanvas.width, innerClassTypesCanvas.height);

            // Draw a generic "Outer Class" container
            innerClassTypesCtx.fillStyle = '#9b59b6';
            innerClassTypesCtx.fillRect(50, 50, 800, 250);
            innerClassTypesCtx.strokeStyle = '#8e44ad';
            innerClassTypesCtx.lineWidth = 3;
            innerClassTypesCtx.strokeRect(50, 50, 800, 250);
            innerClassTypesCtx.fillStyle = 'white';
            innerClassTypesCtx.font = '28px Arial';
            innerClassTypesCtx.fillText('OuterClass', 70, 90);
            innerClassTypesCtx.font = '16px Arial';
            innerClassTypesCtx.fillText('外部类', 70, 115);

            if (currentInnerType === 1) { // Member Inner Class
                innerClassTypesCtx.fillStyle = '#f1c40f';
                innerClassTypesCtx.fillRect(100, 130, 250, 100);
                innerClassTypesCtx.strokeStyle = '#f39c12';
                innerClassTypesCtx.lineWidth = 2;
                innerClassTypesCtx.strokeRect(100, 130, 250, 100);
                innerClassTypesCtx.fillStyle = 'black';
                innerClassTypesCtx.font = '20px Arial';
                innerClassTypesCtx.fillText('MemberInnerClass', 110, 165);
                innerClassTypesCtx.font = '14px Arial';
                innerClassTypesCtx.fillText('作为成员存在，访问外部类所有成员', 110, 190);
            } else if (currentInnerType === 2) { // Local Inner Class
                innerClassTypesCtx.fillStyle = '#e67e22';
                innerClassTypesCtx.fillRect(100, 130, 250, 100);
                innerClassTypesCtx.strokeStyle = '#d35400';
                innerClassTypesCtx.lineWidth = 2;
                innerClassTypesCtx.strokeRect(100, 130, 250, 100);
                innerClassTypesCtx.fillStyle = 'white';
                innerClassTypesCtx.font = '20px Arial';
                innerClassTypesCtx.fillText('public void someMethod() {', 110, 165);
                innerClassTypesCtx.fillStyle = 'black';
                innerClassTypesCtx.fillText('  LocalInnerClass', 130, 190);
                innerClassTypesCtx.font = '14px Arial';
                innerClassTypesCtx.fillText('作用域仅限方法内', 110, 215);
            } else if (currentInnerType === 3) { // Anonymous Inner Class
                innerClassTypesCtx.fillStyle = '#e74c3c';
                innerClassTypesCtx.fillRect(100, 130, 250, 100);
                innerClassTypesCtx.strokeStyle = '#c0392b';
                innerClassTypesCtx.lineWidth = 2;
                innerClassTypesCtx.strokeRect(100, 130, 250, 100);
                innerClassTypesCtx.fillStyle = 'white';
                innerClassTypesCtx.font = '20px Arial';
                innerClassTypesCtx.fillText('new Interface() { ... }', 110, 165);
                innerClassTypesCtx.font = '14px Arial';
                innerClassTypesCtx.fillText('没有名字，用于简化代码', 110, 190);
                innerClassTypesCtx.fillText('常用于接口实现或抽象类继承', 110, 210);
            } else if (currentInnerType === 4) { // Static Nested Class
                innerClassTypesCtx.fillStyle = '#34495e';
                innerClassTypesCtx.fillRect(100, 130, 250, 100);
                innerClassTypesCtx.strokeStyle = '#2c3e50';
                innerClassTypesCtx.lineWidth = 2;
                innerClassTypesCtx.strokeRect(100, 130, 250, 100);
                innerClassTypesCtx.fillStyle = 'white';
                innerClassTypesCtx.font = '20px Arial';
                innerClassTypesCtx.fillText('static StaticNestedClass', 110, 165);
                innerClassTypesCtx.font = '14px Arial';
                innerClassTypesCtx.fillText('不依赖外部类对象，更独立', 110, 190);
                innerClassTypesCtx.fillText('不能直接访问外部非静态成员', 110, 210);
            }

            if (currentInnerType !== 0) {
                innerClassTypesCtx.fillStyle = '#333';
                innerClassTypesCtx.font = '18px Arial';
                innerClassTypesCtx.fillText(innerTypeExplanations[currentInnerType], 400, 180);
            }
        }

        function setInnerType(type) {
            currentInnerType = type;
            drawInnerClassTypesScene();
        }

        function resetInnerTypes() {
            currentInnerType = 0;
            drawInnerClassTypesScene();
        }

        showMemberInnerBtn.addEventListener('click', () => setInnerType(1));
        showLocalInnerBtn.addEventListener('click', () => setInnerType(2));
        showAnonymousInnerBtn.addEventListener('click', () => setInnerType(3));
        showStaticNestedBtn.addEventListener('click', () => setInnerType(4));
        resetTypesBtn.addEventListener('click', resetInnerTypes);

        // Initial draw
        drawInnerClassTypesScene();

    </script>
</body>
</html> 