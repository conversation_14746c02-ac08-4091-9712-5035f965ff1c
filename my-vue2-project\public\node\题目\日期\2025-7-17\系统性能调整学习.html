<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统性能调整 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .system-container {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .system-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 250px;
            position: relative;
            overflow: hidden;
        }

        .system-box:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .system-box.database {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .system-box.application {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .system-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            animation: bounce 2s infinite;
        }

        .system-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .parameters-list {
            list-style: none;
            margin-top: 20px;
        }

        .parameters-list li {
            background: rgba(255,255,255,0.2);
            margin: 8px 0;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(-20px);
        }

        .parameters-list li.show {
            opacity: 1;
            transform: translateX(0);
        }

        .parameters-list li:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(5px);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .quiz-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #444;
            margin-bottom: 30px;
            line-height: 1.6;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: white;
            border: 3px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            animation: correctAnswer 0.6s ease;
        }

        .option.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: wrongAnswer 0.6s ease;
        }

        .explanation {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #performanceCanvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 系统性能调整</h1>
            <p class="subtitle">通过动画和交互学习系统性能优化的核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识讲解</h2>
            <p style="font-size: 1.2rem; line-height: 1.8; color: #555; text-align: center; margin-bottom: 30px;">
                系统性能调整是指通过监控和调整系统参数来优化系统运行效率的过程。<br>
                不同类型的系统需要关注不同的性能指标。
            </p>
            
            <div class="system-container">
                <div class="system-box database" onclick="showDatabaseParams()">
                    <div class="system-icon">🗄️</div>
                    <div class="system-name">数据库系统</div>
                    <ul class="parameters-list" id="dbParams">
                        <li>CPU/内存使用状况</li>
                        <li>数据库设计优化</li>
                        <li>进程/线程使用状态</li>
                        <li>硬盘剩余空间</li>
                        <li>日志文件大小</li>
                    </ul>
                </div>
                
                <div class="system-box application" onclick="showApplicationParams()">
                    <div class="system-icon">💻</div>
                    <div class="system-name">应用系统</div>
                    <ul class="parameters-list" id="appParams">
                        <li>应用系统可用性</li>
                        <li>响应时间</li>
                        <li class="highlight">并发用户数</li>
                        <li>特定应用资源占用</li>
                    </ul>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="performanceCanvas" width="800" height="400"></canvas>
                <br>
                <button class="interactive-btn" onclick="startAnimation()">🎬 开始性能监控动画</button>
                <button class="interactive-btn" onclick="resetAnimation()">🔄 重置动画</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎯 互动测试</h2>
            <div class="quiz-question">
                <strong>题目：</strong>对于应用系统，性能调整主要包括应用系统的可用性、响应时间、<span class="highlight">（请作答此空）</span>、特定应用资源占用等。
            </div>
            
            <div class="options-container">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A.</strong> 并发用户数
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B.</strong> 支持协议和标准
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C.</strong> 最大连接数
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>D.</strong> 时延抖动
                </div>
            </div>
            
            <div class="explanation" id="explanation">
                <h3 style="color: #4CAF50; margin-bottom: 15px;">💡 详细解析</h3>
                <p style="line-height: 1.8; color: #555;">
                    <strong>正确答案：A - 并发用户数</strong><br><br>
                    
                    <strong>🔍 为什么是并发用户数？</strong><br>
                    • <span class="highlight">并发用户数</span>是衡量应用系统性能的核心指标<br>
                    • 它直接影响系统的负载和响应能力<br>
                    • 与可用性、响应时间密切相关<br><br>
                    
                    <strong>❌ 其他选项为什么不对？</strong><br>
                    • B. 支持协议和标准 - 这是功能性需求，不是性能调整参数<br>
                    • C. 最大连接数 - 这更多是数据库系统的参数<br>
                    • D. 时延抖动 - 这主要用于网络性能评估
                </p>
            </div>
        </div>
    </div>

    <script>
        let animationId;
        let isAnimating = false;
        
        // 显示数据库参数动画
        function showDatabaseParams() {
            const params = document.querySelectorAll('#dbParams li');
            params.forEach((param, index) => {
                setTimeout(() => {
                    param.classList.add('show');
                }, index * 200);
            });
        }
        
        // 显示应用系统参数动画
        function showApplicationParams() {
            const params = document.querySelectorAll('#appParams li');
            params.forEach((param, index) => {
                setTimeout(() => {
                    param.classList.add('show');
                }, index * 200);
            });
        }
        
        // 选择答案
        function selectOption(element, isCorrect) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    if (opt.textContent.includes('并发用户数')) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                } else if (opt.textContent.includes('并发用户数')) {
                    opt.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').classList.add('show');
            }, 1000);
        }
        
        // Canvas 动画
        function startAnimation() {
            if (isAnimating) return;
            
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');
            isAnimating = true;
            
            let time = 0;
            let dbLoad = 30;
            let appUsers = 50;
            let responseTime = 200;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('系统性能实时监控', canvas.width/2, 40);
                
                // 数据库系统监控
                drawSystemMonitor(ctx, 100, 80, '数据库系统', dbLoad, '#4facfe');
                
                // 应用系统监控
                drawSystemMonitor(ctx, 500, 80, '应用系统', appUsers, '#43e97b');
                
                // 响应时间图表
                drawResponseChart(ctx, time, responseTime);
                
                // 更新数据
                time += 0.1;
                dbLoad = 30 + Math.sin(time) * 20;
                appUsers = 50 + Math.sin(time * 0.8) * 30;
                responseTime = 200 + Math.sin(time * 1.2) * 50;
                
                if (isAnimating) {
                    animationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }
        
        function drawSystemMonitor(ctx, x, y, title, value, color) {
            // 系统框
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 200, 120);
            
            // 标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + 100, y + 25);
            
            // 数值
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText(Math.round(value) + '%', x + 100, y + 60);
            
            // 进度条
            ctx.fillStyle = 'rgba(255,255,255,0.3)';
            ctx.fillRect(x + 20, y + 80, 160, 20);
            ctx.fillStyle = 'white';
            ctx.fillRect(x + 20, y + 80, (value/100) * 160, 20);
        }
        
        function drawResponseChart(ctx, time, responseTime) {
            const chartX = 150;
            const chartY = 250;
            const chartWidth = 500;
            const chartHeight = 120;
            
            // 图表背景
            ctx.fillStyle = 'white';
            ctx.fillRect(chartX, chartY, chartWidth, chartHeight);
            ctx.strokeStyle = '#ddd';
            ctx.strokeRect(chartX, chartY, chartWidth, chartHeight);
            
            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('响应时间趋势 (ms)', chartX + chartWidth/2, chartY - 10);
            
            // 绘制响应时间曲线
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            for (let i = 0; i < chartWidth; i += 5) {
                const t = (time - (chartWidth - i) * 0.01);
                const y = chartY + chartHeight/2 + Math.sin(t * 1.2) * 30;
                if (i === 0) {
                    ctx.moveTo(chartX + i, y);
                } else {
                    ctx.lineTo(chartX + i, y);
                }
            }
            ctx.stroke();
            
            // 当前值
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(`当前响应时间: ${Math.round(responseTime)}ms`, chartX + 10, chartY + chartHeight + 20);
        }
        
        function resetAnimation() {
            isAnimating = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击"开始性能监控动画"查看实时监控', canvas.width/2, canvas.height/2);
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            resetAnimation();
            
            // 延迟显示参数列表
            setTimeout(() => {
                showDatabaseParams();
                showApplicationParams();
            }, 1000);
        };
    </script>
</body>
</html>
