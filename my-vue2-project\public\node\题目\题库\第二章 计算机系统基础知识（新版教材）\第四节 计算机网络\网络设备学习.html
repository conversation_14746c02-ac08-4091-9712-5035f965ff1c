<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络设备互动学习 - 交换机知识</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .options {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        canvas {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
        }

        .controls {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            animation: fadeIn 1s ease-out;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-3px);
        }

        .concept-title {
            color: #667eea;
            font-size: 1.3em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .concept-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            border-radius: 3px;
            transition: width 1s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络设备互动学习</h1>
            <p>通过动画和交互，轻松掌握交换机知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-title">
                📝 题目：下面关于交换机的说法中，正确的是（ ）
            </div>
            
            <div class="options" id="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 以太网交换机可以连接运行不同网络层协议的网络
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 从工作原理上讲，以太网交换机是一种多端口网桥
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 集线器是一种特殊的交换机
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 通过交换机连接的一组工作站形成一个冲突域
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="showAnswer()">🔍 查看答案解析</button>
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎯 网络设备工作原理可视化</h3>
            <canvas id="networkCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button class="btn" onclick="showHub()">集线器演示</button>
                <button class="btn" onclick="showSwitch()">交换机演示</button>
                <button class="btn" onclick="showBridge()">网桥演示</button>
            </div>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h3>💡 详细解析</h3>
            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">B</div>
                    正确答案：B - 以太网交换机是一种多端口网桥
                </div>
                <p>交换机本质上是网桥的进化版本，具有多个端口，能够学习MAC地址并建立转发表，实现智能转发。</p>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">❌</div>
                    为什么A错误？
                </div>
                <p>交换机工作在数据链路层，只能连接物理层和数据链路层协议不同的网络，网络层及以上必须使用相同协议。连接不同网络层协议需要路由器。</p>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">❌</div>
                    为什么C错误？
                </div>
                <p>集线器是物理层设备，只做信号放大和转发，所有端口共享带宽。交换机是数据链路层设备，每个端口独享带宽。</p>
            </div>

            <div class="concept-card">
                <div class="concept-title">
                    <div class="concept-icon">❌</div>
                    为什么D错误？
                </div>
                <p>交换机的每个端口形成独立的冲突域，而集线器连接的所有设备才形成一个冲突域。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentDemo = 'none';
        
        // 设备和数据包的位置
        const devices = {
            computer1: { x: 100, y: 150, name: 'PC1' },
            computer2: { x: 100, y: 250, name: 'PC2' },
            computer3: { x: 700, y: 150, name: 'PC3' },
            computer4: { x: 700, y: 250, name: 'PC4' },
            center: { x: 400, y: 200, name: '设备' }
        };

        let dataPackets = [];
        let collisionEffect = false;

        function drawDevice(x, y, name, type = 'computer') {
            ctx.save();
            
            if (type === 'computer') {
                // 绘制电脑
                ctx.fillStyle = '#4a90e2';
                ctx.fillRect(x - 20, y - 15, 40, 30);
                ctx.fillStyle = '#2c5aa0';
                ctx.fillRect(x - 15, y - 10, 30, 20);
                ctx.fillStyle = '#ffffff';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(name, x, y + 35);
            } else if (type === 'hub') {
                // 绘制集线器
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(x - 30, y - 20, 60, 40);
                ctx.fillStyle = '#ffffff';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('集线器', x, y + 5);
            } else if (type === 'switch') {
                // 绘制交换机
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(x - 30, y - 20, 60, 40);
                ctx.fillStyle = '#ffffff';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('交换机', x, y + 5);
            } else if (type === 'bridge') {
                // 绘制网桥
                ctx.fillStyle = '#45b7d1';
                ctx.fillRect(x - 25, y - 15, 50, 30);
                ctx.fillStyle = '#ffffff';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('网桥', x, y + 5);
            }
            
            ctx.restore();
        }

        function drawConnection(x1, y1, x2, y2, active = false) {
            ctx.save();
            ctx.strokeStyle = active ? '#ff6b6b' : '#cccccc';
            ctx.lineWidth = active ? 3 : 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            ctx.restore();
        }

        function drawDataPacket(x, y, color = '#ffd93d') {
            ctx.save();
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 8, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.restore();
        }

        function showHub() {
            currentDemo = 'hub';
            clearCanvas();
            
            // 绘制集线器连接
            drawConnection(devices.computer1.x, devices.computer1.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer2.x, devices.computer2.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer3.x, devices.computer3.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer4.x, devices.computer4.y, devices.center.x, devices.center.y);
            
            // 绘制设备
            drawDevice(devices.computer1.x, devices.computer1.y, devices.computer1.name);
            drawDevice(devices.computer2.x, devices.computer2.y, devices.computer2.name);
            drawDevice(devices.computer3.x, devices.computer3.y, devices.computer3.name);
            drawDevice(devices.computer4.x, devices.computer4.y, devices.computer4.name);
            drawDevice(devices.center.x, devices.center.y, '', 'hub');
            
            // 添加说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('集线器：所有端口共享带宽，形成一个冲突域', 400, 50);
            
            // 模拟数据传输冲突
            setTimeout(() => {
                animateHubCollision();
            }, 1000);
        }

        function showSwitch() {
            currentDemo = 'switch';
            clearCanvas();
            
            // 绘制交换机连接
            drawConnection(devices.computer1.x, devices.computer1.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer2.x, devices.computer2.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer3.x, devices.computer3.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer4.x, devices.computer4.y, devices.center.x, devices.center.y);
            
            // 绘制设备
            drawDevice(devices.computer1.x, devices.computer1.y, devices.computer1.name);
            drawDevice(devices.computer2.x, devices.computer2.y, devices.computer2.name);
            drawDevice(devices.computer3.x, devices.computer3.y, devices.computer3.name);
            drawDevice(devices.computer4.x, devices.computer4.y, devices.computer4.name);
            drawDevice(devices.center.x, devices.center.y, '', 'switch');
            
            // 添加说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('交换机：每个端口独立，支持并发传输', 400, 50);
            
            // 模拟智能转发
            setTimeout(() => {
                animateSwitchForwarding();
            }, 1000);
        }

        function showBridge() {
            currentDemo = 'bridge';
            clearCanvas();
            
            // 绘制网桥连接两个网段
            drawConnection(devices.computer1.x, devices.computer1.y, devices.computer2.x, devices.computer2.y);
            drawConnection(devices.computer1.x, devices.computer1.y, devices.center.x, devices.center.y);
            drawConnection(devices.computer2.x, devices.computer2.y, devices.center.x, devices.center.y);
            drawConnection(devices.center.x, devices.center.y, devices.computer3.x, devices.computer3.y);
            drawConnection(devices.center.x, devices.center.y, devices.computer4.x, devices.computer4.y);
            drawConnection(devices.computer3.x, devices.computer3.y, devices.computer4.x, devices.computer4.y);
            
            // 绘制设备
            drawDevice(devices.computer1.x, devices.computer1.y, devices.computer1.name);
            drawDevice(devices.computer2.x, devices.computer2.y, devices.computer2.name);
            drawDevice(devices.computer3.x, devices.computer3.y, devices.computer3.name);
            drawDevice(devices.computer4.x, devices.computer4.y, devices.computer4.name);
            drawDevice(devices.center.x, devices.center.y, '', 'bridge');
            
            // 添加说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('网桥：连接两个网段，学习MAC地址', 400, 50);
            
            // 绘制网段标识
            ctx.fillStyle = 'rgba(255, 107, 107, 0.2)';
            ctx.fillRect(50, 100, 150, 150);
            ctx.fillStyle = 'rgba(78, 205, 196, 0.2)';
            ctx.fillRect(600, 100, 150, 150);
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('网段1', 125, 120);
            ctx.fillText('网段2', 675, 120);
        }

        function animateHubCollision() {
            let frame = 0;
            const maxFrames = 120;
            
            function animate() {
                if (frame >= maxFrames) return;
                
                clearCanvas();
                showHub();
                
                // 模拟两个数据包同时发送导致冲突
                if (frame < 60) {
                    const progress = frame / 60;
                    const packet1X = devices.computer1.x + (devices.center.x - devices.computer1.x) * progress;
                    const packet1Y = devices.computer1.y + (devices.center.y - devices.computer1.y) * progress;
                    const packet2X = devices.computer2.x + (devices.center.x - devices.computer2.x) * progress;
                    const packet2Y = devices.computer2.y + (devices.center.y - devices.computer2.y) * progress;
                    
                    drawDataPacket(packet1X, packet1Y, '#ff6b6b');
                    drawDataPacket(packet2X, packet2Y, '#ff6b6b');
                    
                    if (frame > 50) {
                        // 显示冲突效果
                        ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
                        ctx.beginPath();
                        ctx.arc(devices.center.x, devices.center.y, 50, 0, Math.PI * 2);
                        ctx.fill();
                        
                        ctx.fillStyle = '#ff0000';
                        ctx.font = '20px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('冲突!', devices.center.x, devices.center.y - 60);
                    }
                } else {
                    // 显示冲突后的重传
                    ctx.fillStyle = '#ff0000';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('检测到冲突，等待随机时间后重传...', 400, 350);
                }
                
                frame++;
                requestAnimationFrame(animate);
            }
            
            animate();
        }

        function animateSwitchForwarding() {
            let frame = 0;
            const maxFrames = 120;
            
            function animate() {
                if (frame >= maxFrames) return;
                
                clearCanvas();
                showSwitch();
                
                // 模拟并发传输
                if (frame < 60) {
                    const progress = frame / 60;
                    
                    // PC1 到 PC3
                    const packet1X = devices.computer1.x + (devices.computer3.x - devices.computer1.x) * progress;
                    const packet1Y = devices.computer1.y + (devices.computer3.y - devices.computer1.y) * progress;
                    drawDataPacket(packet1X, packet1Y, '#4ecdc4');
                    
                    // PC2 到 PC4 (同时进行)
                    const packet2X = devices.computer2.x + (devices.computer4.x - devices.computer2.x) * progress;
                    const packet2Y = devices.computer2.y + (devices.computer4.y - devices.computer2.y) * progress;
                    drawDataPacket(packet2X, packet2Y, '#45b7d1');
                    
                    // 高亮活跃连接
                    if (progress < 0.5) {
                        drawConnection(devices.computer1.x, devices.computer1.y, devices.center.x, devices.center.y, true);
                        drawConnection(devices.computer2.x, devices.computer2.y, devices.center.x, devices.center.y, true);
                    } else {
                        drawConnection(devices.center.x, devices.center.y, devices.computer3.x, devices.computer3.y, true);
                        drawConnection(devices.center.x, devices.center.y, devices.computer4.x, devices.computer4.y, true);
                    }
                }
                
                // 显示MAC地址表学习
                if (frame > 30) {
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                    ctx.fillRect(500, 300, 280, 80);
                    ctx.strokeStyle = '#4ecdc4';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(500, 300, 280, 80);
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'left';
                    ctx.fillText('MAC地址表:', 510, 320);
                    ctx.fillText('PC1: 端口1', 510, 340);
                    ctx.fillText('PC2: 端口2', 510, 355);
                    ctx.fillText('PC3: 端口3', 510, 370);
                }
                
                frame++;
                requestAnimationFrame(animate);
            }
            
            animate();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function showAnswer() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                } else {
                    option.classList.add('wrong');
                }
            });
            
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('progressFill').style.width = '100%';
            
            // 添加答案解析动画
            setTimeout(() => {
                document.getElementById('explanation').scrollIntoView({ 
                    behavior: 'smooth' 
                });
            }, 500);
        }

        function startAnimation() {
            showSwitch();
        }

        function resetDemo() {
            clearCanvas();
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            document.getElementById('explanation').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            
            // 显示初始提示
            ctx.fillStyle = '#667eea';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习网络设备！', 400, 200);
        }

        // 初始化
        window.onload = function() {
            resetDemo();
        };

        // 选项点击事件
        document.getElementById('options').addEventListener('click', function(e) {
            if (e.target.classList.contains('option')) {
                const selectedAnswer = e.target.dataset.answer;
                if (selectedAnswer === 'B') {
                    e.target.classList.add('correct');
                    setTimeout(showAnswer, 1000);
                } else {
                    e.target.classList.add('wrong');
                    setTimeout(() => {
                        e.target.classList.remove('wrong');
                    }, 1000);
                }
            }
        });
    </script>
</body>
</html>
