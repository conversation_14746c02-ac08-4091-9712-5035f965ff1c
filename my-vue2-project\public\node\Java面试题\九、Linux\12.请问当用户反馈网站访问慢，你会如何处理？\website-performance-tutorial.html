<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站访问慢排查教程 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .step-container {
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        .step-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.6s ease;
            position: relative;
            overflow: hidden;
        }

        .step-card.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .step-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .step-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 20px;
            animation: pulse 2s infinite;
        }

        .step-title {
            font-size: 1.8rem;
            color: #333;
            font-weight: 600;
        }

        .step-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .solution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .solution-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .solution-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
        }

        .solution-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .floating-element {
            position: absolute;
            animation: bounce 3s infinite;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            white-space: nowrap;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 网站访问慢排查教程</h1>
            <p>通过交互式动画学习如何系统性地排查和解决网站性能问题</p>
            <div style="margin-top: 30px;">
                <button class="interactive-btn" onclick="startQuiz()">🧠 知识测试</button>
                <button class="interactive-btn" onclick="showLearningPath()">📚 学习路径</button>
                <button class="interactive-btn" onclick="showRealCases()">💼 真实案例</button>
            </div>
        </div>

        <div class="step-container">
            <!-- 步骤1：确定问题源头 -->
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-title">确定问题源头</div>
                </div>
                <div class="step-content">
                    <p>当接到用户反馈访问慢时，首先要确定是用户端还是服务端的问题。立即自己访问网站进行测试，如果自己访问正常，基本可以断定是用户端问题。</p>
                </div>
                <div class="demo-area">
                    <div class="canvas-container">
                        <canvas id="sourceCanvas" width="600" height="300"></canvas>
                    </div>
                    <button class="interactive-btn" onclick="startSourceDemo()">🔍 开始排查演示</button>
                    <button class="interactive-btn" onclick="resetSourceDemo()">🔄 重置</button>
                    <button class="interactive-btn" onclick="markStepComplete(1)" style="background: #4CAF50;">✓ 标记完成</button>
                </div>
            </div>

            <!-- 步骤2：浏览器调试分析 -->
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-title">浏览器调试分析</div>
                </div>
                <div class="step-content">
                    <p>如果确认是服务端问题，利用浏览器的开发者工具，分析网络请求，查看哪一项数据加载消耗时间过多，是图片、CSS、JS还是API请求慢。</p>
                </div>
                <div class="demo-area">
                    <div class="canvas-container">
                        <canvas id="debugCanvas" width="600" height="350"></canvas>
                    </div>
                    <button class="interactive-btn" onclick="startDebugDemo()">🛠️ 开始调试分析</button>
                    <button class="interactive-btn" onclick="resetDebugDemo()">🔄 重置</button>
                    <button class="interactive-btn" onclick="markStepComplete(2)" style="background: #4CAF50;">✓ 标记完成</button>
                </div>
            </div>

            <!-- 步骤3：服务器负载检查 -->
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-title">服务器负载检查</div>
                </div>
                <div class="step-content">
                    <p>检查服务器硬件资源使用情况，包括CPU、内存、网络带宽。如果使用云服务，可以通过云平台监控面板查看各项指标。</p>
                </div>
                <div class="demo-area">
                    <div class="canvas-container">
                        <canvas id="serverCanvas" width="600" height="300"></canvas>
                    </div>
                    <button class="interactive-btn" onclick="startServerDemo()">📊 查看服务器状态</button>
                    <button class="interactive-btn" onclick="toggleServerLoad()">⚡ 模拟高负载</button>
                    <button class="interactive-btn" onclick="markStepComplete(3)" style="background: #4CAF50;">✓ 标记完成</button>
                </div>
            </div>

            <!-- 步骤4：数据库性能分析 -->
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">4</div>
                    <div class="step-title">数据库性能分析</div>
                </div>
                <div class="step-content">
                    <p>如果硬件资源正常，需要查看数据库慢查询日志，分析是否有SQL语句执行缓慢导致网站响应慢。</p>
                </div>
                <div class="demo-area">
                    <div class="canvas-container">
                        <canvas id="databaseCanvas" width="600" height="300"></canvas>
                    </div>
                    <button class="interactive-btn" onclick="startDatabaseDemo()">🗄️ 分析数据库性能</button>
                    <button class="interactive-btn" onclick="showSlowQueries()">⚠️ 显示慢查询</button>
                    <button class="interactive-btn" onclick="markStepComplete(4)" style="background: #4CAF50;">✓ 标记完成</button>
                </div>
            </div>

            <!-- 步骤5：解决方案 -->
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">5</div>
                    <div class="step-title">解决方案</div>
                </div>
                <div class="step-content">
                    <p>根据排查结果，采用相应的解决方案来优化网站性能。</p>
                </div>
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="interactive-btn" onclick="markStepComplete(5)" style="background: #4CAF50;">✓ 完成所有学习</button>
                </div>
                <div class="solution-grid">
                    <div class="solution-card" onclick="showSolution('bandwidth')">
                        <div class="solution-icon">🌐</div>
                        <h3>带宽优化</h3>
                        <p>增加出口带宽</p>
                    </div>
                    <div class="solution-card" onclick="showSolution('sql')">
                        <div class="solution-icon">⚡</div>
                        <h3>SQL优化</h3>
                        <p>优化慢查询语句</p>
                    </div>
                    <div class="solution-card" onclick="showSolution('cache')">
                        <div class="solution-icon">💾</div>
                        <h3>缓存系统</h3>
                        <p>添加Redis缓存</p>
                    </div>
                    <div class="solution-card" onclick="showSolution('database')">
                        <div class="solution-icon">🔄</div>
                        <h3>数据库集群</h3>
                        <p>主从读写分离</p>
                    </div>
                    <div class="solution-card" onclick="showSolution('cdn')">
                        <div class="solution-icon">🚀</div>
                        <h3>CDN加速</h3>
                        <p>内容分发网络</p>
                    </div>
                    <div class="solution-card" onclick="showSolution('architecture')">
                        <div class="solution-icon">🏗️</div>
                        <h3>架构优化</h3>
                        <p>负载均衡集群</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let animationFrames = {};
        let learningProgress = {
            step1: false,
            step2: false,
            step3: false,
            step4: false,
            step5: false
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            observeSteps();
            loadProgress();
            createProgressTracker();
        });

        // 创建进度跟踪器
        function createProgressTracker() {
            const tracker = document.createElement('div');
            tracker.id = 'progressTracker';
            tracker.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 999;
                min-width: 200px;
                transition: all 0.3s ease;
            `;

            const totalSteps = 5;
            const completedSteps = Object.values(learningProgress).filter(Boolean).length;
            const progressPercentage = (completedSteps / totalSteps) * 100;

            tracker.innerHTML = `
                <h4 style="margin: 0 0 15px 0; color: #333; font-size: 14px;">学习进度</h4>
                <div style="background: #e0e0e0; height: 8px; border-radius: 4px; margin-bottom: 15px; overflow: hidden;">
                    <div style="background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; width: ${progressPercentage}%; transition: width 0.5s ease;"></div>
                </div>
                <div style="font-size: 12px; color: #666; margin-bottom: 15px;">${completedSteps}/${totalSteps} 步骤完成</div>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    ${Object.entries(learningProgress).map((([step, completed], index) => `
                        <div style="display: flex; align-items: center; font-size: 12px;">
                            <span style="
                                width: 16px;
                                height: 16px;
                                border-radius: 50%;
                                background: ${completed ? '#4CAF50' : '#e0e0e0'};
                                color: white;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 8px;
                                font-size: 10px;
                            ">${completed ? '✓' : index + 1}</span>
                            <span style="color: ${completed ? '#4CAF50' : '#666'};">
                                步骤${index + 1}
                            </span>
                        </div>
                    `)).join('')}
                </div>
                <button onclick="toggleProgressTracker()" style="
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 15px;
                    font-size: 12px;
                    cursor: pointer;
                    margin-top: 15px;
                    width: 100%;
                ">收起</button>
            `;

            document.body.appendChild(tracker);
        }

        // 切换进度跟踪器显示状态
        function toggleProgressTracker() {
            const tracker = document.getElementById('progressTracker');
            const isMinimized = tracker.style.width === '60px';

            if (isMinimized) {
                tracker.style.width = 'auto';
                tracker.style.height = 'auto';
                createProgressTracker();
            } else {
                tracker.style.width = '60px';
                tracker.style.height = '60px';
                tracker.innerHTML = `
                    <div onclick="toggleProgressTracker()" style="
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        font-size: 20px;
                        color: #667eea;
                    ">📊</div>
                `;
            }
        }

        // 标记步骤完成
        function markStepComplete(stepNumber) {
            learningProgress[`step${stepNumber}`] = true;
            saveProgress();
            updateProgressTracker();

            // 显示完成动画
            showCompletionAnimation(stepNumber);
        }

        // 显示完成动画
        function showCompletionAnimation(stepNumber) {
            const stepCard = document.querySelectorAll('.step-card')[stepNumber - 1];
            const celebration = document.createElement('div');
            celebration.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 3rem;
                z-index: 100;
                animation: celebrationBounce 1s ease-out;
                pointer-events: none;
            `;
            celebration.textContent = '🎉';

            stepCard.style.position = 'relative';
            stepCard.appendChild(celebration);

            setTimeout(() => {
                celebration.remove();
            }, 1000);

            // 添加CSS动画
            if (!document.getElementById('celebrationStyle')) {
                const style = document.createElement('style');
                style.id = 'celebrationStyle';
                style.textContent = `
                    @keyframes celebrationBounce {
                        0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
                        50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
                        100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // 更新进度跟踪器
        function updateProgressTracker() {
            const tracker = document.getElementById('progressTracker');
            if (tracker && tracker.style.width !== '60px') {
                createProgressTracker();
            }
        }

        // 保存进度到本地存储
        function saveProgress() {
            localStorage.setItem('websitePerformanceProgress', JSON.stringify(learningProgress));
        }

        // 加载进度从本地存储
        function loadProgress() {
            const saved = localStorage.getItem('websitePerformanceProgress');
            if (saved) {
                learningProgress = { ...learningProgress, ...JSON.parse(saved) };
            }
        }

        // 初始化所有动画
        function initializeAnimations() {
            initSourceCanvas();
            initDebugCanvas();
            initServerCanvas();
            initDatabaseCanvas();
        }

        // 观察步骤卡片进入视口
        function observeSteps() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.step-card').forEach(card => {
                observer.observe(card);
            });
        }

        // 步骤1：源头排查动画
        function initSourceCanvas() {
            const canvas = document.getElementById('sourceCanvas');
            const ctx = canvas.getContext('2d');
            
            function drawSourceDiagram() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制用户
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(100, 150, 30, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('👤', 100, 160);
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('用户反馈', 100, 200);
                ctx.fillText('访问慢', 100, 220);
                
                // 绘制管理员
                ctx.fillStyle = '#764ba2';
                ctx.beginPath();
                ctx.arc(500, 150, 30, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.fillText('👨‍💻', 500, 160);
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('管理员', 500, 200);
                ctx.fillText('立即测试', 500, 220);
                
                // 绘制箭头
                drawArrow(ctx, 150, 150, 450, 150, '#667eea');
                
                // 绘制网站
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(280, 50, 40, 60);
                ctx.fillStyle = '#333';
                ctx.font = '24px Arial';
                ctx.fillText('🌐', 300, 90);
                ctx.font = '12px Arial';
                ctx.fillText('网站', 300, 130);
            }
            
            drawSourceDiagram();
        }

        function startSourceDemo() {
            const canvas = document.getElementById('sourceCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 重绘基础图形
                initSourceCanvas();
                
                // 动画效果
                if (step < 50) {
                    // 用户请求动画
                    const progress = step / 50;
                    const x = 150 + (130 * progress);
                    ctx.fillStyle = '#FF6B6B';
                    ctx.beginPath();
                    ctx.arc(x, 140, 5, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('慢!', x, 130);
                } else if (step < 100) {
                    // 管理员测试动画
                    const progress = (step - 50) / 50;
                    const x = 450 - (130 * progress);
                    ctx.fillStyle = '#4ECDC4';
                    ctx.beginPath();
                    ctx.arc(x, 160, 5, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('测试', x, 150);
                } else {
                    // 显示结果
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 管理员访问正常', 300, 250);
                    ctx.fillText('→ 判断为用户端问题', 300, 270);
                }
                
                step++;
                if (step < 120) {
                    animationFrames.source = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function resetSourceDemo() {
            if (animationFrames.source) {
                cancelAnimationFrame(animationFrames.source);
            }
            initSourceCanvas();
        }

        // 步骤2：调试分析动画
        function initDebugCanvas() {
            const canvas = document.getElementById('debugCanvas');
            const ctx = canvas.getContext('2d');
            
            function drawDebugInterface() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制浏览器窗口
                ctx.fillStyle = '#f5f5f5';
                ctx.fillRect(50, 50, 500, 250);
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, 50, 500, 250);
                
                // 标题栏
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 50, 500, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('开发者工具 - Network', 60, 70);
                
                // 网络请求列表
                const requests = [
                    { name: 'index.html', time: 120, type: 'document' },
                    { name: 'style.css', time: 80, type: 'stylesheet' },
                    { name: 'script.js', time: 200, type: 'script' },
                    { name: 'image.jpg', time: 1500, type: 'image' },
                    { name: 'api/data', time: 3000, type: 'xhr' }
                ];
                
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                let y = 100;
                requests.forEach((req, index) => {
                    const color = req.time > 1000 ? '#F44336' : req.time > 500 ? '#FF9800' : '#4CAF50';
                    ctx.fillStyle = color;
                    ctx.fillRect(60, y, Math.min(req.time / 10, 400), 20);
                    
                    ctx.fillStyle = '#333';
                    ctx.fillText(req.name, 70, y + 14);
                    ctx.fillText(req.time + 'ms', 480, y + 14);
                    y += 30;
                });
            }
            
            drawDebugInterface();
        }

        function startDebugDemo() {
            const canvas = document.getElementById('debugCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function animate() {
                initDebugCanvas();
                
                // 高亮慢请求
                if (step > 30) {
                    ctx.strokeStyle = '#F44336';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(58, 158, 404, 24); // image.jpg
                    ctx.strokeRect(58, 188, 404, 24); // api/data
                    
                    ctx.fillStyle = '#F44336';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('发现慢请求！', 300, 330);
                }
                
                step++;
                if (step < 100) {
                    animationFrames.debug = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function resetDebugDemo() {
            if (animationFrames.debug) {
                cancelAnimationFrame(animationFrames.debug);
            }
            initDebugCanvas();
        }

        // 辅助函数：绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 步骤3：服务器监控动画
        function initServerCanvas() {
            const canvas = document.getElementById('serverCanvas');
            const ctx = canvas.getContext('2d');
            
            function drawServerMonitoring() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制服务器
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 100, 80, 100);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🖥️', 90, 160);
                
                // CPU使用率
                drawMetric(ctx, 200, 80, 'CPU', 45, '#4CAF50');
                // 内存使用率
                drawMetric(ctx, 350, 80, '内存', 70, '#FF9800');
                // 网络带宽
                drawMetric(ctx, 500, 80, '带宽', 30, '#4CAF50');
            }
            
            drawServerMonitoring();
        }

        function drawMetric(ctx, x, y, label, value, color) {
            // 绘制圆形进度条
            const radius = 40;
            const centerX = x;
            const centerY = y + radius;
            
            // 背景圆
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 进度圆
            ctx.strokeStyle = color;
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + (value / 100) * 2 * Math.PI);
            ctx.stroke();
            
            // 文字
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, centerX, centerY - 5);
            ctx.fillText(value + '%', centerX, centerY + 10);
        }

        function startServerDemo() {
            const canvas = document.getElementById('serverCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 动态更新指标
                const cpuValue = 45 + Math.sin(step * 0.1) * 10;
                const memValue = 70 + Math.sin(step * 0.15) * 15;
                const bandwidthValue = 30 + Math.sin(step * 0.08) * 20;
                
                // 绘制服务器
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 100, 80, 100);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🖥️', 90, 160);
                
                drawMetric(ctx, 200, 80, 'CPU', Math.round(cpuValue), cpuValue > 80 ? '#F44336' : cpuValue > 60 ? '#FF9800' : '#4CAF50');
                drawMetric(ctx, 350, 80, '内存', Math.round(memValue), memValue > 80 ? '#F44336' : memValue > 60 ? '#FF9800' : '#4CAF50');
                drawMetric(ctx, 500, 80, '带宽', Math.round(bandwidthValue), bandwidthValue > 80 ? '#F44336' : bandwidthValue > 60 ? '#FF9800' : '#4CAF50');
                
                step++;
                if (step < 200) {
                    animationFrames.server = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        let serverHighLoad = false;
        function toggleServerLoad() {
            serverHighLoad = !serverHighLoad;
            const canvas = document.getElementById('serverCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器
            ctx.fillStyle = serverHighLoad ? '#F44336' : '#667eea';
            ctx.fillRect(50, 100, 80, 100);
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🖥️', 90, 160);
            
            if (serverHighLoad) {
                drawMetric(ctx, 200, 80, 'CPU', 95, '#F44336');
                drawMetric(ctx, 350, 80, '内存', 88, '#F44336');
                drawMetric(ctx, 500, 80, '带宽', 92, '#F44336');
                
                ctx.fillStyle = '#F44336';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('⚠️ 服务器高负载！', 300, 250);
            } else {
                drawMetric(ctx, 200, 80, 'CPU', 45, '#4CAF50');
                drawMetric(ctx, 350, 80, '内存', 70, '#FF9800');
                drawMetric(ctx, 500, 80, '带宽', 30, '#4CAF50');
            }
        }

        // 步骤4：数据库性能动画
        function initDatabaseCanvas() {
            const canvas = document.getElementById('databaseCanvas');
            const ctx = canvas.getContext('2d');
            
            function drawDatabaseMonitoring() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数据库
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(50, 100, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🗄️', 100, 150);
                
                // 查询列表
                ctx.fillStyle = '#f5f5f5';
                ctx.fillRect(200, 50, 350, 200);
                ctx.strokeStyle = '#ddd';
                ctx.strokeRect(200, 50, 350, 200);
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('SQL查询监控', 210, 75);
                
                const queries = [
                    { sql: 'SELECT * FROM users WHERE...', time: 0.5 },
                    { sql: 'SELECT * FROM orders WHERE...', time: 1.2 },
                    { sql: 'SELECT * FROM products WHERE...', time: 0.3 },
                    { sql: 'SELECT * FROM logs WHERE...', time: 5.8 },
                    { sql: 'SELECT * FROM analytics WHERE...', time: 12.3 }
                ];
                
                let y = 100;
                queries.forEach(query => {
                    const color = query.time > 5 ? '#F44336' : query.time > 1 ? '#FF9800' : '#4CAF50';
                    ctx.fillStyle = color;
                    ctx.fillText(`${query.time}s`, 500, y);
                    
                    ctx.fillStyle = '#333';
                    ctx.fillText(query.sql, 210, y);
                    y += 25;
                });
            }
            
            drawDatabaseMonitoring();
        }

        function startDatabaseDemo() {
            const canvas = document.getElementById('databaseCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function animate() {
                initDatabaseCanvas();
                
                // 动画效果：查询执行
                if (step < 100) {
                    const progress = step / 100;
                    ctx.fillStyle = `rgba(102, 126, 234, ${0.3 + 0.7 * Math.sin(progress * Math.PI * 4)})`;
                    ctx.fillRect(50, 100, 100, 80);
                }
                
                step++;
                if (step < 100) {
                    animationFrames.database = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function showSlowQueries() {
            const canvas = document.getElementById('databaseCanvas');
            const ctx = canvas.getContext('2d');
            
            initDatabaseCanvas();
            
            // 高亮慢查询
            ctx.strokeStyle = '#F44336';
            ctx.lineWidth = 3;
            ctx.strokeRect(208, 170, 334, 22);
            ctx.strokeRect(208, 195, 334, 22);
            
            ctx.fillStyle = '#F44336';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('发现慢查询！需要优化', 375, 270);
        }

        // 解决方案展示
        function showSolution(type) {
            const solutions = {
                bandwidth: {
                    title: '🌐 带宽优化解决方案',
                    content: '增加服务器出口带宽，提升数据传输速度。联系云服务商升级带宽套餐。',
                    steps: ['检查当前带宽使用率', '评估业务需求', '联系服务商升级', '监控效果'],
                    canvas: 'bandwidthCanvas'
                },
                sql: {
                    title: '⚡ SQL优化解决方案',
                    content: '分析慢查询日志，添加索引，优化查询语句，避免全表扫描。',
                    steps: ['开启慢查询日志', '分析慢查询语句', '添加合适索引', '重写复杂查询'],
                    canvas: 'sqlCanvas'
                },
                cache: {
                    title: '💾 缓存系统解决方案',
                    content: '部署Redis缓存，缓存热点数据，减少数据库查询压力。',
                    steps: ['安装Redis服务', '识别热点数据', '实现缓存逻辑', '设置过期策略'],
                    canvas: 'cacheCanvas'
                },
                database: {
                    title: '🔄 数据库集群解决方案',
                    content: '搭建MySQL主从架构，实现读写分离，提升数据库并发处理能力。',
                    steps: ['配置主数据库', '设置从数据库', '实现读写分离', '监控同步状态'],
                    canvas: 'dbClusterCanvas'
                },
                cdn: {
                    title: '🚀 CDN加速解决方案',
                    content: '使用内容分发网络，将静态资源缓存到全球节点，加速用户访问。',
                    steps: ['选择CDN服务商', '配置源站', '设置缓存规则', '测试加速效果'],
                    canvas: 'cdnCanvas'
                },
                architecture: {
                    title: '🏗️ 架构优化解决方案',
                    content: '部署负载均衡器，多台服务器提供服务，实现高可用和高性能。',
                    steps: ['设计架构方案', '部署负载均衡', '配置服务器集群', '实现故障转移'],
                    canvas: 'archCanvas'
                }
            };

            const solution = solutions[type];
            showSolutionModal(solution);
        }

        // 显示解决方案模态框
        function showSolutionModal(solution) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 20px;
                padding: 40px;
                max-width: 800px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease;
            `;

            modalContent.innerHTML = `
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                ">×</button>

                <h2 style="color: #333; margin-bottom: 20px;">${solution.title}</h2>
                <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">${solution.content}</p>

                <h3 style="color: #333; margin-bottom: 15px;">实施步骤：</h3>
                <div class="steps-container" style="margin-bottom: 30px;">
                    ${solution.steps.map((step, index) => `
                        <div style="
                            display: flex;
                            align-items: center;
                            margin-bottom: 15px;
                            padding: 15px;
                            background: #f8f9fa;
                            border-radius: 10px;
                            border-left: 4px solid #667eea;
                        ">
                            <div style="
                                width: 30px;
                                height: 30px;
                                border-radius: 50%;
                                background: #667eea;
                                color: white;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 15px;
                                font-weight: bold;
                            ">${index + 1}</div>
                            <span style="color: #333; font-size: 14px;">${step}</span>
                        </div>
                    `).join('')}
                </div>

                <div style="text-align: center;">
                    <canvas id="${solution.canvas}" width="600" height="300" style="
                        border-radius: 10px;
                        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
                    "></canvas>
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            // 绘制解决方案图表
            setTimeout(() => {
                drawSolutionDiagram(solution.canvas, solution.title);
            }, 100);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 绘制解决方案图表
        function drawSolutionDiagram(canvasId, title) {
            const canvas = document.getElementById(canvasId);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (title.includes('带宽')) {
                drawBandwidthDiagram(ctx);
            } else if (title.includes('SQL')) {
                drawSQLDiagram(ctx);
            } else if (title.includes('缓存')) {
                drawCacheDiagram(ctx);
            } else if (title.includes('数据库')) {
                drawDatabaseClusterDiagram(ctx);
            } else if (title.includes('CDN')) {
                drawCDNDiagram(ctx);
            } else if (title.includes('架构')) {
                drawArchitectureDiagram(ctx);
            }
        }

        // 带宽优化图表
        function drawBandwidthDiagram(ctx) {
            // 绘制带宽使用对比
            ctx.fillStyle = '#F44336';
            ctx.fillRect(100, 100, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('优化前', 175, 140);
            ctx.fillText('10Mbps', 175, 160);
            ctx.fillText('拥堵', 175, 180);

            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(350, 100, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('优化后', 425, 140);
            ctx.fillText('100Mbps', 425, 160);
            ctx.fillText('流畅', 425, 180);

            // 箭头
            drawArrow(ctx, 270, 150, 330, 150, '#667eea');

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText('带宽升级效果对比', 300, 250);
        }

        // SQL优化图表
        function drawSQLDiagram(ctx) {
            // 查询时间对比
            const queries = ['用户查询', '订单查询', '商品查询'];
            const beforeTimes = [5.2, 8.1, 12.3];
            const afterTimes = [0.3, 0.8, 1.2];

            queries.forEach((query, index) => {
                const y = 80 + index * 60;

                // 优化前
                ctx.fillStyle = '#F44336';
                ctx.fillRect(50, y, beforeTimes[index] * 20, 20);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(query, 50, y - 5);
                ctx.fillText(beforeTimes[index] + 's', beforeTimes[index] * 20 + 60, y + 15);

                // 优化后
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(350, y, afterTimes[index] * 20, 20);
                ctx.fillText(afterTimes[index] + 's', 350 + afterTimes[index] * 20 + 10, y + 15);
            });

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('优化前', 150, 50);
            ctx.fillText('优化后', 400, 50);
            ctx.fillText('SQL查询性能对比', 300, 270);
        }

        // 缓存系统图表
        function drawCacheDiagram(ctx) {
            // 绘制缓存架构
            // 用户
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(100, 150, 30, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('👤', 100, 160);

            // Redis缓存
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(250, 120, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('Redis', 300, 140);
            ctx.fillText('缓存', 300, 160);

            // 数据库
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(450, 120, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('MySQL', 500, 140);
            ctx.fillText('数据库', 500, 160);

            // 连接线
            drawArrow(ctx, 130, 150, 240, 150, '#4CAF50');
            drawArrow(ctx, 360, 150, 440, 150, '#FF9800');

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('快速响应', 185, 135);
            ctx.fillText('缓存未命中', 400, 135);
            ctx.fillText('缓存架构示意图', 300, 250);
        }

        // 数据库集群图表
        function drawDatabaseClusterDiagram(ctx) {
            // 主数据库
            ctx.fillStyle = '#667eea';
            ctx.fillRect(100, 80, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主数据库', 140, 105);
            ctx.fillText('(写)', 140, 125);

            // 从数据库
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(250, 50, 80, 60);
            ctx.fillRect(250, 130, 80, 60);
            ctx.fillRect(250, 210, 80, 60);

            ctx.fillStyle = 'white';
            ctx.fillText('从数据库1', 290, 75);
            ctx.fillText('(读)', 290, 95);
            ctx.fillText('从数据库2', 290, 155);
            ctx.fillText('(读)', 290, 175);
            ctx.fillText('从数据库3', 290, 235);
            ctx.fillText('(读)', 290, 255);

            // 负载均衡器
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(450, 120, 80, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('负载均衡', 490, 145);
            ctx.fillText('器', 490, 165);

            // 连接线
            drawArrow(ctx, 180, 110, 240, 80, '#333');
            drawArrow(ctx, 180, 110, 240, 160, '#333');
            drawArrow(ctx, 180, 110, 240, 240, '#333');
            drawArrow(ctx, 340, 150, 440, 150, '#333');

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主从读写分离架构', 300, 290);
        }

        // CDN图表
        function drawCDNDiagram(ctx) {
            // 用户分布
            const users = [
                {x: 80, y: 100, label: '北京用户'},
                {x: 520, y: 100, label: '上海用户'},
                {x: 80, y: 200, label: '广州用户'},
                {x: 520, y: 200, label: '深圳用户'}
            ];

            users.forEach(user => {
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(user.x, user.y, 20, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('👤', user.x, user.y + 5);

                ctx.fillStyle = '#333';
                ctx.font = '10px Arial';
                ctx.fillText(user.label, user.x, user.y + 35);
            });

            // CDN节点
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(280, 130, 40, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CDN', 300, 155);

            // 源服务器
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(280, 220, 40, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('源站', 300, 245);

            // 连接线
            users.forEach(user => {
                drawArrow(ctx, user.x + 15, user.y, 275, 150, '#4CAF50');
            });
            drawArrow(ctx, 300, 180, 300, 210, '#FF9800');

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CDN内容分发网络', 300, 290);
        }

        // 架构优化图表
        function drawArchitectureDiagram(ctx) {
            // 负载均衡器
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(250, 50, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('负载均衡器', 300, 75);

            // 服务器集群
            const servers = [
                {x: 150, y: 150},
                {x: 250, y: 150},
                {x: 350, y: 150},
                {x: 450, y: 150}
            ];

            servers.forEach((server, index) => {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(server.x - 25, server.y - 20, 50, 40);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(`服务器${index + 1}`, server.x, server.y);

                // 连接到负载均衡器
                drawArrow(ctx, 300, 100, server.x, server.y - 30, '#333');
            });

            // 数据库
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(275, 220, 50, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据库', 300, 245);

            // 服务器到数据库的连接
            servers.forEach(server => {
                drawArrow(ctx, server.x, server.y + 30, 300, 210, '#999');
            });

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('高可用架构示意图', 300, 290);
        }

        // 添加知识点测试功能
        function startQuiz() {
            const questions = [
                {
                    question: "当用户反馈网站访问慢时，第一步应该做什么？",
                    options: ["立即检查服务器", "自己先测试访问", "重启服务器", "联系用户"],
                    correct: 1,
                    explanation: "首先要确定是用户端还是服务端问题，所以要自己先测试访问网站。"
                },
                {
                    question: "浏览器开发者工具主要用来分析什么？",
                    options: ["服务器性能", "网络请求和加载时间", "数据库查询", "用户行为"],
                    correct: 1,
                    explanation: "浏览器开发者工具的Network面板可以详细分析各种资源的加载时间。"
                },
                {
                    question: "以下哪个不是服务器性能监控的重要指标？",
                    options: ["CPU使用率", "内存使用率", "网络带宽", "用户满意度"],
                    correct: 3,
                    explanation: "用户满意度虽然重要，但不是服务器硬件性能的直接监控指标。"
                }
            ];

            showQuizModal(questions);
        }

        function showQuizModal(questions) {
            let currentQuestion = 0;
            let score = 0;

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const quizContent = document.createElement('div');
            quizContent.style.cssText = `
                background: white;
                border-radius: 20px;
                padding: 40px;
                max-width: 600px;
                width: 90%;
                text-align: center;
            `;

            function showQuestion() {
                const q = questions[currentQuestion];
                quizContent.innerHTML = `
                    <h2 style="color: #333; margin-bottom: 30px;">知识测试 ${currentQuestion + 1}/${questions.length}</h2>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 30px;">
                        <p style="font-size: 18px; color: #333; margin-bottom: 20px;">${q.question}</p>
                        ${q.options.map((option, index) => `
                            <button onclick="selectAnswer(${index})" style="
                                display: block;
                                width: 100%;
                                padding: 15px;
                                margin: 10px 0;
                                border: 2px solid #e0e0e0;
                                border-radius: 10px;
                                background: white;
                                cursor: pointer;
                                font-size: 16px;
                                transition: all 0.3s ease;
                            " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e0e0e0'">
                                ${String.fromCharCode(65 + index)}. ${option}
                            </button>
                        `).join('')}
                    </div>
                    <div style="color: #666;">
                        进度: ${currentQuestion + 1}/${questions.length} | 得分: ${score}/${questions.length}
                    </div>
                `;
            }

            window.selectAnswer = function(selectedIndex) {
                const q = questions[currentQuestion];
                const buttons = quizContent.querySelectorAll('button');

                buttons.forEach((btn, index) => {
                    if (index === q.correct) {
                        btn.style.background = '#4CAF50';
                        btn.style.color = 'white';
                        btn.style.borderColor = '#4CAF50';
                    } else if (index === selectedIndex && index !== q.correct) {
                        btn.style.background = '#F44336';
                        btn.style.color = 'white';
                        btn.style.borderColor = '#F44336';
                    }
                    btn.disabled = true;
                });

                if (selectedIndex === q.correct) {
                    score++;
                }

                // 显示解释
                setTimeout(() => {
                    const explanation = document.createElement('div');
                    explanation.style.cssText = `
                        background: #e3f2fd;
                        padding: 15px;
                        border-radius: 10px;
                        margin-top: 20px;
                        border-left: 4px solid #2196F3;
                    `;
                    explanation.innerHTML = `<p style="color: #333; margin: 0;"><strong>解释：</strong>${q.explanation}</p>`;
                    quizContent.appendChild(explanation);

                    const nextBtn = document.createElement('button');
                    nextBtn.textContent = currentQuestion === questions.length - 1 ? '查看结果' : '下一题';
                    nextBtn.style.cssText = `
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                        margin-top: 20px;
                    `;
                    nextBtn.onclick = () => {
                        currentQuestion++;
                        if (currentQuestion < questions.length) {
                            showQuestion();
                        } else {
                            showResults();
                        }
                    };
                    quizContent.appendChild(nextBtn);
                }, 1000);
            };

            function showResults() {
                const percentage = Math.round((score / questions.length) * 100);
                let message = '';
                let color = '';

                if (percentage >= 80) {
                    message = '优秀！您已经很好地掌握了网站性能排查知识！';
                    color = '#4CAF50';
                } else if (percentage >= 60) {
                    message = '良好！继续学习，您会掌握得更好！';
                    color = '#FF9800';
                } else {
                    message = '需要加强！建议重新学习相关内容。';
                    color = '#F44336';
                }

                quizContent.innerHTML = `
                    <h2 style="color: #333; margin-bottom: 30px;">测试结果</h2>
                    <div style="background: ${color}; color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px;">
                        <div style="font-size: 48px; margin-bottom: 10px;">${percentage}%</div>
                        <div style="font-size: 18px;">您答对了 ${score}/${questions.length} 题</div>
                    </div>
                    <p style="font-size: 16px; color: #333; margin-bottom: 30px;">${message}</p>
                    <button onclick="this.closest('.modal').remove()" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                        margin-right: 10px;
                    ">完成</button>
                    <button onclick="this.closest('.modal').remove(); startQuiz();" style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                    ">重新测试</button>
                `;
            }

            modal.className = 'modal';
            modal.appendChild(quizContent);
            document.body.appendChild(modal);

            showQuestion();

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 显示学习路径
        function showLearningPath() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                border-radius: 20px;
                padding: 40px;
                max-width: 800px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
            `;

            content.innerHTML = `
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                ">×</button>

                <h2 style="color: #333; margin-bottom: 30px; text-align: center;">📚 学习路径规划</h2>

                <div style="position: relative;">
                    <div style="position: absolute; left: 30px; top: 0; bottom: 0; width: 2px; background: #667eea;"></div>

                    <div style="margin-left: 60px;">
                        <div style="margin-bottom: 40px; position: relative;">
                            <div style="position: absolute; left: -45px; top: 10px; width: 20px; height: 20px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">1</div>
                            <h3 style="color: #333; margin-bottom: 10px;">基础概念理解</h3>
                            <p style="color: #666; margin-bottom: 15px;">学习网站性能的基本概念，了解影响网站速度的主要因素。</p>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                                <strong>学习内容：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li>网络延迟与带宽概念</li>
                                    <li>服务器响应时间</li>
                                    <li>前端渲染性能</li>
                                    <li>数据库查询效率</li>
                                </ul>
                                <strong>预计时间：</strong> 2-3小时
                            </div>
                        </div>

                        <div style="margin-bottom: 40px; position: relative;">
                            <div style="position: absolute; left: -45px; top: 10px; width: 20px; height: 20px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">2</div>
                            <h3 style="color: #333; margin-bottom: 10px;">排查工具掌握</h3>
                            <p style="color: #666; margin-bottom: 15px;">熟练使用各种性能分析工具，建立系统化的排查思路。</p>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                                <strong>学习内容：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li>浏览器开发者工具</li>
                                    <li>服务器监控工具</li>
                                    <li>数据库性能分析</li>
                                    <li>网络抓包分析</li>
                                </ul>
                                <strong>预计时间：</strong> 4-5小时
                            </div>
                        </div>

                        <div style="margin-bottom: 40px; position: relative;">
                            <div style="position: absolute; left: -45px; top: 10px; width: 20px; height: 20px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">3</div>
                            <h3 style="color: #333; margin-bottom: 10px;">实战案例练习</h3>
                            <p style="color: #666; margin-bottom: 15px;">通过真实案例练习，提升实际问题解决能力。</p>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                                <strong>学习内容：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li>电商网站慢查询优化</li>
                                    <li>新闻网站图片加载优化</li>
                                    <li>企业官网带宽瓶颈解决</li>
                                    <li>社交平台架构升级</li>
                                </ul>
                                <strong>预计时间：</strong> 6-8小时
                            </div>
                        </div>

                        <div style="margin-bottom: 40px; position: relative;">
                            <div style="position: absolute; left: -45px; top: 10px; width: 20px; height: 20px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">4</div>
                            <h3 style="color: #333; margin-bottom: 10px;">高级优化技术</h3>
                            <p style="color: #666; margin-bottom: 15px;">学习高级优化技术，成为性能优化专家。</p>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px;">
                                <strong>学习内容：</strong>
                                <ul style="margin: 10px 0; padding-left: 20px;">
                                    <li>CDN配置与优化</li>
                                    <li>负载均衡策略</li>
                                    <li>缓存系统设计</li>
                                    <li>微服务架构优化</li>
                                </ul>
                                <strong>预计时间：</strong> 8-10小时
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <p style="color: #666; margin-bottom: 20px;">总学习时间：约20-26小时</p>
                    <button onclick="this.closest('.modal').remove()" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                    ">开始学习</button>
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 显示真实案例
        function showRealCases() {
            const cases = [
                {
                    title: "电商网站订单查询慢",
                    problem: "用户反馈订单查询页面加载时间超过10秒",
                    analysis: "通过慢查询日志发现订单表缺少索引，查询时进行全表扫描",
                    solution: "为订单表的user_id和create_time字段添加复合索引",
                    result: "查询时间从10秒降低到0.3秒，用户体验显著提升",
                    icon: "🛒"
                },
                {
                    title: "新闻网站图片加载慢",
                    problem: "新闻详情页图片加载缓慢，影响用户阅读体验",
                    analysis: "发现图片文件过大，且未使用CDN加速",
                    solution: "压缩图片并启用CDN，实现图片懒加载",
                    result: "页面加载时间减少60%，跳出率降低25%",
                    icon: "📰"
                },
                {
                    title: "企业官网带宽瓶颈",
                    problem: "企业官网在高峰期访问缓慢，特别是视频内容",
                    analysis: "监控发现出口带宽在高峰期达到95%使用率",
                    solution: "升级带宽套餐并优化视频压缩算法",
                    result: "高峰期访问速度提升3倍，用户满意度大幅提升",
                    icon: "🏢"
                },
                {
                    title: "社交平台架构升级",
                    problem: "用户量增长导致单服务器无法承载，频繁宕机",
                    analysis: "单点故障风险高，需要实现负载均衡和高可用",
                    solution: "部署负载均衡器和服务器集群，实现数据库读写分离",
                    result: "系统可用性达到99.9%，支持10倍用户量增长",
                    icon: "👥"
                }
            ];

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                border-radius: 20px;
                padding: 40px;
                max-width: 900px;
                width: 90%;
                max-height: 80%;
                overflow-y: auto;
            `;

            content.innerHTML = `
                <button onclick="this.closest('.modal').remove()" style="
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                ">×</button>

                <h2 style="color: #333; margin-bottom: 30px; text-align: center;">💼 真实案例分析</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px;">
                    ${cases.map(caseItem => `
                        <div style="
                            border: 2px solid #e0e0e0;
                            border-radius: 15px;
                            padding: 25px;
                            transition: all 0.3s ease;
                            cursor: pointer;
                        " onmouseover="this.style.borderColor='#667eea'; this.style.transform='translateY(-5px)'"
                           onmouseout="this.style.borderColor='#e0e0e0'; this.style.transform='translateY(0)'">

                            <div style="text-align: center; margin-bottom: 20px;">
                                <div style="font-size: 3rem; margin-bottom: 10px;">${caseItem.icon}</div>
                                <h3 style="color: #333; margin: 0;">${caseItem.title}</h3>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <strong style="color: #F44336;">问题：</strong>
                                <p style="color: #666; margin: 5px 0;">${caseItem.problem}</p>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <strong style="color: #FF9800;">分析：</strong>
                                <p style="color: #666; margin: 5px 0;">${caseItem.analysis}</p>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <strong style="color: #2196F3;">解决：</strong>
                                <p style="color: #666; margin: 5px 0;">${caseItem.solution}</p>
                            </div>

                            <div>
                                <strong style="color: #4CAF50;">结果：</strong>
                                <p style="color: #666; margin: 5px 0;">${caseItem.result}</p>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <p style="color: #666; margin-bottom: 20px;">这些真实案例展示了系统化排查方法的重要性</p>
                    <button onclick="this.closest('.modal').remove()" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 25px;
                        font-size: 16px;
                        cursor: pointer;
                    ">继续学习</button>
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
    </script>
</body>
</html>
