<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java中==和equals的区别</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h1, h2 {
            color: #0066cc;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .tab-container {
            display: flex;
            border-bottom: 2px solid #0066cc;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #e6f0ff;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #0066cc;
            color: white;
        }
        .content {
            display: none;
        }
        .content.active {
            display: block;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ddd;
        }
        .memory-container, .hashset-container {
            margin: 20px auto;
            position: relative;
        }
        .button-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0052a3;
        }
        .code {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .explanation {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            padding: 10px;
            margin: 10px 0;
        }
        .highlight {
            color: #d32f2f;
            font-weight: bold;
        }
        .reference-box {
            border: 1px dashed #ccc;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }
    </style>
</head>
<body>
    <h1>Java中==和equals的区别</h1>
    
    <div class="container">
        <div class="tab-container">
            <div class="tab active" onclick="switchTab('basics')">基本概念</div>
            <div class="tab" onclick="switchTab('comparison')">对象比较</div>
            <div class="tab" onclick="switchTab('hashcode')">hashCode</div>
            <div class="tab" onclick="switchTab('practice')">实践例子</div>
        </div>
        
        <div id="basics" class="content active">
            <h2>基本概念</h2>
            <div class="explanation">
                <p><span class="highlight">==</span>：对比<span class="highlight">值</span>或<span class="highlight">内存地址</span>：</p>
                <ul>
                    <li>对基本类型：比较<span class="highlight">值</span>是否相等</li>
                    <li>对引用类型：比较<span class="highlight">内存地址</span>是否相等</li>
                </ul>
                <p><span class="highlight">equals()</span>：</p>
                <ul>
                    <li>只用于<span class="highlight">对象</span>比较，不能用于基本类型</li>
                    <li>默认实现与<span class="highlight">==</span>相同（比较内存地址）</li>
                    <li>很多类（如String）<span class="highlight">重写</span>了equals方法，用于比较对象的内容</li>
                </ul>
            </div>
            
            <div class="memory-container">
                <canvas id="basicCanvas" width="700" height="300"></canvas>
            </div>
            
            <div class="button-container">
                <button onclick="showBasicTypes()">演示基本类型比较</button>
                <button onclick="showStringComparison()">演示String比较</button>
                <button onclick="showObjectComparison()">演示Object比较</button>
            </div>
        </div>
        
        <div id="comparison" class="content">
            <h2>对象比较可视化</h2>
            
            <div class="reference-box">
                <div class="code">String a = new String("ab"); // 创建新对象
String b = new String("ab"); // 创建另一个新对象
String aa = "ab";          // 字符串常量池
String bb = "ab";          // 从常量池中获取</div>
            </div>
            
            <canvas id="comparisonCanvas" width="700" height="400"></canvas>
            
            <div class="button-container">
                <button onclick="runComparison('equals')">比较 a.equals(b)</button>
                <button onclick="runComparison('equalsAA')">比较 aa.equals(bb)</button>
                <button onclick="runComparison('doubleEquals')">比较 a == b</button>
                <button onclick="runComparison('doubleEqualsAA')">比较 aa == bb</button>
            </div>
            
            <div id="comparisonResult" class="explanation">
                点击按钮查看比较结果
            </div>
        </div>
        
        <div id="hashcode" class="content">
            <h2>hashCode的作用</h2>
            <div class="explanation">
                <p><span class="highlight">hashCode()</span>：返回对象的哈希码（整数）</p>
                <ul>
                    <li>用于确定对象在哈希表中的位置</li>
                    <li>相同的对象必须有相同的hashCode</li>
                    <li>不同对象可能有相同的hashCode（哈希碰撞）</li>
                    <li>重写equals()时必须重写hashCode()</li>
                </ul>
            </div>
            
            <canvas id="hashCodeCanvas" width="700" height="400"></canvas>
            
            <div class="button-container">
                <button onclick="demonstrateHashCode()">演示HashSet工作原理</button>
                <button onclick="resetHashDemo()">重置演示</button>
            </div>
        </div>
        
        <div id="practice" class="content">
            <h2>实践例子</h2>
            
            <div class="explanation">
                <p>试试下面的例子：</p>
            </div>
            
            <div class="code">// 创建两个字符串
String s1 = new String("hello");
String s2 = new String("hello");

// 创建相同内容的两个自定义对象
MyClass obj1 = new MyClass("test", 100);
MyClass obj2 = new MyClass("test", 100);</div>
            
            <div class="button-container">
                <button onclick="runExample('stringEquals')">s1.equals(s2)</button>
                <button onclick="runExample('stringEqualOp')">s1 == s2</button>
                <button onclick="runExample('objectEquals')">obj1.equals(obj2)</button>
                <button onclick="runExample('objectEqualOp')">obj1 == obj2</button>
                <button onclick="runExample('fixedEquals')">使用正确重写</button>
            </div>
            
            <div id="exampleResult" class="explanation">
                点击按钮查看结果
            </div>
        </div>
    </div>
    
    <script>
        // 切换标签
        function switchTab(tabId) {
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.classList.remove('active'));
            
            document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }
        
        // 基本概念演示
        const basicCanvas = document.getElementById('basicCanvas');
        const basicCtx = basicCanvas.getContext('2d');
        
        function clearBasicCanvas() {
            basicCtx.clearRect(0, 0, basicCanvas.width, basicCanvas.height);
        }
        
        function showBasicTypes() {
            clearBasicCanvas();
            
            basicCtx.font = '16px Arial';
            basicCtx.fillStyle = '#333';
            
            // 绘制两个基本类型变量
            basicCtx.fillStyle = '#4CAF50';
            basicCtx.fillRect(50, 50, 120, 80);
            basicCtx.fillRect(250, 50, 120, 80);
            
            basicCtx.fillStyle = 'white';
            basicCtx.fillText('int a = 42', 60, 90);
            basicCtx.fillText('int b = 42', 260, 90);
            
            // 绘制箭头表示比较
            basicCtx.fillStyle = '#333';
            basicCtx.fillText('a == b', 185, 150);
            
            // 绘制结果
            basicCtx.fillStyle = '#0066cc';
            basicCtx.fillText('结果: true (比较值)', 300, 200);
            basicCtx.fillText('对基本类型，== 比较的是它们的值', 200, 250);
        }
        
        function showStringComparison() {
            clearBasicCanvas();
            
            // 绘制堆内存
            basicCtx.fillStyle = '#FFE0B2';
            basicCtx.fillRect(400, 30, 250, 230);
            basicCtx.fillStyle = '#333';
            basicCtx.fillText('堆内存', 500, 25);
            
            // 绘制String对象
            basicCtx.fillStyle = '#FF9800';
            basicCtx.fillRect(430, 50, 180, 60);
            basicCtx.fillRect(430, 150, 180, 60);
            
            basicCtx.fillStyle = 'black';
            basicCtx.fillText('String对象 "hello"', 460, 80);
            basicCtx.fillText('String对象 "hello"', 460, 180);
            
            // 绘制引用变量
            basicCtx.fillStyle = '#4CAF50';
            basicCtx.fillRect(50, 50, 120, 60);
            basicCtx.fillRect(50, 150, 120, 60);
            
            basicCtx.fillStyle = 'white';
            basicCtx.fillText('s1', 95, 80);
            basicCtx.fillText('s2', 95, 180);
            
            // 绘制引用箭头
            basicCtx.beginPath();
            basicCtx.moveTo(170, 70);
            basicCtx.lineTo(430, 70);
            basicCtx.stroke();
            
            basicCtx.beginPath();
            basicCtx.moveTo(170, 170);
            basicCtx.lineTo(430, 170);
            basicCtx.stroke();
            
            // 绘制比较
            basicCtx.fillStyle = '#333';
            basicCtx.fillText('s1 == s2: false (比较引用地址)', 200, 240);
            basicCtx.fillText('s1.equals(s2): true (比较字符串内容)', 200, 270);
        }
        
        function showObjectComparison() {
            clearBasicCanvas();
            
            // 绘制堆内存
            basicCtx.fillStyle = '#FFE0B2';
            basicCtx.fillRect(400, 30, 250, 230);
            basicCtx.fillStyle = '#333';
            basicCtx.fillText('堆内存', 500, 25);
            
            // 绘制Object对象
            basicCtx.fillStyle = '#2196F3';
            basicCtx.fillRect(430, 50, 180, 60);
            basicCtx.fillRect(430, 150, 180, 60);
            
            basicCtx.fillStyle = 'white';
            basicCtx.fillText('Person对象', 480, 80);
            basicCtx.fillText('Person对象', 480, 180);
            
            // 绘制引用变量
            basicCtx.fillStyle = '#4CAF50';
            basicCtx.fillRect(50, 50, 120, 60);
            basicCtx.fillRect(50, 150, 120, 60);
            
            basicCtx.fillStyle = 'white';
            basicCtx.fillText('p1', 95, 80);
            basicCtx.fillText('p2', 95, 180);
            
            // 绘制引用箭头
            basicCtx.beginPath();
            basicCtx.moveTo(170, 70);
            basicCtx.lineTo(430, 70);
            basicCtx.stroke();
            
            basicCtx.beginPath();
            basicCtx.moveTo(170, 170);
            basicCtx.lineTo(430, 170);
            basicCtx.stroke();
            
            // 绘制比较
            basicCtx.fillStyle = '#333';
            basicCtx.fillText('p1 == p2: false (比较引用地址)', 200, 240);
            basicCtx.fillText('p1.equals(p2): false (未重写equals时，同样比较引用)', 200, 270);
        }
        
        // 对象比较演示
        const comparisonCanvas = document.getElementById('comparisonCanvas');
        const comparisonCtx = comparisonCanvas.getContext('2d');
        
        function clearComparisonCanvas() {
            comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);
        }
        
        function drawMemoryModel() {
            clearComparisonCanvas();
            
            // 绘制堆内存
            comparisonCtx.fillStyle = '#FFE0B2';
            comparisonCtx.fillRect(300, 30, 350, 280);
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.fillText('堆内存', 450, 25);
            
            // 绘制字符串常量池
            comparisonCtx.fillStyle = '#E1F5FE';
            comparisonCtx.fillRect(350, 190, 250, 100);
            comparisonCtx.fillStyle = '#333';
            comparisonCtx.fillText('字符串常量池', 425, 185);
            
            // 绘制String对象
            comparisonCtx.fillStyle = '#FF9800';
            comparisonCtx.fillRect(330, 50, 130, 50);
            comparisonCtx.fillRect(490, 50, 130, 50);
            
            // 常量池中的字符串
            comparisonCtx.fillStyle = '#29B6F6';
            comparisonCtx.fillRect(400, 220, 150, 40);
            
            comparisonCtx.fillStyle = 'black';
            comparisonCtx.fillText('"ab" 内容', 350, 80);
            comparisonCtx.fillText('"ab" 内容', 510, 80);
            comparisonCtx.fillText('"ab" 内容', 435, 245);
            
            // 绘制引用变量
            comparisonCtx.fillStyle = '#4CAF50';
            comparisonCtx.fillRect(50, 50, 100, 50);
            comparisonCtx.fillRect(50, 120, 100, 50);
            comparisonCtx.fillRect(50, 220, 100, 50);
            comparisonCtx.fillRect(50, 290, 100, 50);
            
            comparisonCtx.fillStyle = 'white';
            comparisonCtx.fillText('a', 95, 80);
            comparisonCtx.fillText('b', 95, 150);
            comparisonCtx.fillText('aa', 95, 250);
            comparisonCtx.fillText('bb', 95, 320);
            
            // 绘制引用箭头
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(150, 70);
            comparisonCtx.lineTo(330, 70);
            comparisonCtx.stroke();
            
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(150, 140);
            comparisonCtx.lineTo(490, 70);
            comparisonCtx.stroke();
            
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(150, 240);
            comparisonCtx.lineTo(400, 240);
            comparisonCtx.stroke();
            
            comparisonCtx.beginPath();
            comparisonCtx.moveTo(150, 310);
            comparisonCtx.lineTo(400, 240);
            comparisonCtx.stroke();
        }
        
        function runComparison(type) {
            drawMemoryModel();
            
            const resultDiv = document.getElementById('comparisonResult');
            
            if (type === 'equals') {
                // 高亮两个对象
                comparisonCtx.strokeStyle = '#FF0000';
                comparisonCtx.lineWidth = 2;
                comparisonCtx.strokeRect(330, 50, 130, 50);
                comparisonCtx.strokeRect(490, 50, 130, 50);
                
                resultDiv.innerHTML = '<p><b>a.equals(b)</b> 返回 <span class="highlight">true</span></p>' +
                                      '<p>因为String类重写了equals方法，比较的是字符串内容而不是引用地址。</p>' +
                                      '<p>两个字符串内容都是"ab"，所以结果为true。</p>';
            } else if (type === 'equalsAA') {
                comparisonCtx.strokeStyle = '#FF0000';
                comparisonCtx.lineWidth = 2;
                comparisonCtx.strokeRect(400, 220, 150, 40);
                
                resultDiv.innerHTML = '<p><b>aa.equals(bb)</b> 返回 <span class="highlight">true</span></p>' +
                                      '<p>因为String类重写了equals方法，比较的是字符串内容。</p>' +
                                      '<p>两个引用指向的是同一个常量池中的字符串对象，内容相同，所以结果为true。</p>';
            } else if (type === 'doubleEquals') {
                comparisonCtx.strokeStyle = '#FF0000';
                comparisonCtx.lineWidth = 2;
                comparisonCtx.strokeRect(50, 50, 100, 50);
                comparisonCtx.strokeRect(50, 120, 100, 50);
                
                // 绘制不同的内存地址
                comparisonCtx.fillStyle = 'red';
                comparisonCtx.fillText('内存地址: 0x1234', 330, 120);
                comparisonCtx.fillText('内存地址: 0x5678', 490, 120);
                
                resultDiv.innerHTML = '<p><b>a == b</b> 返回 <span class="highlight">false</span></p>' +
                                      '<p>因为==对引用类型比较的是内存地址。</p>' +
                                      '<p>a和b是两个不同的对象实例，内存地址不同，所以结果为false。</p>';
            } else if (type === 'doubleEqualsAA') {
                comparisonCtx.strokeStyle = '#FF0000';
                comparisonCtx.lineWidth = 2;
                comparisonCtx.strokeRect(50, 220, 100, 50);
                comparisonCtx.strokeRect(50, 290, 100, 50);
                
                // 绘制相同的内存地址
                comparisonCtx.fillStyle = 'red';
                comparisonCtx.fillText('内存地址: 0x9ABC', 400, 280);
                
                resultDiv.innerHTML = '<p><b>aa == bb</b> 返回 <span class="highlight">true</span></p>' +
                                      '<p>因为字符串字面量"ab"是存储在常量池中的。</p>' +
                                      '<p>aa和bb引用指向的是同一个常量池中的对象，内存地址相同，所以结果为true。</p>';
            }
        }
        
        // HashCode演示
        const hashCodeCanvas = document.getElementById('hashCodeCanvas');
        const hashCtx = hashCodeCanvas.getContext('2d');
        
        let hashObjects = [];
        let hashAnimation = null;
        
        function clearHashCanvas() {
            hashCtx.clearRect(0, 0, hashCodeCanvas.width, hashCodeCanvas.height);
        }
        
        function drawHashSet() {
            clearHashCanvas();
            
            // 绘制HashSet
            hashCtx.fillStyle = '#E8EAF6';
            hashCtx.fillRect(50, 50, 600, 300);
            
            hashCtx.fillStyle = '#333';
            hashCtx.font = '16px Arial';
            hashCtx.fillText('HashSet', 350, 40);
            
            // 绘制哈希桶
            for (let i = 0; i < 5; i++) {
                hashCtx.fillStyle = '#C5CAE9';
                hashCtx.fillRect(100, 70 + i * 50, 500, 40);
                
                hashCtx.fillStyle = '#333';
                hashCtx.fillText(`哈希桶 ${i}`, 110, 95 + i * 50);
            }
            
            // 绘制已添加的对象
            hashObjects.forEach(obj => {
                hashCtx.fillStyle = obj.color;
                hashCtx.fillRect(obj.x, obj.y, 80, 30);
                
                hashCtx.fillStyle = 'white';
                hashCtx.fillText(obj.value, obj.x + 10, obj.y + 20);
            });
        }
        
        function resetHashDemo() {
            if (hashAnimation) {
                cancelAnimationFrame(hashAnimation);
            }
            hashObjects = [];
            drawHashSet();
        }
        
        function demonstrateHashCode() {
            resetHashDemo();
            
            const steps = [
                // 添加第一个对象
                () => {
                    const obj = {
                        value: 'Object A',
                        hashCode: 2,
                        x: 300,
                        y: 20,
                        targetY: 90,
                        color: '#3F51B5'
                    };
                    animateObjectToHashSet(obj);
                },
                // 添加第二个对象（不同哈希码）
                () => {
                    const obj = {
                        value: 'Object B',
                        hashCode: 4,
                        x: 300,
                        y: 20,
                        targetY: 190,
                        color: '#009688'
                    };
                    animateObjectToHashSet(obj);
                },
                // 添加第三个对象（相同哈希码，但不相等）
                () => {
                    const obj = {
                        value: 'Object C',
                        hashCode: 2,
                        x: 300,
                        y: 20,
                        targetY: 90,
                        targetX: 380,
                        color: '#E91E63'
                    };
                    animateObjectToHashSet(obj);
                },
                // 添加第四个对象（与Object A相同）
                () => {
                    const obj = {
                        value: 'Object A',
                        hashCode: 2,
                        x: 300,
                        y: 20,
                        targetY: 90,
                        willReject: true,
                        color: '#3F51B5'
                    };
                    animateObjectToHashSet(obj);
                }
            ];
            
            // 执行演示步骤
            let stepIndex = 0;
            
            function runNextStep() {
                if (stepIndex < steps.length) {
                    steps[stepIndex]();
                    stepIndex++;
                    setTimeout(runNextStep, 2500);
                }
            }
            
            runNextStep();
        }
        
        function animateObjectToHashSet(obj) {
            const startTime = performance.now();
            const duration = 1500;
            
            obj.targetX = obj.targetX || 300;
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                drawHashSet();
                
                // 绘制新对象
                const currentY = obj.y + (obj.targetY - obj.y) * progress;
                
                hashCtx.fillStyle = obj.color;
                hashCtx.fillRect(obj.x, currentY, 80, 30);
                
                hashCtx.fillStyle = 'white';
                hashCtx.fillText(obj.value, obj.x + 10, currentY + 20);
                
                // 绘制哈希码
                hashCtx.fillStyle = '#333';
                hashCtx.fillText(`hashCode: ${obj.hashCode}`, obj.x + 90, currentY + 20);
                
                if (progress === 1) {
                    // 检查是否要拒绝这个对象
                    if (obj.willReject) {
                        hashCtx.fillStyle = 'red';
                        hashCtx.fillText('拒绝! equals()返回true', obj.x, obj.targetY - 10);
                        
                        // 高亮现有对象
                        const existingObj = hashObjects.find(o => o.value === obj.value && o.hashCode === obj.hashCode);
                        if (existingObj) {
                            hashCtx.strokeStyle = 'red';
                            hashCtx.lineWidth = 2;
                            hashCtx.strokeRect(existingObj.x, existingObj.y, 80, 30);
                        }
                    } else {
                        // 添加到哈希集
                        hashObjects.push({
                            value: obj.value,
                            hashCode: obj.hashCode,
                            x: obj.targetX,
                            y: obj.targetY,
                            color: obj.color
                        });
                        
                        hashCtx.fillStyle = '#333';
                        if (obj.targetX !== 300) {
                            hashCtx.fillText('哈希冲突 - 使用equals()检查', 380, obj.targetY - 10);
                        }
                    }
                    return;
                }
                
                hashAnimation = requestAnimationFrame(animate);
            }
            
            hashAnimation = requestAnimationFrame(animate);
        }
        
        // 实践示例
        function runExample(type) {
            const resultDiv = document.getElementById('exampleResult');
            
            if (type === 'stringEquals') {
                resultDiv.innerHTML = '<p><b>s1.equals(s2)</b> 返回 <span class="highlight">true</span></p>' +
                                      '<p>因为String类重写了equals方法，比较的是字符串内容而不是引用。</p>';
            } else if (type === 'stringEqualOp') {
                resultDiv.innerHTML = '<p><b>s1 == s2</b> 返回 <span class="highlight">false</span></p>' +
                                      '<p>因为s1和s2是两个不同的String对象，它们的内存地址不同。</p>';
            } else if (type === 'objectEquals') {
                resultDiv.innerHTML = '<p><b>obj1.equals(obj2)</b> 返回 <span class="highlight">false</span></p>' +
                                      '<p>因为MyClass未重写equals方法，使用的是Object默认的equals实现，比较的是引用地址。</p>';
            } else if (type === 'objectEqualOp') {
                resultDiv.innerHTML = '<p><b>obj1 == obj2</b> 返回 <span class="highlight">false</span></p>' +
                                      '<p>因为obj1和obj2是两个不同的对象实例，它们的内存地址不同。</p>';
            } else if (type === 'fixedEquals') {
                resultDiv.innerHTML = 
                `<p><b>正确重写equals和hashCode</b></p>
                <div class="code">@Override
public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null || getClass() != obj.getClass()) return false;
    MyClass other = (MyClass) obj;
    return this.value == other.value && 
           Objects.equals(this.name, other.name);
}

@Override
public int hashCode() {
    return Objects.hash(name, value);
}</div>
                <p>重写后，<span class="highlight">obj1.equals(obj2)</span> 返回 <span class="highlight">true</span></p>
                <p>重要规则：重写equals时必须重写hashCode，确保相等对象具有相等的哈希码！</p>`;
            }
        }
        
        // 初始化页面
        window.onload = function() {
            showBasicTypes();
            drawMemoryModel();
            drawHashSet();
        };
    </script>
</body>
</html> 