<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>财务系统方案对比趣味学习</title>
  <style>
    body {
      background: #f8f9fa;
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      color: #222;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .container {
      max-width: 700px;
      margin: 40px auto 0 auto;
      background: #fff;
      border-radius: 18px;
      box-shadow: 0 4px 32px rgba(0,0,0,0.07);
      padding: 40px 32px 32px 32px;
      display: flex;
      flex-direction: column;
      gap: 32px;
    }
    h1 {
      font-size: 2.2rem;
      font-weight: 700;
      margin: 0 0 12px 0;
      letter-spacing: 2px;
    }
    .question {
      font-size: 1.1rem;
      line-height: 1.7;
      background: #f3f6fa;
      border-radius: 10px;
      padding: 18px 20px;
      margin-bottom: 0;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .options {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 12px;
    }
    .option-btn {
      background: #f8f9fa;
      border: 1.5px solid #e0e3e8;
      border-radius: 8px;
      padding: 14px 18px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.18s cubic-bezier(.4,0,.2,1);
      outline: none;
      text-align: left;
      position: relative;
      overflow: hidden;
    }
    .option-btn.selected {
      border-color: #007aff;
      background: #eaf4ff;
      color: #007aff;
    }
    .option-btn.correct {
      border-color: #2ecc40;
      background: #eaffea;
      color: #2ecc40;
    }
    .option-btn.wrong {
      border-color: #ff4d4f;
      background: #fff0f0;
      color: #ff4d4f;
    }
    .explain {
      background: #f6f7fb;
      border-left: 4px solid #007aff;
      padding: 18px 20px;
      border-radius: 0 10px 10px 0;
      font-size: 1rem;
      margin-top: 18px;
      animation: fadeIn 0.7s;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .canvas-demo {
      width: 100%;
      height: 220px;
      background: #f3f6fa;
      border-radius: 12px;
      margin: 0 auto 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .canvas-btns {
      display: flex;
      gap: 12px;
      margin-top: 10px;
      justify-content: center;
    }
    .canvas-btn {
      background: #fff;
      border: 1.5px solid #e0e3e8;
      border-radius: 8px;
      padding: 7px 18px;
      font-size: 0.98rem;
      cursor: pointer;
      transition: all 0.18s cubic-bezier(.4,0,.2,1);
      outline: none;
    }
    .canvas-btn.active {
      border-color: #007aff;
      background: #eaf4ff;
      color: #007aff;
    }
    .game-section {
      margin-top: 30px;
      background: #f3f6fa;
      border-radius: 12px;
      padding: 24px 18px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      text-align: center;
    }
    .game-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .game-canvas {
      width: 100%;
      height: 180px;
      background: #fff;
      border-radius: 10px;
      margin: 0 auto 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .game-btn {
      margin-top: 14px;
      background: #007aff;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 8px 22px;
      font-size: 1rem;
      cursor: pointer;
      transition: background 0.18s;
    }
    .game-btn:active {
      background: #005bb5;
    }
    .footer {
      margin: 40px 0 10px 0;
      color: #aaa;
      font-size: 0.95rem;
      text-align: center;
    }
    @media (max-width: 800px) {
      .container { padding: 18px 4vw; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>财务系统方案对比趣味学习</h1>
    <div class="question" id="question-block">
      <b>题目：</b><br>
      某高校欲构建财务系统，使得用户可通过校园网访问该系统。根据需求，公司给出如下2套方案：<br><br>
      <b>方案一：</b><br>
      1) 出口设备采用一台配置防火墙板卡的核心交换机，并且使用防火墙策略将需要对校园网做应用的服务器进行地址映射；<br>
      2) 采用4台高性能服务器实现整体架构，其中3台作为财务应用服务器、1台作为数据备份管理服务器；<br>
      3) 通过备份管理软件的备份策略将3台财务应用服务器的数据进行定期备份。<br><br>
      <b>方案二：</b><br>
      1) 出口设备采用一台配置防火墙板卡的核心交换机，并且使用防火墙策略将需要对校园网做应用的服务器进行地址映射；<br>
      2) 采用2台高性能服务器实现整体架构，服务器采用虚拟化技术，建多个虚拟机满足财务系统业务需求。当一台服务器出现物理故障时将业务迁移到另外一台物理服务器上。<br><br>
      <b>与方案一相比，方案二的优点是（ ）。</b><br>
      <b>方案二还有一些缺点，下列不属于其缺点的是（ ）。</b>
    </div>
    <div class="options" id="options-block">
      <button class="option-btn" data-value="A">A 网络的安全性得到保障</button>
      <button class="option-btn" data-value="B">B 数据的安全性得到保障</button>
      <button class="option-btn" data-value="C">C 业务的连续性得到保障</button>
      <button class="option-btn" data-value="D">D 业务的可用性得到保障</button>
    </div>
    <div id="explain-block"></div>
    <div class="canvas-demo">
      <canvas id="archCanvas" width="600" height="200"></canvas>
      <div style="position:absolute;top:10px;right:10px;z-index:2;">
        <div class="canvas-btns">
          <button class="canvas-btn active" id="btn-plan1">方案一动画</button>
          <button class="canvas-btn" id="btn-plan2">方案二动画</button>
        </div>
      </div>
    </div>
    <div class="game-section">
      <div class="game-title">方案二虚拟化服务器迁移小游戏</div>
      <div class="game-canvas" id="game-canvas-wrap">
        <canvas id="gameCanvas" width="600" height="150"></canvas>
      </div>
      <button class="game-btn" id="gameStartBtn">开始迁移</button>
      <div id="gameResult" style="margin-top:10px;font-size:1rem;"></div>
    </div>
  </div>
  <div class="footer">© 财务系统方案对比趣味学习 | 极简风格 | 动画与交互演示 | 解释详尽</div>
  <script>
    // 选项交互与解释
    const options = document.querySelectorAll('.option-btn');
    const explainBlock = document.getElementById('explain-block');
    let answered = false;
    options.forEach(btn => {
      btn.onclick = () => {
        if(answered) return;
        answered = true;
        options.forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
        // 答案C
        if(btn.dataset.value === 'C') {
          btn.classList.add('correct');
          explainBlock.innerHTML = `<div class='explain'><b>解析：</b><br>方案二采用虚拟化技术，当一台服务器出现物理故障时，业务可迁移到另一台物理服务器上，保障了<strong>业务的连续性</strong>。而网络安全性、数据安全性、业务可用性并未因虚拟化本身发生本质提升。</div>`;
        } else {
          btn.classList.add('wrong');
          explainBlock.innerHTML = `<div class='explain'><b>解析：</b><br>正确答案是 <b>C 业务的连续性得到保障</b>。方案二的虚拟化技术主要提升了业务连续性，其他方面并未发生本质变化。</div>`;
        }
      }
    });
    // 动画演示
    const archCanvas = document.getElementById('archCanvas');
    const ctx = archCanvas.getContext('2d');
    let plan = 1;
    function drawPlan1(step=0) {
      ctx.clearRect(0,0,archCanvas.width,archCanvas.height);
      // 画防火墙
      ctx.save();
      ctx.fillStyle = '#007aff';
      ctx.fillRect(40, 80, 40, 40);
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 16px sans-serif';
      ctx.fillText('防火墙', 44, 105);
      ctx.restore();
      // 画3台应用服务器
      for(let i=0;i<3;i++) {
        ctx.save();
        ctx.fillStyle = '#2ecc40';
        ctx.fillRect(140+i*80, 60, 50, 50);
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 14px sans-serif';
        ctx.fillText('应用', 148+i*80, 90);
        ctx.restore();
      }
      // 画备份服务器
      ctx.save();
      ctx.fillStyle = '#ffb300';
      ctx.fillRect(140+3*80, 60, 50, 50);
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 14px sans-serif';
      ctx.fillText('备份', 148+3*80, 90);
      ctx.restore();
      // 画箭头
      ctx.save();
      ctx.strokeStyle = '#aaa';
      ctx.lineWidth = 2;
      for(let i=0;i<3;i++) {
        ctx.beginPath();
        ctx.moveTo(60, 100);
        ctx.lineTo(165+i*80, 85);
        ctx.stroke();
      }
      // 备份箭头
      ctx.setLineDash([5,5]);
      ctx.beginPath();
      ctx.moveTo(165+2*80, 85);
      ctx.lineTo(165+3*80, 85);
      ctx.strokeStyle = '#ffb300';
      ctx.stroke();
      ctx.setLineDash([]);
      ctx.restore();
      // 文字说明
      ctx.save();
      ctx.fillStyle = '#888';
      ctx.font = '14px sans-serif';
      ctx.fillText('校园网', 10, 60);
      ctx.fillText('定期备份', 320, 120);
      ctx.restore();
    }
    function drawPlan2(step=0) {
      ctx.clearRect(0,0,archCanvas.width,archCanvas.height);
      // 画防火墙
      ctx.save();
      ctx.fillStyle = '#007aff';
      ctx.fillRect(40, 80, 40, 40);
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 16px sans-serif';
      ctx.fillText('防火墙', 44, 105);
      ctx.restore();
      // 画2台物理服务器
      for(let i=0;i<2;i++) {
        ctx.save();
        ctx.fillStyle = '#2ecc40';
        ctx.fillRect(160+i*120, 60, 70, 70);
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 14px sans-serif';
        ctx.fillText('物理服务器', 162+i*120, 110);
        ctx.restore();
        // 画虚拟机
        for(let j=0;j<3;j++) {
          ctx.save();
          ctx.strokeStyle = '#007aff';
          ctx.strokeRect(170+i*120, 70+j*18, 50, 14);
          ctx.font = '12px sans-serif';
          ctx.fillStyle = '#007aff';
          ctx.fillText('虚拟机'+(j+1), 172+i*120, 81+j*18);
          ctx.restore();
        }
      }
      // 画箭头
      ctx.save();
      ctx.strokeStyle = '#aaa';
      ctx.lineWidth = 2;
      for(let i=0;i<2;i++) {
        ctx.beginPath();
        ctx.moveTo(60, 100);
        ctx.lineTo(195+i*120, 95);
        ctx.stroke();
      }
      // 故障迁移动画
      if(step>0) {
        // 画迁移箭头
        ctx.save();
        ctx.strokeStyle = '#ff4d4f';
        ctx.setLineDash([5,5]);
        ctx.beginPath();
        ctx.moveTo(195, 95);
        ctx.lineTo(195+120, 95);
        ctx.stroke();
        ctx.setLineDash([]);
        ctx.restore();
        // 故障提示
        ctx.save();
        ctx.fillStyle = '#ff4d4f';
        ctx.font = 'bold 16px sans-serif';
        ctx.fillText('故障', 185, 60);
        ctx.restore();
        // 迁移提示
        ctx.save();
        ctx.fillStyle = '#2ecc40';
        ctx.font = '14px sans-serif';
        ctx.fillText('业务迁移', 250, 120);
        ctx.restore();
      }
      ctx.restore();
      // 文字说明
      ctx.save();
      ctx.fillStyle = '#888';
      ctx.font = '14px sans-serif';
      ctx.fillText('校园网', 10, 60);
      ctx.restore();
    }
    function animatePlan2() {
      let step = 0;
      drawPlan2(0);
      setTimeout(()=>{
        drawPlan2(1);
      }, 900);
    }
    document.getElementById('btn-plan1').onclick = function() {
      plan = 1;
      this.classList.add('active');
      document.getElementById('btn-plan2').classList.remove('active');
      drawPlan1();
    };
    document.getElementById('btn-plan2').onclick = function() {
      plan = 2;
      this.classList.add('active');
      document.getElementById('btn-plan1').classList.remove('active');
      animatePlan2();
    };
    drawPlan1();
    // 游戏化迁移演示
    const gameCanvas = document.getElementById('gameCanvas');
    const gctx = gameCanvas.getContext('2d');
    let gameState = 0; // 0:未开始 1:迁移中 2:完成
    let vmX = 180, vmY = 60, targetX = 420, targetY = 60;
    function drawGame(vmPos) {
      gctx.clearRect(0,0,gameCanvas.width,gameCanvas.height);
      // 物理服务器
      gctx.save();
      gctx.fillStyle = '#2ecc40';
      gctx.fillRect(120, 40, 80, 70);
      gctx.fillRect(400, 40, 80, 70);
      gctx.fillStyle = '#fff';
      gctx.font = 'bold 14px sans-serif';
      gctx.fillText('物理服务器A', 122, 120);
      gctx.fillText('物理服务器B', 402, 120);
      gctx.restore();
      // 故障提示
      if(gameState===1) {
        gctx.save();
        gctx.fillStyle = '#ff4d4f';
        gctx.font = 'bold 16px sans-serif';
        gctx.fillText('故障', 150, 35);
        gctx.restore();
      }
      // 虚拟机
      gctx.save();
      gctx.strokeStyle = '#007aff';
      gctx.strokeRect(vmPos.x, vmPos.y, 50, 18);
      gctx.font = '13px sans-serif';
      gctx.fillStyle = '#007aff';
      gctx.fillText('虚拟机', vmPos.x+7, vmPos.y+14);
      gctx.restore();
      // 迁移箭头
      if(gameState===1) {
        gctx.save();
        gctx.strokeStyle = '#ffb300';
        gctx.setLineDash([5,5]);
        gctx.beginPath();
        gctx.moveTo(vmX+25, vmY+9);
        gctx.lineTo(targetX+25, targetY+9);
        gctx.stroke();
        gctx.setLineDash([]);
        gctx.restore();
      }
    }
    function animateGame() {
      let t = 0;
      gameState = 1;
      function step() {
        let x = vmX + (targetX-vmX)*t/30;
        let y = vmY + (targetY-vmY)*t/30;
        drawGame({x, y});
        if(t<30) {
          t++;
          requestAnimationFrame(step);
        } else {
          gameState = 2;
          drawGame({x:targetX, y:targetY});
          document.getElementById('gameResult').innerHTML = '<span style="color:#2ecc40">迁移成功！业务不中断，连续性保障！</span>';
        }
      }
      step();
    }
    document.getElementById('gameStartBtn').onclick = function() {
      if(gameState===1) return;
      document.getElementById('gameResult').innerHTML = '';
      animateGame();
    };
    drawGame({x:vmX, y:vmY});
  </script>
</body>
</html> 