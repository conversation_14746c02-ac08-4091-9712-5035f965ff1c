<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Accommodate</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        #canvas-container {
            border: 2px solid #ccc;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            background-color: white;
        }
        canvas {
            display: block;
        }
        #controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        #explanation {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            text-align: left;
            line-height: 1.6;
        }
        h1, h2 {
            text-align: center;
            color: #0056b3;
        }
    </style>
</head>
<body>

    <h1>单词动画: Accommodate</h1>

    <div id="canvas-container">
        <canvas id="word-canvas" width="800" height="450"></canvas>
    </div>

    <div id="controls">
        <button id="play-btn">播放动画</button>
        <button id="reset-btn">重置</button>
    </div>

    <div id="explanation">
        <h2>故事解读：accommodate (v. 容纳, 使适应)</h2>
        <p>
            您好！为了帮助您理解单词 <strong>accommodate</strong>，我设计了这个动画。这个单词由几部分构成：
        </p>
        <ul>
            <li><strong>ac-</strong>: 词根 `ad-` 的变体，意思是 "to" (去，朝向)。</li>
            <li><strong>com-</strong>: 意思是 "with, together" (一起，共同)。</li>
            <li><strong>mod</strong>: 意思是 "fit, measure" (适应, 空间, 模式)。</li>
            <li><strong>-ate</strong>: 动词后缀。</li>
        </ul>
        <p>
            <strong>动画故事线：</strong>
            我们把 "accommodate" 想象成一个"为大家提供合适空间"的过程。
        </p>
        <ol>
            <li><strong>第一幕 (ac- "朝向")</strong>: 一个小人走向一片空地，代表着他有一个需求，需要一个"去处"。</li>
            <li><strong>第二幕 (com- "共同")</strong>: 各种建筑材料（砖块、木板、窗户）从四面八方飞来，"聚集"在一起，象征着为了满足需求，各种资源汇集到一起。</li>
            <li><strong>第三幕 (mod "适应")</strong>: 出现了一把尺子来"测量"空间，然后材料自动搭建成一座房子。这代表了"适应"和"匹配"空间的过程。</li>
            <li><strong>第四幕 (accommodate "容纳")</strong>: 房子建成，小人住了进去。房子甚至能根据小人的需要微微调整大小，完美地诠释了 <strong>accommodate</strong> 的核心含义：不仅是提供空间，更是贴心地"满足需求"和"使其适应"。</li>
        </ol>
        <p>
            通过这个先有需求(ac-)，再汇集资源(com-)，然后调整匹配(mod)，最终实现"容纳"和"适应"的故事，希望能让您对 <strong>accommodate</strong> 有更深刻的理解和记忆。
        </p>
    </div>

    <script>
        const canvas = document.getElementById('word-canvas');
        const ctx = canvas.getContext('2d');
        const playBtn = document.getElementById('play-btn');
        const resetBtn = document.getElementById('reset-btn');

        const width = canvas.width;
        const height = canvas.height;

        let animationFrameId;
        let scene = 0; // 0: initial, 1: ac-, 2: com-, 3: mod, 4: accommodate
        let progress = 0; // Animation progress for each scene (0 to 1)

        // Game objects
        const person = {
            x: -30,
            y: height - 80,
            targetX: 100,
            draw() {
                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.arc(this.x, this.y - 25, 15, 0, Math.PI * 2); // head
                ctx.fill();
                ctx.fillRect(this.x - 10, this.y - 10, 20, 30); // body
            }
        };

        const materials = [
            { x: -50, y: 50, w: 30, h: 15, color: '#c0392b', targetX: 400, targetY: height - 60 }, // brick
            { x: width + 50, y: 80, w: 30, h: 15, color: '#c0392b', targetX: 435, targetY: height - 60 }, // brick
            { x: -50, y: 150, w: 30, h: 15, color: '#c0392b', targetX: 400, targetY: height - 80 }, // brick
            { x: width + 50, y: 200, w: 30, h: 15, color: '#c0392b', targetX: 435, targetY: height - 80 }, // brick
            { x: 100, y: -50, w: 80, h: 10, color: '#795548', targetX: 395, targetY: height - 140 }, // wood
            { x: 600, y: -50, w: 60, h: 40, color: '#87CEEB', targetX: 410, targetY: height - 120 }, // window
        ];

        const house = {
            x: 390,
            y: height - 150,
            w: 100,
            h: 100,
            roofH: 40,
            scale: 0,
            finalScale: 1,
            draw() {
                if (this.scale === 0) return;
                ctx.save();
                ctx.translate(this.x + this.w / 2, this.y + this.h);
                ctx.scale(this.scale, this.scale);
                ctx.translate(-(this.x + this.w / 2), -(this.y + this.h));
                
                // base
                ctx.fillStyle = '#d2b48c';
                ctx.fillRect(this.x, this.y, this.w, this.h);

                // roof
                ctx.fillStyle = '#8B4513';
                ctx.beginPath();
                ctx.moveTo(this.x - 10, this.y);
                ctx.lineTo(this.x + this.w / 2, this.y - this.roofH);
                ctx.lineTo(this.x + this.w + 10, this.y);
                ctx.closePath();
                ctx.fill();

                // door
                ctx.fillStyle = '#654321';
                ctx.fillRect(this.x + this.w / 2 - 15, this.y + this.h - 40, 30, 40);
                
                ctx.restore();
            }
        };

        const ruler = {
            x: 350,
            y: height - 200,
            w: 200,
            h: 20,
            alpha: 0,
            draw() {
                if(this.alpha === 0) return;
                ctx.save();
                ctx.globalAlpha = this.alpha;
                ctx.fillStyle = '#f1c40f';
                ctx.fillRect(this.x, this.y, this.w, this.h);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                for (let i = 0; i <= 10; i++) {
                    const markX = this.x + (i * (this.w / 10));
                    ctx.fillRect(markX, this.y, 1, 10);
                    ctx.fillText(i * 10, markX - 8, this.y + this.h + 15);
                }
                ctx.restore();
            }
        };
        
        function drawInitialState() {
            ctx.clearRect(0, 0, width, height);
            
            // Draw ground
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(0, height - 50, width, 50);

            ctx.font = '48px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('accommodate', width / 2, height / 2 - 20);
            
            ctx.font = '24px Arial';
            ctx.fillText('ac- (to) + com- (together) + mod (fit) + -ate', width / 2, height / 2 + 30);
        }

        function animate() {
            ctx.clearRect(0, 0, width, height);
             // Draw ground
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(0, height - 50, width, 50);
            
            progress += 0.01;
            
            // Scene 1: ac-
            if (scene === 1) {
                if (progress > 1) progress = 1;
                // Text
                ctx.font = '36px Arial';
                ctx.fillStyle = 'rgba(0, 0, 0, ' + Math.min(1, progress * 4) + ')';
                ctx.textAlign = 'center';
                ctx.fillText('ac- (to / toward)', width / 2, 60);

                // Person moving
                person.x = -30 + (person.targetX + 30) * progress;
                person.draw();
                
                if (progress >= 1) {
                    setTimeout(() => { scene = 2; progress = 0; }, 1000);
                }
            }

            // Scene 2: com-
            else if (scene === 2) {
                if (progress > 1) progress = 1;
                ctx.font = '36px Arial';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('com- (together)', width / 2, 60);

                person.draw(); // Person is waiting

                materials.forEach(m => {
                    m.currentX = m.x + (m.targetX - m.x) * progress;
                    m.currentY = m.y + (m.targetY - m.y) * progress;
                    ctx.fillStyle = m.color;
                    ctx.fillRect(m.currentX, m.currentY, m.w, m.h);
                });
                
                if (progress >= 1) {
                     setTimeout(() => { scene = 3; progress = 0; }, 1000);
                }
            }
            
            // Scene 3: mod
            else if (scene === 3) {
                if (progress > 1) progress = 1;
                ctx.font = '36px Arial';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('mod (fit / measure)', width / 2, 60);
                person.draw();

                // Ruler appears and fades
                ruler.alpha = Math.sin(Math.PI * progress);
                ruler.draw();
                
                // House assembles
                if(progress > 0.5) {
                     house.scale = (progress - 0.5) * 2;
                } else {
                     materials.forEach(m => {
                        ctx.fillStyle = m.color;
                        ctx.fillRect(m.targetX, m.targetY, m.w, m.h);
                    });
                }
                house.draw();

                if (progress >= 1) {
                     setTimeout(() => { scene = 4; progress = 0; }, 1000);
                }
            }
            
            // Scene 4: accommodate
            else if (scene === 4) {
                 if (progress > 1) progress = 1;
                ctx.font = '36px Arial';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('accommodate (v. provide lodging for, fit in with)', width / 2, 60);

                // Person moves into house
                if (progress < 0.5) {
                    person.x = person.targetX + (house.x + house.w/2 - person.targetX) * (progress * 2);
                    person.y = height - 80 - 100 * (progress * 2);
                } else {
                    person.x = -1000; // hide person
                    house.finalScale = 1 + Math.sin((progress - 0.5) * Math.PI) * 0.1; // house "breathes"
                }
                
                house.scale = house.finalScale;
                house.draw();
                person.draw();
                 if (progress >= 1) {
                    playBtn.disabled = false;
                    cancelAnimationFrame(animationFrameId);
                    return;
                }
            }


            animationFrameId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            playBtn.disabled = true;
            resetState();
            scene = 1;
            progress = 0;
            animationFrameId = requestAnimationFrame(animate);
        }

        function resetState() {
            cancelAnimationFrame(animationFrameId);
            scene = 0;
            progress = 0;
            person.x = -30;
            house.scale = 0;
            house.finalScale = 1;
            ruler.alpha = 0;
            materials.forEach(m => {
                m.currentX = m.x;
                m.currentY = m.y;
            });
            drawInitialState();
            playBtn.disabled = false;
        }

        playBtn.addEventListener('click', startAnimation);
        resetBtn.addEventListener('click', resetState);

        // Initial Draw
        window.onload = () => {
            drawInitialState();
        };
    </script>
</body>
</html> 