<!DOCTYPE html>
<html>
<head>
<title>Intervene Animation</title>
<meta charset="UTF-8">
<style>
    body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #f5f5dc; /* Beige */
    }
    #canvas {
        border: 1px solid #000;
        background-color: #ffffff;
    }
    #explanation {
        width: 800px;
        text-align: left;
        margin-top: 20px;
    }
    #controls {
        margin-top: 10px;
    }
    button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
    }
</style>
</head>
<body>

<h1>Word Animation: Intervene (干预)</h1>
<canvas id="canvas" width="800" height="400"></canvas>
<div id="controls">
    <button id="playBtn">Play Animation</button>
</div>
<div id="explanation">
    <h2>Intervene (干预) = inter- (在...之间) + vene (来)</h2>
    <p><b>故事:</b> 想象有两个正在对峙的人，他们怒气冲冲地走向对方，眼看就要爆发冲突。就在这时，一个勇敢的人从旁边"来"到他们"之间"，成功阻止了这场争斗。这个"来到中间"的动作，就是 "intervene" 的核心含义。</p>
    <p><b>inter- (在...之间):</b> 动画中，我们会看到一个调解人来到两个冲突者<b>之间</b>。</p>
    <p><b>vene (来):</b> 调解人从屏幕外"来"到冲突现场。</p>
    <p><b>交互:</b> 点击 "Play Animation" 按钮，观看这个干预冲突的故事。</p>
</div>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let animationId;
let progress = 0; // 0 to 1

const personA = { startX: 100, endX: 300, y: 300 };
const personB = { startX: 700, endX: 500, y: 300 };
const intervener = { startX: 400, startY: 450, endY: 300 };

function drawPerson(x, y, label, color = 'black') {
    // Body
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x, y - 40);
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.stroke();

    // Head
    ctx.beginPath();
    ctx.arc(x, y - 50, 10, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
    
    // Arms
    ctx.beginPath();
    ctx.moveTo(x - 15, y - 30);
    ctx.lineTo(x + 15, y - 30);
    ctx.stroke();

    // Label
    ctx.fillStyle = 'black';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(label, x, y + 20);
}

function drawIntervener(x, y) {
    drawPerson(x, y, 'Intervener', 'blue');
    // Open arms
    ctx.beginPath();
    ctx.moveTo(x - 25, y - 25);
    ctx.lineTo(x + 25, y - 25);
    ctx.strokeStyle = 'blue';
    ctx.lineWidth = 3;
    ctx.stroke();
}

function drawAnnotation(text, x, y, color = 'red', size = '24px') {
    ctx.font = `${size} Arial`;
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.fillText(text, x, y);
}

function animate() {
    progress += 0.01;
    if (progress > 1) {
        progress = 1;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Animate people moving until intervener steps in
    let currentProgress = progress;
    if (progress > 0.5) {
        currentProgress = 0.5; // Stop them from moving further
    }
    
    const personAX = personA.startX + (personA.endX - personA.startX) * currentProgress;
    const personBX = personB.startX + (personB.endX - personB.startX) * currentProgress;
    
    drawPerson(personAX, personA.y, 'Person A', 'darkred');
    drawPerson(personBX, personB.y, 'Person B', 'darkred');

    // Intervener comes in after a delay
    if (progress > 0.2) {
        let intervenerProgress = (progress - 0.2) / 0.6; // Speed up intervener
        if(intervenerProgress > 1) intervenerProgress = 1;

        const intervenerX = intervener.startX;
        const intervenerY = intervener.startY - (intervener.startY - intervener.endY) * intervenerProgress;
        drawIntervener(intervenerX, intervenerY);
        
        if (intervenerProgress > 0.5 && intervenerProgress < 1) {
            drawAnnotation('vene (来)', intervenerX, intervenerY + 40, 'blue');
        }
    }
    
    // Annotations
    if (progress > 0.8) {
        drawAnnotation('inter- (在...之间)', 400, 250, 'purple');
    }

    if (progress < 0.2) {
        drawAnnotation('冲突将至...', canvas.width / 2, 50, 'darkred', '20px');
    }

    if (progress >= 1) {
        cancelAnimationFrame(animationId);
        playBtn.disabled = false;
        playBtn.textContent = "Play Again";
        drawAnnotation('成功干预!', canvas.width / 2, 50, 'green', '20px');
    } else {
        animationId = requestAnimationFrame(animate);
    }
}

function startAnimation() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    progress = 0;
    playBtn.disabled = true;
    playBtn.textContent = "Animating...";
    animate();
}

playBtn.addEventListener('click', startAnimation);

// Initial draw
function initialDraw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawPerson(personA.startX, personA.y, 'Person A', 'darkred');
    drawPerson(personB.startX, personB.y, 'Person B', 'darkred');
    drawAnnotation('点击播放按钮开始动画', canvas.width / 2, 50, 'black', '20px');
}

initialDraw();

</script>

</body>
</html> 