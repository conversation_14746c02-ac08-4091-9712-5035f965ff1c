<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache存储系统 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
            opacity: 0;
            animation-fill-mode: forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-top: 40px;
        }

        .quiz-title {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .option.selected {
            border-color: #fff;
            background: rgba(255,255,255,0.3);
        }

        .option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.2rem;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .result.show {
            opacity: 1;
        }

        .result.correct {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }

        .result.wrong {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .interactive-hint {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Cache存储系统</h1>
            <p class="subtitle">通过动画和交互理解计算机存储层次结构</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是Cache？</h2>
            <div class="canvas-container">
                <canvas id="cacheIntroCanvas" width="800" height="400"></canvas>
            </div>
            <p class="interactive-hint">点击画布查看Cache工作原理动画</p>
            <div class="explanation">
                <strong>Cache（高速缓存）</strong>是位于CPU和主存之间的小容量高速存储器。它的作用就像一个"聪明的助手"，提前准备好CPU最可能需要的数据，让CPU不用等待慢速的主存。
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">存储层次结构</h2>
            <div class="canvas-container">
                <canvas id="hierarchyCanvas" width="800" height="500"></canvas>
            </div>
            <p class="interactive-hint">点击不同层级查看详细信息</p>
            <div class="explanation">
                计算机存储系统采用<strong>层次结构</strong>：
                <br>• <strong>寄存器</strong>：最快，容量最小，成本最高
                <br>• <strong>Cache</strong>：很快，容量小，成本高
                <br>• <strong>主存</strong>：较快，容量中等，成本中等
                <br>• <strong>硬盘</strong>：较慢，容量大，成本低
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Cache设计原理</h2>
            <div class="canvas-container">
                <canvas id="designCanvas" width="800" height="400"></canvas>
            </div>
            <p class="interactive-hint">点击查看Cache设计考虑因素</p>
            <div class="explanation">
                Cache设计的核心思想是<strong>在合理成本下提高命中率</strong>：
                <br>• 容量越大，命中率越高，但成本也越高
                <br>• 需要在性能和成本之间找到平衡点
                <br>• 目标是获得最优的性能价格比
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="quiz-title">现在来做题吧！</h2>
            <div class="question">
                以下关于Cache的叙述中，正确的是（ ）。
            </div>
            <div class="options">
                <div class="option" data-answer="A">
                    A. 在容量确定的情况下，替换算法的时间复杂度是影响Cache命中率的关键因素
                </div>
                <div class="option" data-answer="B">
                    B. Cache的设计思想是在合理的成本下提高命中率
                </div>
                <div class="option" data-answer="C">
                    C. Cache的设计目标是容量尽可能与主存容量相等
                </div>
                <div class="option" data-answer="D">
                    D. CPU中的Cache容量应大于CPU之外的Cache容量
                </div>
            </div>
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        let selectedOption = null;
        let animationStates = {
            intro: 0,
            hierarchy: 0,
            design: 0
        };

        // Cache介绍动画
        function drawCacheIntro() {
            const canvas = document.getElementById('cacheIntroCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.003;
            
            // CPU
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(50, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', 100, 205);

            // Cache
            ctx.fillStyle = '#4ECDC4';
            ctx.fillRect(250, 175, 80, 50);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('Cache', 290, 205);

            // 主存
            ctx.fillStyle = '#45B7D1';
            ctx.fillRect(450, 150, 120, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('主存', 510, 205);

            // 硬盘
            ctx.fillStyle = '#96CEB4';
            ctx.fillRect(650, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('硬盘', 700, 205);

            if (animationStates.intro > 0) {
                // 数据流动动画
                const progress = (time % 3) / 3;
                
                // 从硬盘到主存
                if (progress < 0.33) {
                    const x = 650 + (450 - 650) * (progress / 0.33);
                    drawDataPacket(ctx, x, 120, '#96CEB4', '数据');
                }
                // 从主存到Cache
                else if (progress < 0.66) {
                    const x = 450 + (250 - 450) * ((progress - 0.33) / 0.33);
                    drawDataPacket(ctx, x, 120, '#45B7D1', '数据');
                }
                // 从Cache到CPU
                else {
                    const x = 250 + (50 - 250) * ((progress - 0.66) / 0.34);
                    drawDataPacket(ctx, x, 120, '#4ECDC4', '数据');
                }

                // 速度标识
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('慢', 700, 280);
                ctx.fillText('较快', 510, 280);
                ctx.fillText('很快', 290, 280);
                ctx.fillText('最快', 100, 280);
            }

            // 连接线
            drawArrow(ctx, 150, 200, 250, 200, '#666');
            drawArrow(ctx, 330, 200, 450, 200, '#666');
            drawArrow(ctx, 570, 200, 650, 200, '#666');
        }

        function drawDataPacket(ctx, x, y, color, text) {
            ctx.fillStyle = color;
            ctx.fillRect(x - 15, y - 10, 30, 20);
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 3);
        }

        function drawArrow(ctx, x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 箭头
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        // 存储层次结构动画
        function drawHierarchy() {
            const canvas = document.getElementById('hierarchyCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const levels = [
                {name: '寄存器', speed: '最快', capacity: '最小', cost: '最高', color: '#FF6B6B', y: 50},
                {name: 'Cache', speed: '很快', capacity: '小', cost: '高', color: '#4ECDC4', y: 150},
                {name: '主存', speed: '较快', capacity: '中等', cost: '中等', color: '#45B7D1', y: 250},
                {name: '硬盘', speed: '较慢', capacity: '大', cost: '低', color: '#96CEB4', y: 350}
            ];

            levels.forEach((level, index) => {
                const width = 200 - index * 30;
                const x = (canvas.width - width) / 2;
                
                // 绘制层级
                ctx.fillStyle = level.color;
                ctx.fillRect(x, level.y, width, 60);
                
                // 层级名称
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(level.name, canvas.width / 2, level.y + 35);
                
                if (animationStates.hierarchy > 0) {
                    // 详细信息
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.fillText(`速度: ${level.speed}`, x + width + 20, level.y + 20);
                    ctx.fillText(`容量: ${level.capacity}`, x + width + 20, level.y + 35);
                    ctx.fillText(`成本: ${level.cost}`, x + width + 20, level.y + 50);
                }
            });

            // 绘制金字塔轮廓
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(300, 50);
            ctx.lineTo(500, 50);
            ctx.lineTo(550, 410);
            ctx.lineTo(250, 410);
            ctx.closePath();
            ctx.stroke();
        }

        // Cache设计原理动画
        function drawDesign() {
            const canvas = document.getElementById('designCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.002;
            
            // 绘制天平
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(400, 100);
            ctx.lineTo(400, 200);
            ctx.moveTo(300, 150);
            ctx.lineTo(500, 150);
            ctx.stroke();

            // 左边：性能
            ctx.fillStyle = '#4ECDC4';
            ctx.fillRect(250, 120, 100, 30);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('性能', 300, 140);

            // 右边：成本
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(450, 120, 100, 30);
            ctx.fillStyle = 'white';
            ctx.fillText('成本', 500, 140);

            if (animationStates.design > 0) {
                // 平衡点
                ctx.fillStyle = '#FFD93D';
                ctx.beginPath();
                ctx.arc(400, 150, 15, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('平衡点', 400, 190);
                
                // 设计要素
                const factors = [
                    '命中率',
                    '访问时间',
                    '容量大小',
                    '制造成本'
                ];
                
                factors.forEach((factor, index) => {
                    const angle = (index / factors.length) * Math.PI * 2 + time;
                    const x = 400 + Math.cos(angle) * 120;
                    const y = 300 + Math.sin(angle) * 60;
                    
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(x - 40, y - 15, 80, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(factor, x, y + 3);
                });
            }
        }

        // 选择选项
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedOption = this.dataset.answer;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }

            const result = document.getElementById('result');
            const options = document.querySelectorAll('.option');
            
            options.forEach(option => {
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.answer !== 'B') {
                    option.classList.add('wrong');
                }
            });

            if (selectedOption === 'B') {
                result.className = 'result show correct';
                result.innerHTML = `
                    <h3>🎉 恭喜你答对了！</h3>
                    <p><strong>正确答案是B：Cache的设计思想是在合理的成本下提高命中率</strong></p>
                    <p>解析：Cache设计需要在性能和成本之间找到平衡。不是容量越大越好，也不是速度越快越好，而是要在合理的成本范围内，通过优化设计来提高命中率，获得最佳的性能价格比。</p>
                `;
            } else {
                result.className = 'result show wrong';
                result.innerHTML = `
                    <h3>❌ 答案不正确</h3>
                    <p><strong>正确答案是B：Cache的设计思想是在合理的成本下提高命中率</strong></p>
                    <p>你选择的选项存在以下问题：</p>
                    <p>${getExplanation(selectedOption)}</p>
                `;
            }
        }

        function getExplanation(answer) {
            const explanations = {
                'A': '替换算法的时间复杂度虽然重要，但不是影响命中率的关键因素。命中率主要取决于程序的局部性和Cache的容量、组织方式等。',
                'C': 'Cache的设计目标不是容量与主存相等。如果容量相等，就失去了Cache的意义，成本也会过高。',
                'D': 'CPU内外的Cache容量关系不是固定的，需要根据具体的系统设计和成本考虑来确定。'
            };
            return explanations[answer] || '';
        }

        // 画布点击事件
        document.getElementById('cacheIntroCanvas').addEventListener('click', () => {
            animationStates.intro = (animationStates.intro + 1) % 2;
        });

        document.getElementById('hierarchyCanvas').addEventListener('click', () => {
            animationStates.hierarchy = (animationStates.hierarchy + 1) % 2;
        });

        document.getElementById('designCanvas').addEventListener('click', () => {
            animationStates.design = (animationStates.design + 1) % 2;
        });

        // 动画循环
        function animate() {
            drawCacheIntro();
            drawHierarchy();
            drawDesign();
            requestAnimationFrame(animate);
        }

        animate();
    </script>
</body>
</html>
