<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件需求分析 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }

        .pyramid-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .pyramid-level {
            margin: 10px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .pyramid-level:hover {
            transform: scale(1.05);
        }

        .level-box {
            padding: 20px 40px;
            border-radius: 15px;
            color: white;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .level-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .level-box:hover::before {
            left: 100%;
        }

        .business-req {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            width: 300px;
        }

        .user-req {
            background: linear-gradient(45deg, #4834d4, #686de0);
            width: 400px;
        }

        .function-req {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            width: 500px;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .interactive-section {
            margin: 40px 0;
        }

        .drag-drop-area {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .drop-zone {
            width: 300px;
            height: 150px;
            border: 3px dashed #ddd;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
            color: #666;
            transition: all 0.3s ease;
            position: relative;
        }

        .drop-zone.active {
            border-color: #4834d4;
            background: rgba(72, 52, 212, 0.1);
        }

        .drop-zone.correct {
            border-color: #00d2d3;
            background: rgba(0, 210, 211, 0.1);
            animation: pulse 0.5s;
        }

        .draggable-item {
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: grab;
            margin: 10px;
            transition: all 0.3s ease;
            border-left: 4px solid #4834d4;
        }

        .draggable-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .draggable-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .explanation-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation-panel.show {
            opacity: 1;
            transform: translateY(0);
        }

        .score-board {
            text-align: center;
            margin: 30px 0;
            font-size: 1.5rem;
            color: #333;
        }

        .score {
            color: #4834d4;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .btn {
            background: linear-gradient(45deg, #4834d4, #686de0);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(72, 52, 212, 0.3);
        }

        .original-question {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border-left: 5px solid #4834d4;
        }

        .original-question h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .original-question p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .question-items {
            margin: 25px 0;
        }

        .question-item {
            display: flex;
            align-items: flex-start;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .question-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .question-item.highlight {
            border: 2px solid #ff6b6b;
            background: linear-gradient(45deg, #fff5f5, #ffffff);
        }

        .item-number {
            background: #4834d4;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .item-text {
            flex: 1;
            color: #333;
            line-height: 1.5;
            margin-right: 15px;
        }

        .item-type {
            color: #666;
            font-style: italic;
            white-space: nowrap;
        }

        .options {
            margin: 25px 0;
        }

        .options h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .option-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .option {
            padding: 10px 15px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #4834d4;
            background: rgba(72, 52, 212, 0.05);
        }

        .answer-section {
            margin-top: 25px;
            padding: 20px;
            background: white;
            border-radius: 10px;
        }

        .correct-answer {
            color: #28a745;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .your-answer {
            color: #dc3545;
            font-weight: bold;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">软件需求分析</h1>
            <p class="subtitle">通过互动游戏学习业务需求、用户需求和功能需求</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    📝 考试真题：软件需求分析
                </div>

                <div class="original-question">
                    <h3>题目背景</h3>
                    <p>某软件公司正在承担开发一个字处理器的任务。在需求分析阶段，公司的相关人员整理出一些相关的系统需求，其中：</p>

                    <div class="question-items">
                        <div class="question-item">
                            <span class="item-number">①</span>
                            <span class="item-text">"找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词"</span>
                            <span class="item-type">属于（用户需求）</span>
                        </div>

                        <div class="question-item">
                            <span class="item-number">②</span>
                            <span class="item-text">"显示提供替换词的对话框以及实现整个文档范围的替换"</span>
                            <span class="item-type">属于（功能需求）</span>
                        </div>

                        <div class="question-item highlight">
                            <span class="item-number">③</span>
                            <span class="item-text">"用户能有效地纠正文档中的拼写错误"</span>
                            <span class="item-type">属于（<strong>请作答此空</strong>）</span>
                        </div>
                    </div>

                    <div class="options">
                        <h4>选项：</h4>
                        <div class="option-grid">
                            <div class="option">A. 业务需求</div>
                            <div class="option">B. 用户需求</div>
                            <div class="option">C. 功能需求</div>
                            <div class="option">D. 性能需求</div>
                        </div>
                    </div>

                    <div class="answer-section">
                        <div class="correct-answer">✅ 正确答案：A (业务需求)</div>
                        <div class="your-answer">❌ 你的答案：B (用户需求)</div>
                    </div>
                </div>
                
                <div class="pyramid-container">
                    <div class="pyramid-level" onclick="showExplanation('business')">
                        <div class="level-box business-req">
                            <h3>业务需求 (Business Requirements)</h3>
                            <p>高层次的目标要求</p>
                        </div>
                    </div>
                    
                    <div class="pyramid-level" onclick="showExplanation('user')">
                        <div class="level-box user-req">
                            <h3>用户需求 (User Requirements)</h3>
                            <p>用户必须完成的任务</p>
                        </div>
                    </div>
                    
                    <div class="pyramid-level" onclick="showExplanation('function')">
                        <div class="level-box function-req">
                            <h3>功能需求 (Functional Requirements)</h3>
                            <p>开发人员必须实现的软件功能</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>

            <div class="interactive-section">
                <h3 style="text-align: center; margin-bottom: 30px; color: #333;">
                    🎮 拖拽游戏：将需求描述拖到正确的分类中
                </h3>
                
                <div class="score-board">
                    得分: <span class="score" id="score">0</span> / 3
                </div>

                <div class="drag-drop-area">
                    <div class="drop-zone" data-type="business">
                        <div>业务需求</div>
                    </div>
                    <div class="drop-zone" data-type="user">
                        <div>用户需求</div>
                    </div>
                    <div class="drop-zone" data-type="function">
                        <div>功能需求</div>
                    </div>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <div class="draggable-item" draggable="true" data-answer="user">
                        找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词
                    </div>
                    <div class="draggable-item" draggable="true" data-answer="function">
                        显示提供替换词的对话框以及实现整个文档范围的替换
                    </div>
                    <div class="draggable-item" draggable="true" data-answer="business">
                        用户能有效地纠正文档中的拼写错误
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn" onclick="resetGame()">重新开始</button>
                    <button class="btn" onclick="showAllExplanations()">查看详解</button>
                </div>
            </div>

            <div class="explanation-panel" id="explanationPanel">
                <h3 id="explanationTitle"></h3>
                <p id="explanationContent"></p>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Canvas 动画
        function initCanvas() {
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');
            
            let animationFrame = 0;
            
            function drawPyramid() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const centerX = canvas.width / 2;
                const levels = [
                    { y: 100, width: 200, color: '#ff6b6b', text: '业务需求', desc: '高层次目标' },
                    { y: 200, width: 300, color: '#4834d4', text: '用户需求', desc: '用户任务' },
                    { y: 300, width: 400, color: '#00d2d3', text: '功能需求', desc: '具体功能' }
                ];
                
                levels.forEach((level, index) => {
                    const offset = Math.sin(animationFrame * 0.02 + index) * 5;
                    
                    // 绘制矩形
                    ctx.fillStyle = level.color;
                    ctx.fillRect(centerX - level.width/2, level.y + offset, level.width, 60);
                    
                    // 绘制文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(level.text, centerX, level.y + offset + 25);
                    
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(level.desc, centerX, level.y + offset + 45);
                });
                
                animationFrame++;
                requestAnimationFrame(drawPyramid);
            }
            
            drawPyramid();
        }

        // 拖拽功能
        let score = 0;
        let draggedElement = null;

        function initDragDrop() {
            const draggableItems = document.querySelectorAll('.draggable-item');
            const dropZones = document.querySelectorAll('.drop-zone');

            draggableItems.forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    draggedElement = e.target;
                    e.target.classList.add('dragging');
                });

                item.addEventListener('dragend', (e) => {
                    e.target.classList.remove('dragging');
                });
            });

            dropZones.forEach(zone => {
                zone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    zone.classList.add('active');
                });

                zone.addEventListener('dragleave', () => {
                    zone.classList.remove('active');
                });

                zone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    zone.classList.remove('active');
                    
                    if (draggedElement) {
                        const correctAnswer = draggedElement.dataset.answer;
                        const zoneType = zone.dataset.type;
                        
                        if (correctAnswer === zoneType) {
                            zone.classList.add('correct');
                            zone.innerHTML = `<div style="color: #00d2d3; font-weight: bold;">✓ 正确!</div><div style="font-size: 0.9rem; margin-top: 10px;">${draggedElement.textContent}</div>`;
                            draggedElement.style.display = 'none';
                            score++;
                            updateScore();
                            
                            if (score === 3) {
                                setTimeout(() => {
                                    alert('🎉 恭喜！你已经掌握了软件需求分析的基本概念！');
                                }, 500);
                            }
                        } else {
                            // 错误动画
                            zone.style.animation = 'shake 0.5s';
                            setTimeout(() => {
                                zone.style.animation = '';
                            }, 500);
                        }
                    }
                });
            });
        }

        function updateScore() {
            document.getElementById('score').textContent = score;
        }

        function resetGame() {
            score = 0;
            updateScore();
            
            // 重置拖拽项
            const draggableItems = document.querySelectorAll('.draggable-item');
            draggableItems.forEach(item => {
                item.style.display = 'block';
            });
            
            // 重置投放区域
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach((zone, index) => {
                zone.classList.remove('correct');
                const types = ['业务需求', '用户需求', '功能需求'];
                zone.innerHTML = `<div>${types[index]}</div>`;
            });
        }

        function showExplanation(type) {
            const panel = document.getElementById('explanationPanel');
            const title = document.getElementById('explanationTitle');
            const content = document.getElementById('explanationContent');
            
            const explanations = {
                business: {
                    title: '🎯 业务需求 (Business Requirements)',
                    content: '业务需求反映了组织机构或客户对系统、产品高层次的目标要求。它回答"为什么要做这个系统？"的问题。例如："用户能有效地纠正文档中的拼写错误"就是一个业务需求，它描述了系统要达到的商业目标。'
                },
                user: {
                    title: '👤 用户需求 (User Requirements)',
                    content: '用户需求描述了用户使用产品必须要完成的任务。它回答"用户要做什么？"的问题。例如："找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词"描述了用户的具体使用场景和任务。'
                },
                function: {
                    title: '⚙️ 功能需求 (Functional Requirements)',
                    content: '功能需求定义了开发人员必须实现的软件功能，使得用户能完成他们的任务。它回答"系统要怎么做？"的问题。例如："显示提供替换词的对话框以及实现整个文档范围的替换"描述了系统的具体功能实现。'
                }
            };
            
            title.textContent = explanations[type].title;
            content.textContent = explanations[type].content;
            panel.classList.add('show');
            
            setTimeout(() => {
                panel.classList.remove('show');
            }, 5000);
        }

        function showAllExplanations() {
            const panel = document.getElementById('explanationPanel');
            const title = document.getElementById('explanationTitle');
            const content = document.getElementById('explanationContent');
            
            title.textContent = '📚 完整解析';
            content.innerHTML = `
                <strong>软件需求的三个层次：</strong><br><br>
                <strong>1. 业务需求：</strong>回答"为什么做？"- 高层次的商业目标<br>
                <strong>2. 用户需求：</strong>回答"用户要做什么？"- 用户的具体任务<br>
                <strong>3. 功能需求：</strong>回答"系统怎么做？"- 具体的技术实现<br><br>
                <strong>记忆口诀：</strong>业务定目标，用户说需要，功能来实现！
            `;
            panel.classList.add('show');
        }

        // 添加摇摆动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                75% { transform: translateX(10px); }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        window.addEventListener('load', () => {
            createParticles();
            initCanvas();
            initDragDrop();
        });
    </script>
</body>
</html>
