<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANSI/IEEE 1471-2000 软件架构标准学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        #architectureCanvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            line-height: 1.8;
            color: #444;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .concept-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-element" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="floating-element" style="top: 20%; right: 15%; animation-delay: 2s;"></div>
    <div class="floating-element" style="bottom: 30%; left: 20%; animation-delay: 4s;"></div>

    <div class="container">
        <h1 class="title">🏗️ 软件架构标准探索之旅</h1>
        
        <div class="question-card">
            <div class="question-text">
                <strong>ANSI/IEEE 1471-2000</strong>是对软件密集型系统的架构进行描述的标准。在该标准中，
                <span style="background: #fff3cd; padding: 2px 8px; border-radius: 5px;">（ ）</span>
                这一概念主要用于描述软件架构模型。在此基础上，通常采用
                <span style="background: #fff3cd; padding: 2px 8px; border-radius: 5px;">（ ）</span>
                描述某个利益相关人（Stakeholder）所关注架构模型的某一方面。
                <span style="background: #fff3cd; padding: 2px 8px; border-radius: 5px;">（ ）</span>
                则是对所有利益相关人关注点的响应和回答。
            </div>

            <div class="options">
                <button class="option" data-answer="A">A. 环境</button>
                <button class="option" data-answer="B">B. 资源</button>
                <button class="option" data-answer="C">C. 视角</button>
                <button class="option" data-answer="D">D. 场景</button>
            </div>

            <div class="canvas-container">
                <canvas id="architectureCanvas"></canvas>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <button class="interactive-btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="interactive-btn" onclick="showConcepts()">📚 查看核心概念</button>
                <button class="interactive-btn" onclick="playGame()">🎮 互动游戏</button>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎯 知识解析</h3>
                <div class="concept-box">
                    <strong>视角（Viewpoint）</strong>：这是正确答案！视角决定了用来创建视图的语言、符号和模型，以及相关的建模方法或分析技术。
                </div>
                <div class="concept-box">
                    <strong>视图（View）</strong>：从特定视角表述架构的某个独立方面，包含一个或多个架构模型。
                </div>
                <div class="concept-box">
                    <strong>架构描述（Architecture Description）</strong>：对所有利益相关人关注点的响应和回答。
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationFrame = 0;
        let isAnimating = false;

        // 核心概念数据
        const concepts = {
            stakeholder: { x: 100, y: 100, radius: 40, color: '#4facfe', label: '利益相关人' },
            concern: { x: 300, y: 100, radius: 35, color: '#f093fb', label: '关注点' },
            viewpoint: { x: 500, y: 100, radius: 35, color: '#a8edea', label: '视角' },
            view: { x: 700, y: 100, radius: 35, color: '#fed6e3', label: '视图' },
            model: { x: 400, y: 300, radius: 40, color: '#667eea', label: '架构模型' },
            description: { x: 400, y: 450, radius: 45, color: '#764ba2', label: '架构描述' }
        };

        // 绘制圆形节点
        function drawNode(node, alpha = 1) {
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // 绘制圆形
            ctx.beginPath();
            ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
            ctx.fillStyle = node.color;
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(node.label, node.x, node.y);
            
            ctx.restore();
        }

        // 绘制连接线
        function drawConnection(from, to, alpha = 1) {
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            ctx.beginPath();
            ctx.moveTo(from.x, from.y);
            ctx.lineTo(to.x, to.y);
            ctx.stroke();
            
            ctx.restore();
        }

        // 基础绘制
        function drawBasic() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, 'rgba(102, 126, 234, 0.1)');
            gradient.addColorStop(1, 'rgba(118, 75, 162, 0.1)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制所有节点
            Object.values(concepts).forEach(node => drawNode(node));
        }

        // 动画演示
        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationFrame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, 'rgba(102, 126, 234, 0.1)');
                gradient.addColorStop(1, 'rgba(118, 75, 162, 0.1)');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                const progress = (animationFrame % 300) / 300;
                
                if (progress < 0.2) {
                    // 阶段1：显示利益相关人
                    drawNode(concepts.stakeholder, Math.min(progress * 5, 1));
                    showText('利益相关人关注系统的不同方面', 50, 50);
                } else if (progress < 0.4) {
                    // 阶段2：显示关注点
                    drawNode(concepts.stakeholder);
                    drawNode(concepts.concern, Math.min((progress - 0.2) * 5, 1));
                    drawConnection(concepts.stakeholder, concepts.concern, Math.min((progress - 0.2) * 5, 1));
                    showText('每个利益相关人都有特定的关注点', 50, 50);
                } else if (progress < 0.6) {
                    // 阶段3：显示视角
                    drawNode(concepts.stakeholder);
                    drawNode(concepts.concern);
                    drawNode(concepts.viewpoint, Math.min((progress - 0.4) * 5, 1));
                    drawConnection(concepts.stakeholder, concepts.concern);
                    drawConnection(concepts.concern, concepts.viewpoint, Math.min((progress - 0.4) * 5, 1));
                    showText('视角决定了如何表述架构', 50, 50);
                } else if (progress < 0.8) {
                    // 阶段4：显示视图
                    drawNode(concepts.stakeholder);
                    drawNode(concepts.concern);
                    drawNode(concepts.viewpoint);
                    drawNode(concepts.view, Math.min((progress - 0.6) * 5, 1));
                    drawConnection(concepts.stakeholder, concepts.concern);
                    drawConnection(concepts.concern, concepts.viewpoint);
                    drawConnection(concepts.viewpoint, concepts.view, Math.min((progress - 0.6) * 5, 1));
                    showText('视图是从特定视角表述的架构方面', 50, 50);
                } else {
                    // 阶段5：完整显示
                    Object.values(concepts).forEach(node => drawNode(node));
                    drawConnection(concepts.stakeholder, concepts.concern);
                    drawConnection(concepts.concern, concepts.viewpoint);
                    drawConnection(concepts.viewpoint, concepts.view);
                    drawConnection(concepts.view, concepts.model);
                    drawConnection(concepts.model, concepts.description);
                    showText('完整的软件架构标准体系', 50, 50);
                }
                
                animationFrame++;
                if (animationFrame < 300) {
                    requestAnimationFrame(animate);
                } else {
                    isAnimating = false;
                }
            }
            
            animate();
        }

        function showText(text, x, y) {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText(text, x, y);
        }

        // 显示概念
        function showConcepts() {
            document.getElementById('explanation').classList.add('show');
        }

        // 互动游戏
        function playGame() {
            alert('🎮 拖拽游戏即将开始！请将概念拖拽到正确的位置来建立软件架构标准的完整体系！');
            // 这里可以添加更复杂的拖拽游戏逻辑
        }

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                if (answer === 'C') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        showConcepts();
                        startAnimation();
                    }, 1000);
                } else {
                    this.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
                    setTimeout(() => {
                        this.style.background = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
                    }, 1000);
                }
            });
        });

        // 初始化
        drawBasic();
        
        // 添加鼠标悬停效果
        canvas.addEventListener('mousemove', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            let hovering = false;
            Object.values(concepts).forEach(node => {
                const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
                if (distance < node.radius) {
                    hovering = true;
                    canvas.style.cursor = 'pointer';
                }
            });
            
            if (!hovering) {
                canvas.style.cursor = 'default';
            }
        });
    </script>
</body>
</html>
