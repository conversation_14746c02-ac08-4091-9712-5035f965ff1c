<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCP/IP协议层次 - TLS位置学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInDown 1s ease-out forwards;
        }

        .subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin-bottom: 50px;
            opacity: 0;
            animation: fadeInDown 1s ease-out 0.3s forwards;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        .question-text {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .option.correct {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #dc3545;
            color: white;
        }

        .layers-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .layers-title {
            text-align: center;
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
        }

        .layer {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            margin: 15px 0;
            padding: 20px;
            border-radius: 15px;
            color: white;
            font-size: 1.2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .layer:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .layer.application {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .layer.transport {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            animation: pulse 2s infinite;
        }

        .layer.network {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .layer.datalink {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .tls-indicator {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #ffd700;
            color: #333;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            opacity: 0;
            animation: bounceIn 1s ease-out 2s forwards;
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
        }

        .explanation.show {
            animation: fadeInUp 1s ease-out forwards;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: translateY(-50%) scale(0.3);
            }
            50% {
                opacity: 1;
                transform: translateY(-50%) scale(1.1);
            }
            100% {
                opacity: 1;
                transform: translateY(-50%) scale(1);
            }
        }

        .highlight {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            }
            to {
                box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌐 TCP/IP协议层次学习</h1>
        <p class="subtitle">通过动画理解TLS协议的位置</p>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>TCP/IP在多个层次中引入了安全机制，其中TLS协议位于（ ）。
            </div>
            <div class="options">
                <div class="option" data-answer="A">A. 数据链路层</div>
                <div class="option" data-answer="B">B. 网络层</div>
                <div class="option" data-answer="C">C. 传输层</div>
                <div class="option" data-answer="D">D. 应用层</div>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="checkAnswer()">提交答案</button>
                <button class="btn" onclick="showExplanation()">查看解析</button>
            </div>
        </div>

        <div class="layers-container">
            <h2 class="layers-title">🏗️ TCP/IP四层模型</h2>
            <div class="layer application" onclick="explainLayer('application')">
                <strong>应用层 (Application Layer)</strong>
                <div>HTTP, FTP, SMTP, DNS...</div>
            </div>
            <div class="layer transport" onclick="explainLayer('transport')">
                <strong>传输层 (Transport Layer)</strong>
                <div>TCP, UDP</div>
                <div class="tls-indicator">TLS在这里！</div>
            </div>
            <div class="layer network" onclick="explainLayer('network')">
                <strong>网络层 (Network Layer)</strong>
                <div>IP, ICMP, ARP...</div>
            </div>
            <div class="layer datalink" onclick="explainLayer('datalink')">
                <strong>数据链路层 (Data Link Layer)</strong>
                <div>Ethernet, WiFi...</div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="animationCanvas" width="800" height="400"></canvas>
            <br>
            <button class="btn" onclick="startAnimation()">🎬 播放数据传输动画</button>
            <button class="btn" onclick="highlightTLS()">✨ 高亮TLS位置</button>
        </div>

        <div class="explanation" id="explanation">
            <h3>📚 知识解析</h3>
            <p><strong>TLS (Transport Layer Security) 传输层安全协议</strong></p>
            <ul>
                <li>🔐 <strong>位置</strong>：位于传输层，通常在TCP协议之上</li>
                <li>🛡️ <strong>作用</strong>：为应用程序之间提供加密通信</li>
                <li>🔄 <strong>工作原理</strong>：在TCP建立连接后，TLS进行握手协商加密参数</li>
                <li>📱 <strong>应用</strong>：HTTPS就是HTTP over TLS的实现</li>
            </ul>
            
            <h3>🎯 做题思路</h3>
            <ol>
                <li>理解TCP/IP四层模型的结构</li>
                <li>记住TLS的全称：Transport Layer Security</li>
                <li>TLS工作在可靠传输协议(如TCP)之上</li>
                <li>TLS为上层应用提供安全服务，但本身属于传输层</li>
            </ol>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let canvas = document.getElementById('animationCanvas');
        let ctx = canvas.getContext('2d');

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'C') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.answer !== 'C') {
                    option.classList.add('wrong');
                }
            });

            if (selectedAnswer === 'C') {
                alert('🎉 恭喜答对了！TLS确实位于传输层！');
            } else {
                alert('❌ 答案错误。正确答案是C：传输层。让我们通过动画来理解为什么！');
            }
        }

        // 显示解析
        function showExplanation() {
            document.getElementById('explanation').classList.add('show');
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        // 解释各层
        function explainLayer(layer) {
            const explanations = {
                application: '应用层：用户直接接触的层面，如网页浏览(HTTP)、邮件(SMTP)等',
                transport: '传输层：负责端到端的数据传输，TCP提供可靠传输，TLS在此层提供安全保障',
                network: '网络层：负责数据包的路由和转发，IP协议工作在这一层',
                datalink: '数据链路层：负责相邻节点间的数据传输，如以太网、WiFi'
            };
            
            alert(explanations[layer]);
        }

        // 动画相关
        function startAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制四层
            const layers = [
                { name: '应用层', y: 50, color: '#667eea' },
                { name: '传输层', y: 150, color: '#f093fb' },
                { name: '网络层', y: 250, color: '#4facfe' },
                { name: '数据链路层', y: 350, color: '#43e97b' }
            ];

            layers.forEach((layer, index) => {
                setTimeout(() => {
                    drawLayer(layer.name, layer.y, layer.color);
                    if (layer.name === '传输层') {
                        setTimeout(() => drawTLS(layer.y), 500);
                    }
                }, index * 300);
            });

            // 绘制数据流动
            setTimeout(() => {
                animateDataFlow();
            }, 2000);
        }

        function drawLayer(name, y, color) {
            ctx.fillStyle = color;
            ctx.fillRect(50, y, 700, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, 400, y + 45);
        }

        function drawTLS(y) {
            ctx.fillStyle = '#ffd700';
            ctx.fillRect(550, y + 20, 100, 40);
            
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('TLS', 600, y + 45);
        }

        function animateDataFlow() {
            let x = 100;
            const animate = () => {
                ctx.clearRect(80, 0, 40, 400);
                
                // 绘制数据包
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(x, 90, 15, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('数据', x, 95);

                x += 2;
                if (x < 700) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function highlightTLS() {
            document.querySelector('.layer.transport').classList.add('highlight');
            setTimeout(() => {
                document.querySelector('.layer.transport').classList.remove('highlight');
            }, 3000);
        }

        // 初始化
        window.onload = function() {
            setTimeout(() => {
                startAnimation();
            }, 1500);
        };
    </script>
</body>
</html>
