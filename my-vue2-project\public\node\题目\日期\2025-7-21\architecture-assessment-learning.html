<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构评估学习 - 敏感点与权衡点</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        #gameCanvas {
            width: 100%;
            height: 500px;
            border-radius: 16px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        #gameCanvas:hover {
            transform: scale(1.02);
        }

        .concept-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 70px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .concept-description {
            font-size: 1rem;
            line-height: 1.6;
            color: #5a6c7d;
        }

        .interactive-demo {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 1.2s forwards;
        }

        .demo-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .slider-container {
            margin: 30px 0;
        }

        .slider-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 10px;
            display: block;
        }

        .slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #ecf0f1;
            outline: none;
            -webkit-appearance: none;
            margin-bottom: 20px;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .metric {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .options-container {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #ecf0f1;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 1rem;
            font-weight: 500;
            color: #495057;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            border-color: #4caf50;
            animation: pulse 1s ease-in-out;
        }

        .option.incorrect {
            background: #ffebee;
            border-color: #f44336;
            color: #d32f2f;
        }

        .reveal-answer-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
        }

        .reveal-answer-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .answer-section {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border-radius: 16px;
            padding: 25px;
            margin-top: 20px;
            border-left: 5px solid #4caf50;
        }

        .correct-answer {
            margin-bottom: 20px;
        }

        .correct-answer h4 {
            color: #2e7d32;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .correct-answer p {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1b5e20;
            margin: 5px 0;
        }

        .explanation {
            border-top: 1px solid #c8e6c9;
            padding-top: 15px;
        }

        .explanation h4 {
            color: #2e7d32;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .explanation p {
            line-height: 1.6;
            color: #2e7d32;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">架构评估学习</h1>
            <p class="subtitle">通过动画和交互理解敏感点与权衡点</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                在架构评估中，<span class="highlight">（ ）</span>是一个或多个构件(和或构件之间的关系的特性。改变加密级别的设计决策属于<span class="highlight">（ ）</span>，因为它可能会对安全性和性能产生非常重要的影响。
            </div>

            <div class="options-container">
                <h4 style="margin: 20px 0 15px 0; color: #2c3e50; font-size: 1.1rem;">选择题选项：</h4>
                <div class="options-grid">
                    <div class="option" data-option="A">A. 敏感点</div>
                    <div class="option" data-option="B">B. 非风险点</div>
                    <div class="option" data-option="C">C. 权衡点</div>
                    <div class="option" data-option="D">D. 风险点</div>
                </div>

                <button class="reveal-answer-btn" onclick="revealAnswer()">🎯 揭示正确答案</button>

                <div class="answer-section" id="answerSection" style="display: none;">
                    <div class="correct-answer">
                        <h4>✅ 正确答案：</h4>
                        <p><strong>第一空：A. 敏感点</strong></p>
                        <p><strong>第二空：C. 权衡点</strong></p>
                    </div>

                    <div class="explanation">
                        <h4>📚 详细解析：</h4>
                        <p><strong>敏感点</strong>：是一个或多个构件（和/或构件之间的关系）的特性，影响单一质量属性。</p>
                        <p><strong>权衡点</strong>：是影响多个质量属性的特性，是多个质量属性的敏感点。改变加密级别会同时影响安全性和性能，因此是权衡点。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas"></canvas>
        </div>

        <div class="concept-cards">
            <div class="concept-card floating">
                <h3 class="concept-title" style="color: #e74c3c;">🎯 敏感点 (Sensitivity Point)</h3>
                <p class="concept-description">
                    是一个或多个构件的特性，影响单一质量属性。就像调节音响的音量旋钮，只影响声音大小这一个属性。
                </p>
            </div>
            <div class="concept-card floating" style="animation-delay: 0.2s;">
                <h3 class="concept-title" style="color: #f39c12;">⚖️ 权衡点 (Tradeoff Point)</h3>
                <p class="concept-description">
                    影响多个质量属性的特性，是多个质量属性的敏感点。就像汽车的发动机功率，既影响速度又影响油耗。
                </p>
            </div>
        </div>

        <div class="interactive-demo">
            <h3 class="demo-title">🔐 加密级别交互演示</h3>
            <div class="slider-container">
                <label class="slider-label">加密级别</label>
                <input type="range" min="1" max="10" value="5" class="slider" id="encryptionSlider">
            </div>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="securityValue" style="color: #27ae60;">50%</div>
                    <div class="metric-label">🛡️ 安全性</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="performanceValue" style="color: #e74c3c;">50%</div>
                    <div class="metric-label">⚡ 性能</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="tradeoffValue" style="color: #f39c12;">中等</div>
                    <div class="metric-label">⚖️ 权衡程度</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Canvas 设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画变量
        let animationTime = 0;
        let selectedConcept = null;
        
        // 概念节点
        const concepts = [
            {
                x: 150, y: 150, radius: 60, 
                color: '#e74c3c', label: '敏感点',
                description: '影响单一质量属性',
                effects: ['性能优化', '内存使用', '响应时间']
            },
            {
                x: 450, y: 150, radius: 80, 
                color: '#f39c12', label: '权衡点',
                description: '影响多个质量属性',
                effects: ['安全性 ↔ 性能', '可用性 ↔ 一致性', '速度 ↔ 准确性']
            }
        ];

        // 粒子系统
        const particles = [];
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * canvas.width / window.devicePixelRatio,
                y: Math.random() * canvas.height / window.devicePixelRatio,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                alpha: Math.random() * 0.5 + 0.2
            });
        }

        function drawParticles() {
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.alpha;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();

                particle.x += particle.vx;
                particle.y += particle.vy;

                if (particle.x < 0 || particle.x > canvas.width / window.devicePixelRatio) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height / window.devicePixelRatio) particle.vy *= -1;
            });
        }

        function drawConcept(concept, isSelected = false) {
            const scale = isSelected ? 1.2 : 1;
            const glowSize = isSelected ? 20 : 10;
            
            // 发光效果
            ctx.save();
            ctx.shadowColor = concept.color;
            ctx.shadowBlur = glowSize;
            ctx.fillStyle = concept.color;
            ctx.globalAlpha = 0.8;
            ctx.beginPath();
            ctx.arc(concept.x, concept.y, concept.radius * scale, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();

            // 主圆圈
            ctx.fillStyle = concept.color;
            ctx.beginPath();
            ctx.arc(concept.x, concept.y, concept.radius * scale, 0, Math.PI * 2);
            ctx.fill();

            // 标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText(concept.label, concept.x, concept.y - 5);
            
            ctx.font = '12px SF Pro Display';
            ctx.fillText(concept.description, concept.x, concept.y + 15);

            // 如果选中，显示效果
            if (isSelected) {
                concept.effects.forEach((effect, index) => {
                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.font = '11px SF Pro Display';
                    ctx.fillText(effect, concept.x, concept.y + 50 + index * 20);
                });
            }
        }

        function animate() {
            animationTime += 0.02;
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio);
            
            // 绘制背景粒子
            drawParticles();
            
            // 绘制概念
            concepts.forEach(concept => {
                drawConcept(concept, selectedConcept === concept);
            });

            // 绘制连接线
            if (selectedConcept) {
                ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                concepts.forEach(concept => {
                    if (concept !== selectedConcept) {
                        ctx.moveTo(selectedConcept.x, selectedConcept.y);
                        ctx.lineTo(concept.x, concept.y);
                    }
                });
                ctx.stroke();
                ctx.setLineDash([]);
            }

            requestAnimationFrame(animate);
        }

        // 鼠标交互
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            concepts.forEach(concept => {
                const distance = Math.sqrt((x - concept.x) ** 2 + (y - concept.y) ** 2);
                if (distance < concept.radius) {
                    selectedConcept = selectedConcept === concept ? null : concept;
                }
            });
        });

        // 滑块交互
        const slider = document.getElementById('encryptionSlider');
        const securityValue = document.getElementById('securityValue');
        const performanceValue = document.getElementById('performanceValue');
        const tradeoffValue = document.getElementById('tradeoffValue');

        slider.addEventListener('input', (e) => {
            const level = parseInt(e.target.value);
            const security = level * 10;
            const performance = 100 - (level - 1) * 8; // 性能随加密级别降低
            
            securityValue.textContent = security + '%';
            performanceValue.textContent = performance + '%';
            
            // 权衡程度计算
            const tradeoffIntensity = Math.abs(security - performance);
            let tradeoffText = '';
            if (tradeoffIntensity < 20) tradeoffText = '轻微';
            else if (tradeoffIntensity < 40) tradeoffText = '中等';
            else if (tradeoffIntensity < 60) tradeoffText = '明显';
            else tradeoffText = '严重';
            
            tradeoffValue.textContent = tradeoffText;
            
            // 颜色变化
            securityValue.style.color = security > 70 ? '#27ae60' : security > 40 ? '#f39c12' : '#e74c3c';
            performanceValue.style.color = performance > 70 ? '#27ae60' : performance > 40 ? '#f39c12' : '#e74c3c';
            tradeoffValue.style.color = tradeoffIntensity > 40 ? '#e74c3c' : '#f39c12';
        });

        // 启动动画
        animate();

        // 答案揭示功能
        function revealAnswer() {
            const answerSection = document.getElementById('answerSection');
            const options = document.querySelectorAll('.option');
            const btn = document.querySelector('.reveal-answer-btn');

            // 显示答案区域
            answerSection.style.display = 'block';
            answerSection.style.animation = 'fadeInUp 0.8s ease-out';

            // 标记正确答案
            options.forEach(option => {
                const optionText = option.getAttribute('data-option');
                if (optionText === 'A' || optionText === 'C') {
                    option.classList.add('correct');
                } else {
                    option.classList.add('incorrect');
                }
            });

            // 隐藏按钮
            btn.style.display = 'none';

            // 添加庆祝动画
            setTimeout(() => {
                createConfetti();
            }, 500);
        }

        // 庆祝动画
        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.cssText = `
                    position: fixed;
                    width: 10px;
                    height: 10px;
                    background: ${['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)]};
                    left: ${Math.random() * 100}vw;
                    top: -10px;
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
                `;
                document.body.appendChild(confetti);

                setTimeout(() => confetti.remove(), 5000);
            }
        }

        // 添加confetti动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 添加提示信息
        setTimeout(() => {
            if (!selectedConcept) {
                const hint = document.createElement('div');
                hint.style.cssText = `
                    position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                    background: rgba(0,0,0,0.8); color: white; padding: 15px 25px;
                    border-radius: 25px; font-size: 14px; z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                hint.textContent = '💡 点击圆圈查看详细信息！';
                document.body.appendChild(hint);

                setTimeout(() => {
                    hint.style.opacity = '0';
                    setTimeout(() => hint.remove(), 500);
                }, 3000);
            }
        }, 2000);
    </script>
</body>
</html>
