<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Distribute</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #337ab7;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #ecf0f1;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #337ab7;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #286090;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        distribute
        <span style="font-size: 0.5em; color: #555;">(dis- + tribute)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>想象一位慷慨的国王，他决定将城堡里的财富（tribute）分配（dis- apart）给王国的四方百姓。这个故事就讲述了财富是如何从一个中心点"分开"并"给予"出去的。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="disBtn">第一幕: dis- (分开)</button>
        <button id="tributeBtn">第二幕: tribute (给予)</button>
        <button id="distributeBtn">第三幕: distribute (分配)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "distribute" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const disBtn = document.getElementById('disBtn');
    const tributeBtn = document.getElementById('tributeBtn');
    const distributeBtn = document.getElementById('distributeBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    const colors = {
        background: '#ecf0f1',
        castle: '#7f8c8d',
        text: '#2c3e50',
        treasure: '#f1c40f',
        arrow: '#2980b9',
        path: '#bdc3c7'
    };
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };
    
    const castle = { x: 400, y: 225, width: 80, height: 100 };

    function drawCastle() {
        ctx.fillStyle = colors.castle;
        // Main body
        ctx.fillRect(castle.x - castle.width / 2, castle.y - castle.height / 2, castle.width, castle.height);
        // Towers
        ctx.fillRect(castle.x - castle.width / 2 - 20, castle.y - castle.height / 2 + 20, 20, 80);
        ctx.fillRect(castle.x + castle.width / 2, castle.y - castle.height / 2 + 20, 20, 80);
        // Roof
        ctx.beginPath();
        ctx.moveTo(castle.x - castle.width / 2, castle.y - castle.height / 2);
        ctx.lineTo(castle.x, castle.y - castle.height / 2 - 40);
        ctx.lineTo(castle.x + castle.width / 2, castle.y - castle.height / 2);
        ctx.closePath();
        ctx.fill();
    }

    function drawTreasure(x, y, size) {
        ctx.fillStyle = colors.treasure;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.beginPath();
        ctx.arc(x - size/3, y - size/3, size/4, 0, Math.PI*2);
        ctx.fill();
    }
    
    const destinations = [
        { x: 100, y: 100 },
        { x: 700, y: 100 },
        { x: 100, y: 350 },
        { x: 700, y: 350 }
    ];

    function drawPaths() {
        ctx.strokeStyle = colors.path;
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        destinations.forEach(dest => {
            ctx.beginPath();
            ctx.moveTo(castle.x, castle.y);
            ctx.lineTo(dest.x, dest.y);
            ctx.stroke();
        });
        ctx.setLineDash([]);
    }

    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawCastle();
        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('distribute', canvas.width / 2, 50);
        drawTreasure(castle.x, castle.y, 15);
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "distribute" 的含义吧！</p>';
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: dis-
    function animateDis() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>dis- (apart, 分开)</strong>: 这个前缀意味着"分开"或"向不同方向"。动画将展示从中心向外扩散的路径。</p>';
        let progress = 0;
        
        function animate() {
            if (progress > 1) {
                // Draw final arrows
                destinations.forEach(dest => {
                    const angle = Math.atan2(dest.y - castle.y, dest.x - castle.x);
                    drawArrow(dest.x, dest.y, angle);
                });
                return;
            }
            progress += 0.02;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawCastle();
            drawTreasure(castle.x, castle.y, 15);
            ctx.fillText('dis-', canvas.width / 2, 50);
            
            ctx.strokeStyle = colors.arrow;
            ctx.lineWidth = 3;

            destinations.forEach(dest => {
                const targetX = castle.x + (dest.x - castle.x) * progress;
                const targetY = castle.y + (dest.y - castle.y) * progress;
                ctx.beginPath();
                ctx.moveTo(castle.x, castle.y);
                ctx.lineTo(targetX, targetY);
                ctx.stroke();
            });

            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    function drawArrow(x, y, angle) {
        ctx.fillStyle = colors.arrow;
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x - 15 * Math.cos(angle - Math.PI / 6), y - 15 * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(x - 15 * Math.cos(angle + Math.PI / 6), y - 15 * Math.sin(angle + Math.PI / 6));
        ctx.closePath();
        ctx.fill();
    }

    // 第二幕: tribute
    function animateTribute() {
        resetAnimation();
        drawPaths();
        explanationDiv.innerHTML = '<p><strong>tribute (to give, 给予)</strong>: 这个词根意为"给予"。想象一下，这就是国王要分发给人民的财富。</p>';
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawCastle();
        drawPaths();
        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('tribute', canvas.width / 2, 50);
        
        let size = 10;
        let growing = true;
        function animate() {
            if (growing) {
                size += 0.2;
                if (size > 20) growing = false;
            } else {
                size -= 0.2;
                if (size < 10) growing = true;
            }
            ctx.clearRect(castle.x - 30, castle.y - 30, 60, 60);
            // Redraw small part of castle
            ctx.fillStyle = colors.castle;
            ctx.fillRect(castle.x - castle.width/2, castle.y, castle.width, castle.height/2);
            drawTreasure(castle.x, castle.y, size);
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }

    // 第三幕: distribute
    function animateDistribute(onComplete) {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>distribute (分配)</strong>: 将财富(tribute)向四方(dis-)给予(give)，就是分配！</p>';
        let progress = 0;
        const treasures = destinations.map(d => ({ x: castle.x, y: castle.y }));

        function animate() {
            if (progress > 1) {
                if(onComplete) onComplete();
                return;
            }
            progress += 0.01;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawCastle();
            drawPaths();
            ctx.fillStyle = colors.text;
            ctx.font = fonts.title;
            ctx.textAlign = 'center';
            ctx.fillText('distribute', canvas.width / 2, 50);

            for(let i = 0; i < treasures.length; i++) {
                const dest = destinations[i];
                const treasure = treasures[i];
                treasure.x = castle.x + (dest.x - castle.x) * progress;
                treasure.y = castle.y + (dest.y - castle.y) * progress;
                drawTreasure(treasure.x, treasure.y, 12);
            }

            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    function playAll() {
        resetAnimation();
        animateDis();
        setTimeout(() => {
            cancelAnimationFrame(animationFrameId);
            animateTribute();
            setTimeout(()=> {
                cancelAnimationFrame(animationFrameId);
                animateDistribute();
            }, 3000);
        }, 3000);
    }

    // Event Listeners
    playBtn.addEventListener('click', playAll);
    disBtn.addEventListener('click', animateDis);
    tributeBtn.addEventListener('click', animateTribute);
    distributeBtn.addEventListener('click', () => animateDistribute(null));
    resetBtn.addEventListener('click', resetAnimation);

    // Initial draw
    window.addEventListener('load', drawInitialState);

</script>
</body>
</html> 